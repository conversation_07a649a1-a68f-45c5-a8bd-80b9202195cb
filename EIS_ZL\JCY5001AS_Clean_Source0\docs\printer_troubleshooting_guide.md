# 打印机故障排除指南

## 问题描述
更换了相同型号的打印机后，电池测试系统无法正常打印标签。

## 常见原因分析

### 1. 设备名称不匹配
- **问题**: 新打印机的设备名称与配置中的名称不一致
- **表现**: 系统显示"打印机未连接"或"打印机不可用"
- **解决方案**: 更新系统配置中的打印机名称

### 2. 驱动程序问题
- **问题**: 新打印机驱动未正确安装或版本不兼容
- **表现**: 打印任务发送失败或打印质量异常
- **解决方案**: 重新安装最新驱动程序

### 3. 端口配置错误
- **问题**: USB端口或通信设置不正确
- **表现**: 系统无法检测到打印机
- **解决方案**: 检查USB连接和端口设置

### 4. 系统缓存问题
- **问题**: 系统仍在寻找旧打印机的配置信息
- **表现**: 间歇性连接问题
- **解决方案**: 清除缓存并重新配置

## 自动修复步骤

### 方法一：使用诊断工具（推荐）

1. **运行诊断工具**
   ```bash
   python utils/printer_diagnostic_tool.py
   ```

2. **查看诊断报告**
   - 系统会自动检测所有可用打印机
   - 显示当前配置状态
   - 提供修复建议

3. **应用自动修复**
   - 选择推荐的NIIMBOT打印机
   - 系统会自动更新配置
   - 验证修复结果

### 方法二：使用修复脚本

1. **运行修复脚本**
   ```bash
   python scripts/fix_printer_config.py
   ```

2. **按照提示操作**
   - 选择要应用的修复选项
   - 确认修复操作
   - 验证修复结果

## 手动修复步骤

### 步骤1：检查硬件连接

1. **验证物理连接**
   - 确认USB线连接牢固
   - 检查打印机电源指示灯
   - 确认打印机处于就绪状态

2. **检查Windows设备管理器**
   - 打开设备管理器
   - 查看"打印机"或"通用串行总线控制器"
   - 确认没有黄色警告图标

### 步骤2：更新驱动程序

1. **卸载旧驱动**
   - 控制面板 → 设备和打印机
   - 右键点击旧打印机 → 删除设备
   - 删除驱动程序包

2. **安装新驱动**
   - 下载最新NIIMBOT驱动程序
   - 以管理员身份运行安装程序
   - 重启计算机

3. **验证安装**
   - 检查打印机是否出现在设备列表中
   - 打印Windows测试页验证功能

### 步骤3：更新系统配置

1. **获取新打印机名称**
   - 打开控制面板 → 设备和打印机
   - 记录新打印机的确切名称

2. **更新配置文件**
   - 打开 `config/app_config.json`
   - 找到 `printer.name` 字段
   - 更新为新打印机的确切名称

3. **重启系统**
   - 关闭电池测试系统
   - 重新启动程序
   - 验证打印机状态

### 步骤4：测试打印功能

1. **系统内测试**
   - 进入设备设置
   - 点击"测试打印"按钮
   - 确认测试页正常打印

2. **标签打印测试**
   - 进入标签设计器
   - 点击"预打印"按钮
   - 验证标签内容和格式

## 配置文件说明

### 关键配置项

```json
{
  "printer": {
    "name": "NIIMBOT K3_W",           // 打印机名称（需要精确匹配）
    "type": "热敏打印机",              // 打印机类型
    "connection": "USB",              // 连接方式
    "quality": "最佳",                // 打印质量
    "density": "high",               // 打印浓度
    "contrast": "high"               // 对比度
  }
}
```

### 重要注意事项

1. **打印机名称必须精确匹配**
   - 包括空格、特殊字符
   - 区分大小写
   - 不能有多余的字符

2. **支持的打印机类型**
   - NIIMBOT K3_W（推荐）
   - 其他兼容的热敏打印机

## 常见错误及解决方案

### 错误1：打印机未连接
```
错误信息：打印机未连接或不可用
解决方案：
1. 检查USB连接
2. 重新安装驱动
3. 更新配置文件中的打印机名称
```

### 错误2：打印任务失败
```
错误信息：打印任务发送失败
解决方案：
1. 检查打印机状态（缺纸、卡纸等）
2. 重启打印机
3. 清除打印队列
```

### 错误3：打印质量异常
```
错误信息：打印内容模糊或不完整
解决方案：
1. 调整打印质量设置
2. 检查标签纸规格
3. 清洁打印头
```

## 预防措施

### 1. 标准化配置
- 使用统一的打印机型号
- 建立标准的安装流程
- 记录配置参数

### 2. 定期维护
- 定期检查打印机状态
- 及时更新驱动程序
- 清洁打印设备

### 3. 备份配置
- 备份工作配置文件
- 记录成功的设置参数
- 建立快速恢复流程

## 技术支持

如果以上步骤无法解决问题，请联系技术支持：

1. **收集诊断信息**
   - 运行诊断工具生成报告
   - 记录错误信息和日志
   - 截图相关设置界面

2. **提供设备信息**
   - 打印机型号和序列号
   - 操作系统版本
   - 驱动程序版本

3. **联系方式**
   - 技术支持邮箱：<EMAIL>
   - 技术支持电话：400-xxx-xxxx
   - 在线技术文档：https://docs.example.com
