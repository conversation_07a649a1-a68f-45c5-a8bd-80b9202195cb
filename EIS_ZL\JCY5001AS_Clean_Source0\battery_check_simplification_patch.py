
# 简化电池检测方法补丁
# 在 backend/battery_pre_test_checker.py 中替换 _check_single_channel_battery_enhanced 方法

def _check_single_channel_battery_enhanced(self, channel_num: int) -> Dict[str, Any]:
    """
    简化版单通道电池状态检查（仅电压检测，无阻抗响应检测）
    
    Args:
        channel_num: 通道号
        
    Returns:
        检测结果：{'status': 'valid'|'no_battery'|'error', 'reason': '原因', 'voltage': 电压值}
    """
    try:
        # 仅进行电压检测
        voltage = self.device_config_manager.read_channel_voltage(channel_num)
        
        if voltage is None:
            return {
                'status': 'error',
                'reason': '电压读取失败',
                'voltage': 0.0
            }
        
        # 检查电压范围
        if voltage < self.voltage_min_threshold:
            return {
                'status': 'no_battery',
                'reason': f'电压过低: {voltage:.3f}V',
                'voltage': voltage
            }
        elif voltage > self.voltage_max_threshold:
            return {
                'status': 'no_battery', 
                'reason': f'电压过高: {voltage:.3f}V',
                'voltage': voltage
            }
        else:
            # 🔧 优化：跳过阻抗响应能力检测，直接返回有效
            return {
                'status': 'valid',
                'reason': f'电压正常: {voltage:.3f}V (已跳过阻抗检测)',
                'voltage': voltage
            }
            
    except Exception as e:
        return {
            'status': 'error',
            'reason': f'检测异常: {str(e)}',
            'voltage': 0.0
        }
