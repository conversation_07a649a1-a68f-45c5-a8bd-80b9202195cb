# -*- coding: utf-8 -*-
"""
组件初始化管理器
从MainWindow中提取的组件初始化相关功能

职责：
- UI组件创建
- 管理器初始化
- 信号连接设置
- 组件配置

Author: Jack
Date: 2025-01-30
"""

import logging
from typing import Dict, Any
from PyQt5.QtCore import QObject

logger = logging.getLogger(__name__)


class ComponentInitializer(QObject):
    """
    组件初始化管理器
    
    职责：
    - 各种管理器的初始化
    - UI组件的创建和配置
    - 信号连接的建立
    - 组件间依赖关系设置
    """
    
    def __init__(self, main_window, config_manager):
        """
        初始化组件初始化管理器
        
        Args:
            main_window: 主窗口实例
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.main_window = main_window
        self.config_manager = config_manager
        
        logger.info("组件初始化管理器初始化完成")
    
    def initialize_communication_manager(self):
        """初始化通信管理器"""
        try:
            from backend.communication_manager import CommunicationManager
            
            # 获取通信配置
            comm_config = {
                'port': self.config_manager.get('device.connection.port', 'COM48'),
                'baudrate': self.config_manager.get('device.connection.baudrate', 115200),
                'device_address': self.config_manager.get('device.connection.device_address', 1),
                'timeout': self.config_manager.get('device.connection.timeout', 2.0)
            }
            
            self.main_window.comm_manager = CommunicationManager(comm_config)
            logger.info("通信管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化通信管理器失败: {e}")
            raise
    
    def initialize_ui_managers(self):
        """初始化UI相关管理器"""
        try:
            # 1. 窗口管理器
            from ui.main_window_managers.window_layout_manager import WindowLayoutManager
            self.main_window.window_layout_manager = WindowLayoutManager(
                self.main_window, self.config_manager
            )
            
            # 2. 菜单管理器
            from ui.menu_manager import MenuManager
            self.main_window.menu_manager = MenuManager(self.main_window, self.config_manager)
            
            # 3. UI组件管理器
            from ui.ui_component_manager import UIComponentManager
            self.main_window.ui_component_manager = UIComponentManager(
                self.main_window, self.config_manager
            )
            
            logger.info("UI管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化UI管理器失败: {e}")
            raise
    
    def initialize_device_managers(self):
        """初始化设备相关管理器"""
        try:
            # 设备连接管理器
            from ui.device_connection_manager import DeviceConnectionManager
            self.main_window.device_connection_manager = DeviceConnectionManager(
                self.main_window, self.config_manager, self.main_window.comm_manager
            )

            # 电池检测管理器 - 使用新的实时电压检测
            from backend.voltage_based_battery_detection_manager import VoltageBasedBatteryDetectionManager
            self.main_window.battery_detection_manager = VoltageBasedBatteryDetectionManager(
                self.main_window.comm_manager, self.config_manager
            )

            # 测试流程管理器
            from ui.test_flow_manager import TestFlowManager
            self.main_window.test_flow_manager = TestFlowManager(
                self.main_window, self.config_manager,
                self.main_window.comm_manager, self.main_window.device_connection_manager
            )

            logger.info("设备管理器初始化完成")

        except Exception as e:
            logger.error(f"初始化设备管理器失败: {e}")
            raise
    
    def initialize_printer_managers(self):
        """初始化打印机相关管理器"""
        try:
            # 打印机管理器
            from ui.printer_manager import PrinterManager
            self.main_window.printer_manager = PrinterManager(self.config_manager)

            # 标签打印管理器
            from ui.label_print_manager import LabelPrintManager
            self.main_window.label_print_manager = LabelPrintManager(
                self.config_manager, self.main_window.printer_manager
            )
            
            logger.info("打印机管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化打印机管理器失败: {e}")
            raise

    def create_ui_components(self, main_layout):
        """
        创建UI组件

        Args:
            main_layout: 主布局
        """
        try:
            # 创建精确比例布局容器
            containers = self.main_window.window_layout_manager.create_proportional_layout(main_layout)

            if not containers:
                logger.error("创建比例布局失败")
                return

            # 创建顶部标题栏（在header容器中）
            header_container = containers['header']
            self.main_window.ui_component_manager.create_header_widget_in_container(header_container)

            # 创建上层区域组件（在upper容器中）
            upper_container = containers['upper']
            upper_layout = self.main_window.window_layout_manager.create_upper_layout(upper_container)
            self.main_window.ui_component_manager.create_upper_widgets(upper_layout)

            # 设置上层区域比例
            self._setup_upper_widget_proportions(upper_layout)

            # 创建通道容器（分为两行）
            channels_row1_container = containers['channels_row1']
            channels_row2_container = containers['channels_row2']
            self.main_window.ui_component_manager.create_split_channels_container(
                channels_row1_container, channels_row2_container
            )

            # 创建状态栏
            self.main_window.ui_component_manager.create_status_bar()

            logger.info("UI组件创建完成")

        except Exception as e:
            logger.error(f"创建UI组件失败: {e}")
            raise

    def _setup_upper_widget_proportions(self, upper_layout):
        """设置上层区域组件比例"""
        try:
            batch_widget = self.main_window.ui_component_manager.get_component('batch_info')
            statistics_widget = self.main_window.ui_component_manager.get_component('statistics')
            control_widget = self.main_window.ui_component_manager.get_component('test_control')

            if all([batch_widget, statistics_widget, control_widget]):
                self.main_window.window_layout_manager.setup_upper_widget_proportions(
                    upper_layout, batch_widget, statistics_widget, control_widget
                )
            else:
                logger.warning("部分上层组件未找到，无法设置比例")

        except Exception as e:
            logger.error(f"设置上层组件比例失败: {e}")

    def setup_signal_connections(self):
        """设置信号连接"""
        try:
            # UI组件信号连接
            self.main_window.ui_component_manager.setup_signal_connections()

            # 设备连接管理器信号
            self.main_window.device_connection_manager.connection_status_changed.connect(
                self.main_window._on_device_connection_changed
            )
            self.main_window.device_connection_manager.device_info_updated.connect(
                self.main_window._on_device_info_updated
            )

            # 测试流程管理器信号
            self.main_window.test_flow_manager.test_started.connect(self.main_window._on_test_started)
            self.main_window.test_flow_manager.test_stopped.connect(self.main_window._on_test_stopped)
            self.main_window.test_flow_manager.test_progress_updated.connect(
                self.main_window._on_test_progress_updated
            )
            self.main_window.test_flow_manager.test_failed.connect(self.main_window._on_test_failed)
            self.main_window.test_flow_manager.channel_test_completed.connect(
                self.main_window._on_channel_test_completed
            )

            # UI组件管理器信号
            self.main_window.ui_component_manager.component_ready.connect(
                self.main_window._on_component_ready
            )
            self.main_window.ui_component_manager.component_error.connect(
                self.main_window._on_component_error
            )

            # 打印机管理器信号
            self.main_window.printer_manager.printer_status_changed.connect(
                self.main_window._on_printer_status_changed
            )

            # 标签打印管理器信号
            self.main_window.label_print_manager.print_started.connect(
                self.main_window._on_label_print_started
            )
            self.main_window.label_print_manager.print_completed.connect(
                self.main_window._on_label_print_completed
            )
            self.main_window.label_print_manager.print_queue_updated.connect(
                self.main_window._on_print_queue_updated
            )

            # 🔧 修复：配置变更信号连接
            if hasattr(self.main_window, 'config_changed'):
                self.main_window.config_changed.connect(self.main_window._on_config_changed)
                logger.debug("已连接主窗口配置变更信号")

            # 🔧 修复：连接配置管理器的配置变更信号
            if hasattr(self.main_window, 'config_manager') and hasattr(self.main_window.config_manager, 'config_changed'):
                self.main_window.config_manager.config_changed.connect(self.main_window._on_config_changed)
                logger.debug("已连接配置管理器配置变更信号")

            logger.info("信号连接设置完成")

        except Exception as e:
            logger.error(f"设置信号连接失败: {e}")

    def get_initialization_status(self) -> Dict[str, Any]:
        """
        获取初始化状态

        Returns:
            初始化状态字典
        """
        try:
            manager_status = {
                'communication_manager': hasattr(self.main_window, 'comm_manager'),
                'window_layout_manager': hasattr(self.main_window, 'window_layout_manager'),
                'menu_manager': hasattr(self.main_window, 'menu_manager'),
                'ui_component_manager': hasattr(self.main_window, 'ui_component_manager'),
                'device_connection_manager': hasattr(self.main_window, 'device_connection_manager'),
                'test_flow_manager': hasattr(self.main_window, 'test_flow_manager'),
                'printer_manager': hasattr(self.main_window, 'printer_manager'),
                'label_print_manager': hasattr(self.main_window, 'label_print_manager')
            }

            # 创建结果字典，包含统计信息
            result: Dict[str, Any] = dict(manager_status)  # 复制管理器状态
            result['all_initialized'] = all(manager_status.values())
            result['initialized_count'] = sum(manager_status.values())
            result['total_count'] = len(manager_status)

            return result

        except Exception as e:
            logger.error(f"获取初始化状态失败: {e}")
            return {'error': str(e)}