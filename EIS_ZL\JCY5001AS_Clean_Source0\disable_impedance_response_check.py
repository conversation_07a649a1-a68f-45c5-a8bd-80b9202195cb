#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁用阻抗响应能力检测
直接修改电池检测器，移除不必要的12秒阻抗响应检测

Author: Cline
Date: 2025-06-21
"""

import json
import logging
import os
import shutil
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)


def backup_file(file_path):
    """备份文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    shutil.copy2(file_path, backup_path)
    logger.info(f"文件已备份: {backup_path}")
    return backup_path


def disable_impedance_response_check():
    """禁用阻抗响应能力检测"""
    
    battery_checker_path = "backend/battery_pre_test_checker.py"
    
    # 备份原文件
    backup_path = backup_file(battery_checker_path)
    
    # 读取原文件
    with open(battery_checker_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改内容：禁用阻抗响应检测
    modified_content = content.replace(
        'self.impedance_response_check_enabled = True  # 是否启用阻抗响应检测',
        'self.impedance_response_check_enabled = False  # 🔧 优化：禁用阻抗响应检测，节省12秒'
    )
    
    # 如果上面的替换没有找到，尝试其他可能的位置
    if modified_content == content:
        # 在初始化方法中查找并修改
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'impedance_response_check_enabled = True' in line:
                lines[i] = line.replace('True', 'False') + '  # 🔧 优化：禁用阻抗响应检测，节省12秒'
                break
        modified_content = '\n'.join(lines)
    
    # 写入修改后的文件
    with open(battery_checker_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    logger.info(f"✅ 已禁用阻抗响应能力检测")
    logger.info(f"   文件: {battery_checker_path}")
    logger.info(f"   备份: {backup_path}")
    logger.info(f"   预期节省时间: 约10-12秒")
    
    return True


def update_config_file():
    """更新配置文件，添加相关设置"""
    
    config_path = "config/app_config.json"
    
    # 备份配置文件
    backup_path = backup_file(config_path)
    
    # 读取配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 添加或更新电池检测配置
    if 'battery_detection' not in config:
        config['battery_detection'] = {}
    
    # 更新配置
    config['battery_detection'].update({
        'enabled': True,
        'voltage_threshold_min': 2.0,
        'voltage_threshold_max': 5.0,
        'detection_interval': 0.5,  # 减少检测间隔
        'stable_count_required': 2,  # 减少稳定计数要求
        'auto_restart_delay': 1.0,   # 减少重启延时
        'impedance_response_check_enabled': False,  # 禁用阻抗响应检测
        'optimization_note': "已禁用阻抗响应检测以节省12秒启动时间"
    })
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 配置文件已更新")
    logger.info(f"   文件: {config_path}")
    logger.info(f"   备份: {backup_path}")
    
    return True


def create_simple_battery_check_method():
    """创建简化的电池检测方法补丁"""
    
    patch_content = '''
# 简化电池检测方法补丁
# 在 backend/battery_pre_test_checker.py 中替换 _check_single_channel_battery_enhanced 方法

def _check_single_channel_battery_enhanced(self, channel_num: int) -> Dict[str, Any]:
    """
    简化版单通道电池状态检查（仅电压检测，无阻抗响应检测）
    
    Args:
        channel_num: 通道号
        
    Returns:
        检测结果：{'status': 'valid'|'no_battery'|'error', 'reason': '原因', 'voltage': 电压值}
    """
    try:
        # 仅进行电压检测
        voltage = self.device_config_manager.read_channel_voltage(channel_num)
        
        if voltage is None:
            return {
                'status': 'error',
                'reason': '电压读取失败',
                'voltage': 0.0
            }
        
        # 检查电压范围
        if voltage < self.voltage_min_threshold:
            return {
                'status': 'no_battery',
                'reason': f'电压过低: {voltage:.3f}V',
                'voltage': voltage
            }
        elif voltage > self.voltage_max_threshold:
            return {
                'status': 'no_battery', 
                'reason': f'电压过高: {voltage:.3f}V',
                'voltage': voltage
            }
        else:
            # 🔧 优化：跳过阻抗响应能力检测，直接返回有效
            return {
                'status': 'valid',
                'reason': f'电压正常: {voltage:.3f}V (已跳过阻抗检测)',
                'voltage': voltage
            }
            
    except Exception as e:
        return {
            'status': 'error',
            'reason': f'检测异常: {str(e)}',
            'voltage': 0.0
        }
'''
    
    patch_file = "battery_check_simplification_patch.py"
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    logger.info(f"简化电池检测补丁已创建: {patch_file}")
    return patch_file


def main():
    """主函数"""
    print("=== 禁用阻抗响应能力检测工具 ===")
    print("目标：移除12秒的不必要阻抗响应检测")
    
    try:
        # 1. 禁用阻抗响应检测
        logger.info("1. 禁用阻抗响应能力检测...")
        disable_impedance_response_check()
        
        # 2. 更新配置文件
        logger.info("2. 更新配置文件...")
        update_config_file()
        
        # 3. 创建补丁文件
        logger.info("3. 创建简化检测方法补丁...")
        patch_file = create_simple_battery_check_method()
        
        print("\n=== 优化完成 ===")
        print("✅ 已禁用阻抗响应能力检测")
        print("✅ 预期节省时间: 约10-12秒")
        print("✅ 配置文件已更新")
        print(f"✅ 补丁文件已创建: {patch_file}")
        
        print("\n=== 重启说明 ===")
        print("请重启应用程序以使更改生效")
        print("重启后，电池检测将只进行电压检测，大大缩短检测时间")
        
        print("\n=== 验证方法 ===")
        print("重启后观察日志，应该看到：")
        print("- 电池检测时间从12秒减少到1-2秒")
        print("- 日志中显示 '已跳过阻抗检测'")
        
        return True
        
    except Exception as e:
        logger.error(f"优化失败: {e}")
        return False


if __name__ == "__main__":
    main()
