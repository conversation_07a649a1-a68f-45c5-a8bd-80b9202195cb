<?xml version="1.0" encoding="UTF-8"?>
<!-- Microsoft Visio模板文件 -->
<!-- 可以导入到Visio或其他支持的绘图软件中 -->

<visio:drawing xmlns:visio="http://schemas.microsoft.com/visio/2003/core">
    <visio:pages>
        <visio:page name="图1-系统架构图" width="1400" height="1000">
            
            <!-- 标题 -->
            <visio:shape id="title" type="text" x="700" y="50" width="800" height="40">
                <visio:text font-size="24" font-weight="bold" text-align="center">
                    图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图
                </visio:text>
            </visio:shape>
            
            <!-- 系统组件 -->
            <visio:shape id="battery" type="rectangle" x="100" y="150" width="200" height="120" 
                        fill="#ADD8E6" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>1-被测电池
1.9V-5.5V</visio:text>
            </visio:shape>
            
            <visio:shape id="fixture" type="rectangle" x="100" y="350" width="200" height="120" 
                        fill="#F5DEB3" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>7-测试夹具
四线制连接
精确测量</visio:text>
            </visio:shape>
            
            <visio:shape id="chip" type="rectangle" x="450" y="300" width="300" height="200" 
                        fill="#90EE90" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>2-DNB1101BB
EIS测试芯片
0.0075Hz-7800Hz</visio:text>
            </visio:shape>
            
            <visio:shape id="current_source" type="rectangle" x="100" y="600" width="200" height="150" 
                        fill="#F08080" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>4-外部电流源
PMV28UNEA
20Ω/10Ω/6.67Ω/5Ω</visio:text>
            </visio:shape>
            
            <visio:shape id="mcu" type="rectangle" x="850" y="300" width="250" height="200" 
                        fill="#FFFFE0" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>3-STM32F103RCT6
主控制器
72MHz ARM</visio:text>
            </visio:shape>
            
            <visio:shape id="display" type="rectangle" x="850" y="600" width="250" height="150" 
                        fill="#D3D3D3" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>5-串口显示屏
实时显示
测试结果</visio:text>
            </visio:shape>
            
            <visio:shape id="pc" type="rectangle" x="850" y="100" width="250" height="120" 
                        fill="#B0C4DE" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>6-PC上位机
Modbus RTU
数据分析</visio:text>
            </visio:shape>
            
            <!-- 连接器 -->
            <visio:connector id="conn1" from="battery" to="fixture" type="double-arrow" 
                           stroke="black" stroke-width="2" label="电气连接"/>
            
            <visio:connector id="conn2" from="fixture" to="chip" type="arrow" 
                           stroke="blue" stroke-width="2" label="电压/电流测量信号"/>
            
            <visio:connector id="conn3" from="chip" to="mcu" type="arrow" 
                           stroke="purple" stroke-width="2" label="SPI 1Mbps"/>
            
            <visio:connector id="conn4" from="mcu" to="pc" type="arrow" 
                           stroke="red" stroke-width="2" label="USB/UART"/>
            
            <visio:connector id="conn5" from="mcu" to="display" type="arrow" 
                           stroke="green" stroke-width="2" label="UART 115200bps"/>
            
            <visio:connector id="conn6" from="chip" to="current_source" type="arrow" 
                           stroke="orange" stroke-width="2" label="VSW/VDR控制信号"/>
            
            <visio:connector id="conn7" from="current_source" to="fixture" type="arrow" 
                           stroke="red" stroke-width="2" label="激励电流"/>
            
            <!-- 说明框 -->
            <visio:shape id="description" type="rectangle" x="400" y="800" width="600" height="120" 
                        fill="#FFFACD" stroke="gray" stroke-width="1">
                <visio:text>信号流向说明：
1. 电池通过测试夹具连接到系统
2. DNB1101BB芯片测量电池的电压和电流
3. 外部电流源提供EIS测试所需的激励信号
4. STM32控制器处理测试数据和系统控制
5. 测试结果同时显示在本地屏幕和上位机</visio:text>
            </visio:shape>
            
            <!-- 技术参数 -->
            <visio:shape id="params" type="text" x="400" y="970" width="600" height="30">
                <visio:text font-style="italic" text-align="center">
                    系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C
                </visio:text>
            </visio:shape>
            
        </visio:page>
    </visio:pages>
</visio:drawing>