# -*- coding: utf-8 -*-
"""
奈奎斯特图管理器
负责奈奎斯特图绘制和交互功能

从data_export_dialog.py中提取，遵循单一职责原则

Author: Augment Agent  
Date: 2025-06-04
"""

import logging
from typing import List, Dict, Optional
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QCheckBox
from PyQt5.QtCore import QTimer

logger = logging.getLogger(__name__)

# matplotlib导入处理
try:
    import numpy as np
    import matplotlib
    matplotlib.use('Qt5Agg')  # 设置后端

    import matplotlib.pyplot as plt
    try:
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    except ImportError:
        from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.font_manager as fm

    # 设置中文字体
    try:
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
        for font_name in chinese_fonts:
            try:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                logger.debug(f"成功设置matplotlib中文字体: {font_name}")
                break
            except:
                continue
        else:
            logger.warning("未找到合适的中文字体，使用默认字体")
    except Exception as e:
        logger.warning(f"设置matplotlib中文字体失败: {e}")

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    
    # 创建占位符类
    class DummyFigureCanvas:
        def __init__(self, *args, **kwargs):
            pass
        def setMinimumSize(self, *args):
            pass
        def draw(self):
            pass
        def draw_idle(self):
            pass
        def mpl_connect(self, *args):
            pass

    class DummyFigure:
        def __init__(self, *args, **kwargs):
            pass
        def add_subplot(self, *args):
            return DummyAxes()
        def clear(self):
            pass

    class DummyAxes:
        def __init__(self):
            pass
        def set_xlabel(self, *args): pass
        def set_ylabel(self, *args): pass
        def set_title(self, *args): pass
        def grid(self, *args, **kwargs): pass
        def set_aspect(self, *args): pass
        def clear(self): pass
        def scatter(self, *args, **kwargs): return None
        def plot(self, *args, **kwargs): return None
        def legend(self, *args, **kwargs): pass
        def set_xlim(self, *args): pass
        def set_ylim(self, *args): pass
        def text(self, *args, **kwargs): pass
        def get_xlim(self): return (0, 1)
        def get_ylim(self): return (0, 1)
        def annotate(self, *args, **kwargs): return None

    FigureCanvas = DummyFigureCanvas
    Figure = DummyFigure
    np = None
    plt = None


class NyquistPlotManager:
    """
    奈奎斯特图管理器

    职责：
    - 管理奈奎斯特图的绘制
    - 处理图表交互（缩放、悬停等）
    - 管理多通道显示
    - 阻抗单位切换功能
    """

    def __init__(self, parent_widget: QWidget):
        """
        初始化奈奎斯特图管理器

        Args:
            parent_widget: 父窗口部件
        """
        self.parent_widget = parent_widget

        # 图表相关变量
        self.nyquist_canvas = None
        self.nyquist_figure = None
        self.nyquist_ax = None

        # 交互相关变量
        self._hover_annotation = None
        self._current_hover_data = None
        self._current_plot_data = []

        # 多通道显示相关变量
        self._selected_channels_data = {}
        self._channel_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
        self._multi_channel_mode = False

        # 阻抗单位相关变量
        self._impedance_unit = 'mΩ'  # 默认使用毫欧
        self._unit_conversion_factor = 1.0  # mΩ到当前单位的转换因子

        # 延迟绘制相关变量
        self._pending_plot_details = None
        self._plot_timer = None

        # 创建图表组件
        self._create_plot_components()

        logger.debug("奈奎斯特图管理器初始化完成")
    
    def _create_plot_components(self):
        """创建图表组件"""
        if not MATPLOTLIB_AVAILABLE:
            from PyQt5.QtWidgets import QLabel
            self.nyquist_canvas = QLabel("奈奎斯特图功能需要安装matplotlib库")
            self.nyquist_canvas.setMinimumSize(400, 300)
            return

        # 创建matplotlib图表
        self.nyquist_figure = Figure(figsize=(8, 6), dpi=100)
        self.nyquist_canvas = FigureCanvas(self.nyquist_figure)
        self.nyquist_canvas.setMinimumSize(400, 300)

        # 创建坐标轴
        self.nyquist_ax = self.nyquist_figure.add_subplot(111)
        self._update_axis_labels()
        self.nyquist_ax.set_title('奈奎斯特图')
        self.nyquist_ax.grid(True, alpha=0.3)
        self.nyquist_ax.set_aspect('equal')

        # 设置鼠标悬停事件
        self._setup_hover_events()
    
    def get_plot_widget(self) -> QWidget:
        """获取图表组件"""
        return self.nyquist_canvas

    def _update_axis_labels(self):
        """更新坐标轴标签"""
        if not MATPLOTLIB_AVAILABLE or not self.nyquist_ax:
            return

        self.nyquist_ax.set_xlabel(f'实部阻抗 ({self._impedance_unit})')
        self.nyquist_ax.set_ylabel(f'虚部阻抗 ({self._impedance_unit})')

    def set_impedance_unit(self, unit: str):
        """
        设置阻抗单位

        Args:
            unit: 阻抗单位 ('mΩ' 或 'μΩ')
        """
        if unit not in ['mΩ', 'μΩ']:
            logger.warning(f"不支持的阻抗单位: {unit}")
            return

        self._impedance_unit = unit

        # 设置转换因子
        if unit == 'mΩ':
            self._unit_conversion_factor = 1.0  # mΩ是基准单位
        elif unit == 'μΩ':
            self._unit_conversion_factor = 1000.0  # 1 mΩ = 1000 μΩ

        # 更新坐标轴标签
        self._update_axis_labels()

        # 如果有数据，重新绘制图表
        if self._current_plot_data:
            self._redraw_current_plot()

        logger.info(f"阻抗单位已切换为: {unit}")

    def get_impedance_unit(self) -> str:
        """获取当前阻抗单位"""
        return self._impedance_unit

    def _convert_impedance_value(self, value_in_mohm: float) -> float:
        """
        将mΩ单位的阻抗值转换为当前单位

        Args:
            value_in_mohm: mΩ单位的阻抗值

        Returns:
            转换后的阻抗值
        """
        return value_in_mohm * self._unit_conversion_factor
    
    def create_zoom_controls(self) -> QWidget:
        """创建缩放控制按钮组"""
        controls_widget = QWidget()
        controls_widget.setMaximumHeight(80)  # 增加高度以容纳两行控件
        main_layout = QVBoxLayout(controls_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 第一行：缩放控制按钮
        zoom_layout = QHBoxLayout()
        zoom_layout.setSpacing(10)

        # 自动回正按钮
        auto_fit_btn = QPushButton("自动回正")
        auto_fit_btn.setToolTip("重新计算最佳显示范围")
        auto_fit_btn.clicked.connect(self._auto_fit_plot)
        zoom_layout.addWidget(auto_fit_btn)

        # 智能缩放按钮
        smart_zoom_btn = QPushButton("智能缩放")
        smart_zoom_btn.setToolTip("根据数据大小智能调整缩放级别，解决图形太小问题")
        smart_zoom_btn.clicked.connect(self._smart_zoom_plot)
        zoom_layout.addWidget(smart_zoom_btn)

        # 居中显示按钮
        center_btn = QPushButton("居中显示")
        center_btn.setToolTip("以数据中心为基准居中显示")
        center_btn.clicked.connect(self._center_plot)
        zoom_layout.addWidget(center_btn)

        # 重置缩放按钮
        reset_zoom_btn = QPushButton("重置缩放")
        reset_zoom_btn.setToolTip("重置到默认缩放级别")
        reset_zoom_btn.clicked.connect(self._reset_zoom)
        zoom_layout.addWidget(reset_zoom_btn)

        # 完全重建按钮
        rebuild_btn = QPushButton("完全重建")
        rebuild_btn.setToolTip("完全重建matplotlib组件，解决累积问题")
        rebuild_btn.clicked.connect(self._rebuild_and_replot)
        zoom_layout.addWidget(rebuild_btn)

        zoom_layout.addStretch()
        main_layout.addLayout(zoom_layout)

        # 第二行：显示选项控制
        options_layout = QHBoxLayout()
        options_layout.setSpacing(15)

        # 阻抗单位选择器
        from PyQt5.QtWidgets import QLabel, QComboBox
        unit_label = QLabel("阻抗单位:")
        options_layout.addWidget(unit_label)

        self.unit_combo = QComboBox()
        self.unit_combo.addItem("毫欧 (mΩ)", "mΩ")
        self.unit_combo.addItem("微欧 (μΩ)", "μΩ")
        self.unit_combo.setCurrentText("毫欧 (mΩ)")
        self.unit_combo.setToolTip("选择阻抗显示单位")
        self.unit_combo.currentTextChanged.connect(lambda text: self._on_unit_changed(self.unit_combo.currentData()))
        options_layout.addWidget(self.unit_combo)

        options_layout.addStretch()

        # 多通道显示复选框
        self.multi_channel_checkbox = QCheckBox("多通道对比显示")
        self.multi_channel_checkbox.setToolTip("选中后可以同时显示多个通道的奈奎斯特图进行对比")
        self.multi_channel_checkbox.toggled.connect(self._on_multi_channel_toggled)
        options_layout.addWidget(self.multi_channel_checkbox)

        main_layout.addLayout(options_layout)

        return controls_widget

    def _on_unit_changed(self, unit: str):
        """阻抗单位变更处理"""
        self.set_impedance_unit(unit)

    def _on_multi_channel_toggled(self, checked: bool):
        """多通道显示切换处理"""
        self._multi_channel_mode = checked
        logger.info(f"多通道对比显示: {'启用' if checked else '禁用'}")

        if checked:
            # 启用多通道模式时，如果有选中的数据，切换到多通道显示
            if self._selected_channels_data:
                self._update_multi_channel_display()
        else:
            # 禁用多通道模式时，如果有当前数据，显示单通道
            if self._current_plot_data:
                self._redraw_current_plot()

    def _redraw_current_plot(self):
        """重新绘制当前图表"""
        if not self._current_plot_data:
            return

        if self._multi_channel_mode and self._selected_channels_data:
            self._update_multi_channel_display()
        else:
            # 重新绘制单通道图表
            if hasattr(self, '_last_test_result'):
                self.update_single_channel_plot(self._current_plot_data, self._last_test_result)
            else:
                self.update_single_channel_plot(self._current_plot_data)

    def update_single_channel_plot(self, details: List[Dict], test_result: Dict = None):
        """更新单通道奈奎斯特图 - 完全重写版本，修复显示问题"""
        try:
            logger.debug(f"开始更新单通道奈奎斯特图，数据点数: {len(details) if details else 0}")

            if not MATPLOTLIB_AVAILABLE or not details:
                self._clear_plot()
                return

            # 保存测试结果用于重绘
            self._last_test_result = test_result

            # 如果启用了多通道模式，将数据添加到选中通道数据中
            if self._multi_channel_mode and test_result:
                channel_key = f"ch_{test_result.get('channel_number', 'unknown')}"
                self._selected_channels_data[channel_key] = {
                    'details': details,
                    'channel_info': test_result
                }
                self._update_multi_channel_display()
                return

            # 清空之前的图表
            self.nyquist_ax.clear()

            # 提取并验证数据
            valid_data = []
            for detail in details:
                real_mohm = detail.get('impedance_real', 0)  # mΩ
                imag_mohm = detail.get('impedance_imag', 0)  # mΩ
                freq = detail.get('frequency', 0)

                # 数据验证
                if isinstance(real_mohm, (int, float)) and isinstance(imag_mohm, (int, float)) and freq > 0:
                    # 应用单位转换
                    real_converted = self._convert_impedance_value(real_mohm)
                    imag_converted = self._convert_impedance_value(imag_mohm)

                    valid_data.append({
                        'real': real_converted,
                        'imag': imag_converted,
                        'real_mohm': real_mohm,  # 保留原始mΩ值用于悬停显示
                        'imag_mohm': imag_mohm,
                        'frequency': freq,
                        'sequence': detail.get('test_sequence', 0)
                    })

            if not valid_data:
                logger.warning("没有有效的阻抗数据")
                self._clear_plot()
                return

            # 按频率排序（从高频到低频，这是奈奎斯特图的标准顺序）
            valid_data.sort(key=lambda x: x['frequency'], reverse=True)

            # 提取排序后的数据
            real_parts = [d['real'] for d in valid_data]
            imag_parts = [d['imag'] for d in valid_data]
            frequencies = [d['frequency'] for d in valid_data]

            # 绘制奈奎斯特图
            self._draw_nyquist_plot(real_parts, imag_parts, frequencies, test_result)

            # 保存当前数据用于悬停功能
            self._current_plot_data = valid_data

            # 🔧 新增：数据更新后自动应用最佳缩放
            self._apply_auto_scaling_if_needed(real_parts, imag_parts)

            logger.debug("单通道奈奎斯特图更新完成")

        except Exception as e:
            logger.error(f"更新单通道奈奎斯特图失败: {e}")
            self._clear_plot()

    def _draw_nyquist_plot(self, real_parts, imag_parts, frequencies, test_result=None):
        """绘制奈奎斯特图的核心方法 - 修复颜色条重复问题"""
        try:
            if not MATPLOTLIB_AVAILABLE:
                return

            # 完全清理图形，包括所有子图和颜色条
            self.nyquist_figure.clear()

            # 重新创建坐标轴
            self.nyquist_ax = self.nyquist_figure.add_subplot(111)

            # 设置标题和标签
            if test_result:
                title = f"奈奎斯特图 - 通道{test_result.get('channel_number', 'N/A')} - {test_result.get('battery_code', 'N/A')}"
            else:
                title = "奈奎斯特图"
            self.nyquist_ax.set_title(title)

            # 使用当前单位设置坐标轴标签
            self._update_axis_labels()

            # 检查数据点数量
            if len(real_parts) < 3:
                logger.warning(f"数据点太少({len(real_parts)}个)，无法绘制完整奈奎斯特图")
                # 仍然绘制，但添加警告文本
                self.nyquist_ax.text(0.5, 0.95, f'数据点不足({len(real_parts)}个)',
                                   transform=self.nyquist_ax.transAxes,
                                   ha='center', va='top',
                                   bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))

            # 绘制连线（奈奎斯特图的特征）
            if len(real_parts) >= 2:
                self.nyquist_ax.plot(real_parts, imag_parts,
                                   'b-', linewidth=2.5, alpha=0.8,
                                   label='阻抗轨迹')

            # 绘制数据点，使用频率作为颜色映射
            if len(real_parts) > 0:
                scatter = self.nyquist_ax.scatter(real_parts, imag_parts,
                                                c=frequencies, cmap='plasma',
                                                s=80, alpha=0.9, edgecolors='white',
                                                linewidth=1.5)

                # 标记起始点和结束点
                if len(real_parts) > 0:
                    # 高频起始点（红色圆圈）
                    self.nyquist_ax.scatter(real_parts[0], imag_parts[0],
                                           c='red', s=120, marker='o',
                                           edgecolors='darkred', linewidth=2,
                                           label=f'高频起点 ({frequencies[0]:.1f} Hz)')

                    if len(real_parts) > 1:
                        # 低频结束点（蓝色方块）
                        self.nyquist_ax.scatter(real_parts[-1], imag_parts[-1],
                                               c='blue', s=120, marker='s',
                                               edgecolors='darkblue', linewidth=2,
                                               label=f'低频终点 ({frequencies[-1]:.1f} Hz)')

                # 添加单个颜色条（修复重复问题）
                if len(frequencies) > 1:  # 只有多个频率点时才显示颜色条
                    try:
                        cbar = self.nyquist_figure.colorbar(scatter, ax=self.nyquist_ax,
                                                          shrink=0.6, aspect=20, pad=0.05)
                        cbar.set_label('频率 (Hz)', fontsize=9)
                        cbar.ax.tick_params(labelsize=8)

                        # 优化颜色条位置，避免遮挡数据
                        cbar.ax.yaxis.set_label_position('left')
                    except Exception as e:
                        logger.debug(f"颜色条创建失败: {e}")

            # 添加图例
            self.nyquist_ax.legend(loc='upper right', fontsize=8)

            # 设置网格
            self.nyquist_ax.grid(True, alpha=0.3)

            # 🔧 修复：使用自适应缩放而不是固定缩放
            self._set_optimal_axis_limits(real_parts, imag_parts)

            # 设置等比例（奈奎斯特图的重要特性）
            self.nyquist_ax.set_aspect('equal')

            # 调整布局以防止颜色条重叠
            self.nyquist_figure.tight_layout()

            # 刷新画布
            self.nyquist_canvas.draw()

        except Exception as e:
            logger.error(f"绘制奈奎斯特图失败: {e}")
            raise
    
    def update_multi_channel_plot(self, all_channel_data: Dict):
        """更新多通道奈奎斯特图"""
        try:
            logger.debug(f"开始更新多通道奈奎斯特图，通道数: {len(all_channel_data) if all_channel_data else 0}")
            
            if not MATPLOTLIB_AVAILABLE or not all_channel_data:
                self._clear_plot()
                return
            
            # 清空之前的图表
            self.nyquist_ax.clear()
            
            # 重新设置坐标轴标签和标题
            self._update_axis_labels()
            self.nyquist_ax.set_title(f"多通道奈奎斯特图对比 ({len(all_channel_data)}个通道)")
            
            # 绘制多个通道的数据
            all_real_parts = []
            all_imag_parts = []
            legend_labels = []
            
            # 保存数据点信息用于悬停功能
            self._current_plot_data = []
            
            for i, (channel_key, channel_data) in enumerate(all_channel_data.items()):
                details = channel_data['details']
                channel_info = channel_data['channel_info']
                
                if not details:
                    continue
                
                # 提取数据并应用单位转换
                real_parts = []
                imag_parts = []

                for detail in details:
                    real_mohm = detail.get('impedance_real', 0)  # 数据本身是mΩ
                    imag_mohm = detail.get('impedance_imag', 0)  # 数据本身是mΩ

                    # 应用单位转换
                    real_converted = self._convert_impedance_value(real_mohm)
                    imag_converted = self._convert_impedance_value(imag_mohm)

                    real_parts.append(real_converted)
                    imag_parts.append(imag_converted)

                    # 保存数据点信息
                    self._current_plot_data.append({
                        'real': real_converted,
                        'imag': imag_converted,
                        'real_mohm': real_mohm,  # 保留原始mΩ值
                        'imag_mohm': imag_mohm,
                        'frequency': detail.get('frequency', 0),
                        'sequence': detail.get('test_sequence', 0),
                        'channel': channel_info.get('channel_number', 'N/A'),
                        'battery_code': channel_info.get('battery_code', 'N/A')
                    })
                
                # 选择颜色
                color = self._channel_colors[i % len(self._channel_colors)]
                
                # 绘制散点图
                self.nyquist_ax.scatter(real_parts, imag_parts, 
                                      c=color, s=50, alpha=0.7, 
                                      edgecolors='black', linewidth=0.5,
                                      label=f"通道{channel_info.get('channel_number', 'N/A')}")
                
                # 收集所有数据点用于设置坐标轴范围
                all_real_parts.extend(real_parts)
                all_imag_parts.extend(imag_parts)
                
                # 添加图例标签
                legend_labels.append(f"通道{channel_info.get('channel_number', 'N/A')} - {channel_info.get('battery_code', 'N/A')}")
            
            # 添加图例
            if legend_labels:
                self.nyquist_ax.legend(loc='best', fontsize=8)
            
            # 设置网格和等比例
            self.nyquist_ax.grid(True, alpha=0.3)
            self.nyquist_ax.set_aspect('equal')
            
            # 设置坐标轴范围
            if all_real_parts and all_imag_parts:
                self._set_optimal_axis_limits(all_real_parts, all_imag_parts)
            
            # 刷新画布
            self.nyquist_canvas.draw()
            
            logger.debug("多通道奈奎斯特图更新完成")
            
        except Exception as e:
            logger.error(f"更新多通道奈奎斯特图失败: {e}")
            self._clear_plot()
    
    def clear_plot(self):
        """清空奈奎斯特图"""
        self._clear_plot()
    
    def _clear_plot(self):
        """内部清空图表方法"""
        try:
            if not MATPLOTLIB_AVAILABLE:
                return

            self.nyquist_ax.clear()
            self._update_axis_labels()
            self.nyquist_ax.set_title('奈奎斯特图')
            self.nyquist_ax.grid(True, alpha=0.3)
            self.nyquist_ax.set_aspect('equal')

            # 清空数据
            self._current_plot_data = []

            # 刷新画布
            self.nyquist_canvas.draw()

        except Exception as e:
            logger.error(f"清空奈奎斯特图失败: {e}")

    def _update_multi_channel_display(self):
        """更新多通道显示"""
        if not self._multi_channel_mode or not self._selected_channels_data:
            return

        try:
            logger.debug(f"更新多通道显示，通道数: {len(self._selected_channels_data)}")
            self.update_multi_channel_plot(self._selected_channels_data)
        except Exception as e:
            logger.error(f"更新多通道显示失败: {e}")

    def add_channel_to_comparison(self, details: List[Dict], test_result: Dict):
        """
        添加通道到多通道对比中

        Args:
            details: 阻抗明细数据
            test_result: 测试结果信息
        """
        if not test_result:
            return

        channel_key = f"ch_{test_result.get('channel_number', 'unknown')}"
        self._selected_channels_data[channel_key] = {
            'details': details,
            'channel_info': test_result
        }

        if self._multi_channel_mode:
            self._update_multi_channel_display()

        logger.info(f"通道{test_result.get('channel_number')}已添加到对比显示")

    def remove_channel_from_comparison(self, channel_number: int):
        """
        从多通道对比中移除通道

        Args:
            channel_number: 通道号
        """
        channel_key = f"ch_{channel_number}"
        if channel_key in self._selected_channels_data:
            del self._selected_channels_data[channel_key]

            if self._multi_channel_mode:
                self._update_multi_channel_display()

            logger.info(f"通道{channel_number}已从对比显示中移除")

    def clear_all_channels(self):
        """清空所有通道数据"""
        self._selected_channels_data.clear()
        self._clear_plot()
        logger.info("已清空所有通道对比数据")

    def _setup_hover_events(self):
        """设置鼠标悬停事件"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        def on_hover(event):
            """鼠标悬停事件处理"""
            try:
                if event.inaxes != self.nyquist_ax:
                    self._hide_hover_annotation()
                    return
                
                # 查找最近的数据点
                point_data = self._find_closest_point(event.xdata, event.ydata)
                if point_data:
                    self._show_hover_annotation(event, point_data)
                else:
                    self._hide_hover_annotation()
                    
            except Exception as e:
                logger.debug(f"悬停事件处理失败: {e}")
        
        # 连接事件
        self.nyquist_canvas.mpl_connect('motion_notify_event', on_hover)

    def _find_closest_point(self, x, y):
        """查找最近的数据点"""
        if not MATPLOTLIB_AVAILABLE or not self._current_plot_data:
            return None

        try:
            min_distance = float('inf')
            closest_point = None

            for point in self._current_plot_data:
                distance = ((point['real'] - x) ** 2 + (point['imag'] - y) ** 2) ** 0.5
                if distance < min_distance:
                    min_distance = distance
                    closest_point = point

            # 只有距离足够近才显示
            if min_distance < 50:  # 调整这个阈值来控制敏感度
                return closest_point

            return None

        except Exception as e:
            logger.debug(f"查找最近数据点失败: {e}")
            return None

    def _show_hover_annotation(self, event, point_data):
        """显示悬停标注"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            # 移除之前的标注
            self._hide_hover_annotation()

            # 获取原始mΩ值用于显示（更准确）
            real_mohm = point_data.get('real_mohm', point_data.get('real', 0))
            imag_mohm = point_data.get('imag_mohm', point_data.get('imag', 0))

            # 根据当前单位转换显示值
            real_display = self._convert_impedance_value(real_mohm) if 'real_mohm' in point_data else point_data.get('real', 0)
            imag_display = self._convert_impedance_value(imag_mohm) if 'imag_mohm' in point_data else point_data.get('imag', 0)

            # 创建标注文本 - 🔧 修复：改为3位小数显示
            if 'channel' in point_data:
                # 多通道模式
                text = f"通道: {point_data['channel']}\n电池: {point_data['battery_code']}\n频率: {point_data['frequency']:.3f} Hz\n实部: {real_display:.3f} {self._impedance_unit}\n虚部: {imag_display:.3f} {self._impedance_unit}"
            else:
                # 单通道模式
                text = f"频率: {point_data['frequency']:.3f} Hz\n实部: {real_display:.3f} {self._impedance_unit}\n虚部: {imag_display:.3f} {self._impedance_unit}\n序号: {point_data['sequence']}"

            # 智能定位标注位置，避免遮挡数据
            # 获取图表范围
            xlim = self.nyquist_ax.get_xlim()
            ylim = self.nyquist_ax.get_ylim()
            x_range = xlim[1] - xlim[0]
            y_range = ylim[1] - ylim[0]

            # 根据数据点位置调整标注位置
            x_pos = point_data['real']
            y_pos = point_data['imag']

            # 计算相对位置
            x_rel = (x_pos - xlim[0]) / x_range
            y_rel = (y_pos - ylim[0]) / y_range

            # 智能选择标注位置
            if x_rel > 0.7:  # 右侧
                xytext = (-80, 20)
            elif x_rel < 0.3:  # 左侧
                xytext = (80, 20)
            else:  # 中间
                if y_rel > 0.7:  # 上方
                    xytext = (20, -60)
                else:  # 下方
                    xytext = (20, 60)

            # 创建标注
            self._hover_annotation = self.nyquist_ax.annotate(
                text,
                xy=(x_pos, y_pos),
                xytext=xytext, textcoords="offset points",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.9, edgecolor='gray'),
                arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=0.1", color='gray'),
                fontsize=8,
                ha='left'
            )

            # 刷新画布
            self.nyquist_canvas.draw_idle()

        except Exception as e:
            logger.debug(f"显示悬停标注失败: {e}")

    def _hide_hover_annotation(self):
        """隐藏悬停标注"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            if self._hover_annotation:
                self._hover_annotation.remove()
                self._hover_annotation = None
                self.nyquist_canvas.draw_idle()

        except Exception as e:
            logger.debug(f"隐藏悬停标注失败: {e}")

    def _set_optimal_axis_limits(self, real_parts: list, imag_parts: list):
        """设置最佳的坐标轴范围 - 改进的自适应缩放算法"""
        try:
            if not real_parts or not imag_parts:
                logger.warning("数据为空，无法设置坐标轴范围")
                return

            # 计算数据范围
            x_min, x_max = min(real_parts), max(real_parts)
            y_min, y_max = min(imag_parts), max(imag_parts)

            # 计算数据中心
            x_center = (x_min + x_max) / 2
            y_center = (y_min + y_max) / 2

            # 计算数据跨度
            x_span = x_max - x_min
            y_span = y_max - y_min

            # 🔧 改进：智能最小显示范围计算
            # 根据数据的绝对大小动态调整最小显示范围
            abs_max = max(abs(x_min), abs(x_max), abs(y_min), abs(y_max))

            if abs_max < 0.001:  # 微欧级别
                min_span = abs_max * 2 if abs_max > 0 else 0.001
            elif abs_max < 0.01:  # 毫欧级别
                min_span = abs_max * 1.5 if abs_max > 0 else 0.01
            elif abs_max < 0.1:  # 十毫欧级别
                min_span = abs_max * 1.2 if abs_max > 0 else 0.1
            else:  # 更大的值
                min_span = max(abs_max * 0.1, 1.0)

            # 确保最小显示范围
            if x_span < min_span:
                x_span = min_span
            if y_span < min_span:
                y_span = min_span

            # 🔧 改进：自适应边距计算
            # 根据数据点数量和分布调整边距
            data_count = len(real_parts)
            if data_count <= 5:
                margin_ratio = 0.3  # 数据点少时给更多边距
            elif data_count <= 20:
                margin_ratio = 0.25
            else:
                margin_ratio = 0.2  # 数据点多时减少边距

            # 计算边距
            x_margin = x_span * margin_ratio
            y_margin = y_span * margin_ratio

            # 计算最终的显示范围
            x_display_min = x_center - (x_span + x_margin) / 2
            x_display_max = x_center + (x_span + x_margin) / 2
            y_display_min = y_center - (y_span + y_margin) / 2
            y_display_max = y_center + (y_span + y_margin) / 2

            # 设置坐标轴范围
            self.nyquist_ax.set_xlim(x_display_min, x_display_max)
            self.nyquist_ax.set_ylim(y_display_min, y_display_max)

            logger.debug(f"🔧 自适应坐标轴范围: X[{x_display_min:.6f}, {x_display_max:.6f}], Y[{y_display_min:.6f}, {y_display_max:.6f}]")
            logger.debug(f"   数据范围: X={x_span:.6f}, Y={y_span:.6f}, 数据点数={data_count}, 边距比例={margin_ratio}")

        except Exception as e:
            logger.error(f"设置坐标轴范围失败: {e}")

    def _apply_auto_scaling_if_needed(self, real_parts: list, imag_parts: list):
        """数据更新后自动应用最佳缩放（如果需要）"""
        try:
            if not MATPLOTLIB_AVAILABLE or not real_parts or not imag_parts:
                return

            # 检查当前显示范围是否合适
            current_xlim = self.nyquist_ax.get_xlim()
            current_ylim = self.nyquist_ax.get_ylim()

            # 计算数据范围
            data_x_min, data_x_max = min(real_parts), max(real_parts)
            data_y_min, data_y_max = min(imag_parts), max(imag_parts)

            # 检查数据是否超出当前显示范围或显示范围过大
            x_out_of_range = (data_x_min < current_xlim[0] or data_x_max > current_xlim[1])
            y_out_of_range = (data_y_min < current_ylim[0] or data_y_max > current_ylim[1])

            # 检查显示范围是否过大（数据只占显示范围的很小一部分）
            data_x_span = data_x_max - data_x_min
            data_y_span = data_y_max - data_y_min
            display_x_span = current_xlim[1] - current_xlim[0]
            display_y_span = current_ylim[1] - current_ylim[0]

            # 如果数据范围小于显示范围的20%，认为显示范围过大
            x_too_large = data_x_span > 0 and (data_x_span / display_x_span) < 0.2
            y_too_large = data_y_span > 0 and (data_y_span / display_y_span) < 0.2

            # 如果需要重新缩放
            if x_out_of_range or y_out_of_range or x_too_large or y_too_large:
                logger.debug("🔧 数据更新后自动应用最佳缩放")
                self._set_optimal_axis_limits(real_parts, imag_parts)
                self.nyquist_canvas.draw()
            else:
                logger.debug("当前显示范围合适，无需自动缩放")

        except Exception as e:
            logger.debug(f"自动缩放检查失败: {e}")

    def _set_stable_axis_limits(self, real_parts: list, imag_parts: list):
        """设置稳定的坐标轴范围，防止X轴自动缩小"""
        try:
            if not real_parts or not imag_parts:
                logger.warning("数据为空，无法设置坐标轴范围")
                return

            # 计算数据范围
            x_min, x_max = min(real_parts), max(real_parts)
            y_min, y_max = min(imag_parts), max(imag_parts)

            # 计算数据中心
            x_center = (x_min + x_max) / 2
            y_center = (y_min + y_max) / 2

            # 计算数据跨度
            x_span = x_max - x_min
            y_span = y_max - y_min

            # 设置最小显示范围，防止范围过小
            min_span = 5.0  # mΩ
            if x_span < min_span:
                x_span = min_span
            if y_span < min_span:
                y_span = min_span

            # 添加固定的边距比例，防止数据点贴边
            margin_ratio = 0.2  # 20%的边距
            x_margin = x_span * margin_ratio
            y_margin = y_span * margin_ratio

            # 计算最终的显示范围
            x_display_min = x_center - (x_span + x_margin) / 2
            x_display_max = x_center + (x_span + x_margin) / 2
            y_display_min = y_center - (y_span + y_margin) / 2
            y_display_max = y_center + (y_span + y_margin) / 2

            # 设置坐标轴范围
            self.nyquist_ax.set_xlim(x_display_min, x_display_max)
            self.nyquist_ax.set_ylim(y_display_min, y_display_max)

            logger.debug(f"设置稳定坐标轴范围: X[{x_display_min:.2f}, {x_display_max:.2f}], Y[{y_display_min:.2f}, {y_display_max:.2f}]")

        except Exception as e:
            logger.error(f"设置稳定坐标轴范围失败: {e}")

    def _calculate_adaptive_range(self, real_parts: list, imag_parts: list, x_range: float, y_range: float):
        """计算自适应的数据范围 - 智能缩放优化"""
        try:
            # 计算数据的实际范围
            actual_x_range = max(real_parts) - min(real_parts)
            actual_y_range = max(imag_parts) - min(imag_parts)

            # 智能最小显示范围：根据数据大小动态调整
            if actual_x_range > 0:
                # 如果有实际数据范围，使用数据范围的1.5倍作为最小显示范围
                min_x_display_range = max(actual_x_range * 1.5, 0.01)  # 最小0.01 mΩ
            else:
                min_x_display_range = 0.1  # 默认0.1 mΩ

            if actual_y_range > 0:
                min_y_display_range = max(actual_y_range * 1.5, 0.01)  # 最小0.01 mΩ
            else:
                min_y_display_range = 0.1  # 默认0.1 mΩ

            # 应用智能最小显示范围
            if x_range < min_x_display_range:
                x_range = min_x_display_range
            if y_range < min_y_display_range:
                y_range = min_y_display_range

            logger.debug(f"智能缩放计算: 实际范围X={actual_x_range:.4f}, Y={actual_y_range:.4f}, 显示范围X={x_range:.4f}, Y={y_range:.4f}")
            return x_range, y_range

        except Exception as e:
            logger.error(f"计算自适应范围失败: {e}")
            return max(x_range, 0.1), max(y_range, 0.1)

    def _calculate_adaptive_margins(self, x_range: float, y_range: float, data_count: int):
        """计算自适应的边距 - 智能缩放优化"""
        try:
            # 智能基础边距比例：根据数据范围动态调整
            if x_range > 10.0 or y_range > 10.0:
                base_margin_ratio = 0.1  # 大范围数据使用较小边距
            elif x_range > 1.0 or y_range > 1.0:
                base_margin_ratio = 0.15  # 中等范围数据使用中等边距
            else:
                base_margin_ratio = 0.25  # 小范围数据使用较大边距，确保可见性

            # 根据数据点数量调整
            if data_count < 10:
                margin_ratio = base_margin_ratio * 1.5  # 数据点少时增加边距
            elif data_count > 50:
                margin_ratio = base_margin_ratio * 0.8  # 数据点多时减少边距
            else:
                margin_ratio = base_margin_ratio

            # 智能最小边距：根据数据范围动态调整
            min_x_margin = max(x_range * 0.05, 0.001)  # 最小边距为数据范围的5%或0.001 mΩ
            min_y_margin = max(y_range * 0.05, 0.001)  # 最小边距为数据范围的5%或0.001 mΩ

            # 计算边距
            x_margin = max(x_range * margin_ratio, min_x_margin)
            y_margin = max(y_range * margin_ratio, min_y_margin)

            logger.debug(f"智能边距计算: 数据范围X={x_range:.4f}, Y={y_range:.4f}, 边距X={x_margin:.4f}, Y={y_margin:.4f}")
            return x_margin, y_margin

        except Exception as e:
            logger.error(f"计算自适应边距失败: {e}")
            return max(x_range * 0.15, 0.001), max(y_range * 0.15, 0.001)

    def _auto_fit_plot(self):
        """自动回正：重新计算最佳显示范围"""
        try:
            logger.info("🔧 执行自动回正操作")

            if not MATPLOTLIB_AVAILABLE or not self._current_plot_data:
                return

            # 重新计算数据范围
            real_parts = [point['real'] for point in self._current_plot_data]
            imag_parts = [point['imag'] for point in self._current_plot_data]

            if real_parts and imag_parts:
                self._set_optimal_axis_limits(real_parts, imag_parts)
                self.nyquist_canvas.draw()
                logger.info("自动回正完成")

        except Exception as e:
            logger.error(f"自动回正失败: {e}")

    def _smart_zoom_plot(self):
        """智能缩放：根据数据大小智能调整缩放级别，解决图形太小问题"""
        try:
            logger.info("🔍 执行智能缩放操作")

            if not MATPLOTLIB_AVAILABLE or not self._current_plot_data:
                return

            # 提取数据
            real_parts = [point['real'] for point in self._current_plot_data]
            imag_parts = [point['imag'] for point in self._current_plot_data]

            if real_parts and imag_parts:
                # 计算数据的实际范围
                x_min, x_max = min(real_parts), max(real_parts)
                y_min, y_max = min(imag_parts), max(imag_parts)

                actual_x_range = x_max - x_min
                actual_y_range = y_max - y_min

                logger.debug(f"数据实际范围: X={actual_x_range:.6f} mΩ, Y={actual_y_range:.6f} mΩ")

                # 智能缩放策略：根据数据大小选择合适的显示范围
                if actual_x_range < 0.01 or actual_y_range < 0.01:
                    # 非常小的数据：使用紧密缩放
                    zoom_factor = 3.0
                    logger.info("检测到微小数据，使用紧密缩放")
                elif actual_x_range < 0.1 or actual_y_range < 0.1:
                    # 小数据：使用中等缩放
                    zoom_factor = 2.0
                    logger.info("检测到小范围数据，使用中等缩放")
                else:
                    # 正常数据：使用标准缩放
                    zoom_factor = 1.5
                    logger.info("检测到正常范围数据，使用标准缩放")

                # 计算智能显示范围
                x_center = (x_min + x_max) / 2
                y_center = (y_min + y_max) / 2

                # 确保最小显示范围
                display_x_range = max(actual_x_range * zoom_factor, 0.005)  # 最小5微欧
                display_y_range = max(actual_y_range * zoom_factor, 0.005)  # 最小5微欧

                # 设置显示范围
                x_half_range = display_x_range / 2
                y_half_range = display_y_range / 2

                self.nyquist_ax.set_xlim(x_center - x_half_range, x_center + x_half_range)
                self.nyquist_ax.set_ylim(y_center - y_half_range, y_center + y_half_range)

                self.nyquist_canvas.draw()

                logger.info(f"智能缩放完成: 显示范围X=[{x_center - x_half_range:.6f}, {x_center + x_half_range:.6f}], Y=[{y_center - y_half_range:.6f}, {y_center + y_half_range:.6f}]")

        except Exception as e:
            logger.error(f"智能缩放失败: {e}")

    def _center_plot(self):
        """居中显示：以数据中心为基准居中显示"""
        try:
            logger.info("🎯 执行居中显示操作")

            if not MATPLOTLIB_AVAILABLE or not self._current_plot_data:
                return

            # 计算数据中心
            real_parts = [point['real'] for point in self._current_plot_data]
            imag_parts = [point['imag'] for point in self._current_plot_data]

            if real_parts and imag_parts:
                center_x = (min(real_parts) + max(real_parts)) / 2
                center_y = (min(imag_parts) + max(imag_parts)) / 2

                # 获取当前显示范围
                current_xlim = self.nyquist_ax.get_xlim()
                current_ylim = self.nyquist_ax.get_ylim()

                # 计算当前显示范围的大小
                x_span = current_xlim[1] - current_xlim[0]
                y_span = current_ylim[1] - current_ylim[0]

                # 以数据中心为基准设置新的显示范围
                self.nyquist_ax.set_xlim(center_x - x_span/2, center_x + x_span/2)
                self.nyquist_ax.set_ylim(center_y - y_span/2, center_y + y_span/2)

                self.nyquist_canvas.draw()
                logger.info("居中显示完成")

        except Exception as e:
            logger.error(f"居中显示失败: {e}")

    def _reset_zoom(self):
        """重置缩放：重置到默认缩放级别"""
        try:
            logger.info("🔄 执行缩放重置操作")

            if not MATPLOTLIB_AVAILABLE:
                return

            # 重建图表组件并重新绘制
            self._rebuild_and_replot()

        except Exception as e:
            logger.error(f"缩放重置失败: {e}")

    def _rebuild_and_replot(self):
        """完全重建matplotlib组件并重新绘制当前数据"""
        try:
            logger.info("🔄 执行完全重建和重新绘制操作")

            if not MATPLOTLIB_AVAILABLE:
                return

            # 保存当前数据
            current_data = self._current_plot_data.copy()

            # 重建matplotlib组件
            self._rebuild_matplotlib_components()

            # 如果有数据，重新绘制
            if current_data:
                self._current_plot_data = current_data
                # 这里需要根据数据类型决定绘制单通道还是多通道
                # 简化处理：清空后让外部重新调用绘制方法
                logger.info("完全重建完成，请重新选择数据进行绘制")

        except Exception as e:
            logger.error(f"完全重建和重新绘制失败: {e}")

    def _rebuild_matplotlib_components(self):
        """重建matplotlib组件"""
        try:
            if not MATPLOTLIB_AVAILABLE:
                return

            # 清理现有组件
            if self.nyquist_figure:
                self.nyquist_figure.clear()

            # 重新创建坐标轴
            self.nyquist_ax = self.nyquist_figure.add_subplot(111)
            self._update_axis_labels()
            self.nyquist_ax.set_title('奈奎斯特图')
            self.nyquist_ax.grid(True, alpha=0.3)
            self.nyquist_ax.set_aspect('equal')

            # 重新设置悬停事件
            self._setup_hover_events()

            # 刷新画布
            self.nyquist_canvas.draw()

            logger.debug("matplotlib组件重建完成")

        except Exception as e:
            logger.error(f"重建matplotlib组件失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if self._plot_timer:
                self._plot_timer.stop()
                self._plot_timer = None

            self._current_plot_data = []
            self._selected_channels_data = {}

            if MATPLOTLIB_AVAILABLE and self.nyquist_figure:
                self.nyquist_figure.clear()

            logger.debug("奈奎斯特图管理器清理完成")

        except Exception as e:
            logger.error(f"清理奈奎斯特图管理器失败: {e}")
