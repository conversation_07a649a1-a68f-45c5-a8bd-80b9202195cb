"""
重构后的并行错频测试管理器 - 简化版本
只保留核心接口，具体功能由专门的管理器实现
"""

import time
import logging
from typing import Optional, Callable
from enum import Enum

# 导入重构后的管理器
from backend.parallel_test_managers.frequency_classifier import FrequencyClassifier
from backend.parallel_test_managers.staggered_test_executor import StaggeredTestExecutor
from backend.parallel_test_managers.simultaneous_test_executor import SimultaneousTestExecutor
from backend.parallel_test_managers.test_data_collector import TestDataCollector
from backend.parallel_test_managers.test_progress_tracker import TestProgressTracker
from backend.parallel_test_managers.test_error_recovery import TestErrorRecovery

logger = logging.getLogger(__name__)


class ParallelStaggeredTestState(Enum):
    """并行错频测试状态"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    CLASSIFYING_FREQUENCIES = "classifying_frequencies"
    STARTING_STAGGERED_MEASUREMENT = "starting_staggered_measurement"
    STAGGERED_MEASUREMENT = "staggered_measurement"
    STARTING_SIMULTANEOUS_MEASUREMENT = "starting_simultaneous_measurement"
    SIMULTANEOUS_MEASUREMENT = "simultaneous_measurement"
    COLLECTING_DATA = "collecting_data"
    COMPLETED = "completed"
    ERROR = "error"
    STOPPED = "stopped"


class ParallelStaggeredTestConfig:
    """并行错频测试配置"""
    def __init__(self):
        self.enabled_channels = []
        self.frequencies = []
        self.critical_frequency = 100.0
        self.timeout_seconds = 30.0
        self.status_check_interval = 0.1


class ParallelStaggeredTestManagerSimplified:
    """
    重构后的并行错频测试管理器 - 简化版本
    
    这个版本只保留核心接口，具体功能由6个专门的管理器实现：
    1. FrequencyClassifier - 频率分类
    2. StaggeredTestExecutor - 错频测试执行
    3. SimultaneousTestExecutor - 同时测试执行
    4. TestDataCollector - 数据收集
    5. TestProgressTracker - 进度跟踪
    6. TestErrorRecovery - 错误恢复
    """

    def __init__(self, comm_manager):
        """初始化重构后的并行错频测试管理器"""
        self.comm_manager = comm_manager

        # 测试状态
        self.state = ParallelStaggeredTestState.IDLE
        self.config: Optional[ParallelStaggeredTestConfig] = None
        self.start_time = 0.0

        # 🔧 修复：添加停止事件
        import threading
        self.stop_event = threading.Event()

        # 初始化重构后的6个管理器
        self._initialize_refactored_managers()

        # 回调函数（保持向后兼容）
        self.status_callback: Optional[Callable] = None
        self.progress_callback: Optional[Callable] = None
        self.channel_progress_callback: Optional[Callable] = None

        # 🧹 初始化测试状态清理器
        try:
            from .test_state_cleaner import TestStateCleaner
            self.state_cleaner = TestStateCleaner()
        except ImportError:
            logger.warning("测试状态清理器模块未找到，跳过初始化")
            self.state_cleaner = None

        logger.debug("重构后的并行错频测试管理器初始化完成")

    def set_channel_progress_callback(self, callback: Callable):
        """设置通道进度回调函数"""
        self.channel_progress_callback = callback

        # 将回调函数传递给子执行器（安全检查）
        try:
            if hasattr(self, 'staggered_executor') and self.staggered_executor:
                if hasattr(self.staggered_executor, 'set_channel_progress_callback'):
                    self.staggered_executor.set_channel_progress_callback(callback)
            if hasattr(self, 'simultaneous_executor') and self.simultaneous_executor:
                if hasattr(self.simultaneous_executor, 'channel_progress_callback'):
                    self.simultaneous_executor.channel_progress_callback = callback

            logger.debug("通道进度回调函数已设置并传递给子执行器")
        except Exception as e:
            logger.error(f"传递回调函数到子执行器失败: {e}")

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
        logger.debug("进度回调函数已设置")

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
        logger.debug("状态回调函数已设置")

    def _initialize_refactored_managers(self):
        """初始化重构后的6个专门管理器"""
        try:
            # 1. 频率分类器
            self.frequency_classifier = FrequencyClassifier()

            # 2. 错频测试执行器（🔧 修复：传递停止事件）
            self.staggered_executor = StaggeredTestExecutor(
                self.comm_manager,
                self.frequency_classifier,
                self.stop_event
            )

            # 3. 同时测试执行器（🔧 修复：传递停止事件）
            self.simultaneous_executor = SimultaneousTestExecutor(
                self.comm_manager,
                self.frequency_classifier,
                self.stop_event
            )

            # 4. 数据收集器（传递通信管理器以便创建数据库管理器）
            self.data_collector = TestDataCollector()

            # 5. 进度跟踪器
            self.progress_tracker = TestProgressTracker()

            # 6. 错误恢复器
            self.error_recovery = TestErrorRecovery(self.comm_manager)

            # 设置回调函数（如果已经设置了的话）
            self._setup_callbacks()

            logger.debug("✅ 重构后的6个管理器初始化完成")

        except Exception as e:
            logger.error(f"❌ 初始化重构后的管理器失败: {e}")
            raise

    def _setup_callbacks(self):
        """设置回调函数到子管理器"""
        try:
            # 将通道进度回调传递给执行器
            if self.channel_progress_callback:
                if self.staggered_executor:
                    self.staggered_executor.set_channel_progress_callback(self.channel_progress_callback)
                if self.simultaneous_executor:
                    self.simultaneous_executor.channel_progress_callback = self.channel_progress_callback

            logger.debug("回调函数已设置到子管理器")
        except Exception as e:
            logger.error(f"设置回调函数失败: {e}")

    def _set_state(self, new_state: ParallelStaggeredTestState):
        """设置测试状态"""
        try:
            old_state = self.state
            self.state = new_state
            logger.debug(f"状态变更: {old_state.value} -> {new_state.value}")
            
            # 通知状态回调
            if self.status_callback:
                try:
                    self.status_callback({
                        'state': new_state.value,
                        'old_state': old_state.value,
                        'timestamp': time.time()
                    })
                except Exception as e:
                    logger.error(f"状态回调失败: {e}")
                    
        except Exception as e:
            logger.error(f"设置状态失败: {e}")

    def start_test(self, config: ParallelStaggeredTestConfig) -> bool:
        """
        启动重构后的并行错频测试
        
        Args:
            config: 测试配置
            
        Returns:
            是否启动成功
        """
        try:
            # 1. 验证配置
            if not config or not config.enabled_channels or not config.frequencies:
                logger.error("❌ 测试配置无效")
                return False
            
            print(f"🚀 [并行错频管理器] 启动测试: {len(config.enabled_channels)}个通道, "
                  f"{len(config.frequencies)}个频点, 临界频点: {config.critical_frequency}Hz")
            logger.info(f"🚀 启动并行错频测试: {len(config.enabled_channels)}个通道, "
                       f"{len(config.frequencies)}个频点, 临界频点: {config.critical_frequency}Hz")
            
            # 保存配置
            self.config = config
            self.start_time = time.time()

            # 🔧 修复：清除停止事件，准备新的测试
            self.stop_event.clear()

            # 🧹 新增：全面清理测试状态，确保干净的测试环境
            self._clean_test_environment()

            # 清空重构后的管理器数据
            self.data_collector.clear_all_data()
            self.error_recovery.reset()
            
            # 检查设备连接（如果通信管理器支持连接检查）
            if hasattr(self.comm_manager, 'is_connected') and not self.comm_manager.is_connected:
                print(f"⚠️⚠️⚠️ [并行错频管理器] 设备未连接，尝试自动连接...")
                logger.warning("设备未连接，尝试自动连接...")

                # 尝试自动连接
                try:
                    if hasattr(self.comm_manager, 'connect') and callable(self.comm_manager.connect):
                        if not self.comm_manager.connect():
                            logger.error("❌ 自动连接失败")
                            return False
                        logger.info("✅ 自动连接成功")
                except Exception as e:
                    logger.error(f"❌ 自动连接异常: {e}")
                    return False

            # 2. 使用重构后的频率分类器
            self._set_state(ParallelStaggeredTestState.CLASSIFYING_FREQUENCIES)
            high_frequencies, low_frequencies = self.frequency_classifier.classify_frequencies(
                config.frequencies, config.critical_frequency
            )
            
            # 3. 使用重构后的错频测试执行器测试高频点
            staggered_success = True
            if high_frequencies:
                logger.info(f"🔧 开始测试{len(high_frequencies)}个高频点")
                staggered_success = self.staggered_executor.execute_high_frequency_test(
                    config.enabled_channels, config
                )
                if staggered_success:
                    staggered_results = self.staggered_executor.get_test_results()
                    self.data_collector.collect_staggered_results(staggered_results)
                    logger.info(f"✅ 高频点测试成功，收集了{len(staggered_results)}个结果")
                else:
                    logger.error(f"❌ 高频点测试失败！")
                    # 高频点测试失败时，仍然继续低频点测试，但记录警告
                    logger.warning(f"⚠️ 高频点测试失败，将继续执行低频点测试")

            # 4. 使用重构后的同时测试执行器测试低频点
            simultaneous_success = True
            if low_frequencies:
                logger.info(f"🔧 开始测试{len(low_frequencies)}个低频点")
                simultaneous_success = self.simultaneous_executor.execute_low_frequency_test(
                    config.enabled_channels, config
                )
                if simultaneous_success:
                    simultaneous_results = self.simultaneous_executor.get_test_results()
                    self.data_collector.collect_simultaneous_results(simultaneous_results)
                    logger.info(f"✅ 低频点测试成功，收集了{len(simultaneous_results)}个结果")
                else:
                    logger.error(f"❌ 低频点测试失败！")

            # 5. 合并所有测试结果
            self.data_collector.combine_all_results(config.enabled_channels)

            # 所有频点测试完成
            self._set_state(ParallelStaggeredTestState.COMPLETED)
            
            elapsed_time = time.time() - self.start_time
            print(f"✅✅✅ [并行错频管理器] 测试完成，总耗时: {elapsed_time:.3f}秒")
            logger.info(f"✅ 并行错频测试完成，总耗时: {elapsed_time:.3f}秒")
            
            return True
            
        except Exception as e:
            logger.error(f"执行重构后的并行错频测试失败: {e}")
            self._set_state(ParallelStaggeredTestState.ERROR)
            return False

    def stop_test(self):
        """停止测试（增强版）"""
        try:
            logger.info("🛑 [增强版] 并行错频测试管理器开始停止...")
            
            # 1. 立即设置停止标志
            self.is_testing = False
            self.stop_event.set()
            
            # 2. 立即停止设备测试
            self._enhanced_stop_device_immediately()
            
            # 3. 停止所有子执行器
            self._enhanced_stop_all_executors()
            
            # 4. 清理状态
            self._enhanced_cleanup_state()
            
            logger.info("✅ [增强版] 并行错频测试管理器停止完成")
            
        except Exception as e:
            logger.error(f"❌ [增强版] 并行错频测试管理器停止失败: {e}")
    
    def _enhanced_stop_device_immediately(self):
        """增强的立即停止设备"""
        try:
            if hasattr(self, 'comm_manager') and self.comm_manager:
                # 停止所有通道
                all_channels = list(range(8))
                self.comm_manager.stop_impedance_measurement(all_channels)
                logger.info("✅ 设备测试已强制停止")
        except Exception as e:
            logger.error(f"强制停止设备失败: {e}")
    
    def _enhanced_stop_all_executors(self):
        """增强的停止所有执行器"""
        try:
            # 停止错频测试执行器
            if hasattr(self, 'staggered_executor') and self.staggered_executor:
                try:
                    self.staggered_executor.stop_event.set()
                    logger.info("✅ 错频测试执行器已停止")
                except Exception as e:
                    logger.error(f"停止错频测试执行器失败: {e}")
            
            # 停止同时测试执行器
            if hasattr(self, 'simultaneous_executor') and self.simultaneous_executor:
                try:
                    self.simultaneous_executor.stop_event.set()
                    logger.info("✅ 同时测试执行器已停止")
                except Exception as e:
                    logger.error(f"停止同时测试执行器失败: {e}")
        except Exception as e:
            logger.error(f"停止所有执行器失败: {e}")
    
    def _enhanced_cleanup_state(self):
        """增强的状态清理"""
        try:
            # 重置所有状态
            self.is_testing = False
            if hasattr(self, 'current_round'):
                self.current_round = 0
            if hasattr(self, 'test_results'):
                self.test_results.clear()
            logger.info("✅ 状态清理完成")
        except Exception as e:
            logger.error(f"状态清理失败: {e}")
    def get_test_results(self):
        """获取测试结果"""
        try:
            if self.data_collector:
                # 获取原始数据
                raw_data = self.data_collector.export_to_dict()

                # 转换为TestExecutor期望的格式: {frequency: {channel_index: data}}
                combined_results = {}

                # 合并错频测试结果
                staggered_results = raw_data.get('staggered_results', {})
                for frequency, channels_data in staggered_results.items():
                    if frequency not in combined_results:
                        combined_results[frequency] = {}
                    combined_results[frequency].update(channels_data)

                # 合并同时测试结果
                simultaneous_results = raw_data.get('simultaneous_results', {})
                for frequency, channels_data in simultaneous_results.items():
                    if frequency not in combined_results:
                        combined_results[frequency] = {}
                    combined_results[frequency].update(channels_data)

                # 合并组合结果
                combined_data = raw_data.get('combined_results', {})
                for channel_index, channel_frequencies in combined_data.items():
                    for frequency, data in channel_frequencies.items():
                        if frequency not in combined_results:
                            combined_results[frequency] = {}
                        combined_results[frequency][channel_index] = data

                logger.info(f"测试结果格式转换完成: {len(combined_results)}个频点")
                return combined_results
            return {}
        except Exception as e:
            logger.error(f"获取测试结果失败: {e}")
            return {}

    def get_test_state(self) -> ParallelStaggeredTestState:
        """获取当前测试状态"""
        return self.state

    def is_testing(self) -> bool:
        """是否正在测试"""
        return self.state not in [
            ParallelStaggeredTestState.IDLE,
            ParallelStaggeredTestState.COMPLETED,
            ParallelStaggeredTestState.ERROR,
            ParallelStaggeredTestState.STOPPED
        ]

    def _clean_test_environment(self):
        """清理测试环境，确保干净的测试状态"""
        try:
            logger.debug("🧹 并行错频测试管理器开始清理测试环境...")

            # 清理内部状态
            self.start_time = 0.0

            # 清理数据收集器
            if hasattr(self, 'data_collector') and self.data_collector:
                self.data_collector.clear_all_data()

            # 重置进度跟踪器
            if hasattr(self, 'progress_tracker') and self.progress_tracker:
                if hasattr(self.progress_tracker, 'reset'):
                    self.progress_tracker.reset()

            # 重置错误恢复器
            if hasattr(self, 'error_recovery') and self.error_recovery:
                self.error_recovery.reset()

            logger.debug("✅ 并行错频测试管理器测试环境清理完成")

        except Exception as e:
            logger.error(f"❌ 并行错频测试管理器清理测试环境失败: {e}")

    def clean_channel_states(self, channel_widgets: list):
        """清理通道状态（供外部调用）"""
        try:
            if hasattr(self, 'state_cleaner') and self.state_cleaner:
                return self.state_cleaner.clean_all_test_states(channel_widgets)
            else:
                logger.warning("状态清理器未初始化，跳过通道状态清理")
                return False

        except Exception as e:
            logger.error(f"清理通道状态失败: {e}")
            return False
