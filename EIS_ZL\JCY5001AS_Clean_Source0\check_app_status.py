#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JCY5001AS应用程序运行状态
"""

import psutil
import sys
import time

def check_python_processes():
    """检查Python进程"""
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and len(cmdline) > 1:
                    script_name = cmdline[1] if len(cmdline) > 1 else 'Unknown'
                    python_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'script': script_name
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return python_processes

def main():
    """主函数"""
    print("🔍 检查JCY5001AS应用程序运行状态...")
    print("=" * 50)
    
    # 检查Python进程
    processes = check_python_processes()
    
    jcy_processes = []
    test_processes = []
    
    for proc in processes:
        script = proc['script']
        if 'main.py' in script:
            jcy_processes.append(proc)
        elif 'test_channel_layout' in script:
            test_processes.append(proc)
    
    print(f"📊 发现 {len(processes)} 个Python进程")
    
    if jcy_processes:
        print("\n✅ JCY5001AS主应用程序:")
        for proc in jcy_processes:
            print(f"   PID: {proc['pid']} - {proc['script']}")
    else:
        print("\n❌ 未发现JCY5001AS主应用程序进程")
    
    if test_processes:
        print("\n✅ 布局测试应用程序:")
        for proc in test_processes:
            print(f"   PID: {proc['pid']} - {proc['script']}")
    else:
        print("\n❌ 未发现布局测试应用程序进程")
    
    # 检查是否有GUI窗口
    print("\n🖥️ 应用程序状态:")
    if jcy_processes or test_processes:
        print("   ✅ 应用程序正在运行")
        print("   💡 如果看不到窗口，请检查任务栏或最小化的窗口")
        print("   💡 可能需要点击任务栏图标来显示窗口")
    else:
        print("   ❌ 没有检测到相关应用程序进程")
    
    print("\n📋 运行中的所有Python进程:")
    for i, proc in enumerate(processes, 1):
        script_name = proc['script'].split('\\')[-1] if '\\' in proc['script'] else proc['script']
        print(f"   {i}. PID: {proc['pid']} - {script_name}")
    
    print("\n" + "=" * 50)
    print("💡 提示:")
    print("   • 如果应用程序已启动但看不到窗口，请检查任务栏")
    print("   • GUI应用程序通常不会在控制台显示输出")
    print("   • 可以使用Alt+Tab切换窗口查看应用程序")

if __name__ == "__main__":
    main()
