#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A电池阻抗测试系统 Nuitka Standalone打包脚本

使用Nuitka将Python项目打包成standalone模式（文件夹形式）
优化依赖包含，减少体积，提高性能

作者：Jack
日期：2025-01-31
版本：2.0 - 优化版
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def clean_previous_builds():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")

    dirs_to_clean = ["dist", "main.build", "main.dist", "__pycache__"]

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                if os.path.isdir(dir_name):
                    shutil.rmtree(dir_name)
                    print(f"✅ 已清理目录: {dir_name}")
                else:
                    os.remove(dir_name)
                    print(f"✅ 已清理文件: {dir_name}")
            except Exception as e:
                print(f"⚠️  清理 {dir_name} 时出错: {e}")

    print("✅ 构建环境清理完成")

def build_executable():
    """使用Nuitka构建standalone可执行文件"""

    print("🚀 开始使用Nuitka打包JCY5001A电池阻抗测试系统（Standalone模式）...")

    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 输出目录名
    output_name = f"JCY5001A_EIS_System_{timestamp}"

    # Nuitka编译命令 - 优化的Standalone模式
    nuitka_cmd = [
        "python", "-m", "nuitka",
        "--standalone",                    # 独立模式，生成文件夹
        "--windows-console-mode=disable", # Windows下禁用控制台窗口
        "--enable-plugin=pyqt5",          # 启用PyQt5插件

        # 核心依赖包 - 精确包含
        "--include-package=PyQt5",        # PyQt5 GUI框架
        "--include-package=numpy",        # 数值计算
        "--include-package=scipy",        # 科学计算（用户要求检查）
        "--include-package=serial",       # 串口通信（pyserial的导入名）
        "--include-package=openpyxl",     # Excel文件处理
        "--include-package=xlsxwriter",   # Excel写入
        "--include-package=PIL",          # 图像处理
        "--include-package=psutil",       # 系统信息
        "--include-package=sqlalchemy",   # 数据库ORM
        "--include-package=pandas",       # 数据分析
        "--include-package=matplotlib",   # 绘图库

        # Windows特定依赖（用户要求检查PYWIN32）
        "--include-package=win32api",     # Windows API
        "--include-package=win32con",     # Windows常量
        "--include-package=win32gui",     # Windows GUI API
        "--include-package=win32print",   # Windows打印API
        "--include-package=pywintypes",   # Windows类型

        # 数据文件
        "--include-data-dir=resources=resources",     # 资源文件夹
        "--include-data-dir=config=config",           # 配置文件夹
        "--include-data-dir=templates=templates",     # 模板文件夹
        "--include-data-files=requirements.txt=requirements.txt",  # 依赖文件

        # 排除不需要的包以减少体积
        "--nofollow-import-to=pytest",    # 排除测试框架
        "--nofollow-import-to=setuptools", # 排除安装工具
        "--nofollow-import-to=distutils", # 排除分发工具
        "--nofollow-import-to=tkinter",   # 排除Tkinter
        "--nofollow-import-to=IPython",   # 排除IPython
        "--nofollow-import-to=jupyter",   # 排除Jupyter

        # 输出设置
        f"--output-dir=dist",             # 输出目录
        f"--output-filename={output_name}",  # 输出文件名

        # 编译选项
        "--assume-yes-for-downloads",     # 自动确认下载
        "--show-progress",                # 显示进度
        "--show-memory",                  # 显示内存使用
        "--remove-output",                # 清理之前的输出
        "--jobs=4",                       # 并行编译（4个进程）

        # 主入口文件
        "main.py"
    ]
    
    # 如果存在图标文件，添加图标
    icon_path = os.path.join("resources", "icons", "app_icon.ico")
    if os.path.exists(icon_path):
        nuitka_cmd.insert(-1, f"--windows-icon-from-ico={icon_path}")
        print(f"✅ 找到应用图标: {icon_path}")
    
    print("📦 Nuitka编译命令:")
    print(" ".join(nuitka_cmd))
    print()
    
    try:
        # 执行Nuitka编译
        print("⏳ 开始编译，这可能需要10-20分钟时间...")
        print("💡 Standalone模式会生成一个包含所有依赖的文件夹")
        print()
        
        result = subprocess.run(nuitka_cmd, check=True, capture_output=False)
        
        if result.returncode == 0:
            print(f"✅ 编译成功！")
            
            # 检查输出目录
            output_dir = os.path.join("dist", output_name + ".dist")
            if os.path.exists(output_dir):
                print(f"📁 输出目录: {output_dir}")
                
                # 检查主执行文件
                exe_file = os.path.join(output_dir, output_name + ".exe")
                if os.path.exists(exe_file):
                    file_size = os.path.getsize(exe_file) / (1024 * 1024)
                    print(f"📊 主执行文件大小: {file_size:.1f} MB")
                
                # 计算整个目录大小
                total_size = 0
                for dirpath, _, filenames in os.walk(output_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                
                total_size_mb = total_size / (1024 * 1024)
                print(f"📊 总目录大小: {total_size_mb:.1f} MB")

                # 创建启动脚本
                create_startup_script(output_dir, output_name)

                print("\n🎉 打包完成！")
                print(f"💡 运行方式: 双击 {output_dir}\\{output_name}.exe")
                print(f"💡 或使用启动脚本: {output_dir}\\启动程序.bat")
                print("💡 整个文件夹可以复制到其他电脑上运行")
            
        else:
            print(f"❌ 编译失败，返回码: {result.returncode}")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka编译失败: {e}")
        print("💡 建议检查:")
        print("   1. 确保所有依赖包已正确安装")
        print("   2. 检查Python环境是否完整")
        print("   3. 确保有足够的磁盘空间（至少2GB）")
        return False
        
    except FileNotFoundError:
        print("❌ 找不到Nuitka命令")
        print("💡 请先安装Nuitka: pip install nuitka")
        return False
        
    except Exception as e:
        print(f"❌ 打包过程中发生错误: {e}")
        return False
    
    return True

def check_dependencies():
    """检查关键依赖是否存在"""
    print("🔍 检查关键依赖...")

    critical_deps = [
        ("PyQt5", "PyQt5"),
        ("numpy", "numpy"),
        ("scipy", "scipy"),  # 用户要求检查
        ("pyserial", "serial"),
        ("openpyxl", "openpyxl"),
        ("xlsxwriter", "xlsxwriter"),
        ("PIL", "PIL"),
        ("psutil", "psutil"),
        ("sqlalchemy", "sqlalchemy"),
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib"),
        ("pywin32", "win32api"),  # 用户要求检查PYWIN32
    ]

    missing_deps = []

    for dep_name, import_name in critical_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name}")
        except ImportError:
            print(f"❌ {dep_name} - 缺失")
            missing_deps.append(dep_name)

    if missing_deps:
        print(f"\n⚠️  发现缺失依赖: {', '.join(missing_deps)}")
        print("💡 请先安装缺失的依赖包:")
        for dep in missing_deps:
            print(f"   pip install {dep}")
        return False

    print("✅ 所有关键依赖检查通过")
    return True

def check_nuitka():
    """检查Nuitka是否安装"""
    try:
        result = subprocess.run(["python", "-m", "nuitka", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Nuitka版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Nuitka未安装或无法运行")
        print("💡 请先安装Nuitka: pip install nuitka")
        return False

def create_startup_script(output_dir, exe_name):
    """创建启动脚本"""
    try:
        startup_script = f"""@echo off
echo 正在启动JCY5001A电池阻抗测试系统...
echo.
cd /d "%~dp0"
"{exe_name}.exe"
if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查：
    echo 1. 确保所有文件完整
    echo 2. 检查系统兼容性
    echo 3. 联系技术支持
    pause
)
"""

        script_path = os.path.join(output_dir, "启动程序.bat")
        with open(script_path, 'w', encoding='gbk') as f:
            f.write(startup_script)

        print(f"✅ 已创建启动脚本: 启动程序.bat")
        return True

    except Exception as e:
        print(f"⚠️  创建启动脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("JCY5001A电池阻抗测试系统 - Nuitka Standalone打包工具 v2.0")
    print("=" * 70)
    print("🔧 优化特性:")
    print("  • 独立模式打包，无需Python环境")
    print("  • 包含scipy和pywin32依赖检查")
    print("  • 排除不必要的包以减少体积")
    print("  • 并行编译提高速度")
    print("=" * 70)

    # 清理之前的构建
    clean_previous_builds()
    print()

    # 检查Nuitka
    if not check_nuitka():
        sys.exit(1)

    print()

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    print()

    # 开始打包
    if build_executable():
        print("\n🎯 打包任务完成！")
        print("\n📝 使用说明:")
        print("1. 生成的文件夹包含所有运行时依赖")
        print("2. 可以将整个文件夹复制到其他Windows电脑上运行")
        print("3. 不需要在目标电脑上安装Python或其他依赖")
        print("4. 首次运行可能需要几秒钟初始化时间")
        print("5. 使用'启动程序.bat'可以更方便地启动软件")
        print("\n⚡ 性能提示:")
        print("• 首次运行会进行初始化，后续启动会更快")
        print("• 建议将整个文件夹放在SSD上以获得最佳性能")
        print("• 如遇到问题，请检查Windows Defender等杀毒软件设置")
    else:
        print("\n💥 打包任务失败！")
        print("💡 常见解决方案:")
        print("1. 确保所有依赖包已正确安装")
        print("2. 检查磁盘空间是否充足（建议至少3GB）")
        print("3. 关闭杀毒软件后重试")
        print("4. 使用管理员权限运行")
        sys.exit(1)

if __name__ == "__main__":
    main()
