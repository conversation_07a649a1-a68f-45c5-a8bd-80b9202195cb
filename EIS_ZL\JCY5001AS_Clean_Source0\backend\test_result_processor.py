# -*- coding: utf-8 -*-
"""
测试结果处理器
负责管理测试结果计算、分析、评定、格式化等功能

从TestEngine中提取的结果处理功能，遵循单一职责原则

Author: Jack
Date: 2025-01-30
"""

import logging
from typing import Dict, List, Optional, Callable, Any, Tuple
from datetime import datetime
import time

logger = logging.getLogger(__name__)


class TestResultProcessor:
    """
    测试结果处理器
    
    职责：
    - 测试结果计算和分析
    - Rs/Rct值计算
    - 电池等级评定
    - 结果数据格式化
    - 结果保存和导出
    - 合格性评估
    """
    
    def __init__(self, config_manager, db_manager, data_processor, result_callback=None):
        """
        初始化测试结果处理器
        
        Args:
            config_manager: 配置管理器
            db_manager: 数据库管理器
            data_processor: 数据处理器
            result_callback: 结果回调函数
        """
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.data_processor = data_processor
        self.result_callback = result_callback
        
        # 当前批次信息
        self.current_batch_id = None
        
        logger.info("测试结果处理器初始化完成")
    
    def process_test_result(self, channel, test_config: Dict) -> Dict:
        """
        处理测试结果
        
        Args:
            channel: 通道测试对象
            test_config: 测试配置
            
        Returns:
            处理后的结果字典
        """
        try:
            # 获取原始数据
            raw_data = getattr(channel, 'test_data', {})
            if not raw_data:
                logger.warning(f"通道{channel.channel_num}没有测试数据")
                return {}
            
            # 获取阻抗明细数据
            impedance_details = getattr(channel, 'impedance_details', [])
            
            # 构建完整的数据结构供数据处理器使用
            processing_data = {
                'impedance_data': raw_data.get('impedance_data', {}),
                'impedance_details': impedance_details
            }
            
            # 使用数据处理器处理数据
            processed_data = self.data_processor.process_impedance_data(
                processing_data,
                test_config['frequencies']
            )
            
            # 计算Rs和Rct值
            rs_value = processed_data.get('rs', 0.0)
            rct_value = processed_data.get('rct', 0.0)
            
            # 先判断是否合格
            is_pass = self.evaluate_pass_fail(rs_value, rct_value)
            
            # 只有合格的电池才分档位
            if is_pass:
                rs_grade, rct_grade = self.calculate_grades(rs_value, rct_value)
            else:
                rs_grade, rct_grade = None, None  # 不合格的电池不分档位
            
            # 计算测试时长
            duration = self._calculate_test_duration(channel)
            
            # 构建完整结果
            result = {
                'channel_number': channel.channel_num,
                'battery_code': channel.battery_code,
                'voltage': raw_data.get('voltage', 0.0),
                'rs_value': rs_value,
                'rct_value': rct_value,
                'w_impedance': processed_data.get('w_impedance', 0.0),
                'rs_grade': rs_grade,
                'rct_grade': rct_grade,
                'is_pass': is_pass,
                'fail_reason': None if is_pass else self.get_fail_reason(rs_value, rct_value),
                'test_duration': duration,
                'frequency_data': processed_data.get('frequency_data', []),
                'impedance_details': impedance_details,
                'raw_data': raw_data,
                'test_start_time': channel.start_time,
                'test_end_time': channel.end_time,
                'timestamp': datetime.now()
            }
            
            logger.info(f"通道{channel.channel_num}结果处理完成: Rs={rs_value:.3f}mΩ, "
                       f"Rct={rct_value:.3f}mΩ, 结果: {'合格' if is_pass else '不合格'}")
            
            return result
            
        except Exception as e:
            logger.error(f"处理测试结果失败: {e}")
            return {}
    
    def calculate_grades(self, rs_value: float, rct_value: float) -> Tuple[int, int]:
        """
        计算Rs和Rct档位
        
        Args:
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            
        Returns:
            (rs_grade, rct_grade) 档位元组
        """
        try:
            # 获取档位标准
            new_rs_max = self.config_manager.get('impedance.new_rs_max', 8.0)
            new_rct_max = self.config_manager.get('impedance.new_rct_max', 15.0)
            eol_rs_max = self.config_manager.get('impedance.eol_rs_max', 20.0)
            eol_rct_max = self.config_manager.get('impedance.eol_rct_max', 50.0)
            
            # 计算Rs档位
            if rs_value <= new_rs_max:
                rs_grade = 1  # New
            elif rs_value <= eol_rs_max:
                rs_grade = 2  # EOL
            else:
                rs_grade = 3  # 超标
            
            # 计算Rct档位
            if rct_value <= new_rct_max:
                rct_grade = 1  # New
            elif rct_value <= eol_rct_max:
                rct_grade = 2  # EOL
            else:
                rct_grade = 3  # 超标
            
            logger.debug(f"档位计算: Rs={rs_value:.3f}mΩ->档位{rs_grade}, "
                        f"Rct={rct_value:.3f}mΩ->档位{rct_grade}")
            
            return rs_grade, rct_grade
            
        except Exception as e:
            logger.error(f"计算档位失败: {e}")
            return 3, 3  # 默认为超标
    
    def evaluate_pass_fail(self, rs_value: float, rct_value: float, voltage: float = None) -> bool:
        """
        评估是否合格
        
        Args:
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            voltage: 电压值 (V, 可选)
            
        Returns:
            是否合格
        """
        try:
            # 获取合格标准（使用最高档位的最大值作为合格标准）
            eol_rs_max = self.config_manager.get('impedance.eol_rs_max', 20.0)
            eol_rct_max = self.config_manager.get('impedance.eol_rct_max', 50.0)
            
            # 获取Rs和Rct的最小值
            rs_min = self.config_manager.get('impedance.rs_min', 0.5)
            rct_min = self.config_manager.get('impedance.rct_min', 0.5)
            
            # 检查Rs范围
            if rs_value > eol_rs_max or rs_value < rs_min:
                logger.debug(f"Rs值超出范围: {rs_value:.3f}mΩ (范围: {rs_min}-{eol_rs_max}mΩ)")
                return False
            
            # 检查Rct范围
            if rct_value > eol_rct_max or rct_value < rct_min:
                logger.debug(f"Rct值超出范围: {rct_value:.3f}mΩ (范围: {rct_min}-{eol_rct_max}mΩ)")
                return False
            
            # 检查电压范围（如果提供）
            if voltage is not None:
                voltage_range = self.config_manager.get('test_params.voltage_range', {'min': 2.0, 'max': 5.0})
                voltage_min = voltage_range.get('min', 2.0)
                voltage_max = voltage_range.get('max', 5.0)
                
                if voltage < voltage_min or voltage > voltage_max:
                    logger.debug(f"电压值超出范围: {voltage:.3f}V (范围: {voltage_min}-{voltage_max}V)")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"评估合格性失败: {e}")
            return False
    
    def get_fail_reason(self, rs_value: float, rct_value: float, voltage: float = None) -> str:
        """
        获取失败原因
        
        Args:
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            voltage: 电压值 (V, 可选)
            
        Returns:
            失败原因字符串
        """
        try:
            reasons = []
            
            # 获取标准值
            eol_rs_max = self.config_manager.get('impedance.eol_rs_max', 20.0)
            eol_rct_max = self.config_manager.get('impedance.eol_rct_max', 50.0)
            rs_min = self.config_manager.get('impedance.rs_min', 0.5)
            rct_min = self.config_manager.get('impedance.rct_min', 0.5)
            
            # 检查Rs
            if rs_value > eol_rs_max:
                reasons.append(f"Rs过高({rs_value:.3f}mΩ > {eol_rs_max}mΩ)")
            elif rs_value < rs_min:
                reasons.append(f"Rs过低({rs_value:.3f}mΩ < {rs_min}mΩ)")
            
            # 检查Rct
            if rct_value > eol_rct_max:
                reasons.append(f"Rct过高({rct_value:.3f}mΩ > {eol_rct_max}mΩ)")
            elif rct_value < rct_min:
                reasons.append(f"Rct过低({rct_value:.3f}mΩ < {rct_min}mΩ)")
            
            # 检查电压
            if voltage is not None:
                voltage_range = self.config_manager.get('test_params.voltage_range', {'min': 2.0, 'max': 5.0})
                voltage_min = voltage_range.get('min', 2.0)
                voltage_max = voltage_range.get('max', 5.0)
                
                if voltage < voltage_min:
                    reasons.append(f"电压过低({voltage:.3f}V < {voltage_min}V)")
                elif voltage > voltage_max:
                    reasons.append(f"电压过高({voltage:.3f}V > {voltage_max}V)")
            
            return "; ".join(reasons) if reasons else "未知原因"
            
        except Exception as e:
            logger.error(f"获取失败原因失败: {e}")
            return "获取失败原因异常"
    
    def format_result_for_display(self, result: Dict) -> Dict:
        """
        格式化结果用于显示
        
        Args:
            result: 原始结果字典
            
        Returns:
            格式化后的结果字典
        """
        try:
            formatted = {
                'channel': result.get('channel_number', 0),
                'battery_code': result.get('battery_code', ''),
                'voltage': f"{result.get('voltage', 0.0):.3f}V",
                'rs_value': f"{result.get('rs_value', 0.0):.3f}mΩ",
                'rct_value': f"{result.get('rct_value', 0.0):.3f}mΩ",
                'w_impedance': f"{result.get('w_impedance', 0.0):.3f}mΩ",
                'rs_grade': self._format_grade(result.get('rs_grade')),
                'rct_grade': self._format_grade(result.get('rct_grade')),
                'is_pass': result.get('is_pass', False),
                'pass_status': '合格' if result.get('is_pass', False) else '不合格',
                'fail_reason': result.get('fail_reason', ''),
                'test_duration': f"{result.get('test_duration', 0.0):.1f}s",
                'timestamp': result.get('timestamp', datetime.now()).strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return formatted
            
        except Exception as e:
            logger.error(f"格式化结果失败: {e}")
            return {}
    
    def format_result_for_export(self, result: Dict) -> Dict:
        """
        格式化结果用于导出
        
        Args:
            result: 原始结果字典
            
        Returns:
            格式化后的导出结果字典
        """
        try:
            export_data = {
                '通道号': result.get('channel_number', 0),
                '电池码': result.get('battery_code', ''),
                '电压(V)': round(result.get('voltage', 0.0), 3),
                'Rs值(mΩ)': round(result.get('rs_value', 0.0), 3),
                'Rct值(mΩ)': round(result.get('rct_value', 0.0), 3),
                'W阻抗(mΩ)': round(result.get('w_impedance', 0.0), 3),
                'Rs档位': self._format_grade(result.get('rs_grade')),
                'Rct档位': self._format_grade(result.get('rct_grade')),
                '测试结果': '合格' if result.get('is_pass', False) else '不合格',
                '失败原因': result.get('fail_reason', ''),
                '测试时长(s)': round(result.get('test_duration', 0.0), 1),
                '开始时间': result.get('test_start_time', '').strftime('%Y-%m-%d %H:%M:%S') if result.get('test_start_time') else '',
                '结束时间': result.get('test_end_time', '').strftime('%Y-%m-%d %H:%M:%S') if result.get('test_end_time') else ''
            }
            
            return export_data
            
        except Exception as e:
            logger.error(f"格式化导出结果失败: {e}")
            return {}
    
    def save_test_result(self, channel, result: Dict, test_config: Dict):
        """
        保存测试结果到数据库
        
        Args:
            channel: 通道测试对象
            result: 测试结果
            test_config: 测试配置
        """
        try:
            test_data = {
                'batch_id': self.current_batch_id,
                'channel_number': channel.channel_num,
                'battery_code': channel.battery_code,
                'test_start_time': channel.start_time,
                'test_end_time': channel.end_time,
                'test_duration': result.get('test_duration', 0.0),
                'voltage': result.get('voltage', 0.0),
                'rs_value': result.get('rs_value', 0.0),
                'rct_value': result.get('rct_value', 0.0),
                'w_impedance': result.get('w_impedance', 0.0),
                'rs_grade': result.get('rs_grade'),
                'rct_grade': result.get('rct_grade'),
                'is_pass': result.get('is_pass', False),
                'fail_reason': result.get('fail_reason'),
                'test_mode': test_config.get('test_mode', ''),
                'frequency_list': test_config.get('frequencies', []),
                'frequency_data': result.get('frequency_data', []),
                'raw_data': result.get('raw_data', {})
            }
            
            self.db_manager.save_test_result(test_data)
            logger.debug(f"通道{channel.channel_num}测试结果已保存到数据库")
            
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")
    
    def notify_result(self, channel_num: int, result: Dict):
        """
        通知测试结果
        
        Args:
            channel_num: 通道号
            result: 测试结果
        """
        if self.result_callback:
            try:
                self.result_callback(channel_num, result)
            except Exception as e:
                logger.error(f"结果回调失败: {e}")
    
    def validate_result(self, result: Dict) -> bool:
        """
        验证结果数据完整性
        
        Args:
            result: 测试结果
            
        Returns:
            是否验证通过
        """
        try:
            required_fields = [
                'channel_number', 'battery_code', 'voltage',
                'rs_value', 'rct_value', 'is_pass'
            ]
            
            for field in required_fields:
                if field not in result:
                    logger.warning(f"结果缺少必要字段: {field}")
                    return False
            
            # 验证数值范围
            if result['rs_value'] < 0 or result['rct_value'] < 0:
                logger.warning("Rs或Rct值为负数")
                return False
            
            if result['voltage'] < 0:
                logger.warning("电压值为负数")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证结果失败: {e}")
            return False
    
    def get_grade_statistics(self, results: List[Dict]) -> Dict:
        """
        获取档位统计信息
        
        Args:
            results: 结果列表
            
        Returns:
            统计信息字典
        """
        try:
            stats = {
                'total_count': len(results),
                'pass_count': 0,
                'fail_count': 0,
                'rs_grades': {1: 0, 2: 0, 3: 0},
                'rct_grades': {1: 0, 2: 0, 3: 0},
                'pass_rate': 0.0
            }
            
            for result in results:
                if result.get('is_pass', False):
                    stats['pass_count'] += 1
                    
                    # 统计档位（只统计合格的）
                    rs_grade = result.get('rs_grade')
                    rct_grade = result.get('rct_grade')
                    
                    if rs_grade in stats['rs_grades']:
                        stats['rs_grades'][rs_grade] += 1
                    
                    if rct_grade in stats['rct_grades']:
                        stats['rct_grades'][rct_grade] += 1
                else:
                    stats['fail_count'] += 1
            
            # 计算合格率
            if stats['total_count'] > 0:
                stats['pass_rate'] = (stats['pass_count'] / stats['total_count']) * 100
            
            return stats
            
        except Exception as e:
            logger.error(f"获取档位统计失败: {e}")
            return {}
    
    def set_current_batch_id(self, batch_id: str):
        """设置当前批次ID"""
        self.current_batch_id = batch_id
        logger.debug(f"设置当前批次ID: {batch_id}")
    
    def set_result_callback(self, callback: Callable):
        """设置结果回调函数"""
        self.result_callback = callback
        logger.debug("结果回调函数已设置")
    
    def _calculate_test_duration(self, channel) -> float:
        """
        计算测试持续时间
        
        Args:
            channel: 通道对象
            
        Returns:
            测试持续时间（秒）
        """
        try:
            if channel.start_time and channel.end_time:
                return (channel.end_time - channel.start_time).total_seconds()
            return 0.0
        except Exception as e:
            logger.error(f"计算测试持续时间失败: {e}")
            return 0.0
    
    def _format_grade(self, grade: Optional[int]) -> str:
        """
        格式化档位显示
        
        Args:
            grade: 档位数值
            
        Returns:
            格式化后的档位字符串
        """
        if grade is None:
            return "未分级"
        elif grade == 1:
            return "1级(New)"
        elif grade == 2:
            return "2级(EOL)"
        elif grade == 3:
            return "3级(超标)"
        else:
            return f"{grade}级"