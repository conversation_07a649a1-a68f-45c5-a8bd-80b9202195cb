# 奈奎斯特图拟合曲线功能说明

## 功能概述

在数据分析模块的奈奎斯特图中新增了**拟合曲线显示功能**，可以直观地看到数据平滑处理的效果，帮助识别和消除测量数据中的噪声和异常点。

## 功能特点

### 🎯 **解决的问题**
- ✅ 消除奈奎斯特图中的凸起和不平滑现象
- ✅ 减少测量噪声对数据分析的影响
- ✅ 提供更清晰的EIS数据趋势显示
- ✅ 提高Rs/Rct计算的准确性

### 📊 **技术特性**
- **高精度三次样条插值**：优先使用CubicSpline算法，保持EIS数据特征
- **对数频率参数**：更适合EIS数据的指数频率特性
- **自适应密度控制**：根据数据点数智能调整插值密度
- **多层级拟合策略**：三次样条 → 加权样条 → 备用插值
- **端点精确匹配**：确保拟合曲线完全通过起始和结束点
- **质量验证机制**：自动检查拟合结果的合理性

## 使用方法

### 1. **启用拟合曲线显示**
在数据分析界面的奈奎斯特图控制面板中：
- 勾选 **"显示拟合曲线"** 复选框
- 拟合曲线将以红色线条显示在图表上

### 2. **图例说明**
- **蓝色线条**：原始测量数据
- **红色线条**：拟合平滑曲线

### 3. **实时切换**
- 可以随时勾选/取消勾选来对比原始数据和拟合效果
- 切换后图表会自动重新绘制

## 配置选项

### 数据处理配置 (config/app_config.json)
```json
"data": {
    "optimization": true,
    "outlier_filter": true,
    "smoothing": true,
    "smoothing_strength": 7,
    "curve_fitting": true,          // 启用拟合曲线功能
    "advanced_smoothing": true,     // 启用高级平滑算法
    "auto_save": true,
    "save_raw": false
}
```

### 关键参数说明
- **curve_fitting**: 控制是否启用拟合曲线功能
- **smoothing_strength**: 平滑强度 (1-10)，推荐值为7
- **advanced_smoothing**: 启用多级平滑算法

## 算法原理

### 拟合算法流程
1. **数据预处理**：按频率排序，转换为对数频率
2. **主要方法**：使用CubicSpline进行高精度三次样条拟合
3. **自适应密度**：根据数据点数智能调整插值密度
4. **端点匹配**：强制拟合曲线通过原始数据的端点
5. **质量验证**：检查拟合结果的合理性和稳定性
6. **备用策略**：如果主要方法失败，自动切换到加权样条方法

### 密度控制策略
```
≤10个点: 密度因子 = 2.0 (适度增密)
≤20个点: 密度因子 = 1.8 (轻度增密)
>20个点: 密度因子 = 1.5 (保持密度)
```

### 适用条件
- 最少需要5个数据点才能生成拟合曲线
- 数据点越多，拟合效果越好
- 适用于所有频率范围的EIS数据

## 效果验证

### 测试结果（改进后）
- **数据点扩展**：原始15点 → 拟合30点（智能密度控制）
- **拟合精度**：实部RMSE < 0.02mΩ，虚部RMSE < 0.03mΩ
- **端点匹配**：起始和结束点误差 = 0.000mΩ（完全匹配）
- **曲线平滑度**：显著改善数据连续性，同时保持EIS特征

### 应用场景
1. **质量检测**：识别测量过程中的异常数据
2. **趋势分析**：更清晰地观察阻抗变化趋势
3. **报告生成**：提供更专业的数据展示
4. **参数计算**：为Rs/Rct计算提供更准确的数据基础

## 注意事项

### ⚠️ **使用建议**
- 拟合曲线仅用于数据展示和趋势分析
- 实际的Rs/Rct计算仍基于原始测量数据
- 如果原始数据质量很好，拟合曲线可能与原始数据重合
- 对于数据点较少的情况，拟合效果可能有限

### 🔧 **性能考虑**
- 拟合计算会增加少量处理时间
- 对于大量数据点，建议适当调整平滑强度
- 可以通过配置文件禁用此功能以提高性能

## 故障排除

### 常见问题
1. **拟合曲线不显示**
   - 检查数据点数是否≥5
   - 确认"显示拟合曲线"复选框已勾选
   - 查看日志是否有错误信息

2. **拟合效果不理想**
   - 调整smoothing_strength参数 (推荐5-9)
   - 检查原始数据质量
   - 确认频率数据的有效性

3. **性能问题**
   - 在配置中设置curve_fitting为false
   - 减少数据点数量
   - 降低平滑强度

---

**版本信息**：V0.80.10+
**更新日期**：2025-06-29
**作者**：Jack
