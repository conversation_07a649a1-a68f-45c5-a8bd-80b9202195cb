<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
    <!-- 标题 -->
    <text x="700" y="40" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="black">
        图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图
    </text>

    <!-- 1-被测电池 -->
    <rect x="100" y="150" width="200" height="120" fill="#ADD8E6" stroke="black" stroke-width="2" rx="10"/>
    <text x="200" y="200" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="200" dy="0">1-被测电池</tspan>
        <tspan x="200" dy="15">1.9V-5.5V</tspan>
    </text>

    <!-- 7-测试夹具 -->
    <rect x="100" y="350" width="200" height="120" fill="#F5DEB3" stroke="black" stroke-width="2" rx="10"/>
    <text x="200" y="395" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="200" dy="0">7-测试夹具</tspan>
        <tspan x="200" dy="15">四线制连接</tspan>
        <tspan x="200" dy="15">精确测量</tspan>
    </text>

    <!-- 2-DNB1101BB芯片 -->
    <rect x="450" y="300" width="300" height="200" fill="#90EE90" stroke="black" stroke-width="2" rx="10"/>
    <text x="600" y="385" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="600" dy="0">2-DNB1101BB</tspan>
        <tspan x="600" dy="15">EIS测试芯片</tspan>
        <tspan x="600" dy="15">0.0075Hz-7800Hz</tspan>
    </text>

    <!-- 4-外部电流源 -->
    <rect x="100" y="600" width="200" height="150" fill="#F08080" stroke="black" stroke-width="2" rx="10"/>
    <text x="200" y="660" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="200" dy="0">4-外部电流源</tspan>
        <tspan x="200" dy="15">PMV28UNEA</tspan>
        <tspan x="200" dy="15">20Ω/10Ω/6.67Ω/5Ω</tspan>
    </text>

    <!-- 3-STM32控制器 -->
    <rect x="850" y="300" width="250" height="200" fill="#FFFFE0" stroke="black" stroke-width="2" rx="10"/>
    <text x="975" y="385" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="975" dy="0">3-STM32F103RCT6</tspan>
        <tspan x="975" dy="15">主控制器</tspan>
        <tspan x="975" dy="15">72MHz ARM</tspan>
    </text>

    <!-- 5-串口显示屏 -->
    <rect x="850" y="600" width="250" height="150" fill="#D3D3D3" stroke="black" stroke-width="2" rx="10"/>
    <text x="975" y="660" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="975" dy="0">5-串口显示屏</tspan>
        <tspan x="975" dy="15">实时显示</tspan>
        <tspan x="975" dy="15">测试结果</tspan>
    </text>

    <!-- 6-PC上位机 -->
    <rect x="850" y="100" width="250" height="120" fill="#B0C4DE" stroke="black" stroke-width="2" rx="10"/>
    <text x="975" y="150" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
        <tspan x="975" dy="0">6-PC上位机</tspan>
        <tspan x="975" dy="15">Modbus RTU</tspan>
        <tspan x="975" dy="15">数据分析</tspan>
    </text>

    <!-- 定义箭头标记 -->
    <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
            <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
        </marker>
        <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
            <polygon points="0 0, 10 3.5, 0 7" fill="blue"/>
        </marker>
        <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
            <polygon points="0 0, 10 3.5, 0 7" fill="purple"/>
        </marker>
        <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
            <polygon points="0 0, 10 3.5, 0 7" fill="red"/>
        </marker>
        <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
            <polygon points="0 0, 10 3.5, 0 7" fill="green"/>
        </marker>
        <marker id="arrowhead-orange" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
            <polygon points="0 0, 10 3.5, 0 7" fill="orange"/>
        </marker>
    </defs>

    <!-- 连接线 -->
    <!-- 电池 ↔ 测试夹具 -->
    <line x1="200" y1="270" x2="200" y2="350" stroke="black" stroke-width="3" marker-end="url(#arrowhead)" marker-start="url(#arrowhead)"/>
    <text x="220" y="310" font-family="Arial" font-size="10" fill="black">电气连接</text>

    <!-- 测试夹具 → DNB1101BB -->
    <line x1="300" y1="410" x2="450" y2="400" stroke="blue" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
    <text x="375" y="395" font-family="Arial" font-size="10" fill="blue">电压/电流测量信号</text>

    <!-- DNB1101BB → STM32 -->
    <line x1="750" y1="400" x2="850" y2="400" stroke="purple" stroke-width="3" marker-end="url(#arrowhead-purple)"/>
    <text x="800" y="390" font-family="Arial" font-size="10" fill="purple">SPI 1Mbps</text>

    <!-- STM32 → PC -->
    <line x1="975" y1="300" x2="975" y2="220" stroke="red" stroke-width="3" marker-end="url(#arrowhead-red)"/>
    <text x="985" y="260" font-family="Arial" font-size="10" fill="red">USB/UART</text>

    <!-- STM32 → 显示屏 -->
    <line x1="975" y1="500" x2="975" y2="600" stroke="green" stroke-width="3" marker-end="url(#arrowhead-green)"/>
    <text x="985" y="550" font-family="Arial" font-size="10" fill="green">UART 115200bps</text>

    <!-- DNB1101BB → 电流源 -->
    <line x1="500" y1="500" x2="300" y2="650" stroke="orange" stroke-width="3" marker-end="url(#arrowhead-orange)"/>
    <text x="400" y="575" font-family="Arial" font-size="10" fill="orange">VSW/VDR控制信号</text>

    <!-- 电流源 → 测试夹具 -->
    <line x1="200" y1="600" x2="200" y2="470" stroke="red" stroke-width="3" marker-end="url(#arrowhead-red)"/>
    <text x="220" y="535" font-family="Arial" font-size="10" fill="red">激励电流</text>

    <!-- 说明框 -->
    <rect x="400" y="800" width="600" height="120" fill="#FFFACD" stroke="gray" stroke-width="1" rx="5"/>
    <text x="420" y="820" font-family="Arial" font-size="12" font-weight="bold" fill="black">信号流向说明：</text>
    <text x="420" y="840" font-family="Arial" font-size="11" fill="black">1. 电池通过测试夹具连接到系统</text>
    <text x="420" y="855" font-family="Arial" font-size="11" fill="black">2. DNB1101BB芯片测量电池的电压和电流</text>
    <text x="420" y="870" font-family="Arial" font-size="11" fill="black">3. 外部电流源提供EIS测试所需的激励信号</text>
    <text x="420" y="885" font-family="Arial" font-size="11" fill="black">4. STM32控制器处理测试数据和系统控制</text>
    <text x="420" y="900" font-family="Arial" font-size="11" fill="black">5. 测试结果同时显示在本地屏幕和上位机</text>

    <!-- 技术参数 -->
    <text x="700" y="970" text-anchor="middle" font-family="Arial" font-size="12" font-style="italic" font-weight="bold" fill="black">
        系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C
    </text>
</svg>