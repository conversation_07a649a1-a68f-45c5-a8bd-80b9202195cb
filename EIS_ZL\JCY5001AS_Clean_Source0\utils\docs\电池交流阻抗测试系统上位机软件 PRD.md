# 电池交流阻抗测试系统上位机软件 PRD

**文档版本**：1.4
**创建日期**：2025-05-03
**更新日期**：2025-05-25
**作者**：Jack
**状态**：更新中

## 文档修订历史

| 版本 | 日期 | 修订人 | 修订内容 |
|------|------|--------|----------|
| 1.0 | 2025-05-03 | Jack | 初始版本 |
| 1.1 | 2025-05-10 | Jack | 更新功能状态、添加测试计划与质量保证章节、添加技术规格详情章节 |
| 1.2 | 2025-05-15 | Jack | 添加参考EIS软件列表、更新产线模式工作流程（添加扫码步骤）、添加阻抗档位分类方法、添加自动学习功能 |
| 1.3 | 2025-05-20 | Jack | 将电池二维码功能设置为可选、添加软件在线升级功能、添加产线测试系统参考案例 |
| 1.4 | 2025-05-25 | Jack | 更新频率设置功能，添加预设频率点、自定义频率点、增益自动适配等功能 |

## 1. 产品概述

### 1.1 产品定位

电池交流阻抗测试系统上位机软件是一款专为电池测试设计的应用程序，支持研发模式和产线模式两种工作方式。该软件通过RS422接口与硬件设备通信，可同时处理8个测试通道的数据，提供高精度的电池阻抗测量和分析功能。

### 1.2 目标用户

- **研发模式**：实验室技术人员，主要进行电池特性测试和建模分析
- **产线模式**：生产线操作员，主要进行电池快速阻抗筛选和电芯配对

### 1.3 产品价值

- 为实验室提供全面的电池阻抗分析工具，支持电池建模和特性研究
- 为生产线提供高效的电池筛选和配对解决方案，提高生产效率
- 通过云端数据关联，实现电池全生命周期的质量追踪

## 2. 系统架构

### 2.1 硬件规格

- **频率范围**：0.01Hz - 7.8kHz
- **测试通道**：8个并行测试通道
- **测量精度**：电压偏差 ±2mV
- **通信接口**：RS422，波特率115200
- **测量参数**：Rs、Rct、W阻抗、电池容量、电压等

### 2.2 软件架构

- **前端**：PyQt5图形界面，支持数据可视化和用户交互
- **后端**：数据处理模块、通信模块、分析模块、报告生成模块
- **数据存储**：本地数据库 + 云端数据同步
- **模式切换**：研发模式与产线模式的无缝切换

## 3. 功能需求（TODO列表）

### 3.1 通用功能

#### 3.1.1 系统设置
- [x] 通信参数配置（COM端口、波特率等）
- [x] 测试参数配置（频率范围、测试点数等）
- [ ] 用户权限管理
- [ ] 系统校准功能

#### 3.1.2 数据管理
- [ ] 测试数据保存与加载
- [ ] 数据导出（CSV、Excel格式）
- [ ] 历史数据查询
- [ ] 数据备份与恢复

#### 3.1.3 报告生成
- [ ] 测试报告模板配置
- [ ] PDF/Word格式报告生成
- [ ] 批量报告生成
- [ ] 报告自动归档

#### 3.1.4 云端集成与系统维护
- [ ] 与电池二维码关联（可选功能）
- [ ] 云端数据同步
- [ ] 远程数据访问
- [ ] API接口支持
- [ ] 软件在线升级功能
- [ ] 版本管理与回退
- [ ] 升级日志记录

### 3.2 研发模式功能

#### 3.2.1 测试配置
- [x] 频率扫描范围设置（0.01Hz - 7.8kHz）
- [x] 测试点数设置（支持自定义频率点列表）
- [x] 增益设置（1、4、16）
- [ ] 平均次数设置（1-64）
- [ ] 采样电阻选择（20R、10R、6.67R、5R）

#### 3.2.2 数据可视化
- [ ] 奈奎斯特图（实部vs虚部）
- [ ] 波特图（幅值和相位vs频率）
- [ ] 多通道数据对比
- [ ] 实时数据更新
- [ ] 图表缩放、平移、导出功能

#### 3.2.3 数据分析
- [ ] 等效电路模型拟合
- [ ] 参数提取（Rs、Rct、Cdl、W等）
- [ ] 离群特性分析
- [ ] 容量预估功能
- [ ] 温度影响分析
- [ ] 老化趋势分析

#### 3.2.4 高级功能
- [ ] 自定义测试序列
- [ ] 长时间测试监控
- [ ] 阈值报警设置
- [ ] 数据异常检测

### 3.3 产线模式功能

#### 3.3.1 快速测试
- [ ] 电池二维码扫描（可选功能，可通过系统设置启用/禁用）
- [ ] 电池ID一致性验证（可选功能，启用二维码功能时生效）
- [ ] 预设测试方案选择
- [ ] 一键启动测试
- [ ] 批量测试支持
- [ ] 测试进度实时显示
- [ ] 测试结果自动保存

#### 3.3.2 结果显示
- [ ] 8通道并行显示框，每个通道显示对应测试结果
- [ ] 电压范围设置功能（设定上下限）
- [x] 电压超出范围时的警告提示
- [x] 关键参数显示（Rs、Rct、W阻抗、电池容量、电压）
- [x] 合格/不合格判断（基于设置的阈值）
- [ ] 阻抗档位分类（基于Rs和Rct的二维分类）
- [ ] 配对建议（基于总档位的电池配对）

#### 3.3.3 自动学习功能
- [ ] 样本数据采集（测试数十个电池样本）
- [ ] 参数自动优化（根据样本数据自动调整测试参数）
- [ ] 档位自动划分（根据样本数据自动设置Rs和Rct的档位边界）
- [ ] 学习结果保存与加载
- [ ] 手动调整与微调功能

#### 3.3.4 生产管理
- [ ] 批次管理
- [ ] 产量统计
- [ ] 不良率统计
- [ ] 操作员绩效跟踪

#### 3.3.5 质量控制
- [ ] SPC控制图
- [ ] 趋势分析
- [ ] 异常报警
- [ ] 质量追溯

## 4. 用户界面设计

### 4.1 通用界面元素

- [ ] 主菜单栏
- [ ] 工具栏
- [ ] 状态栏
- [ ] 模式切换按钮
- [ ] 通道选择面板

### 4.2 研发模式界面

- [ ] 测试参数配置面板
- [ ] 图表显示区域（可切换奈奎斯特图/波特图）
- [ ] 数据分析面板
- [ ] 等效电路模型编辑器
- [ ] 测试控制面板

### 4.3 产线模式界面

#### 4.3.1 主界面元素
- [ ] 批次信息输入区
- [ ] 电池二维码扫描输入区（可选功能，可在设置中启用/禁用）
- [ ] 8通道测试结果并行显示区
- [ ] 电压范围设置控件
- [ ] 合格/不合格指示（每个通道）
- [ ] 阻抗档位显示（Rs档位+Rct档位）
- [ ] 配对分组显示
- [ ] 生产统计信息
- [ ] 软件更新提示区域

#### 4.3.2 参数设置界面
- [x] 测试频率设置（支持预设频率点和自定义频率点）
- [x] 增益设置（自动适配最佳增益）
- [ ] 电压范围设置
- [ ] Rs阈值设置
- [ ] Rct阈值设置
- [ ] 档位边界设置
- [ ] 二维码功能启用/禁用开关
- [ ] 软件更新设置

#### 4.3.3 自动学习界面
- [ ] 样本数量设置
- [ ] 学习进度显示
- [ ] 样本数据列表
- [ ] 数据分布图表（Rs和Rct分布）
- [ ] 档位边界可视化
- [ ] 学习结果保存/加载按钮
- [ ] 手动微调控件

## 5. 非功能需求

### 5.1 性能需求

- [ ] 支持8个通道同时数据采集和处理
- [ ] 图形界面刷新率≥10Hz
- [ ] 数据保存响应时间<1秒
- [ ] 报告生成时间<5秒

### 5.2 可靠性需求

- [ ] 系统稳定运行时间>24小时
- [ ] 数据自动备份机制
- [ ] 异常恢复功能
- [ ] 断电保护

### 5.3 安全性需求

- [ ] 用户权限分级管理
- [ ] 操作日志记录
- [ ] 数据加密传输
- [ ] 敏感数据保护

### 5.4 兼容性需求

- [ ] 支持Windows 10/11操作系统
- [ ] 支持不同型号的阻抗测试硬件
- [ ] 兼容主流数据格式

## 6. 实现计划

### 6.1 开发阶段

1. **需求分析与设计**（2周）
   - [x] 需求确认
   - [x] 系统架构设计
   - [ ] UI/UX设计

2. **底层功能开发**（4周）
   - [x] 通信模块开发
   - [x] 数据处理模块开发
   - [x] 底层功能测试

3. **通信稳定性优化**（2周）
   - [x] 电池电压读取重试机制
   - [x] 低频测量稳定性优化
   - [x] 停止测量命令响应异常修复

4. **阻抗分析功能**（2周）
   - [x] 阻抗参数提取算法（Rs、Rct、W阻抗）
   - [x] 电池容量估算
   - [x] 合格/不合格判断
   - [x] 阻抗档位分类

5. **自动学习功能**（2周）
   - [x] 样本数据采集
   - [x] 统计分析和聚类算法
   - [x] 档位边界自动计算
   - [x] 学习结果保存与加载

6. **多线程优化**（1周）
   - [x] 线程管理模块
   - [x] 测量控制器
   - [x] 后台任务处理

7. **前端界面开发**（3周）
   - [ ] 通用界面元素实现
   - [ ] 产线模式界面实现
   - [ ] 研发模式界面实现

8. **产线模式功能实现**（2周）
   - [ ] 8通道并行显示
   - [ ] 快速测试流程
   - [ ] 二维码扫描（可选功能）
   - [ ] 配对建议

9. **研发模式功能实现**（2周）
   - [ ] 数据可视化功能
   - [ ] 分析功能
   - [ ] 高级功能

10. **数据管理功能**（2周）
    - [ ] 测试报告生成
    - [ ] 数据存储功能
    - [ ] 数据导出功能

11. **在线升级功能**（1周）
    - [ ] 版本检测
    - [ ] 更新下载
    - [ ] 安装与回退

12. **集成与测试**（2周）
    - [ ] 模块集成
    - [ ] 系统测试
    - [ ] 性能优化

13. **部署与培训**（2周）
    - [ ] 系统部署
    - [ ] 用户培训
    - [ ] 文档完善

### 6.2 里程碑

- [x] M1: 需求确认完成
- [x] M2: 系统架构设计完成
- [x] M3: 底层功能开发完成
- [x] M4: 通信稳定性优化完成
- [x] M5: 阻抗分析功能完成
- [x] M6: 自动学习功能完成
- [x] M7: 多线程优化完成
- [ ] M8: 前端界面开发完成
- [ ] M9: 产线模式功能完成
- [ ] M10: 研发模式功能完成
- [ ] M11: 数据管理功能完成
- [ ] M12: 在线升级功能完成
- [ ] M13: 系统测试完成
- [ ] M14: 系统部署完成

### 6.3 当前迭代计划（3周）

#### 6.3.1 前端界面开发

1. **通用界面元素**（1周）
   - [ ] 主窗口框架
   - [ ] 通道显示组件
   - [ ] 参数配置面板
   - [ ] 数据可视化组件
   - [ ] 状态栏和工具栏

2. **产线模式界面**（1周）
   - [ ] 8通道测试结果显示
   - [ ] 测试参数配置界面
   - [ ] 档位分类可视化
   - [ ] 合格/不合格指示
   - [ ] 批量测试控制

3. **研发模式界面**（1周）
   - [ ] 频率扫描设置
   - [ ] 阻抗谱图显示
   - [ ] 等效电路拟合
   - [ ] 数据分析工具
   - [ ] 高级参数配置

### 6.4 开发进度跟踪

#### 6.4.1 已完成功能

1. **通信模块**
   - [x] 串口通信（SerialComm类）
   - [x] 阻抗测试仪通信（ImpedanceMeter类）
   - [x] 电池电压读取重试机制
   - [x] 低频测量稳定性优化
   - [x] 停止测量命令响应异常修复

2. **阻抗分析功能**
   - [x] 阻抗参数提取算法（ImpedanceAnalyzer类）
   - [x] 电池容量估算
   - [x] 合格/不合格判断
   - [x] 阻抗档位分类

3. **自动学习功能**
   - [x] 样本数据采集（ImpedanceLearner类）
   - [x] 统计分析和聚类算法
   - [x] 档位边界自动计算
   - [x] 学习结果保存与加载

4. **多线程优化**
   - [x] 线程管理模块（SimpleThreadManager类）
   - [x] 测量控制器（SimpleMeasurementController类）
   - [x] 后台任务处理（BackgroundTaskProcessor类）

5. **多通道控制**
   - [x] 多通道测量协调（MultiChannelController类）
   - [x] 高频干扰处理
   - [x] 错频启动功能
   - [x] 通道自动侦测（ChannelDetector类）

#### 6.4.2 进行中功能

1. **前端界面开发**
   - [ ] 通用界面元素
   - [ ] 产线模式界面
   - [ ] 研发模式界面

#### 6.4.3 待开发功能

1. **产线模式功能**
   - [ ] 8通道并行显示
   - [ ] 快速测试流程
   - [ ] 二维码扫描（可选功能）
   - [ ] 配对建议

2. **研发模式功能**
   - [ ] 数据可视化
   - [ ] 分析功能
   - [ ] 高级功能

3. **数据管理功能**
   - [ ] 测试报告生成
   - [ ] 数据存储
   - [ ] 数据导出

4. **在线升级功能**
   - [ ] 版本检测
   - [ ] 更新下载
   - [ ] 安装与回退

## 7. 风险与应对

| 风险 | 影响 | 可能性 | 应对措施 |
|------|------|--------|----------|
| 硬件通信不稳定 | 高 | 中 | 实现通信重试机制，增加错误处理 |
| 大量数据处理性能不足 | 中 | 中 | 优化数据处理算法，实现数据分批处理 |
| 用户操作复杂度高 | 中 | 高 | 简化UI，提供向导式操作，完善帮助文档 |
| 云端集成困难 | 高 | 中 | 提前规划API接口，预留本地缓存机制 |
| 自动学习样本不足 | 高 | 中 | 提供默认参数设置，允许手动调整，增加验证机制 |
| 二维码扫描与电池不匹配 | 高 | 高 | 添加二次确认机制，提供手动输入选项，记录操作日志 |

## 8. 测试计划与质量保证

### 8.1 测试策略

#### 8.1.1 单元测试
- 通信模块测试：验证与设备的通信稳定性和正确性
- 数据处理模块测试：验证阻抗分析算法的准确性
- UI组件测试：验证界面元素的功能和交互

#### 8.1.2 集成测试
- 模块间接口测试：验证各模块之间的数据传递
- 功能流程测试：验证完整的测试流程
- 性能测试：验证系统在高负载下的响应时间和稳定性

#### 8.1.3 系统测试
- 功能测试：验证所有功能需求的实现
- 兼容性测试：验证在不同操作系统和硬件环境下的兼容性
- 用户体验测试：验证软件的易用性和用户体验

### 8.2 测试用例设计

#### 8.2.1 通信模块测试用例
- 测试串口连接与断开
- 测试命令发送与响应接收
- 测试通信异常处理
- 测试低频测量稳定性（10Hz、100Hz）
- 测试电池电压读取重试机制

#### 8.2.2 阻抗分析测试用例
- 测试Rs、Rct、W阻抗参数提取
- 测试容量估算算法
- 测试合格/不合格判断逻辑
- 测试阻抗单位转换（μΩ到mΩ）

#### 8.2.3 UI测试用例
- 测试8通道并行显示
- 测试参数配置界面
- 测试报告生成功能
- 测试数据可视化组件

### 8.3 质量指标

- 代码覆盖率：单元测试覆盖率≥80%
- 缺陷密度：每千行代码缺陷数<5
- 性能指标：UI响应时间<200ms，数据处理时间<500ms
- 稳定性指标：连续运行24小时无崩溃

### 8.4 已验证功能

1. 通信模块 - 已验证可以正确连接设备并读取数据
2. 阻抗测量 - 已验证可以在1000Hz下测量阻抗
3. 阻抗分析 - 已验证可以提取Rs、Rct、W阻抗参数
4. 单位转换 - 已验证从设备读取的阻抗值单位为μΩ，需转换为mΩ显示
5. 电池电压读取重试机制 - 已验证可以在通信不稳定时重试读取电池电压

## 9. 附录

### 9.1 术语表

- **Rs**: 串联电阻，代表电解质和电极材料的欧姆电阻
- **Rct**: 电荷转移电阻，代表电极/电解质界面的电荷转移过程
- **W阻抗**: 瓦伯格阻抗，代表扩散过程
- **奈奎斯特图**: 复平面上的阻抗谱，横轴为实部，纵轴为虚部
- **波特图**: 频率域上的阻抗谱，显示幅值和相位随频率的变化
- **μΩ**: 微欧姆，阻抗单位，1μΩ = 0.001mΩ = 0.000001Ω

### 9.2 参考文档

- 电池阻抗测试仪硬件规格书
- RS422通信协议文档
- 电池等效电路模型理论
- JCY5001阻抗测试仪指令说明_0.5.pdf

### 9.3 参考EIS上位机软件

以下是市场上主流的电化学阻抗谱(EIS)上位机软件，可作为本项目的参考：

1. **Gamry Framework**
   - 开发商：Gamry Instruments
   - 特点：完整的EIS测量和分析功能，支持等效电路拟合，提供丰富的数据可视化
   - 优势：专业的数据分析工具，支持多种电化学测试方法
   - 界面参考：[Gamry Framework](https://www.gamry.com/software/)

2. **EC-Lab**
   - 开发商：Bio-Logic
   - 特点：模块化设计，支持多种电化学测试技术，强大的数据处理功能
   - 优势：用户友好的界面，灵活的测试序列设计
   - 界面参考：[EC-Lab Software](https://www.biologic.net/products/ec-lab-software/)

3. **NOVA**
   - 开发商：Metrohm Autolab
   - 特点：直观的用户界面，支持脚本编程，提供丰富的数据分析工具
   - 优势：高度可定制的测试流程，强大的数据导出功能
   - 界面参考：[NOVA Software](https://www.metrohm.com/en/products/electrochemistry/autolab-electrochemical-software/)

4. **ZView**
   - 开发商：Scribner Associates
   - 特点：专注于EIS数据分析，提供丰富的等效电路模型和拟合工具
   - 优势：强大的阻抗数据分析能力，直观的图形界面
   - 界面参考：[ZView Software](https://www.scribner.com/software/68-general-electrochemistr/376-zview-for-windows/)

5. **BatteryLab**
   - 开发商：Basytec
   - 特点：专为电池测试设计，支持多通道并行测试，提供完整的电池参数分析
   - 优势：针对产线应用优化，支持批量测试和数据管理
   - 界面参考：[BatteryLab Software](https://www.basytec.de/produkte/software/)

### 9.4 产线测试系统参考案例

以下是电池产线测试系统的参考案例，可为本项目的产线模式提供借鉴：

1. **HIOKI BT4560电池阻抗测试仪**
   - 开发商：HIOKI日置
   - 特点：支持0.01Hz~1.05kHz频率范围的EIS测量，同时测量阻抗、电压和温度
   - 产线应用：提供LAN接口和专用扫描仪，支持生产线集成
   - 界面参考：[HIOKI BT4560](https://www.hioki.cn/products/150.html)

2. **Chroma 17020E能源回收式电池模组测试系统**
   - 开发商：Chroma致茂电子
   - 特点：提供Battery Pro软件平台，符合二次电池组的各种测试需求
   - 产线应用：具备断电数据保存恢复功能，高度稳定性与安全性
   - 界面参考：[Chroma 17020E](https://www.chromaate.com/cn/product/regenerative_battery_pack_test_system_17020e_224)

3. **新威电池测试系统BTS**
   - 开发商：深圳新威尔
   - 特点：提供友好的操作界面，支持多通道电池测试
   - 产线应用：支持自动化生产线集成，提供完整的数据管理功能
   - 界面参考：[新威BTS系统](https://www.neware-technology.com/)

4. **Bitrode电池测试系统**
   - 开发商：Bitrode
   - 特点：提供电池生产线化成和实验室测试设备，支持各种电池化学成分
   - 产线应用：提供软件工具、电池模拟和电池制造自动化工具
   - 界面参考：[Bitrode系统](http://www.bitrode.com/)

### 9.5 软件在线升级功能说明

软件在线升级功能是保持系统持续优化和问题修复的重要机制，具体实现方式如下：

1. **升级机制**
   - 服务器检测：定期检查服务器是否有新版本
   - 手动检测：用户可手动触发检测更新
   - 增量更新：仅下载和安装变更部分，减少网络流量
   - 完整更新：提供完整安装包下载选项

2. **升级流程**
   - 版本检测：比对本地版本与服务器版本
   - 更新提示：显示新版本信息和更新内容
   - 用户确认：用户确认是否进行更新
   - 下载安装：后台下载并安装更新
   - 重启应用：完成安装后提示重启应用

3. **安全保障**
   - 数字签名：确保更新包的真实性和完整性
   - 版本回退：支持回退到之前的稳定版本
   - 升级日志：记录所有升级操作和结果
   - 断点续传：支持更新过程中断后继续

4. **管理功能**
   - 版本管理：查看历史版本和当前版本
   - 更新通道：支持稳定版和测试版两种更新通道
   - 自动更新：可设置自动下载和安装更新
   - 更新计划：可设置在指定时间进行更新

## 10. 技术规格详情

### 10.1 硬件通信协议

#### 10.1.1 通信参数
- 接口类型：RS422
- 波特率：115200bps
- 数据位：8位
- 停止位：1位
- 校验位：无
- 流控制：无

#### 10.1.2 命令格式
- 基于Modbus RTU协议
- 设备地址：0x01
- 功能码：0x03（读保持寄存器）、0x04（读输入寄存器）、0x05（写单个线圈）、0x10（写多个寄存器）等
- CRC校验：Modbus-16 CRC校验

### 10.2 数据格式

#### 10.2.1 阻抗数据
- 实部/虚部：32位浮点数，IEEE 754格式
- 单位：微欧姆（μΩ）
- 数据范围：0.1μΩ ~ 10MΩ

#### 10.2.2 电池电压数据
- 格式：16位无符号整数，4位定点小数
- 单位：伏特（V）
- 数据范围：0.0000V ~ 6.5535V
- 示例：4.1234V = 41234 = 0xA112

### 10.3 软件技术栈

#### 10.3.1 开发语言与框架
- 编程语言：Python 3.8+
- GUI框架：PyQt5
- 数据处理：NumPy, SciPy
- 数据可视化：Matplotlib, PyQtGraph
- 数据库：SQLite
- 报告生成：ReportLab

#### 10.3.2 软件架构
- 设计模式：MVC（Model-View-Controller）
- 模块化设计：通信模块、数据处理模块、UI模块、报告模块
- 多线程处理：QThread用于后台通信和数据处理
- 事件驱动：基于Qt信号槽机制

### 10.4 算法规格

#### 10.4.1 阻抗分析算法
- 等效电路模型：Randles模型（Rs + Rct//Cdl + W）
- 参数提取方法：非线性最小二乘拟合
- 容量估算算法：基于Rct和W阻抗的经验公式

#### ******** 频率设置优化
- **预设频率点**：提供多组预设频率点列表，适用于不同类型电池
  - 磁性材料电池：0.1Hz, 1Hz, 10Hz, 100Hz, 1000Hz
  - 锡离子电池：0.01Hz, 0.1Hz, 1Hz, 10Hz, 100Hz, 1000Hz
  - 磁性材料电池：0.05Hz, 0.5Hz, 5Hz, 50Hz, 500Hz, 5000Hz
- **自定义频率点**：支持用户自定义频率点列表，可保存为模板
- **频率扫描策略**：支持线性扫描和对数扫描两种模式
- **自动频率优化**：基于样本数据自动确定最佳测试频率点
- **增益自动适配**：根据不同频率点自动选择最佳增益值，确保测量精度

#### 10.4.2 数据处理
- 异常值检测：基于统计方法的离群值检测
- 数据平滑：移动平均滤波
- 频率响应分析：FFT和频域分析

#### 10.4.3 阻抗档位分类算法
- **二维分类方法**：基于Rs和Rct两个参数进行二维分类
- **Rs档位划分**：将Rs值划分为多个档位（例如1-5档）
- **Rct档位划分**：将Rct值划分为多个档位（例如1-5档）
- **总档位计算**：Rs档位 + Rct档位组合形成总档位编码
- **档位边界确定**：
  - 自动学习模式：通过样本数据统计分析自动确定
  - 手动设置模式：由用户根据经验手动设置
- **配对策略**：相同或相近总档位的电池可以配对使用

#### 10.4.4 自动学习算法
- **数据采集**：收集样本电池的阻抗测试数据（建议30-50个样本）
- **统计分析**：计算Rs和Rct的分布特性（均值、标准差、分位数等）
- **聚类分析**：使用K-means等聚类算法自动划分档位边界
- **参数优化**：根据样本数据特性自动调整测试频率、增益等参数
- **验证与微调**：通过交叉验证评估分类效果，提供手动微调接口

### 9.6 简明需求摘要（摘自docs/PRD.md）

# 电池交流阻抗测试系统 - 产品需求文档 (PRD)

## 1. 产品概述

电池交流阻抗测试系统是一款用于测试电池内阻的专业设备，可以测量电池的串联电阻(Rs)、电荷转移电阻(Rct)和瓦伯格阻抗(W)等参数，帮助用户评估电池的性能和质量。

## 2. 用户角色

- **实验室技术人员**：使用研究模式进行深入分析
- **生产线操作员**：使用产线模式进行批量测试

## 3. 功能需求

### 3.1 通用功能

- 支持8通道并行测试
- 自动检测通道连接状态
- 测量电池电压
- 测量电池内阻参数(Rs, Rct, W)
- 支持参数设置和保存
- 支持测试结果保存和导出

### 3.2 研究模式功能

- 支持0.01Hz-7.8kHz频率范围测试
- 支持Nyquist图和Bode图显示
- 支持数据拟合和分析
- 支持异常点分析

### 3.3 产线模式功能

- 支持批量测试
- 支持二维码扫描输入
- 支持测试结果分类
- 支持合格/不合格判断
- 支持测试报告生成

## 4. 技术规格

### 4.1 测量参数

- **频率范围**：0.01Hz-7.8kHz
- **通道数量**：8通道
- **测量精度**：±2mV
- **电压范围**：2V-5V

### 4.2 频率设置

频率设置支持单频点和多频点两种模式：

#### 4.2.1 单频点模式

- 支持选择预设频率：0.1Hz, 1Hz, 10Hz, 100Hz, 1kHz

#### 4.2.2 多频点模式

- **频率范围**：可设置最小频率和最大频率
- **频点数量**：可设置2-100个频点
- **分布方式**：
  - **对数分布**：频点在对数坐标上均匀分布，适合宽频率范围测试
  - **线性分布**：频点在线性坐标上均匀分布，适合窄频率范围测试
  - **十倍频分布**：频点按照十倍频关系分布，如0.1Hz, 1Hz, 10Hz, 100Hz等

### 4.3 测试结果分类

测试结果按照Rs-Rct档位进行分类，例如：

- **1-1**：Rs和Rct均在第1档
- **1-2**：Rs在第1档，Rct在第2档
- **2-1**：Rs在第2档，Rct在第1档
- **2-2**：Rs和Rct均在第2档
- **3-x**或**x-3**：Rs或Rct在第3档，表示不合格

## 5. 用户界面

### 5.1 通用界面要求

- 采用苹果风格设计，简洁美观
- 支持中文界面
- 支持暗色/亮色主题切换

### 5.2 产线模式界面

- 显示8个通道卡片
- 每个通道卡片显示电池二维码、电压、内阻参数和测试结果
- 支持通道自动检测和显示
- 支持测试进度显示

### 5.3 设置界面

- 支持通道启用/禁用设置
- 支持测量参数设置
- 支持阈值设置

## 6. 数据管理

- 支持测试结果本地保存
- 支持测试报告导出
- 支持云端数据同步

## 7. 系统要求

- 支持Windows 10/11操作系统
- 最小屏幕分辨率：1280x800
- 支持USB通信

## 8. 未来扩展

- 支持多语言界面
- 支持移动端应用
- 支持更多电池类型测试
- 支持AI辅助分析
