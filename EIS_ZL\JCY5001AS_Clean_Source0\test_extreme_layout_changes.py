#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试极端布局修改效果
验证电池扫码值框扩展和RS/RCT显示区域压缩的明显效果
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QLineEdit, QGroupBox, QFrame, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.components.channel_display_widget import ChannelDisplayWidget
    from utils.config_manager import ConfigManager
    REAL_COMPONENT_AVAILABLE = True
except ImportError as e:
    print(f"导入真实组件失败: {e}")
    REAL_COMPONENT_AVAILABLE = False

class ExtremeLayoutTest(QMainWindow):
    """极端布局修改测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 极端布局修改测试")
        self.setGeometry(100, 100, 1400, 600)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 极端布局修改测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
        极端修改内容：
        • 左右列权重从 15:35 改为 10:40 (1:4比例)
        • RS/RCT数值框宽度从120-200px压缩到60-75px
        • 电池码输入框宽度从180-300px扩展到200-400px
        • 所有左侧组件字体和宽度大幅压缩
        • RS/RCT标签通过更大弹性空间向右移动
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("color: #7f8c8d; background-color: #fff3cd; padding: 10px; border-radius: 5px; border: 1px solid #ffeaa7;")
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        self._create_test_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
    
    def _create_test_area(self, layout):
        """创建测试区域"""
        test_frame = QFrame()
        test_frame.setStyleSheet("background-color: #f5f5f5; border-radius: 10px; padding: 10px;")
        test_layout = QVBoxLayout(test_frame)
        
        if REAL_COMPONENT_AVAILABLE:
            # 使用真实组件
            try:
                config_manager = ConfigManager()
                
                # 创建通道组件
                channels_layout = QHBoxLayout()
                
                # 创建两个通道进行测试
                for i in range(2):
                    channel_number = i + 1
                    channel = ChannelDisplayWidget(channel_number, config_manager)
                    
                    # 设置测试数据
                    self._set_test_data(channel, channel_number)
                    
                    channels_layout.addWidget(channel)
                
                test_layout.addLayout(channels_layout)
                
            except Exception as e:
                print(f"创建真实通道组件失败: {e}")
                # 回退到演示版本
                demo_layout = self._create_demo_channels()
                test_layout.addLayout(demo_layout)
        else:
            # 使用演示版本
            demo_layout = self._create_demo_channels()
            test_layout.addLayout(demo_layout)
        
        layout.addWidget(test_frame)
    
    def _set_test_data(self, channel, channel_number):
        """设置测试数据"""
        try:
            # 设置超长电池码进行测试
            ultra_long_codes = [
                "BATTERY_CODE_123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_ULTRA_LONG_TEST_001",  # 64字符超长码
                "CELL_BARCODE_987654321ZYXWVUTSRQPONMLKJIHGFEDCBA_EXTREME_LENGTH_002"   # 66字符超长码
            ]
            
            battery_code = ultra_long_codes[channel_number - 1]
            if hasattr(channel, 'battery_code_edit'):
                channel.battery_code_edit.setText(battery_code)
            
            # 设置5位数阻抗值测试
            rs_values = ["12345", "98765"]  # 5位数测试
            rct_values = ["23456", "87654"]  # 5位数测试
            
            if hasattr(channel, 'rs_label'):
                channel.rs_label.setText(rs_values[channel_number - 1])
            if hasattr(channel, 'rct_label'):
                channel.rct_label.setText(rct_values[channel_number - 1])
            
            # 设置其他测试数据
            if hasattr(channel, 'test_count_label'):
                channel.test_count_label.setText(str(channel_number * 100))  # 3位数测试
            if hasattr(channel, 'test_time_label'):
                channel.test_time_label.setText(f"01:2{channel_number}:45")
            if hasattr(channel, 'voltage_label'):
                channel.voltage_label.setText(f"{3.234 + channel_number * 0.111:.3f}")  # 5位数测试
                
        except Exception as e:
            print(f"设置测试数据失败: {e}")
    
    def _create_demo_channels(self):
        """创建演示通道（当无法加载真实组件时）"""
        demo_layout = QHBoxLayout()
        
        for i in range(2):
            channel_number = i + 1
            demo_channel = self._create_demo_channel(channel_number)
            demo_layout.addWidget(demo_channel)
        
        return demo_layout
    
    def _create_demo_channel(self, channel_number):
        """创建演示通道"""
        # 创建通道组框
        channel_group = QGroupBox(f"通道 {channel_number}")
        channel_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d1d5db;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 12px 0 12px;
                color: #1f2937;
                font-size: 15pt;
                font-weight: 600;
                background-color: #ffffff;
            }
        """)
        
        # 创建内容布局
        content_layout = QVBoxLayout(channel_group)
        content_layout.setContentsMargins(6, 8, 6, 6)
        content_layout.setSpacing(3)
        
        # 创建主内容区域 - 极端权重比例
        main_container = QFrame()
        main_layout = QHBoxLayout(main_container)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(8, 8, 8, 8)
        
        # 左列：基本信息（极度压缩）
        left_column = self._create_demo_left_column(channel_number)
        main_layout.addLayout(left_column, 10)  # 1份权重（极度压缩）
        
        # 右列：阻抗值显示（大幅扩展）
        right_column = self._create_demo_right_column(channel_number)
        main_layout.addLayout(right_column, 40)  # 4份权重（大幅扩展）
        
        content_layout.addWidget(main_container)
        
        return channel_group
    
    def _create_demo_left_column(self, channel_number):
        """创建演示左列（极度压缩版本）"""
        left_column = QVBoxLayout()
        left_column.setSpacing(4)
        
        # 测试计数和时间（极度压缩版本）
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(2)
        
        # 计数
        count_label = QLabel("计数:")
        count_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        count_label.setMaximumWidth(30)
        count_time_layout.addWidget(count_label)
        
        count_value = QLabel(str(channel_number * 100))
        count_value.setStyleSheet("font-size: 10pt; color: #27ae60; font-weight: bold;")
        count_value.setMaximumWidth(35)
        count_time_layout.addWidget(count_value)
        
        # 分隔符
        separator = QLabel("|")
        separator.setStyleSheet("font-size: 8pt; color: #bdc3c7;")
        separator.setMaximumWidth(8)
        count_time_layout.addWidget(separator)
        
        # 时间
        time_label = QLabel("用时:")
        time_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        time_label.setMaximumWidth(30)
        count_time_layout.addWidget(time_label)
        
        time_value = QLabel(f"01:2{channel_number}:45")
        time_value.setStyleSheet("""
            font-size: 9pt; font-weight: bold; color: #3498db;
            background-color: #ebf3fd; border: 1px solid #3498db;
            border-radius: 2px; padding: 1px 2px;
            max-height: 14px; min-width: 50px; max-width: 60px;
        """)
        count_time_layout.addWidget(time_value)
        
        count_time_layout.addStretch()
        left_column.addLayout(count_time_layout)
        
        # 电池码输入（超级扩展版本）
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(2)
        
        battery_label = QLabel("电池:")
        battery_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        battery_label.setMaximumWidth(30)
        battery_layout.addWidget(battery_label)
        
        # 超长电池码测试
        ultra_long_codes = [
            "BATTERY_CODE_123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_ULTRA_LONG_TEST_001",
            "CELL_BARCODE_987654321ZYXWVUTSRQPONMLKJIHGFEDCBA_EXTREME_LENGTH_002"
        ]
        
        battery_edit = QLineEdit(ultra_long_codes[channel_number - 1])
        battery_edit.setStyleSheet("""
            border: 1px solid #bdc3c7; border-radius: 3px;
            padding: 2px 8px; background-color: white;
            font-size: 11pt; max-height: 28px; color: #2c3e50;
            min-height: 26px; min-width: 200px; max-width: 400px;
        """)
        battery_layout.addWidget(battery_edit, 3)  # 给更大权重
        
        left_column.addLayout(battery_layout)
        
        # 电压显示（极度压缩版本）
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(2)
        
        voltage_label = QLabel("电压:")
        voltage_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        voltage_label.setMaximumWidth(30)
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel(f"{3.234 + channel_number * 0.111:.3f}")
        voltage_value.setStyleSheet("""
            font-size: 10pt; font-weight: 600; color: #1f2937;
            background-color: #f9fafb; border: 1px solid #e5e7eb;
            border-radius: 4px; padding: 3px 4px;
            min-width: 35px; max-width: 50px; max-height: 28px;
        """)
        voltage_layout.addWidget(voltage_value)
        
        voltage_layout.addStretch()
        left_column.addLayout(voltage_layout)
        
        left_column.addStretch()
        return left_column
    
    def _create_demo_right_column(self, channel_number):
        """创建演示右列（明显右移版本）"""
        right_column = QVBoxLayout()
        right_column.setSpacing(4)
        
        # RS显示（明显右移 + 压缩版本）
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(6)
        rs_layout.addStretch(3)  # 大幅增加左侧弹性空间，明显右移
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        rs_title.setMinimumWidth(55)
        rs_title.setMaximumWidth(65)
        rs_title.setAlignment(Qt.AlignRight)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("12345" if channel_number == 1 else "98765")  # 5位数测试
        rs_value.setStyleSheet("""
            font-size: 12pt; font-weight: 600; color: #059669;
            background-color: #ecfdf5; border: 1px solid #10b981;
            border-radius: 6px; padding: 4px 6px; margin: 0px;
            min-width: 60px; min-height: 36px; max-width: 75px;
        """)
        rs_value.setAlignment(Qt.AlignLeft)
        rs_layout.addWidget(rs_value)
        
        # 不添加右侧弹性空间
        right_column.addLayout(rs_layout)
        
        # RCT显示（明显右移 + 压缩版本）
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(6)
        rct_layout.addStretch(3)  # 大幅增加左侧弹性空间，明显右移
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        rct_title.setMinimumWidth(55)
        rct_title.setMaximumWidth(65)
        rct_title.setAlignment(Qt.AlignRight)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("23456" if channel_number == 1 else "87654")  # 5位数测试
        rct_value.setStyleSheet("""
            font-size: 12pt; font-weight: 600; color: #1e40af;
            background-color: #eff6ff; border: 1px solid #3b82f6;
            border-radius: 6px; padding: 4px 6px; margin: 0px;
            min-width: 60px; min-height: 36px; max-width: 75px;
        """)
        rct_value.setAlignment(Qt.AlignLeft)
        rct_layout.addWidget(rct_value)
        
        # 不添加右侧弹性空间
        right_column.addLayout(rct_layout)
        
        right_column.addStretch()
        return right_column
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 测试超长电池码按钮
        test_ultra_long_btn = QPushButton("测试超长电池码显示")
        test_ultra_long_btn.clicked.connect(self._test_ultra_long_code)
        test_ultra_long_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(test_ultra_long_btn)
        
        # 测试5位数阻抗值按钮
        test_5digit_btn = QPushButton("测试5位数阻抗值")
        test_5digit_btn.clicked.connect(self._test_5digit_values)
        test_5digit_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        button_layout.addWidget(test_5digit_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _test_ultra_long_code(self):
        """测试超长电池码"""
        print("✅ 超长电池码测试:")
        print("   • 64-66字符的电池码应该能完整显示")
        print("   • 电池码输入框应该明显扩展")
        print("   • 左侧组件应该明显压缩")
    
    def _test_5digit_values(self):
        """测试5位数阻抗值"""
        print("✅ 5位数阻抗值测试:")
        print("   • RS和RCT应该能完整显示5位数字")
        print("   • 数值框宽度应该压缩到60-75px")
        print("   • 标签应该明显向右移动")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = ExtremeLayoutTest()
    window.show()
    
    print("🚀 JCY5001AS 极端布局修改测试启动")
    print("=" * 50)
    print("📋 测试内容:")
    print("   • 左右列权重比例: 1:4 (极端压缩)")
    print("   • 电池码输入框: 200-400px (大幅扩展)")
    print("   • RS/RCT数值框: 60-75px (大幅压缩)")
    print("   • 所有左侧组件: 极度压缩字体和宽度")
    print("   • RS/RCT标签: 通过大弹性空间明显右移")
    print("=" * 50)
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
