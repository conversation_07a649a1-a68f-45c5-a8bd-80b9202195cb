#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A 应用程序功能测试脚本
测试各个核心功能是否能正常工作
"""

import sys
import os
import json
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_functionality():
    """测试配置管理功能"""
    print('\n--- 测试配置管理功能 ---')
    try:
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        
        # 测试读取配置
        app_name = config.get("app.name")
        print(f'应用名称: {app_name}')
        
        # 测试设置配置
        test_key = "test.value"
        test_value = "测试值"
        config.set(test_key, test_value)
        
        # 测试读取刚设置的配置
        retrieved_value = config.get(test_key)
        assert retrieved_value == test_value, f"配置设置失败: 期望 {test_value}, 实际 {retrieved_value}"
        
        print('✅ 配置管理功能正常')
        return True
    except Exception as e:
        print(f'❌ 配置管理功能测试失败: {e}')
        return False

def test_database_functionality():
    """测试数据库功能"""
    print('\n--- 测试数据库功能 ---')
    try:
        from data.database_manager import initialize_database_manager
        db_manager = initialize_database_manager()

        if db_manager is None:
            print('❌ 数据库管理器初始化失败')
            return False

        # 测试数据库信息获取
        db_info = db_manager.get_database_info()
        print(f'数据库路径: {db_info["db_path"]}')
        print(f'数据库大小: {db_info["db_size"]} bytes')

        # 测试表统计
        if 'table_stats' in db_info:
            for table, count in db_info['table_stats'].items():
                print(f'表 {table}: {count} 条记录')

        print('✅ 数据库功能正常')
        return True
    except Exception as e:
        print(f'❌ 数据库功能测试失败: {e}')
        return False

def test_eis_analyzer():
    """测试EIS分析器功能"""
    print('\n--- 测试EIS分析器功能 ---')
    try:
        from backend.eis_analyzer import EISAnalyzer

        # 创建测试数据
        frequencies = [0.01, 0.1, 1.0, 10.0, 100.0, 1000.0, 10000.0]  # Hz
        # 模拟简单的阻抗数据
        real_parts = [3.0, 2.8, 2.5, 2.2, 2.1, 2.05, 2.0]  # mΩ
        imag_parts = [-0.5, -0.3, -0.1, 0.0, 0.1, 0.05, 0.01]  # mΩ

        analyzer = EISAnalyzer()

        # 测试标准Rs和Rct计算
        rs_value, rct_value = analyzer.calculate_rs_rct_standard(frequencies, real_parts, imag_parts)
        print(f'Rs计算结果: {rs_value:.3f} mΩ')
        print(f'Rct计算结果: {rct_value:.3f} mΩ')

        # 测试数据验证
        validation_result = analyzer.validate_eis_data(frequencies, real_parts, imag_parts)
        print(f'数据验证: {validation_result["data_quality"]}')

        print('✅ EIS分析器功能正常')
        return True
    except Exception as e:
        print(f'❌ EIS分析器功能测试失败: {e}')
        return False

def test_communication_manager():
    """测试通信管理器功能"""
    print('\n--- 测试通信管理器功能 ---')
    try:
        from backend.communication_manager import CommunicationManager
        from utils.config_manager import ConfigManager
        import serial.tools.list_ports

        config = ConfigManager()
        comm_manager = CommunicationManager(config)

        # 测试串口列表获取（使用serial库直接获取）
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append(port.device)
        print(f'可用串口: {ports}')

        # 测试连接信息获取
        conn_info = comm_manager.get_connection_info()
        print(f'连接信息: {conn_info}')

        # 测试连接状态
        print(f'连接状态: {"已连接" if comm_manager.is_connected else "未连接"}')

        print('✅ 通信管理器功能正常')
        return True
    except Exception as e:
        print(f'❌ 通信管理器功能测试失败: {e}')
        return False

def test_license_manager():
    """测试授权管理器功能"""
    print('\n--- 测试授权管理器功能 ---')
    try:
        from utils.license_manager import LicenseManager
        from utils.config_manager import ConfigManager
        
        config = ConfigManager()
        license_manager = LicenseManager(config)
        
        # 测试授权状态获取
        status = license_manager.get_license_status()
        print(f'授权状态: {status}')
        
        # 测试硬件指纹获取
        fingerprint = license_manager.get_hardware_fingerprint()
        print(f'硬件指纹: {fingerprint[:20]}...')
        
        print('✅ 授权管理器功能正常')
        return True
    except Exception as e:
        print(f'❌ 授权管理器功能测试失败: {e}')
        return False

def main():
    """主测试函数"""
    print('=' * 60)
    print('JCY5001A 应用程序功能测试')
    print('=' * 60)
    
    test_functions = [
        test_config_functionality,
        test_database_functionality,
        test_eis_analyzer,
        test_communication_manager,
        test_license_manager
    ]
    
    results = []
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f'❌ 测试函数 {test_func.__name__} 执行失败: {e}')
            results.append(False)
    
    print('\n' + '=' * 60)
    print('功能测试结果汇总:')
    print('=' * 60)
    
    success_count = sum(results)
    total_count = len(results)
    
    test_names = [
        '配置管理功能',
        '数据库功能', 
        'EIS分析器功能',
        '通信管理器功能',
        '授权管理器功能'
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = '✅ 通过' if result else '❌ 失败'
        print(f'{name}: {status}')
    
    print(f'\n总计: {success_count}/{total_count} 个功能测试通过')
    
    if success_count == total_count:
        print('🎉 所有功能测试通过！应用程序功能正常，可以进行打包。')
        return True
    else:
        print('⚠️ 部分功能测试失败，建议修复后再进行打包。')
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
