"""
打印机管理器模块

负责检测打印机连接状态并通知主界面更新状态栏显示
"""

import logging
import win32print
from datetime import datetime
from typing import Dict, Any, List
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

logger = logging.getLogger(__name__)


class PrinterManager(QObject):
    """打印机管理器"""
    
    # 信号定义
    printer_status_changed = pyqtSignal(bool)  # 打印机状态变更信号
    
    def __init__(self, config_manager, parent=None):
        """
        初始化打印机管理器
        
        Args:
            config_manager: 配置管理器
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config_manager = config_manager
        self.current_printer_connected = False
        self.last_printer_name = ""
        self.last_status_check = None
        
        # 初始化定时器
        self._init_timer()
        
        # 执行初始检测
        self._check_printer_status()
        
        logger.info("打印机管理器初始化完成")
    
    def _init_timer(self):
        """初始化状态检测定时器"""
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._check_printer_status)
        
        # 每5秒检测一次打印机状态
        check_interval = self.config_manager.get('printer.status_check_interval', 5000)
        self.status_timer.start(check_interval)
        
        logger.debug(f"打印机状态检测定时器已启动，间隔: {check_interval}ms")
    
    def _check_printer_status(self):
        """检测打印机状态"""
        try:
            # 更新检查时间
            self.last_status_check = datetime.now()

            # 获取配置的打印机名称
            configured_printer = self.config_manager.get('printer.name', '')
            logger.debug(f"配置的打印机名称: '{configured_printer}'")

            if not configured_printer:
                # 没有配置打印机，尝试自动发现NIIMBOT打印机
                logger.debug("未配置打印机，尝试自动发现NIIMBOT打印机")
                connected = self._auto_discover_printer()
            else:
                # 检查指定的打印机
                logger.debug(f"检查指定打印机: {configured_printer}")
                connected = self._check_specific_printer(configured_printer)
                if connected:
                    self.last_printer_name = configured_printer
                else:
                    # 如果配置的打印机不可用，尝试自动发现
                    logger.warning(f"配置的打印机 {configured_printer} 不可用，尝试自动发现")
                    connected = self._auto_discover_printer()

            # 如果状态发生变化，发送信号
            if connected != self.current_printer_connected:
                self.current_printer_connected = connected
                self.printer_status_changed.emit(connected)

                status_text = "已连接" if connected else "未连接"
                logger.info(f"打印机状态变更: {status_text}")
            else:
                # 即使状态没有变化，也记录当前状态（用于调试）
                status_text = "已连接" if connected else "未连接"
                logger.debug(f"打印机状态保持: {status_text}")

        except Exception as e:
            logger.error(f"检测打印机状态失败: {e}")

            # 出现异常时认为打印机未连接
            if self.current_printer_connected:
                self.current_printer_connected = False
                self.printer_status_changed.emit(False)
    
    def _check_default_printer(self) -> bool:
        """检查默认打印机是否可用"""
        try:
            # 获取默认打印机
            default_printer = win32print.GetDefaultPrinter()
            
            if default_printer:
                return self._check_printer_availability(default_printer)
            else:
                logger.debug("未找到默认打印机")
                return False
                
        except Exception as e:
            logger.debug(f"获取默认打印机失败: {e}")
            return False
    
    def _check_specific_printer(self, printer_name: str) -> bool:
        """检查指定打印机是否可用"""
        try:
            return self._check_printer_availability(printer_name)
        except Exception as e:
            logger.debug(f"检查打印机 {printer_name} 失败: {e}")
            return False
    
    def _check_printer_availability(self, printer_name: str) -> bool:
        """检查打印机是否可用"""
        try:
            # 尝试打开打印机
            handle = win32print.OpenPrinter(printer_name)

            # 获取打印机状态
            printer_info = win32print.GetPrinter(handle, 2)
            win32print.ClosePrinter(handle)

            # 检查打印机状态
            status = printer_info.get('Status', 0)
            attributes = printer_info.get('Attributes', 0)

            # 针对NIIMBOT K3_W打印机的特殊处理
            if "NIIMBOT" in printer_name.upper() or "K3_W" in printer_name.upper():
                # NIIMBOT打印机的可用性检查
                is_available = self._check_niimbot_availability(printer_name, status, attributes)
            else:
                # 通用打印机可用性检查
                is_available = (
                    status == 0 or  # 正常状态
                    (status & 0x00000001) == 0  # 不是离线状态
                )

            if is_available:
                logger.debug(f"打印机 {printer_name} 可用，状态: {status}")
            else:
                logger.debug(f"打印机 {printer_name} 不可用，状态: {status}")

            return is_available

        except Exception as e:
            logger.debug(f"检查打印机 {printer_name} 可用性失败: {e}")
            return False

    def _auto_discover_printer(self) -> bool:
        """自动发现可用的打印机，优先选择NIIMBOT"""
        try:
            logger.debug("开始自动发现打印机...")

            # 获取所有打印机
            printer_list = win32print.EnumPrinters(
                win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS
            )

            niimbot_printers = []
            other_printers = []

            for printer_info in printer_list:
                printer_name = printer_info[2]

                try:
                    if self._check_printer_availability(printer_name):
                        if 'NIIMBOT' in printer_name.upper() or 'K3_W' in printer_name.upper():
                            niimbot_printers.append(printer_name)
                            logger.debug(f"发现可用的NIIMBOT打印机: {printer_name}")
                        else:
                            other_printers.append(printer_name)
                            logger.debug(f"发现可用的打印机: {printer_name}")
                except Exception as e:
                    logger.debug(f"检查打印机 {printer_name} 失败: {e}")
                    continue

            # 优先选择NIIMBOT打印机
            if niimbot_printers:
                selected_printer = niimbot_printers[0]
                logger.info(f"自动选择NIIMBOT打印机: {selected_printer}")

                # 更新配置
                self.config_manager.set('printer.name', selected_printer)
                self.last_printer_name = selected_printer
                return True

            elif other_printers:
                selected_printer = other_printers[0]
                logger.info(f"自动选择打印机: {selected_printer}")

                # 更新配置
                self.config_manager.set('printer.name', selected_printer)
                self.last_printer_name = selected_printer
                return True

            else:
                logger.warning("未发现任何可用的打印机")
                return False

        except Exception as e:
            logger.error(f"自动发现打印机失败: {e}")
            return False

    def _check_niimbot_availability(self, printer_name: str, status: int, attributes: int) -> bool:
        """检查NIIMBOT打印机的可用性"""
        try:
            # NIIMBOT打印机的状态检查逻辑
            # 状态码参考：
            # 0x00000000 = 正常
            # 0x00000001 = 离线
            # 0x00000002 = 纸张用完
            # 0x00000004 = 纸张卡住
            # 0x00000008 = 门打开
            # 0x00000010 = 错误
            # 0x00000020 = 手动进纸
            # 0x00000040 = 缺纸
            # 0x00000080 = 输出满
            # 0x00000100 = 页面错误
            # 0x00000200 = 用户干预
            # 0x00000400 = 内存不足
            # 0x00000800 = 服务器未知

            # 对于NIIMBOT，我们认为以下状态是可用的：
            # 1. 状态为0（完全正常）
            # 2. 只有手动进纸状态（0x00000020）
            # 3. 只有缺纸状态（0x00000040）- 打印机在线但缺纸

            if status == 0:
                # 完全正常状态
                return True
            elif status == 0x00000020:
                # 手动进纸状态，打印机在线
                logger.debug(f"NIIMBOT打印机 {printer_name} 处于手动进纸状态，但可用")
                return True
            elif status == 0x00000040:
                # 缺纸状态，打印机在线但缺纸
                logger.debug(f"NIIMBOT打印机 {printer_name} 缺纸，但打印机在线")
                return True
            else:
                # 其他状态认为不可用
                logger.debug(f"NIIMBOT打印机 {printer_name} 状态异常: 0x{status:08X}")
                return False

        except Exception as e:
            logger.error(f"检查NIIMBOT打印机 {printer_name} 可用性失败: {e}")
            return False
    
    def get_current_status(self) -> bool:
        """获取当前打印机连接状态"""
        return self.current_printer_connected
    
    def refresh_status(self):
        """手动刷新打印机状态"""
        logger.info("手动刷新打印机状态")
        self._check_printer_status()

    def force_emit_current_status(self):
        """强制发送当前状态信号（用于UI同步）"""
        try:
            logger.debug(f"强制发送打印机状态信号: {'已连接' if self.current_printer_connected else '未连接'}")
            self.printer_status_changed.emit(self.current_printer_connected)
        except Exception as e:
            logger.error(f"强制发送打印机状态信号失败: {e}")
    
    def set_check_interval(self, interval_ms: int):
        """设置状态检测间隔"""
        if interval_ms > 0:
            self.status_timer.setInterval(interval_ms)
            logger.info(f"打印机状态检测间隔已设置为: {interval_ms}ms")
        else:
            logger.warning("无效的检测间隔，必须大于0")
    
    def start_monitoring(self):
        """开始监控打印机状态"""
        if not self.status_timer.isActive():
            self.status_timer.start()
            logger.info("打印机状态监控已启动")
    
    def stop_monitoring(self):
        """停止监控打印机状态"""
        if self.status_timer.isActive():
            self.status_timer.stop()
            logger.info("打印机状态监控已停止")
    
    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, 'status_timer') and self.status_timer:
                self.status_timer.stop()
        except:
            pass

    def is_printer_ready(self) -> bool:
        """检查打印机是否就绪"""
        try:
            return self.current_printer_connected and bool(self.last_printer_name)
        except Exception as e:
            logger.error(f"检查打印机就绪状态失败: {e}")
            return False

    def get_printer_status(self) -> Dict[str, Any]:
        """获取打印机状态信息"""
        try:
            return {
                'connected': self.current_printer_connected,
                'name': self.last_printer_name,
                'type': self.config_manager.get('printer.type', ''),
                'quality': self.config_manager.get('printer.quality', '草稿'),
                'last_check': self.last_status_check.isoformat() if self.last_status_check else None
            }
        except Exception as e:
            logger.error(f"获取打印机状态失败: {e}")
            return {
                'connected': False,
                'name': '',
                'type': '',
                'quality': '',
                'last_check': None
            }

    def get_available_printers(self) -> List[str]:
        """获取可用打印机列表"""
        try:
            printers = []
            printer_list = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)

            for printer_info in printer_list:
                printer_name = printer_info[2]  # 打印机名称
                if self._check_printer_availability(printer_name):
                    printers.append(printer_name)

            return printers

        except Exception as e:
            logger.error(f"获取可用打印机列表失败: {e}")
            return []

    def get_printer_details(self) -> Dict[str, Any]:
        """获取打印机详细信息"""
        try:
            result = {
                'configured_printer': self.config_manager.get('printer.name', ''),
                'current_status': self.current_printer_connected,
                'last_check': self.last_status_check.isoformat() if self.last_status_check else None,
                'available_printers': [],
                'niimbot_printers': []
            }

            # 获取所有打印机信息
            printer_list = win32print.EnumPrinters(
                win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS
            )

            for printer_info in printer_list:
                printer_name = printer_info[2]

                try:
                    handle = win32print.OpenPrinter(printer_name)
                    printer_details = win32print.GetPrinter(handle, 2)
                    win32print.ClosePrinter(handle)

                    is_available = self._check_printer_availability(printer_name)
                    is_niimbot = 'NIIMBOT' in printer_name.upper() or 'K3_W' in printer_name.upper()

                    printer_info_dict = {
                        'name': printer_name,
                        'status': printer_details.get('Status', 0),
                        'driver': printer_details.get('pDriverName', 'Unknown'),
                        'port': printer_details.get('pPortName', 'Unknown'),
                        'is_available': is_available,
                        'is_niimbot': is_niimbot
                    }

                    if is_available:
                        result['available_printers'].append(printer_info_dict)

                    if is_niimbot:
                        result['niimbot_printers'].append(printer_info_dict)

                except Exception as e:
                    logger.debug(f"获取打印机 {printer_name} 详细信息失败: {e}")
                    continue

            return result

        except Exception as e:
            logger.error(f"获取打印机详细信息失败: {e}")
            return {
                'configured_printer': '',
                'current_status': False,
                'available_printers': [],
                'niimbot_printers': []
            }

    def update_printer_config(self, printer_name: str) -> bool:
        """
        更新打印机配置

        Args:
            printer_name: 新的打印机名称

        Returns:
            是否更新成功
        """
        try:
            # 验证打印机是否可用
            if not self._check_printer_availability(printer_name):
                logger.error(f"打印机 {printer_name} 不可用，无法配置")
                return False

            # 更新配置
            old_printer = self.config_manager.get('printer.name', '')
            self.config_manager.set('printer.name', printer_name)

            # 更新内部状态
            self.last_printer_name = printer_name

            # 立即检查新打印机状态
            self._check_printer_status()

            # 🔧 新增：强制发送状态变更信号，确保UI同步
            self.force_emit_current_status()

            logger.info(f"✅ 打印机配置已更新: {old_printer} -> {printer_name}")
            return True

        except Exception as e:
            logger.error(f"更新打印机配置失败: {e}")
            return False
