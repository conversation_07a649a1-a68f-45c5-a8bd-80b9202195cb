#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS RS和RCT最终修复测试
1. 进一步缩短"0.000"数值框宽度至真正刚好显示4个字符
2. RS和RCT标签向右移动
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.components.channel_display_widget import ChannelDisplayWidget

class TestRsRctFinalFixWindow(QMainWindow):
    """RS和RCT最终修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS RS和RCT最终修复测试")
        self.setGeometry(100, 100, 1400, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS RS和RCT最终修复测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
🎯 修复目标：

1. ✅ 进一步缩短"0.000"数值框宽度
   - 从32px进一步缩短至24px
   - 真正做到刚好显示"0.000"四个字符
   - 完全去除右侧多余空白区域
   - 减少内边距从4px到3px，更紧凑显示

2. ✅ RS和RCT标签向右移动
   - 增加左侧弹性空间权重从3到5
   - 标签宽度从30px增加到35px，确保完整显示
   - 最大宽度从35px增加到40px
   - 保持右对齐，向中心靠拢

🔧 技术实现：
• 数值框宽度：32px → 24px（进一步缩短25%）
• 内边距：4px → 3px（更紧凑）
• 左侧弹性空间：权重3 → 权重5（更大幅度右移）
• 标签宽度：30-35px → 35-40px（确保完整显示）

✅ 预期效果：
• "0.000"数值框明显更窄，真正刚好适配内容
• 完全去除数值框右侧的空白区域
• RS(mΩ)和Rct(mΩ)标签明显向右移动
• 整体布局更紧凑，为电池扫码框释放更多空间
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 创建测试区域 - 显示多个通道进行对比
        test_layout = QHBoxLayout()
        
        # 通道1
        channel1_frame = QFrame()
        channel1_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #3498db;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
        """)
        channel1_layout = QVBoxLayout(channel1_frame)
        
        channel1_title = QLabel("通道1 - 修复后效果")
        channel1_title.setAlignment(Qt.AlignCenter)
        channel1_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #3498db; padding: 5px;")
        channel1_layout.addWidget(channel1_title)
        
        self.channel1_widget = ChannelDisplayWidget(channel_id=1)
        channel1_layout.addWidget(self.channel1_widget)
        
        test_layout.addWidget(channel1_frame)
        
        # 通道2
        channel2_frame = QFrame()
        channel2_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
        """)
        channel2_layout = QVBoxLayout(channel2_frame)
        
        channel2_title = QLabel("通道2 - 修复后效果")
        channel2_title.setAlignment(Qt.AlignCenter)
        channel2_title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #e74c3c; padding: 5px;")
        channel2_layout.addWidget(channel2_title)
        
        self.channel2_widget = ChannelDisplayWidget(channel_id=2)
        channel2_layout.addWidget(self.channel2_widget)
        
        test_layout.addWidget(channel2_frame)
        
        main_layout.addLayout(test_layout)
        
        # 添加测试数据
        self.load_test_data()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 通道1测试数据
            self.channel1_widget.update_test_data({
                'voltage': 3.456,
                'rs_value': 12.34,  # 4位数测试
                'rct_value': 56.78,  # 4位数测试
                'battery_code': 'TEST123456789ABCDEFGHIJK',
                'test_count': 25,
                'test_time': '15:30:45'
            })
            
            # 通道2测试数据
            self.channel2_widget.update_test_data({
                'voltage': 3.789,
                'rs_value': 98.76,  # 4位数测试
                'rct_value': 54.32,  # 4位数测试
                'battery_code': 'SAMPLE987654321ZYXWVUTS',
                'test_count': 42,
                'test_time': '16:45:12'
            })
            
            print("✅ 测试数据加载完成")
            print("🔋 通道1: 电压=3.456V, Rs=12.34mΩ, Rct=56.78mΩ")
            print("🔋 通道2: 电压=3.789V, Rs=98.76mΩ, Rct=54.32mΩ")
            print("🎯 重点观察:")
            print("   1. Rs和Rct后面的数值框是否进一步变窄（24px）")
            print("   2. 数值框是否真正刚好适配'0.000'四个字符")
            print("   3. Rs(mΩ)和Rct(mΩ)标签是否明显向右移动")
            print("   4. 是否完全去除了数值框右侧的空白区域")
            print("   5. 电池扫码框是否获得了更多显示空间")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("JCY5001AS RS和RCT最终修复测试")
    app.setApplicationVersion("1.0")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = TestRsRctFinalFixWindow()
    window.show()
    
    print("🚀 JCY5001AS RS和RCT最终修复测试启动")
    print("📝 测试重点:")
    print("   1. 数值框是否进一步缩短至真正刚好显示4个字符")
    print("   2. RS和RCT标签是否明显向右移动")
    print("   3. 是否完全去除多余空白区域")
    print("   4. 整体布局是否更加紧凑合理")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
