# -*- coding: utf-8 -*-
"""
设置对话框框架
提供6个选项卡的设置界面

Author: Jack
Date: 2025-01-27
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QPushButton, QMessageBox, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QKeyEvent
import logging

logger = logging.getLogger(__name__)

from utils.config_manager import ConfigManager


class SettingsDialog(QDialog):
    """设置对话框主框架"""

    # 信号定义
    settings_applied = pyqtSignal()  # 设置应用信号
    settings_saved = pyqtSignal()   # 设置保存信号
    device_config_changed = pyqtSignal(dict)  # 设备配置变更信号

    def __init__(self, config_manager: ConfigManager, parent=None, comm_manager=None):
        """
        初始化设置对话框

        Args:
            config_manager: 配置管理器
            parent: 父窗口
            comm_manager: 通信管理器（用于设备配置下发）
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.comm_manager = comm_manager
        self.original_config = {}  # 保存原始配置
        self.has_changes = False   # 是否有未保存的更改

        # 设备配置相关参数
        self.device_config_keys = [
            'test_params.gain',
            'test_params.average_times',
            'test_params.resistance_range'
        ]

        # 初始化界面
        self._init_ui()
        self._init_connections()
        self._load_settings()

        logger.debug("设置对话框初始化完成")

    def _init_ui(self):
        """初始化用户界面"""
        # 设置对话框属性
        self.setWindowTitle("设置")  # 修改窗体标题为"设置"
        self.setWindowIcon(QIcon("resources/icons/settings.png"))
        self.setModal(True)

        # 获取主窗体的高度并设置相同高度，实现居中显示
        parent_widget = self.parent()
        if parent_widget and hasattr(parent_widget, 'height'):
            parent_height = parent_widget.height()
            parent_width = parent_widget.width()
            parent_pos = parent_widget.pos()

            # 设置与主窗体相同的高度，宽度稍小一些
            dialog_width = int(parent_width * 0.9)
            dialog_height = parent_height

            self.resize(dialog_width, dialog_height)

            # 计算居中位置
            center_x = parent_pos.x() + (parent_width - dialog_width) // 2
            center_y = parent_pos.y()

            self.move(center_x, center_y)
        else:
            # 如果没有父窗体，使用默认设置
            self.showMaximized()

        # 设置最小尺寸（当用户退出全屏时的备用尺寸）
        self.setMinimumSize(1200, 850)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建选项卡容器
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        main_layout.addWidget(self.tab_widget)

        # 创建各个设置页面
        self._create_setting_tabs()

        # 创建按钮区域
        button_layout = self._create_button_area()
        main_layout.addLayout(button_layout)

        # 应用样式
        self._apply_styles()

    def _create_setting_tabs(self):
        """创建设置选项卡"""
        # 导入各个设置页面（延迟导入避免循环依赖）
        from .parameter_config_widget import ParameterConfigWidget
        from .frequency_settings_widget import FrequencySettingsWidget
        from .product_info_widget import ProductInfoWidget
        from .test_config_widget import TestConfigWidget
        from .device_settings_widget import DeviceSettingsWidget
        from .grade_settings_widget import GradeSettingsWidget
        from .channel_enable_widget import ChannelEnableWidget

        from .storage_management_widget import StorageManagementWidget  # 🔧 新增
        # from .capacity_prediction_widget import CapacityPredictionWidget  # 🔋 已移除容量预测
        # from .soh_settings_widget import SOHSettingsWidget  # 🔋 已移除SOH设置

        from .about_widget import AboutWidget

        # 1. 通道配置页面（传递通信管理器以支持回读功能）
        self.parameter_config = ParameterConfigWidget(self.config_manager, self, self.comm_manager)
        self.tab_widget.addTab(self.parameter_config, "⚙️ 通道配置")

        # 2. 判断页面（原档位设置页面）
        self.grade_settings = GradeSettingsWidget(self.config_manager)
        self.tab_widget.addTab(self.grade_settings, "⚖️ 判断页面")

        # 3. 频率设置页面
        self.frequency_settings = FrequencySettingsWidget(self.config_manager)
        self.tab_widget.addTab(self.frequency_settings, "📡 频率设置")

        # 4. 产品信息页面
        self.product_info = ProductInfoWidget(self.config_manager)
        self.tab_widget.addTab(self.product_info, "📋 产品信息")

        # 5. 测试配置页面
        self.test_config = TestConfigWidget(self.config_manager)
        self.tab_widget.addTab(self.test_config, "🔄 测试配置")

        # 6. 通道使能页面（启用）
        self.channel_enable = ChannelEnableWidget(self.config_manager)
        channel_enable_index = self.tab_widget.addTab(self.channel_enable, "📋 通道使能")
        self.tab_widget.setTabEnabled(channel_enable_index, True)  # 🔧 修复：启用通道使能功能

        # 7. 学习功能页面（已移除）
        # self.learning_widget = LearningWidget(self.config_manager)
        # learning_index = self.tab_widget.addTab(self.learning_widget, "🧠 学习功能")
        # self.tab_widget.setTabEnabled(learning_index, True)  # 启用学习功能

        # 8. 设备设置页面
        self.device_settings = DeviceSettingsWidget(self.config_manager)
        self.tab_widget.addTab(self.device_settings, "🖥️ 设备设置")

        # 9. 🔧 新增：存储管理页面
        self.storage_management = StorageManagementWidget(self.config_manager)
        self.tab_widget.addTab(self.storage_management, "💾 存储管理")

        # 10. 🔋 容量预测页面（已移除）
        # self.capacity_prediction = CapacityPredictionWidget(self.config_manager)
        # self.tab_widget.addTab(self.capacity_prediction, "🔋 容量预测")

        # 11. 🔋 SOH设置页面（已移除）
        # self.soh_settings = SOHSettingsWidget(self.config_manager)
        # self.tab_widget.addTab(self.soh_settings, "🔋 SOH设置")

        # 12. 关于页面
        self.about_widget = AboutWidget(self.config_manager)
        self.tab_widget.addTab(self.about_widget, "ℹ️ 关于")

        # 连接各页面的变更信号
        self._connect_page_signals()

    def _create_button_area(self) -> QHBoxLayout:
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 添加弹性空间
        button_layout.addStretch()

        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.setMinimumWidth(80)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMinimumWidth(80)
        button_layout.addWidget(self.cancel_button)

        # 应用按钮
        self.apply_button = QPushButton("应用")
        self.apply_button.setMinimumWidth(80)
        self.apply_button.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.apply_button)

        return button_layout

    def _connect_page_signals(self):
        """连接各页面的信号"""
        # 连接各页面的变更信号
        pages = [
            self.parameter_config,
            self.grade_settings,
            self.frequency_settings,
            self.product_info,
            self.test_config,
            self.channel_enable,
            self.device_settings,
            self.storage_management,  # 🔧 新增
            # self.capacity_prediction,  # 🔋 已移除容量预测
            # self.soh_settings  # 🔋 已移除SOH设置
        ]

        for page in pages:
            if hasattr(page, 'settings_changed'):
                page.settings_changed.connect(self._on_settings_changed)



    def _init_connections(self):
        """初始化信号连接"""
        # 按钮信号
        self.ok_button.clicked.connect(self._on_ok_clicked)
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        self.apply_button.clicked.connect(self._on_apply_clicked)

        # 选项卡变更信号
        self.tab_widget.currentChanged.connect(self._on_tab_changed)

    def _apply_styles(self):
        """应用样式表"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }

            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: white;
                border-radius: 4px;
            }

            QTabWidget::tab-bar {
                alignment: left;
            }

            QTabBar::tab {
                background-color: #e0e0e0;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 16px;
                margin-right: 2px;
                min-width: 100px;
            }

            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }

            QTabBar::tab:hover {
                background-color: #f0f0f0;
            }

            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #1976D2;
            }

            QPushButton:pressed {
                background-color: #0D47A1;
            }

            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }

            QPushButton#cancel_button {
                background-color: #757575;
            }

            QPushButton#cancel_button:hover {
                background-color: #616161;
            }
        """)

        # 设置按钮对象名称用于样式
        self.cancel_button.setObjectName("cancel_button")

    def _load_settings(self):
        """加载设置"""
        try:
            # 保存原始配置的副本
            self.original_config = self.config_manager.config.copy()

            # 通知各页面加载设置
            pages = [
                self.parameter_config,
                self.grade_settings,
                self.frequency_settings,
                self.product_info,
                self.test_config,
                self.channel_enable,
                # self.learning_widget,  # 已移除学习功能
                self.device_settings,
                self.storage_management,  # 🔧 新增
                # self.capacity_prediction,  # 🔋 已移除容量预测
                # self.soh_settings,  # 🔋 已移除SOH设置
                self.about_widget
            ]

            for page in pages:
                if hasattr(page, 'load_settings'):
                    page.load_settings()

            logger.debug("设置加载完成")

        except Exception as e:
            logger.error(f"加载设置失败: {e}")
            QMessageBox.warning(self, "警告", f"加载设置失败: {e}")

    def _save_settings(self) -> bool:
        """
        保存设置

        修复设备配置同步问题：检测设备配置变动并下发到设备

        Returns:
            是否保存成功
        """
        try:
            # 验证所有页面的设置
            if not self._validate_all_settings():
                return False

            # 检测设备配置变动
            device_config_changed = self._check_device_config_changes()

            # 应用所有页面的设置
            pages = [
                self.parameter_config,
                self.grade_settings,
                self.frequency_settings,
                self.product_info,
                self.test_config,
                self.channel_enable,
                # self.learning_widget,  # 🔧 已移除学习功能页面
                self.device_settings,
                self.storage_management,  # 🔧 新增
                # self.capacity_prediction,  # 🔋 已移除容量预测
                # self.soh_settings  # 🔋 已移除SOH设置
            ]

            for page in pages:
                if hasattr(page, 'apply_settings'):
                    page.apply_settings()

            # 如果设备配置有变动，下发到设备
            if device_config_changed and self.comm_manager:
                self._apply_device_config_changes()

            # 保存配置到文件
            self.config_manager.save_config()

            # 发送配置变更信号到主界面
            self._emit_config_changes()

            # 重置变更标志
            self.has_changes = False
            self.apply_button.setEnabled(False)

            # 发送信号
            self.settings_applied.emit()
            self.settings_saved.emit()

            # 如果有设备配置变动，发送变更信号
            if device_config_changed:
                self.device_config_changed.emit(device_config_changed)

            logger.info("设置保存成功")
            return True

        except Exception as e:
            logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
            return False

    def _validate_all_settings(self) -> bool:
        """
        验证所有页面的设置

        Returns:
            是否验证通过
        """
        pages = [
            ("通道配置", self.parameter_config),
            ("判断页面", self.grade_settings),
            ("频率设置", self.frequency_settings),
            ("产品信息", self.product_info),
            ("测试配置", self.test_config),
            ("通道使能", self.channel_enable),
            ("设备设置", self.device_settings)
        ]

        logger.debug("开始验证所有页面设置...")

        # 注释掉强制重新加载，避免覆盖用户修改
        # if hasattr(self.product_info, 'force_reload_settings'):
        #     logger.debug("强制重新加载产品信息设置...")
        #     self.product_info.force_reload_settings()

        for page_name, page in pages:
            if hasattr(page, 'validate_settings'):
                logger.debug(f"验证 {page_name} 页面...")
                try:
                    if not page.validate_settings():
                        logger.warning(f"{page_name}页面验证失败")
                        # 不在这里显示消息框，让具体页面处理错误显示
                        return False
                    else:
                        logger.debug(f"{page_name}页面验证通过")
                except Exception as e:
                    logger.error(f"{page_name}页面验证时发生异常: {e}")
                    QMessageBox.critical(
                        self, "验证错误",
                        f"{page_name}页面验证时发生错误: {e}"
                    )
                    return False
            else:
                logger.debug(f"{page_name}页面没有验证方法，跳过")

        logger.debug("所有页面验证通过")
        return True

    def _restore_settings(self):
        """恢复原始设置"""
        try:
            # 恢复配置管理器的配置
            for key, value in self.original_config.items():
                self.config_manager.set(key, value)

            # 重新加载各页面的设置
            self._load_settings()

            logger.debug("设置已恢复")

        except Exception as e:
            logger.error(f"恢复设置失败: {e}")

    def _on_settings_changed(self):
        """设置变更处理"""
        self.has_changes = True
        self.apply_button.setEnabled(True)

    def _on_ok_clicked(self):
        """确定按钮点击处理"""
        if self._save_settings():
            # 确保设备状态同步到主界面
            self._sync_device_status_to_main_window()
            self.accept()

    def _on_cancel_clicked(self):
        """取消按钮点击处理"""
        if self.has_changes:
            reply = QMessageBox.question(
                self, "确认取消",
                "有未保存的更改，确定要取消吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self._restore_settings()
                self.reject()
        else:
            self.reject()

    def _on_apply_clicked(self):
        """应用按钮点击处理"""
        self._save_settings()

    def _on_tab_changed(self, index: int):
        """选项卡变更处理"""
        current_widget = self.tab_widget.widget(index)
        if hasattr(current_widget, 'on_tab_activated'):
            current_widget.on_tab_activated()



    def keyPressEvent(self, event: QKeyEvent):
        """
        重写键盘事件处理，防止回车键意外关闭对话框

        Args:
            event: 键盘事件
        """
        try:
            # 获取按键代码
            key = event.key()

            # 如果是回车键，检查焦点控件
            if key in (0x01000004, 0x01000005):  # Qt.Key_Return, Qt.Key_Enter
                focused_widget = self.focusWidget()

                # 如果焦点在SafeLineEdit上，让它自己处理
                if focused_widget and hasattr(focused_widget, '__class__'):
                    class_name = focused_widget.__class__.__name__
                    if 'SafeLineEdit' in class_name:
                        logger.debug("设置对话框: 回车键事件交给SafeLineEdit处理")
                        focused_widget.keyPressEvent(event)
                        return

                # 如果焦点在按钮上，正常处理
                if isinstance(focused_widget, QPushButton):
                    logger.debug("设置对话框: 回车键触发按钮点击")
                    super().keyPressEvent(event)
                    return

                # 其他情况下，不处理回车键，防止意外关闭对话框
                logger.debug("设置对话框: 拦截回车键事件，防止意外关闭")
                return

            # 其他键正常处理
            super().keyPressEvent(event)

        except Exception as e:
            logger.error(f"设置对话框键盘事件处理失败: {e}")
            # 发生异常时也要调用父类方法
            try:
                super().keyPressEvent(event)
            except Exception as fallback_error:
                logger.error(f"设置对话框键盘事件回退处理也失败: {fallback_error}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.has_changes:
            reply = QMessageBox.question(
                self, "确认关闭",
                "有未保存的更改，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self._restore_settings()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def get_current_tab_name(self) -> str:
        """
        获取当前选项卡名称

        Returns:
            当前选项卡名称
        """
        current_index = self.tab_widget.currentIndex()
        return self.tab_widget.tabText(current_index)

    def switch_to_tab(self, tab_name: str):
        """
        切换到指定选项卡

        Args:
            tab_name: 选项卡名称
        """
        for i in range(self.tab_widget.count()):
            if tab_name in self.tab_widget.tabText(i):
                self.tab_widget.setCurrentIndex(i)

                # 如果切换到设备设置页面，同步连接状态
                if tab_name == "设备设置":
                    self._sync_device_connection_status()
                break

    def _sync_device_connection_status(self):
        """同步设备连接状态到设备设置页面"""
        try:
            # 尝试获取主窗口的设备连接管理器
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'device_connection_manager'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'device_connection_manager'):
                device_manager = main_window.device_connection_manager
                device_manager.sync_status_to_settings(self.device_settings)
                logger.debug("已同步设备连接状态到设置页面")
            else:
                logger.warning("无法找到主窗口的设备连接管理器")

        except Exception as e:
            logger.error(f"同步设备连接状态失败: {e}")

    def _sync_device_status_to_main_window(self):
        """同步设备状态到主界面"""
        try:
            # 获取主窗口
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'device_connection_manager'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'device_connection_manager'):
                device_manager = main_window.device_connection_manager

                # 获取设备设置页面的连接配置
                if hasattr(self, 'device_settings') and self.device_settings:
                    # 应用设备设置
                    self.device_settings.apply_settings()

                    # 如果设备当前已连接，重新连接以应用新设置
                    if device_manager.is_connected:
                        logger.info("设备设置已变更，重新连接设备...")
                        device_manager.disconnect_device()

                        # 延迟重新连接
                        from PyQt5.QtCore import QTimer
                        QTimer.singleShot(500, device_manager._perform_auto_connect)
                    else:
                        # 如果未连接，尝试自动连接
                        device_manager._perform_auto_connect()

                # 同步头部状态显示
                if hasattr(main_window, 'header_widget'):
                    main_window.header_widget.update_connection_status()

                # 同步生产界面状态
                if hasattr(main_window, 'production_widget'):
                    main_window.production_widget.update_device_status()

                logger.info("设备状态已同步到主界面")
            else:
                logger.warning("无法找到主窗口的设备连接管理器")

        except Exception as e:
            logger.error(f"同步设备状态到主界面失败: {e}")

    def _check_device_config_changes(self) -> dict:
        """
        检测设备配置参数是否有变动

        Returns:
            变动的配置字典，如果没有变动返回空字典
        """
        try:
            changes = {}

            for key in self.device_config_keys:
                original_value = self._get_nested_value(self.original_config, key)
                current_value = self.config_manager.get(key)

                if original_value != current_value:
                    changes[key] = {
                        'old': original_value,
                        'new': current_value
                    }
                    logger.info(f"检测到设备配置变动: {key} {original_value} -> {current_value}")

            return changes

        except Exception as e:
            logger.error(f"检测设备配置变动失败: {e}")
            return {}

    def _get_nested_value(self, config: dict, key: str):
        """
        获取嵌套配置值

        Args:
            config: 配置字典
            key: 配置键（支持点号分隔）

        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = config
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return None
            return value
        except Exception:
            return None

    def _apply_device_config_changes(self):
        """
        设备配置变动处理（已移除手动下发功能）
        
        注意：设备参数现在在点击"开始测试"时自动下发，
        不再需要在设置保存时手动下发，避免重复下发问题。
        """
        logger.info("设备配置已更新，将在下次测试开始时自动下发到设备")

    def _emit_config_changes(self):
        """发送配置变更信号到主界面"""
        try:
            # 获取主界面引用
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'config_changed'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'config_changed'):
                # 🔧 新增：发送设备连接配置变更信号
                device_port = self.config_manager.get('device.connection.port', '')
                main_window.config_changed.emit('device.connection.port', device_port)
                
                device_baudrate = self.config_manager.get('device.connection.baudrate', 115200)
                main_window.config_changed.emit('device.connection.baudrate', device_baudrate)

                # 🔧 新增：发送打印机配置变更信号
                printer_name = self.config_manager.get('printer.name', '')
                main_window.config_changed.emit('printer.name', printer_name)
                
                printer_type = self.config_manager.get('printer.type', '')
                main_window.config_changed.emit('printer.type', printer_type)

                # 发送通道使能设置变更信号
                enabled_channels = self.config_manager.get('test.enabled_channels', list(range(1, 9)))
                main_window.config_changed.emit('test.enabled_channels', enabled_channels)

                # 发送顶针寿命设置变更信号
                warning_threshold = self.config_manager.get('probe_pin.warning_threshold', 1000)
                main_window.config_changed.emit('probe_pin.warning_threshold', warning_threshold)

                max_lifetime = self.config_manager.get('probe_pin.max_lifetime', 10000)
                main_window.config_changed.emit('probe_pin.max_lifetime', max_lifetime)

                # 发送测试计数变更信号
                for channel_num in range(1, 9):
                    count = self.config_manager.get(f'test_count.channel_{channel_num}', 0)
                    main_window.config_changed.emit(f'test_count.channel_{channel_num}', count)

                # 发送连续测试模式配置变更信号
                continuous_test = self.config_manager.get('test.continuous_test', False)
                main_window.config_changed.emit('test.continuous_test', continuous_test)

                auto_detect = self.config_manager.get('test.auto_detect', True)
                main_window.config_changed.emit('test.auto_detect', auto_detect)

                continuous_delay = self.config_manager.get('test.continuous_test_delay', 2.0)
                main_window.config_changed.emit('test.continuous_test_delay', continuous_delay)

                count_limit_enabled = self.config_manager.get('test.count_limit_enabled', False)
                main_window.config_changed.emit('test.count_limit_enabled', count_limit_enabled)

                max_count = self.config_manager.get('test.max_count', 100)
                main_window.config_changed.emit('test.max_count', max_count)

                # 发送档位配置变更信号
                self._emit_grade_config_changes(main_window)

                # 🔧 新增：发送标签模板配置变更信号
                self._emit_label_template_config_changes(main_window)

                # 🔧 新增：触发设置同步管理器强制同步
                if hasattr(main_window, 'settings_sync_manager'):
                    main_window.settings_sync_manager.force_sync_all_settings()
                    logger.info("✅ 已触发设置同步管理器强制同步")

                logger.debug("配置变更信号已发送到主界面")
            else:
                logger.warning("无法找到主界面，配置变更信号未发送")

        except Exception as e:
            logger.error(f"发送配置变更信号失败: {e}")

    def _emit_grade_config_changes(self, main_window):
        """发送档位配置变更信号到主界面"""
        try:
            # 发送Rs档位配置变更信号
            rs_grade_count = self.config_manager.get('grade_settings.rs_grade_count', 3)
            main_window.config_changed.emit('grade_settings.rs_grade_count', rs_grade_count)

            rs_min = self.config_manager.get('grade_settings.rs_min', 0.5)
            main_window.config_changed.emit('grade_settings.rs_min', rs_min)

            rs_max = self.config_manager.get('grade_settings.rs_max', 50.0)
            main_window.config_changed.emit('grade_settings.rs_max', rs_max)

            # 发送Rs自动计算选项变更信号
            rs_auto_calc = self.config_manager.get('grade_settings.rs_auto_calc', True)
            main_window.config_changed.emit('grade_settings.rs_auto_calc', rs_auto_calc)

            # 发送Rs档位阈值变更信号
            for i in range(1, rs_grade_count + 1):
                rs_max_value = self.config_manager.get(f'grade_settings.rs{i}_max')
                if rs_max_value is not None:
                    main_window.config_changed.emit(f'grade_settings.rs{i}_max', rs_max_value)
                    # 同步到impedance节点（向后兼容）
                    main_window.config_changed.emit(f'impedance.rs_grade{i}_max', rs_max_value)

            # 发送Rct档位配置变更信号
            rct_min = self.config_manager.get('grade_settings.rct_min', 5.0)
            main_window.config_changed.emit('grade_settings.rct_min', rct_min)

            rct_max = self.config_manager.get('grade_settings.rct_max', 100.0)
            main_window.config_changed.emit('grade_settings.rct_max', rct_max)

            # 发送Rct自动计算选项变更信号
            rct_auto_calc = self.config_manager.get('grade_settings.rct_auto_calc', True)
            main_window.config_changed.emit('grade_settings.rct_auto_calc', rct_auto_calc)

            # 发送Rct档位阈值变更信号（固定3档）
            for i in range(1, 4):
                rct_max_value = self.config_manager.get(f'grade_settings.rct{i}_max')
                if rct_max_value is not None:
                    main_window.config_changed.emit(f'grade_settings.rct{i}_max', rct_max_value)
                    # 同步到impedance节点（向后兼容）
                    main_window.config_changed.emit(f'impedance.rct_grade{i}_max', rct_max_value)

            # 发送档位数量变更信号
            main_window.config_changed.emit('impedance.rs_grade_count', rs_grade_count)
            main_window.config_changed.emit('impedance.rct_grade_count', 3)

            # 发送特殊的档位配置更新信号，通知统计区域更新显示
            main_window.config_changed.emit('grade_config_updated', True)

            logger.debug("档位配置变更信号已发送到主界面")

        except Exception as e:
            logger.error(f"发送档位配置变更信号失败: {e}")

    def _emit_label_template_config_changes(self, main_window):
        """发送标签模板配置变更信号到主界面"""
        try:
            # 🔧 新增：发送标签模板配置变更信号
            current_template_id = self.config_manager.get('label_template.current_template_id', 'standard_50x30')

            # 检查主界面是否有标签模板配置变更处理方法
            if hasattr(main_window, '_on_label_template_config_changed'):
                main_window._on_label_template_config_changed('label_template.current_template_id', current_template_id)
                logger.info(f"✅ 已发送标签模板配置变更信号: {current_template_id}")
            else:
                logger.warning("⚠️ 主界面没有标签模板配置变更处理方法")

        except Exception as e:
            logger.error(f"❌ 发送标签模板配置变更信号失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
