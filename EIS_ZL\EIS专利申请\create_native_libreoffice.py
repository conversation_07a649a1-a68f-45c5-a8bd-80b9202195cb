#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建LibreOffice Draw原生可编辑文件
生成真正可编辑的ODG格式文件

Author: Augment Agent
Date: 2025-01-09
"""

import os
import subprocess
import tempfile

class LibreOfficeNativeGenerator:
    """LibreOffice Draw原生文件生成器"""
    
    def __init__(self, output_dir="editable_figures"):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def create_libreoffice_macro(self):
        """创建LibreOffice Basic宏来生成可编辑的图形"""
        macro_content = '''REM 创建专利附图的LibreOffice Basic宏
Sub CreatePatentFigure()
    Dim oDoc As Object
    Dim oDrawPage As Object
    Dim oShape As Object
    Dim oText As Object
    
    REM 创建新的Draw文档
    oDoc = StarDesktop.loadComponentFromURL("private:factory/sdraw", "_blank", 0, Array())
    oDrawPage = oDoc.getDrawPages().getByIndex(0)
    
    REM 设置页面大小为A4横向
    Dim oPageProps As Object
    oPageProps = oDrawPage
    oPageProps.Width = 29700  REM A4宽度(mm*100)
    oPageProps.Height = 21000 REM A4高度(mm*100)
    
    REM 创建标题
    oShape = oDoc.createInstance("com.sun.star.drawing.TextShape")
    oShape.Position = CreatePoint(14850, 1000)  REM 居中位置
    oShape.Size = CreateSize(15000, 1500)
    oShape.String = "图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图"
    oDrawPage.add(oShape)
    
    REM 1-被测电池
    oShape = CreateRectangleWithText(oDoc, 2000, 3000, 4000, 2500, "1-被测电池" + Chr(10) + "1.9V-5.5V", RGB(173, 216, 230))
    oDrawPage.add(oShape)
    
    REM 7-测试夹具
    oShape = CreateRectangleWithText(oDoc, 2000, 7000, 4000, 2500, "7-测试夹具" + Chr(10) + "四线制连接" + Chr(10) + "精确测量", RGB(245, 222, 179))
    oDrawPage.add(oShape)
    
    REM 2-DNB1101BB芯片
    oShape = CreateRectangleWithText(oDoc, 9000, 6000, 6000, 4000, "2-DNB1101BB" + Chr(10) + "EIS测试芯片" + Chr(10) + "0.0075Hz-7800Hz", RGB(144, 238, 144))
    oDrawPage.add(oShape)
    
    REM 4-外部电流源
    oShape = CreateRectangleWithText(oDoc, 2000, 12000, 4000, 3000, "4-外部电流源" + Chr(10) + "PMV28UNEA" + Chr(10) + "20Ω/10Ω/6.67Ω/5Ω", RGB(240, 128, 128))
    oDrawPage.add(oShape)
    
    REM 3-STM32控制器
    oShape = CreateRectangleWithText(oDoc, 17000, 6000, 5000, 4000, "3-STM32F103RCT6" + Chr(10) + "主控制器" + Chr(10) + "72MHz ARM", RGB(255, 255, 224))
    oDrawPage.add(oShape)
    
    REM 5-串口显示屏
    oShape = CreateRectangleWithText(oDoc, 17000, 12000, 5000, 3000, "5-串口显示屏" + Chr(10) + "实时显示" + Chr(10) + "测试结果", RGB(211, 211, 211))
    oDrawPage.add(oShape)
    
    REM 6-PC上位机
    oShape = CreateRectangleWithText(oDoc, 17000, 2000, 5000, 2500, "6-PC上位机" + Chr(10) + "Modbus RTU" + Chr(10) + "数据分析", RGB(176, 196, 222))
    oDrawPage.add(oShape)
    
    REM 保存文档
    Dim sURL As String
    sURL = "file:///D:/PROG/EIS_ZL/EIS专利申请/editable_figures/figure1_native.odg"
    oDoc.storeAsURL(sURL, Array())
    
    MsgBox "专利附图已创建完成！文件保存为: figure1_native.odg"
End Sub

Function CreateRectangleWithText(oDoc As Object, x As Long, y As Long, width As Long, height As Long, text As String, color As Long) As Object
    Dim oShape As Object
    
    REM 创建矩形
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.Position = CreatePoint(x, y)
    oShape.Size = CreateSize(width, height)
    oShape.FillColor = color
    oShape.LineColor = RGB(0, 0, 0)
    oShape.LineWidth = 50  REM 线宽
    
    REM 添加文本
    oShape.String = text
    
    REM 设置文本格式
    Dim oText As Object
    oText = oShape.getText()
    oText.setString(text)
    
    CreateRectangleWithText = oShape
End Function

Function CreatePoint(x As Long, y As Long) As Object
    Dim oPoint As Object
    oPoint = CreateUnoStruct("com.sun.star.awt.Point")
    oPoint.X = x
    oPoint.Y = y
    CreatePoint = oPoint
End Function

Function CreateSize(width As Long, height As Long) As Object
    Dim oSize As Object
    oSize = CreateUnoStruct("com.sun.star.awt.Size")
    oSize.Width = width
    oSize.Height = height
    CreateSize = oSize
End Function'''
        
        # 保存宏文件
        macro_path = os.path.join(self.output_dir, "CreatePatentFigure.bas")
        with open(macro_path, 'w', encoding='utf-8') as f:
            f.write(macro_content)
        
        print(f"LibreOffice宏文件已保存: {macro_path}")
        return macro_path
    
    def create_manual_instructions(self):
        """创建手动创建指南"""
        instructions = """# 在LibreOffice Draw中手动创建可编辑专利附图

## 方法一：导入SVG后取消组合

### 步骤：
1. **打开LibreOffice Draw**
2. **文件 → 打开** → 选择SVG文件
3. **选择整个图形**（点击图形）
4. **右键 → 取消组合**（或按 Ctrl+Shift+Alt+G）
5. **再次右键 → 取消组合**（可能需要多次）
6. **现在每个元素都可以单独编辑了**

## 方法二：从空白文档重新创建

### 1. 创建新文档
- 文件 → 新建 → 绘图

### 2. 设置页面
- 格式 → 页面
- 纸张：A4，方向：横向

### 3. 创建组件
使用绘图工具栏：

#### 矩形工具创建组件框：
1. **点击矩形工具** 📐
2. **绘制矩形**
3. **右键 → 区域** → 设置填充颜色
4. **右键 → 线条** → 设置边框

#### 文本工具添加文字：
1. **点击文本工具** 📝
2. **在矩形内点击**
3. **输入文字**
4. **格式 → 字符** → 设置字体

#### 连接线工具创建箭头：
1. **点击连接线工具** ↗️
2. **从起点拖拽到终点**
3. **右键 → 线条** → 设置箭头样式

### 4. 组件坐标参考
```
组件名称          X坐标   Y坐标   宽度   高度   颜色
1-被测电池        2cm     3cm     4cm    2.5cm  浅蓝色
7-测试夹具        2cm     7cm     4cm    2.5cm  小麦色
2-DNB1101BB      9cm     6cm     6cm    4cm    浅绿色
4-外部电流源      2cm     12cm    4cm    3cm    浅红色
3-STM32控制器    17cm    6cm     5cm    4cm    浅黄色
5-串口显示屏      17cm    12cm    5cm    3cm    浅灰色
6-PC上位机       17cm    2cm     5cm    2.5cm  浅蓝灰色
```

### 5. 连接线设置
- 电池 ↔ 测试夹具：双向箭头，黑色
- 测试夹具 → DNB1101BB：单向箭头，蓝色
- DNB1101BB → STM32：单向箭头，紫色
- STM32 → PC：单向箭头，红色
- STM32 → 显示屏：单向箭头，绿色
- DNB1101BB → 电流源：单向箭头，橙色
- 电流源 → 测试夹具：单向箭头，红色

## 方法三：使用宏自动创建

1. **工具 → 宏 → 运行宏**
2. **选择宏文件**：CreatePatentFigure.bas
3. **运行CreatePatentFigure宏**
4. **自动生成可编辑的图形**

## 编辑技巧

### 选择和移动
- **单击选择**单个对象
- **拖拽移动**到新位置
- **Ctrl+点击**选择多个对象

### 编辑文本
- **双击文本区域**进入编辑模式
- **修改文字内容**
- **按Esc退出**编辑模式

### 修改颜色
- **选择对象**
- **右键 → 区域**
- **选择新颜色**

### 调整箭头
- **选择连接线**
- **右键 → 线条**
- **线条末端选项卡**
- **设置起点和终点箭头样式**

### 对齐工具
- **选择多个对象**
- **格式 → 对齐和分布**
- **选择对齐方式**

## 保存和导出

### 保存可编辑版本
- **文件 → 另存为**
- **格式**：ODF绘图(.odg)

### 导出用于专利申请
- **文件 → 导出为PDF**
- **质量**：最高
- **分辨率**：300 DPI

---

**提示**：如果SVG导入后仍然无法编辑，尝试多次"取消组合"操作，直到可以选择单个元素为止。"""
        
        instructions_path = os.path.join(self.output_dir, "手动创建指南.md")
        with open(instructions_path, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print(f"手动创建指南已保存: {instructions_path}")
        return instructions_path

if __name__ == "__main__":
    generator = LibreOfficeNativeGenerator()
    generator.create_libreoffice_macro()
    generator.create_manual_instructions()
    print("\n解决方案已生成！")
    print("请按照手动创建指南操作，或使用宏自动创建。")
