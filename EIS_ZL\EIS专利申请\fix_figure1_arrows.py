#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正图1系统整体架构图的箭头连接
专门用于修正箭头方向、位置和连接逻辑

Author: Augment Agent
Date: 2025-01-09
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_improved_figure1():
    """生成改进的图1：系统整体架构图"""
    fig, ax = plt.subplots(figsize=(14, 10))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 标题
    ax.text(6, 9.5, '图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    # 重新布局组件位置，使连接更清晰
    
    # 1. 被测电池 (左上)
    battery = FancyBboxPatch((1, 7), 2, 1.2, boxstyle="round,pad=0.1", 
                            facecolor='lightblue', edgecolor='black', linewidth=2)
    ax.add_patch(battery)
    ax.text(2, 7.6, '1-被测电池\n1.9V-5.5V', ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 2. 测试夹具 (左中)
    fixture = FancyBboxPatch((1, 5), 2, 1.2, boxstyle="round,pad=0.1", 
                            facecolor='wheat', edgecolor='black', linewidth=2)
    ax.add_patch(fixture)
    ax.text(2, 5.6, '7-测试夹具\n四线制连接\n精确测量', ha='center', va='center', fontsize=10)
    
    # 3. DNB1101BB芯片 (中心)
    chip = FancyBboxPatch((4.5, 5.5), 3, 2, boxstyle="round,pad=0.1", 
                         facecolor='lightgreen', edgecolor='black', linewidth=2)
    ax.add_patch(chip)
    ax.text(6, 6.5, '2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz', 
            ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 4. 外部电流源 (左下)
    current_source = FancyBboxPatch((1, 2.5), 2, 1.5, boxstyle="round,pad=0.1", 
                                   facecolor='lightcoral', edgecolor='black', linewidth=2)
    ax.add_patch(current_source)
    ax.text(2, 3.25, '4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω', 
            ha='center', va='center', fontsize=10)
    
    # 5. STM32控制器 (右中)
    mcu = FancyBboxPatch((8.5, 5.5), 2.5, 2, boxstyle="round,pad=0.1", 
                        facecolor='lightyellow', edgecolor='black', linewidth=2)
    ax.add_patch(mcu)
    ax.text(9.75, 6.5, '3-STM32F103RCT6\n主控制器\n72MHz ARM', 
            ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 6. 串口显示屏 (右下)
    display = FancyBboxPatch((8.5, 2.5), 2.5, 1.5, boxstyle="round,pad=0.1", 
                            facecolor='lightgray', edgecolor='black', linewidth=2)
    ax.add_patch(display)
    ax.text(9.75, 3.25, '5-串口显示屏\n实时显示\n测试结果', 
            ha='center', va='center', fontsize=10)
    
    # 7. PC上位机 (右上)
    pc = FancyBboxPatch((8.5, 8), 2.5, 1.2, boxstyle="round,pad=0.1", 
                       facecolor='lightsteelblue', edgecolor='black', linewidth=2)
    ax.add_patch(pc)
    ax.text(9.75, 8.6, '6-PC上位机\nModbus RTU\n数据分析', 
            ha='center', va='center', fontsize=10)
    
    # 改进的箭头连接 - 使用更清晰的连接逻辑
    
    # 1. 电池 ↔ 测试夹具 (双向连接)
    ax.annotate('', xy=(2, 7), xytext=(2, 6.2), 
                arrowprops=dict(arrowstyle='<->', color='black', lw=2))
    ax.text(2.3, 6.6, '电气连接', ha='left', va='center', fontsize=9, rotation=90)
    
    # 2. 测试夹具 → DNB1101BB芯片 (测量信号)
    ax.annotate('', xy=(4.5, 6.5), xytext=(3, 5.6), 
                arrowprops=dict(arrowstyle='->', color='blue', lw=2))
    ax.text(3.7, 6, '电压/电流\n测量信号', ha='center', va='center', fontsize=9, color='blue')
    
    # 3. DNB1101BB芯片 → STM32 (SPI通信)
    ax.annotate('', xy=(8.5, 6.5), xytext=(7.5, 6.5), 
                arrowprops=dict(arrowstyle='->', color='purple', lw=2))
    ax.text(8, 6.8, 'SPI 1Mbps', ha='center', va='center', fontsize=9, color='purple')
    
    # 4. STM32 → PC上位机 (数据上传)
    ax.annotate('', xy=(9.75, 8), xytext=(9.75, 7.5), 
                arrowprops=dict(arrowstyle='->', color='red', lw=2))
    ax.text(10.2, 7.75, 'USB/UART', ha='left', va='center', fontsize=9, color='red')
    
    # 5. STM32 → 串口显示屏 (本地显示)
    ax.annotate('', xy=(9.75, 4), xytext=(9.75, 5.5), 
                arrowprops=dict(arrowstyle='->', color='green', lw=2))
    ax.text(10.2, 4.75, 'UART\n115200bps', ha='left', va='center', fontsize=9, color='green')
    
    # 6. DNB1101BB芯片 → 外部电流源 (控制信号)
    ax.annotate('', xy=(3, 3.25), xytext=(4.5, 5.8), 
                arrowprops=dict(arrowstyle='->', color='orange', lw=2))
    ax.text(3.5, 4.5, 'VSW/VDR\n控制信号', ha='center', va='center', fontsize=9, color='orange')
    
    # 7. 外部电流源 → 测试夹具 (激励电流)
    ax.annotate('', xy=(2, 5), xytext=(2, 4), 
                arrowprops=dict(arrowstyle='->', color='red', lw=2))
    ax.text(2.3, 4.5, '激励电流', ha='left', va='center', fontsize=9, color='red', rotation=90)
    
    # 添加信号流向说明
    flow_text = """信号流向说明：
    1. 电池通过测试夹具连接到系统
    2. DNB1101BB芯片测量电池的电压和电流
    3. 外部电流源提供EIS测试所需的激励信号
    4. STM32控制器处理测试数据和系统控制
    5. 测试结果同时显示在本地屏幕和上位机"""
    
    ax.text(6, 1.5, flow_text, ha='center', va='center', fontsize=10, 
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.8))
    
    # 技术参数标注
    ax.text(6, 0.3, '系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C', 
            ha='center', va='center', fontsize=11, style='italic', fontweight='bold')
    
    plt.tight_layout()
    
    # 确保输出目录存在
    output_dir = "patent_figures"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(f'{output_dir}/图1_系统整体架构图_修正版.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("图1：系统整体架构图（修正版）- 生成完成")

if __name__ == "__main__":
    generate_improved_figure1()
