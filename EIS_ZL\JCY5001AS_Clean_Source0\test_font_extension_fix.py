#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 字体补全和显示框扩展测试
测试字体完整显示和框内内容扩展到右边空白位置
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.components.statistics_widget import StatisticsWidget

class TestFontExtensionWindow(QMainWindow):
    """字体补全和显示框扩展测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS 字体补全和显示框扩展测试")
        self.setGeometry(100, 100, 1400, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 字体补全和显示框扩展测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
测试内容：
1. ✅ 字体有缺失请补全
   - Rs档位范围和Rct档位范围标题字体完整显示
   - 增加标题宽度到90px，确保"Rs档位范围:"和"Rct档位范围:"完整显示
   - 设置字体大小为11pt，确保文字清晰可见

2. ✅ 框内的字显示不全加长至右边空白位置
   - 档位范围显示框最小宽度扩展到500px
   - 最大宽度扩展到800px，充分利用右边空白区域
   - 内容字体增大到10pt，确保数值完整显示
   - 增加内边距和高度，避免文字被截断

3. ✅ 布局优化
   - 改回网格布局，更好控制扩展效果
   - 设置列权重，让内容列拉伸占据右边空白位置
   - 防止文字换行和截断，确保完整显示
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 创建统计显示组件
        self.statistics_widget = StatisticsWidget()
        main_layout.addWidget(self.statistics_widget)
        
        # 添加测试数据
        self.load_test_data()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 更新统计数据
            self.statistics_widget.update_statistics(
                total_count=1530,
                qualified_count=870,
                unqualified_count=660,
                yield_rate=56.9
            )
            
            # 更新档位分布数据
            grade_data = {
                (0, 0): 114, (0, 1): 112, (0, 2): 90,   # Rs1行
                (1, 0): 96,  (1, 1): 106, (1, 2): 101,  # Rs2行  
                (2, 0): 88,  (2, 1): 82,  (2, 2): 81    # Rs3行
            }
            
            self.statistics_widget.update_grade_distribution(grade_data)
            
            print("✅ 测试数据加载完成")
            print("📊 统计数据: 总数=1530, 合格=870, 不合格=660, 良率=56.9%")
            print("📋 档位分布: 3x3表格数据已填充")
            print("🎯 重点观察:")
            print("   1. Rs档位范围和Rct档位范围标题是否完整显示")
            print("   2. 档位范围数值内容是否完整显示，不被截断")
            print("   3. 显示框是否扩展到右边空白位置")
            print("   4. 字体是否清晰完整")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("JCY5001AS字体扩展测试")
    app.setApplicationVersion("1.0")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = TestFontExtensionWindow()
    window.show()
    
    print("🚀 JCY5001AS 字体补全和显示框扩展测试启动")
    print("📝 测试重点:")
    print("   1. 字体是否完整显示，无缺失")
    print("   2. 框内内容是否完整显示，无截断")
    print("   3. 显示框是否扩展到右边空白位置")
    print("   4. 整体布局是否合理美观")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
