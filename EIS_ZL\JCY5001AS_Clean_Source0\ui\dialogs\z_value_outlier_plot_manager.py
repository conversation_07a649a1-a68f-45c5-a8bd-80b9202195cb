#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Z值离群检测图表管理器

用于学习功能界面中的Z值趋势图显示，替换原有的奈奎斯特图
支持多曲线对比、上下限标注、动态阈值调整等功能

Author: Jack
Date: 2025-06-22
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
    QSlider, QPushButton, QCheckBox, QComboBox, QSpinBox,
    QSplitter, QFrame, QGridLayout, QFormLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from PyQt5.QtGui import QFont

# 尝试导入matplotlib
try:
    import matplotlib
    matplotlib.use('Qt5Agg')
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

logger = logging.getLogger(__name__)


class ZValueOutlierPlotManager(QObject):
    """
    Z值偏差百分比对比图表管理器

    职责：
    - Z值偏差百分比图显示（频率 vs 偏差百分比）
    - 多曲线对比功能
    - 一致性分析
    - 动态阈值调整
    - 交互功能（缩放、悬停等）
    """
    
    # 信号定义
    threshold_changed = pyqtSignal(float)  # 阈值变更信号
    curve_visibility_changed = pyqtSignal(str, bool)  # 曲线显示/隐藏信号
    
    def __init__(self, parent=None):
        """
        初始化Z值离群检测图表管理器
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.parent_widget = parent
        
        # 图表组件
        self.figure = None
        self.canvas = None
        self.ax = None
        
        # 数据存储
        self.current_data = []  # 当前显示的数据
        self.median_data = {}   # 中位值数据
        self.limit_curves = {} # 上下限曲线数据
        self.current_threshold = 10.0  # 当前阈值
        
        # 显示控制
        self.curve_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                           '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        self.curve_visibility = {}  # 曲线显示状态
        
        # UI组件
        self.widgets = {}
        
        # 初始化组件
        self._init_plot_components()
        
        logger.debug("Z值离群检测图表管理器初始化完成")
    
    def _init_plot_components(self):
        """初始化图表组件"""
        if not MATPLOTLIB_AVAILABLE:
            logger.warning("matplotlib不可用，Z值图表功能将受限")
            return
        
        try:
            # 创建matplotlib图表
            self.figure = Figure(figsize=(10, 6), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            self.canvas.setMinimumSize(600, 400)
            
            # 创建坐标轴
            self.ax = self.figure.add_subplot(111)
            self._setup_axis()
            
            # 设置交互事件
            self._setup_interactive_events()
            
            logger.debug("Z值图表组件初始化完成")
            
        except Exception as e:
            logger.error(f"初始化Z值图表组件失败: {e}")
    
    def _setup_axis(self):
        """设置坐标轴"""
        if not self.ax:
            return

        try:
            # 设置标题和标签
            self.ax.set_title('Z值偏差百分比对比图', fontsize=14, fontweight='bold')
            self.ax.set_xlabel('频率 (Hz)', fontsize=12)
            self.ax.set_ylabel('偏差百分比 (%)', fontsize=12)

            # 设置对数刻度（X轴）
            self.ax.set_xscale('log')
            self.ax.set_xlim(0.01, 10000)  # 0.01Hz - 10kHz

            # 设置Y轴范围（偏差百分比）
            self.ax.set_ylim(-50, 50)  # -50% 到 +50%

            # 添加0%基准线
            self.ax.axhline(y=0, color='gray', linestyle='--', alpha=0.7, linewidth=1, label='基准线 (0%)')

            # 设置网格
            self.ax.grid(True, alpha=0.3, which='both')
            self.ax.grid(True, alpha=0.1, which='minor')

            # 设置图例位置
            self.ax.legend(loc='upper right', fontsize=10)

        except Exception as e:
            logger.error(f"设置坐标轴失败: {e}")
    
    def _setup_interactive_events(self):
        """设置交互事件"""
        if not self.canvas:
            return
        
        try:
            # 鼠标悬停事件
            self.canvas.mpl_connect('motion_notify_event', self._on_mouse_hover)
            
            # 鼠标点击事件
            self.canvas.mpl_connect('button_press_event', self._on_mouse_click)
            
        except Exception as e:
            logger.error(f"设置交互事件失败: {e}")
    
    def get_plot_widget(self) -> QWidget:
        """获取图表组件"""
        if not MATPLOTLIB_AVAILABLE:
            # 返回占位符标签
            placeholder = QLabel("Z值图表功能需要安装matplotlib库")
            placeholder.setMinimumSize(600, 400)
            placeholder.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
            return placeholder
        
        return self.canvas
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        control_widget = QWidget()
        layout = QHBoxLayout(control_widget)

        # 阈值控制组
        threshold_group = self._create_threshold_control()
        layout.addWidget(threshold_group)

        # 显示控制组
        display_group = self._create_display_control()
        layout.addWidget(display_group)

        # 操作按钮组
        action_group = self._create_action_buttons()
        layout.addWidget(action_group)

        layout.addStretch()

        return control_widget

    def create_statistics_panel(self) -> QWidget:
        """创建统计信息面板"""
        stats_widget = QGroupBox("统计信息")
        layout = QFormLayout(stats_widget)

        # 合格率标签
        qualification_rate_label = QLabel("--")
        self.widgets['qualification_rate_label'] = qualification_rate_label
        layout.addRow("合格率:", qualification_rate_label)

        # 超限点数标签
        outlier_count_label = QLabel("--")
        self.widgets['outlier_count_label'] = outlier_count_label
        layout.addRow("超限点数:", outlier_count_label)

        # 总数据点标签
        total_points_label = QLabel("--")
        self.widgets['total_points_label'] = total_points_label
        layout.addRow("总数据点:", total_points_label)

        # 最大偏差标签
        max_deviation_label = QLabel("--")
        self.widgets['max_deviation_label'] = max_deviation_label
        layout.addRow("最大偏差:", max_deviation_label)

        # 当前阈值标签
        current_threshold_label = QLabel(f"{self.current_threshold:.1f}%")
        self.widgets['current_threshold_label'] = current_threshold_label
        layout.addRow("当前阈值:", current_threshold_label)

        # 添加分隔线
        layout.addRow("", QLabel(""))

        # 一致性分析标签
        consistency_std_label = QLabel("--")
        self.widgets['consistency_std_label'] = consistency_std_label
        layout.addRow("数据一致性:", consistency_std_label)

        # 一致性评估标签
        consistency_level_label = QLabel("--")
        self.widgets['consistency_level_label'] = consistency_level_label
        layout.addRow("一致性评估:", consistency_level_label)

        # 添加分隔线
        layout.addRow("", QLabel(""))

        # 基准信息标签
        baseline_info_label = QLabel("--")
        self.widgets['baseline_info_label'] = baseline_info_label
        layout.addRow("对比基准:", baseline_info_label)

        return stats_widget
    
    def _create_threshold_control(self) -> QGroupBox:
        """创建阈值控制组"""
        group = QGroupBox("阈值调整")
        layout = QFormLayout(group)
        
        # 阈值滑块
        threshold_slider = QSlider(Qt.Horizontal)
        threshold_slider.setRange(1, 50)  # 1%-50%
        threshold_slider.setValue(int(self.current_threshold))
        threshold_slider.setTickPosition(QSlider.TicksBelow)
        threshold_slider.setTickInterval(10)
        threshold_slider.valueChanged.connect(self._on_threshold_changed)
        self.widgets['threshold_slider'] = threshold_slider
        
        # 阈值标签
        threshold_label = QLabel(f"{self.current_threshold:.1f}%")
        self.widgets['threshold_label'] = threshold_label
        
        # 布局
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(threshold_slider)
        threshold_layout.addWidget(threshold_label)
        
        layout.addRow("偏差阈值:", threshold_layout)
        
        return group
    
    def _create_display_control(self) -> QGroupBox:
        """创建显示控制组"""
        group = QGroupBox("显示选项")
        layout = QVBoxLayout(group)

        # 显示中位值基准线（0%基准线）
        show_median_cb = QCheckBox("显示中位值基准线")
        show_median_cb.setChecked(True)
        show_median_cb.setToolTip("显示0%偏差基准线")
        show_median_cb.toggled.connect(self._on_display_option_changed)
        self.widgets['show_median_cb'] = show_median_cb
        layout.addWidget(show_median_cb)

        # 显示上下限线（合格区域）
        show_limits_cb = QCheckBox("显示上下限线")
        show_limits_cb.setChecked(False)  # 默认关闭，避免图表过于复杂
        show_limits_cb.setToolTip("显示合格区域填充")
        show_limits_cb.toggled.connect(self._on_display_option_changed)
        self.widgets['show_limits_cb'] = show_limits_cb
        layout.addWidget(show_limits_cb)

        # 显示超限点标记
        show_outliers_cb = QCheckBox("标记超限点")
        show_outliers_cb.setChecked(True)
        show_outliers_cb.setToolTip("高亮显示超出阈值的数据点")
        show_outliers_cb.toggled.connect(self._on_display_option_changed)
        self.widgets['show_outliers_cb'] = show_outliers_cb
        layout.addWidget(show_outliers_cb)

        return group
    
    def _create_action_buttons(self) -> QGroupBox:
        """创建操作按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout(group)
        
        # 重置图表按钮
        reset_view_btn = QPushButton("重置图表")
        reset_view_btn.clicked.connect(self._reset_view)
        layout.addWidget(reset_view_btn)

        # 导出图形按钮
        export_btn = QPushButton("导出图形")
        export_btn.clicked.connect(self._export_chart)
        layout.addWidget(export_btn)
        
        return group
    
    def update_z_value_plot(self, selected_data: List[Dict], median_data: Optional[Dict] = None,
                           limit_curves: Optional[Dict] = None, threshold: Optional[float] = None):
        """
        更新Z值偏差百分比对比图

        Args:
            selected_data: 选中的测试数据
            median_data: 中位值数据
            limit_curves: 上下限曲线数据
            threshold: 偏差阈值
        """
        try:
            if not MATPLOTLIB_AVAILABLE or not self.ax:
                logger.warning("matplotlib不可用，无法更新偏差百分比图表")
                return

            # 检查数据数量要求
            if len(selected_data) < 2:
                self._show_insufficient_data_message()
                return

            # 保存数据
            self.current_data = selected_data
            if median_data:
                self.median_data = median_data
            if limit_curves:
                self.limit_curves = limit_curves
            if threshold is not None:
                self.current_threshold = threshold

            # 清除现有图表
            self.ax.clear()
            self._setup_axis()

            # 绘制偏差百分比曲线
            self._draw_deviation_percentage_curves()

            # 绘制阈值线
            self._draw_threshold_lines()

            # 根据显示选项绘制额外内容
            self._draw_optional_elements()

            # 更新图例
            self.ax.legend(loc='upper right', fontsize=10)

            # 更新统计信息（包括一致性分析）
            self._update_statistics_with_consistency()

            # 刷新画布
            self.canvas.draw()

            logger.debug(f"偏差百分比图表更新完成，数据量: {len(selected_data)}")

        except Exception as e:
            logger.error(f"更新偏差百分比图表失败: {e}")

    def _show_insufficient_data_message(self):
        """显示数据不足的提示信息"""
        try:
            self.ax.clear()
            self.ax.text(0.5, 0.5, '请选择至少2条测试数据进行对比分析',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=16, color='gray')
            self.ax.set_xlim(0, 1)
            self.ax.set_ylim(0, 1)
            self.ax.set_xticks([])
            self.ax.set_yticks([])
            self.canvas.draw()

        except Exception as e:
            logger.error(f"显示数据不足提示失败: {e}")

    def _update_statistics(self):
        """更新统计信息"""
        try:
            if not self.current_data or not self.median_data:
                return

            total_points = 0
            outlier_points = 0
            max_deviation = 0.0

            # 计算统计信息
            for data in self.current_data:
                channel = data.get('channel_number')
                impedance_details = data.get('impedance_details', [])

                if channel not in self.median_data:
                    continue

                for detail in impedance_details:
                    freq = detail.get('frequency', 0)
                    real = detail.get('impedance_real', 0)
                    imag = detail.get('impedance_imag', 0)

                    if freq <= 0:
                        continue

                    z_value = np.sqrt(real**2 + imag**2)
                    total_points += 1

                    # 检查是否有对应频率的中位值
                    if freq in self.median_data[channel]:
                        median_z = self.median_data[channel][freq].get('z_value', 0)

                        if median_z > 0:
                            deviation_percent = abs((z_value - median_z) / median_z) * 100
                            max_deviation = max(max_deviation, deviation_percent)

                            # 如果超出阈值，计为超限点
                            if deviation_percent > self.current_threshold:
                                outlier_points += 1

            # 计算合格率
            qualification_rate = ((total_points - outlier_points) / total_points * 100) if total_points > 0 else 0

            # 更新UI显示
            if 'qualification_rate_label' in self.widgets:
                self.widgets['qualification_rate_label'].setText(f"{qualification_rate:.1f}%")

            if 'outlier_count_label' in self.widgets:
                self.widgets['outlier_count_label'].setText(f"{outlier_points}")

            if 'total_points_label' in self.widgets:
                self.widgets['total_points_label'].setText(f"{total_points}")

            if 'max_deviation_label' in self.widgets:
                self.widgets['max_deviation_label'].setText(f"{max_deviation:.2f}%")

            if 'current_threshold_label' in self.widgets:
                self.widgets['current_threshold_label'].setText(f"{self.current_threshold:.1f}%")

        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def _draw_deviation_percentage_curves(self):
        """绘制偏差百分比曲线（支持跨通道对比）"""
        if not self.current_data or not self.median_data:
            return

        try:
            # 计算统一基准（支持跨通道对比）
            unified_baseline = self._calculate_unified_baseline()
            if not unified_baseline:
                logger.warning("无法计算统一基准，跳过偏差百分比绘制")
                return

            # 存储所有曲线的偏差数据用于一致性分析
            self.deviation_data = {}

            for i, data in enumerate(self.current_data):
                battery_code = data.get('battery_code', f'Battery_{i+1}')
                channel_number = data.get('channel_number', 1)
                impedance_details = data.get('impedance_details', [])

                if not impedance_details:
                    continue

                # 计算偏差百分比（使用统一基准）
                frequencies = []
                deviation_percentages = []

                for detail in impedance_details:
                    freq = detail.get('frequency', 0)
                    real = detail.get('impedance_real', 0)
                    imag = detail.get('impedance_imag', 0)

                    if freq <= 0:
                        continue

                    # 计算实测Z值
                    z_value = np.sqrt(real**2 + imag**2)

                    # 获取对应频率的统一基准值
                    if freq in unified_baseline:
                        baseline_z = unified_baseline[freq]

                        if baseline_z > 0:
                            # 计算偏差百分比
                            deviation_percent = ((z_value - baseline_z) / baseline_z) * 100
                            frequencies.append(freq)
                            deviation_percentages.append(deviation_percent)

                if not frequencies:
                    continue

                # 按频率排序
                sorted_data = sorted(zip(frequencies, deviation_percentages))
                frequencies, deviation_percentages = zip(*sorted_data)

                # 使用通道号作为数据标识键，支持多通道对比
                data_key = f"通道{channel_number}"
                if battery_code and battery_code != f'Battery_{i+1}':
                    # 如果有有效的电池编码，组合显示
                    data_key = f"通道{channel_number}({battery_code})"

                # 存储偏差数据用于一致性分析
                self.deviation_data[data_key] = {
                    'frequencies': frequencies,
                    'deviations': deviation_percentages,
                    'channel_number': channel_number,
                    'battery_code': battery_code
                }

                # 根据一致性状态选择颜色
                color = self._get_curve_color(deviation_percentages, i)

                # 绘制曲线 - 使用通道号作为标识
                self.ax.plot(frequencies, deviation_percentages,
                           color=color, linewidth=2, marker='o', markersize=4,
                           label=data_key, alpha=0.8)

                # 记录曲线显示状态
                self.curve_visibility[data_key] = True

        except Exception as e:
            logger.error(f"绘制偏差百分比曲线失败: {e}")

    def _calculate_unified_baseline(self):
        """
        计算统一基准（支持跨通道对比）

        策略：
        1. 如果所有数据来自同一通道，使用该通道的中位值
        2. 如果数据来自多个通道，直接从原始数据计算跨通道统一基准
        3. 确保所有通道的数据都使用相同的基准进行对比

        Returns:
            Dict: {frequency: baseline_z_value}
        """
        try:
            if not self.current_data:
                return {}

            # 统计当前数据涉及的通道
            channels_in_data = set()
            for data in self.current_data:
                channel_number = data.get('channel_number', 1)
                channels_in_data.add(channel_number)

            logger.info(f"当前数据涉及通道: {sorted(channels_in_data)}")

            # 策略1：如果只有一个通道，使用该通道的中位值（如果可用）
            if len(channels_in_data) == 1:
                channel = list(channels_in_data)[0]
                if self.median_data and channel in self.median_data:
                    baseline = {}
                    for freq, data in self.median_data[channel].items():
                        if isinstance(data, dict) and 'z_value' in data:
                            baseline[freq] = data['z_value']
                    logger.info(f"使用单通道基准 (通道{channel})，频率点数: {len(baseline)}")

                    # 存储基准信息
                    self.current_baseline_info = {
                        'type': 'single_channel',
                        'channels': [channel],
                        'frequency_count': len(baseline)
                    }
                    return baseline

            # 策略2：多通道情况，直接从原始数据计算跨通道统一基准
            logger.info("多通道数据，计算跨通道统一基准")

            # 收集所有频率点的原始Z值数据
            frequency_z_values = {}

            for data in self.current_data:
                impedance_details = data.get('impedance_details', [])

                for detail in impedance_details:
                    freq = detail.get('frequency', 0)
                    real = detail.get('impedance_real', 0)
                    imag = detail.get('impedance_imag', 0)

                    if freq <= 0:
                        continue

                    # 计算Z值
                    z_value = np.sqrt(real**2 + imag**2)

                    if freq not in frequency_z_values:
                        frequency_z_values[freq] = []
                    frequency_z_values[freq].append(z_value)

            # 计算每个频率点的统一基准值（使用所有通道数据的中位数）
            unified_baseline = {}
            for freq, z_values in frequency_z_values.items():
                if len(z_values) >= 2:  # 至少需要2个数据点
                    # 使用中位数作为跨通道统一基准
                    unified_baseline[freq] = np.median(z_values)

            logger.info(f"跨通道统一基准计算完成，涉及通道: {sorted(channels_in_data)}，频率点数: {len(unified_baseline)}")

            # 存储基准信息用于显示
            self.current_baseline_info = {
                'type': 'cross_channel_unified',
                'channels': sorted(channels_in_data),
                'frequency_count': len(unified_baseline)
            }

            return unified_baseline

        except Exception as e:
            logger.error(f"计算统一基准失败: {e}")
            return {}

    def _get_curve_color(self, deviation_percentages, index):
        """根据偏差情况获取曲线颜色"""
        try:
            # 检查是否有超出阈值的点
            max_abs_deviation = max(abs(d) for d in deviation_percentages)

            if max_abs_deviation <= self.current_threshold:
                return 'green'  # 一致性良好
            else:
                return 'red'    # 超出阈值

        except Exception as e:
            logger.error(f"获取曲线颜色失败: {e}")
            # 返回默认颜色
            return self.curve_colors[index % len(self.curve_colors)]

    def _draw_threshold_lines(self):
        """绘制阈值线"""
        try:
            # 绘制上阈值线
            self.ax.axhline(y=self.current_threshold, color='red', linestyle='--',
                          alpha=0.7, linewidth=1.5, label=f'上阈值 (+{self.current_threshold:.1f}%)')

            # 绘制下阈值线
            self.ax.axhline(y=-self.current_threshold, color='blue', linestyle='--',
                          alpha=0.7, linewidth=1.5, label=f'下阈值 (-{self.current_threshold:.1f}%)')

        except Exception as e:
            logger.error(f"绘制阈值线失败: {e}")

    def _draw_optional_elements(self):
        """根据显示选项绘制可选元素"""
        try:
            # 检查显示选项状态
            show_median = self.widgets.get('show_median_cb', None)
            show_limits = self.widgets.get('show_limits_cb', None)
            show_outliers = self.widgets.get('show_outliers_cb', None)

            # 绘制基准中位值线
            if show_median and show_median.isChecked():
                self._draw_median_baseline()

            # 绘制上下限线
            if show_limits and show_limits.isChecked():
                self._draw_limit_curves()

            # 标记超限点
            if show_outliers and show_outliers.isChecked():
                self._mark_outlier_points()

        except Exception as e:
            logger.error(f"绘制可选元素失败: {e}")

    def _calculate_consistency_metrics(self):
        """计算一致性指标"""
        try:
            if not hasattr(self, 'deviation_data') or len(self.deviation_data) < 2:
                return {}

            # 收集所有频率点的偏差数据
            frequency_deviations = {}

            for data_key, data in self.deviation_data.items():
                frequencies = data['frequencies']
                deviations = data['deviations']

                for freq, deviation in zip(frequencies, deviations):
                    if freq not in frequency_deviations:
                        frequency_deviations[freq] = []
                    frequency_deviations[freq].append(deviation)

            # 计算每个频率点的统计指标
            frequency_stats = {}
            for freq, deviations in frequency_deviations.items():
                if len(deviations) >= 2:  # 至少需要2个数据点
                    std_dev = np.std(deviations)
                    mean_dev = np.mean(deviations)
                    cv = (std_dev / abs(mean_dev)) * 100 if mean_dev != 0 else 0

                    frequency_stats[freq] = {
                        'std_dev': std_dev,
                        'mean_dev': mean_dev,
                        'cv': cv
                    }

            # 计算整体一致性指标
            if frequency_stats:
                overall_std = np.mean([stats['std_dev'] for stats in frequency_stats.values()])
                overall_cv = np.mean([stats['cv'] for stats in frequency_stats.values()])

                # 评估一致性等级
                if overall_std <= 5.0:
                    consistency_level = "一致性良好"
                elif overall_std <= 15.0:
                    consistency_level = "一致性一般"
                else:
                    consistency_level = "一致性较差"

                return {
                    'overall_std': overall_std,
                    'overall_cv': overall_cv,
                    'consistency_level': consistency_level,
                    'frequency_count': len(frequency_stats)
                }

            return {}

        except Exception as e:
            logger.error(f"计算一致性指标失败: {e}")
            return {}

    def _update_statistics_with_consistency(self):
        """更新统计信息（包括一致性分析）"""
        try:
            if not self.current_data:
                return

            # 计算基本统计信息
            total_points = 0
            outlier_points = 0
            max_deviation = 0.0

            if hasattr(self, 'deviation_data'):
                for data_key, data in self.deviation_data.items():
                    deviations = data['deviations']
                    total_points += len(deviations)

                    for deviation in deviations:
                        max_deviation = max(max_deviation, abs(deviation))
                        if abs(deviation) > self.current_threshold:
                            outlier_points += 1

            # 计算合格率
            qualification_rate = ((total_points - outlier_points) / total_points * 100) if total_points > 0 else 0

            # 计算一致性指标
            consistency_metrics = self._calculate_consistency_metrics()

            # 更新UI显示
            if 'qualification_rate_label' in self.widgets:
                self.widgets['qualification_rate_label'].setText(f"{qualification_rate:.1f}%")

            if 'outlier_count_label' in self.widgets:
                self.widgets['outlier_count_label'].setText(f"{outlier_points}")

            if 'total_points_label' in self.widgets:
                self.widgets['total_points_label'].setText(f"{total_points}")

            if 'max_deviation_label' in self.widgets:
                self.widgets['max_deviation_label'].setText(f"{max_deviation:.2f}%")

            if 'current_threshold_label' in self.widgets:
                self.widgets['current_threshold_label'].setText(f"{self.current_threshold:.1f}%")

            # 更新一致性指标显示
            if consistency_metrics:
                if 'consistency_std_label' in self.widgets:
                    self.widgets['consistency_std_label'].setText(f"{consistency_metrics.get('overall_std', 0):.2f}%")

                if 'consistency_level_label' in self.widgets:
                    self.widgets['consistency_level_label'].setText(consistency_metrics.get('consistency_level', '--'))

            # 更新基准信息显示
            if hasattr(self, 'current_baseline_info') and 'baseline_info_label' in self.widgets:
                baseline_info = self.current_baseline_info
                if baseline_info['type'] == 'single_channel':
                    info_text = f"通道{baseline_info['channels'][0]} ({baseline_info['frequency_count']}个频点)"
                elif baseline_info['type'] == 'cross_channel_unified':
                    channels_str = ','.join(map(str, baseline_info['channels']))
                    info_text = f"跨通道统一基准 (通道{channels_str}, {baseline_info['frequency_count']}个频点)"
                else:
                    channels_str = ','.join(map(str, baseline_info['channels']))
                    info_text = f"综合基准 (通道{channels_str}, {baseline_info['frequency_count']}个频点)"

                self.widgets['baseline_info_label'].setText(info_text)

        except Exception as e:
            logger.error(f"更新一致性统计信息失败: {e}")
    
    def _draw_reference_lines(self):
        """绘制基准线和上下限线"""
        try:
            # 检查显示选项
            show_median = self.widgets.get('show_median_cb', {}).isChecked() if 'show_median_cb' in self.widgets else True
            show_limits = self.widgets.get('show_limits_cb', {}).isChecked() if 'show_limits_cb' in self.widgets else True

            if not (show_median or show_limits):
                return

            # 绘制中位值基准线
            if show_median and self.median_data:
                self._draw_median_baseline()

            # 绘制上下限线
            if show_limits and self.limit_curves:
                self._draw_limit_curves()

        except Exception as e:
            logger.error(f"绘制基准线和上下限线失败: {e}")

    def _draw_median_baseline(self):
        """绘制基准中位值线（偏差百分比图中为0%基准线）"""
        try:
            # 在偏差百分比图中，基准线就是0%线
            if hasattr(self, 'current_data') and self.current_data:
                # 获取频率范围
                all_frequencies = set()
                for data in self.current_data:
                    for detail in data.get('impedance_details', []):
                        freq = detail.get('frequency', 0)
                        if freq > 0:
                            all_frequencies.add(freq)

                if all_frequencies:
                    frequencies = sorted(all_frequencies)
                    zero_line = [0] * len(frequencies)

                    # 绘制0%基准线
                    self.ax.plot(frequencies, zero_line,
                               color='green', linewidth=2, linestyle='-',
                               label='基准中位值 (0%)', alpha=0.8)

        except Exception as e:
            logger.error(f"绘制基准中位值线失败: {e}")

    def _draw_limit_curves(self):
        """绘制上下限线（偏差百分比图中为阈值线的补充显示）"""
        try:
            # 在偏差百分比图中，上下限线就是阈值线的延伸
            # 这里可以绘制更详细的限制区域或其他辅助线
            if hasattr(self, 'current_data') and self.current_data:
                # 获取频率范围
                all_frequencies = set()
                for data in self.current_data:
                    for detail in data.get('impedance_details', []):
                        freq = detail.get('frequency', 0)
                        if freq > 0:
                            all_frequencies.add(freq)

                if all_frequencies:
                    frequencies = sorted(all_frequencies)

                    # 绘制上限区域线（可选的额外显示）
                    upper_line = [self.current_threshold] * len(frequencies)
                    lower_line = [-self.current_threshold] * len(frequencies)

                    # 填充阈值区域（可选）
                    self.ax.fill_between(frequencies, lower_line, upper_line,
                                       alpha=0.1, color='gray',
                                       label=f'合格区域 (±{self.current_threshold:.1f}%)')

        except Exception as e:
            logger.error(f"绘制上下限线失败: {e}")

    def _mark_outlier_points(self):
        """标记超限点（在偏差百分比图中标记超出阈值的点）"""
        try:
            if not hasattr(self, 'deviation_data') or not self.deviation_data:
                return

            outlier_frequencies = []
            outlier_deviations = []

            # 检查每条曲线的超限点
            for data_key, data in self.deviation_data.items():
                frequencies = data['frequencies']
                deviations = data['deviations']

                for freq, deviation in zip(frequencies, deviations):
                    # 如果偏差超出阈值，标记为超限点
                    if abs(deviation) > self.current_threshold:
                        outlier_frequencies.append(freq)
                        outlier_deviations.append(deviation)

            # 绘制超限点
            if outlier_frequencies:
                self.ax.scatter(outlier_frequencies, outlier_deviations,
                              color='red', s=60, marker='x', linewidth=3,
                              label=f'超限点 ({len(outlier_frequencies)}个)', alpha=0.8, zorder=5)

        except Exception as e:
            logger.error(f"标记超限点失败: {e}")
    
    def _on_threshold_changed(self, value):
        """阈值变更处理"""
        self.current_threshold = float(value)
        self.widgets['threshold_label'].setText(f"{self.current_threshold:.1f}%")
        self.threshold_changed.emit(self.current_threshold)
        
        # 重新绘制图表
        if self.current_data:
            self.update_z_value_plot(self.current_data, self.median_data, 
                                   self.limit_curves, self.current_threshold)
    
    def _on_display_option_changed(self):
        """显示选项变更处理"""
        # 重新绘制图表
        if self.current_data:
            self.update_z_value_plot(self.current_data, self.median_data, 
                                   self.limit_curves, self.current_threshold)
    
    def _on_mouse_hover(self, event):
        """鼠标悬停事件处理"""
        # 这个方法将在后续实现
        pass
    
    def _on_mouse_click(self, event):
        """鼠标点击事件处理"""
        # 这个方法将在后续实现
        pass
    
    def _reset_view(self):
        """重置图表视图"""
        try:
            logger.info("重置Z值偏差百分比图表视图")

            if not MATPLOTLIB_AVAILABLE or not self.ax:
                logger.warning("matplotlib不可用，无法重置视图")
                return

            # 清除当前图表
            self.ax.clear()

            # 重新设置坐标轴
            self._setup_axis()

            # 如果有当前数据，重新绘制
            if hasattr(self, 'current_data') and self.current_data:
                logger.debug("重新绘制当前数据")
                self.update_z_value_plot(
                    self.current_data,
                    getattr(self, 'median_data', None),
                    getattr(self, 'limit_curves', None),
                    getattr(self, 'current_threshold', 10.0)
                )
            else:
                # 显示空白图表
                self.ax.text(0.5, 0.5, '请选择数据进行分析',
                           horizontalalignment='center', verticalalignment='center',
                           transform=self.ax.transAxes, fontsize=16, color='gray')
                self.canvas.draw()

            logger.info("图表视图重置完成")

        except Exception as e:
            logger.error(f"重置图表视图失败: {e}")

    def _export_chart(self):
        """导出图表到文件"""
        try:
            logger.info("开始导出Z值偏差百分比图表")

            if not MATPLOTLIB_AVAILABLE or not self.figure:
                logger.warning("matplotlib不可用，无法导出图表")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(None, "警告", "matplotlib不可用，无法导出图表")
                return

            # 检查是否有数据
            if not hasattr(self, 'current_data') or not self.current_data:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(None, "警告", "没有可导出的图表数据，请先进行分析")
                return

            # 选择保存文件
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            default_filename = f"Z值偏差百分比对比图_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

            filename, _ = QFileDialog.getSaveFileName(
                None,
                "导出图表",
                default_filename,
                "PNG图片 (*.png);;PDF文件 (*.pdf);;SVG矢量图 (*.svg);;JPG图片 (*.jpg)"
            )

            if not filename:
                logger.debug("用户取消了导出操作")
                return

            # 保存图表
            # 设置高质量输出参数
            dpi = 300  # 高分辨率
            bbox_inches = 'tight'  # 紧凑布局

            self.figure.savefig(filename, dpi=dpi, bbox_inches=bbox_inches,
                              facecolor='white', edgecolor='none')

            logger.info(f"图表导出成功: {filename}")

            # 显示成功消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(None, "导出成功", f"图表已成功导出到:\n{filename}")

        except Exception as e:
            logger.error(f"导出图表失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "导出失败", f"导出图表时发生错误:\n{str(e)}")

    def create_baseline_list_widget(self) -> QWidget:
        """
        创建基准列表显示组件

        Returns:
            QWidget: 基准列表组件
        """
        try:
            from PyQt5.QtWidgets import (QGroupBox, QVBoxLayout, QHBoxLayout,
                                       QTableWidget, QTableWidgetItem, QPushButton,
                                       QHeaderView, QAbstractItemView, QSplitter,
                                       QLabel, QWidget)

            # 创建基准列表组
            group = QGroupBox("基准数据列表")
            layout = QVBoxLayout(group)

            # 创建分割器，上下分布
            splitter = QSplitter()
            splitter.setOrientation(1)  # 垂直分割

            # 上半部分：基准概要列表
            baseline_summary_widget = QWidget()
            summary_layout = QVBoxLayout(baseline_summary_widget)
            summary_layout.setContentsMargins(0, 0, 0, 0)

            # 基准概要表格
            baseline_table = QTableWidget()
            baseline_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            baseline_table.setSelectionMode(QAbstractItemView.SingleSelection)
            baseline_table.setAlternatingRowColors(True)
            baseline_table.setMaximumHeight(200)  # 限制高度，为明细表格留出空间

            # 设置表头
            headers = ['基准名称', '频率点数', '创建时间', '描述']
            baseline_table.setColumnCount(len(headers))
            baseline_table.setHorizontalHeaderLabels(headers)

            # 调整列宽
            header = baseline_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 基准名称
            header.setSectionResizeMode(1, QHeaderView.Fixed)  # 频率点数
            header.resizeSection(1, 80)
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 创建时间
            header.setSectionResizeMode(3, QHeaderView.Stretch)  # 描述

            # 连接选择变更信号
            baseline_table.itemSelectionChanged.connect(self._on_baseline_selection_changed)

            summary_layout.addWidget(baseline_table)
            self.widgets['baseline_table'] = baseline_table

            # 下半部分：基准Z值明细
            baseline_details_widget = QWidget()
            details_layout = QVBoxLayout(baseline_details_widget)
            details_layout.setContentsMargins(0, 0, 0, 0)

            # 明细标题
            details_label = QLabel("基准Z值明细")
            details_label.setStyleSheet("font-weight: bold; font-size: 12px; color: #333;")
            details_layout.addWidget(details_label)

            # 基准明细表格
            baseline_details_table = QTableWidget()
            baseline_details_table.setAlternatingRowColors(True)
            baseline_details_table.setSelectionBehavior(QAbstractItemView.SelectRows)

            # 设置明细表头 - 增加样本数量列
            detail_headers = ['频点(Hz)', 'Z值(mΩ)', '样本数', '保存时间']
            baseline_details_table.setColumnCount(len(detail_headers))
            baseline_details_table.setHorizontalHeaderLabels(detail_headers)

            # 调整明细表格列宽
            details_header = baseline_details_table.horizontalHeader()
            details_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 频点
            details_header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Z值
            details_header.setSectionResizeMode(2, QHeaderView.Fixed)  # 样本数
            details_header.resizeSection(2, 60)  # 样本数列宽度60px
            details_header.setSectionResizeMode(3, QHeaderView.Stretch)  # 保存时间

            details_layout.addWidget(baseline_details_table)
            self.widgets['baseline_details_table'] = baseline_details_table

            # 添加到分割器
            splitter.addWidget(baseline_summary_widget)
            splitter.addWidget(baseline_details_widget)
            splitter.setSizes([200, 300])  # 设置初始大小比例

            layout.addWidget(splitter)

            # 创建操作按钮区域
            button_layout = QHBoxLayout()

            # 刷新按钮
            refresh_btn = QPushButton("刷新列表")
            refresh_btn.clicked.connect(self.refresh_baseline_list)
            button_layout.addWidget(refresh_btn)

            # 导出基准按钮
            export_baseline_btn = QPushButton("导出基准")
            export_baseline_btn.clicked.connect(self._export_selected_baseline)
            button_layout.addWidget(export_baseline_btn)

            # 导入基准按钮
            import_baseline_btn = QPushButton("导入基准")
            import_baseline_btn.clicked.connect(self._import_baseline)
            button_layout.addWidget(import_baseline_btn)

            # 🆕 删除基准按钮
            delete_baseline_btn = QPushButton("删除基准")
            delete_baseline_btn.clicked.connect(self._delete_selected_baseline)
            delete_baseline_btn.setStyleSheet("background-color: #d32f2f; color: white;")  # 红色警告样式
            button_layout.addWidget(delete_baseline_btn)

            button_layout.addStretch()
            layout.addLayout(button_layout)

            # 初始加载基准列表
            self.refresh_baseline_list()

            return group

        except Exception as e:
            logger.error(f"创建基准列表组件失败: {e}")
            return QWidget()

    def refresh_baseline_list(self):
        """刷新基准列表显示"""
        try:
            if 'baseline_table' not in self.widgets:
                return

            table = self.widgets['baseline_table']

            # 获取基准数据
            from backend.outlier_detection_manager import OutlierDetectionManager
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt

            outlier_manager = OutlierDetectionManager()
            baselines = outlier_manager.get_all_baselines()

            # 清空表格
            table.setRowCount(0)

            # 填充数据
            for i, baseline in enumerate(baselines):
                table.insertRow(i)

                # 基准名称
                name_item = QTableWidgetItem(baseline.get('baseline_name', ''))
                table.setItem(i, 0, name_item)

                # 🔧 修复：频率点数 - 显示去重后的唯一频点数量
                baseline_id = baseline.get('id', 0)
                unique_frequency_count = self._get_unique_frequency_count(baseline_id) if baseline_id else 0
                count_item = QTableWidgetItem(str(unique_frequency_count))
                table.setItem(i, 1, count_item)

                # 创建时间
                created_at = baseline.get('created_at', '')
                if created_at:
                    # 格式化时间显示
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        time_str = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        time_str = created_at[:16]  # 截取前16个字符
                else:
                    time_str = '--'
                time_item = QTableWidgetItem(time_str)
                table.setItem(i, 2, time_item)

                # 描述
                description = baseline.get('description', '')
                # 只显示第一行描述，避免表格过高
                desc_lines = description.split('\n')
                desc_display = desc_lines[0] if desc_lines else ''
                if len(desc_display) > 50:
                    desc_display = desc_display[:47] + '...'
                desc_item = QTableWidgetItem(desc_display)
                desc_item.setToolTip(description)  # 完整描述作为提示
                table.setItem(i, 3, desc_item)

                # 存储基准ID用于后续操作
                name_item.setData(Qt.UserRole, baseline.get('id'))

            logger.debug(f"基准列表已刷新，共 {len(baselines)} 条记录")

        except Exception as e:
            logger.error(f"刷新基准列表失败: {e}")

    def _get_unique_frequency_count(self, baseline_id: int) -> int:
        """
        获取基准数据的唯一频点数量

        Args:
            baseline_id: 基准ID

        Returns:
            int: 唯一频点数量
        """
        try:
            from backend.outlier_detection_manager import OutlierDetectionManager

            if not baseline_id:
                return 0

            outlier_manager = OutlierDetectionManager()
            baseline_details = outlier_manager.get_baseline_details(baseline_id)

            if not baseline_details:
                return 0

            # 获取唯一频点集合
            unique_frequencies = set()
            for detail in baseline_details:
                frequency = detail.get('frequency', 0)
                if frequency > 0:  # 排除无效频点
                    unique_frequencies.add(frequency)

            return len(unique_frequencies)

        except Exception as e:
            logger.error(f"获取唯一频点数量失败: {e}")
            return 0

    def _on_baseline_selection_changed(self):
        """基准选择变更处理"""
        try:
            if 'baseline_table' not in self.widgets or 'baseline_details_table' not in self.widgets:
                return

            baseline_table = self.widgets['baseline_table']
            details_table = self.widgets['baseline_details_table']

            current_row = baseline_table.currentRow()

            # 清空明细表格
            details_table.setRowCount(0)

            if current_row < 0:
                return

            # 获取选中的基准ID
            name_item = baseline_table.item(current_row, 0)
            if not name_item:
                return

            baseline_id = name_item.data(Qt.UserRole)
            if not baseline_id:
                return

            # 加载基准明细数据
            self._load_baseline_details(baseline_id)

        except Exception as e:
            logger.error(f"基准选择变更处理失败: {e}")

    def _load_baseline_details(self, baseline_id: int):
        """
        加载基准明细数据到明细表格

        Args:
            baseline_id: 基准ID
        """
        try:
            from backend.outlier_detection_manager import OutlierDetectionManager
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt
            import statistics
            from collections import defaultdict

            if 'baseline_details_table' not in self.widgets:
                return

            details_table = self.widgets['baseline_details_table']

            # 获取基准明细数据
            outlier_manager = OutlierDetectionManager()
            baseline_details = outlier_manager.get_baseline_details(baseline_id)

            if not baseline_details:
                logger.debug(f"基准ID {baseline_id} 没有明细数据")
                return

            # 🔧 修复：按频点分组并计算中位数，确保每个频点只显示一条记录
            frequency_groups = defaultdict(list)

            # 按频点分组收集所有Z值数据
            for detail in baseline_details:
                frequency = detail.get('frequency', 0)
                median_z_value = detail.get('median_z_value', 0)
                median_real = detail.get('median_real', 0)
                median_imag = detail.get('median_imag', 0)
                created_at = detail.get('created_at', '')

                frequency_groups[frequency].append({
                    'median_z_value': median_z_value,
                    'median_real': median_real,
                    'median_imag': median_imag,
                    'created_at': created_at
                })

            # 🔧 修复：对每个频点计算聚合的中位数值
            aggregated_data = []
            for frequency, data_list in frequency_groups.items():
                if not data_list:
                    continue

                # 计算Z值的中位数
                z_values = [d['median_z_value'] for d in data_list if d['median_z_value'] > 0]
                real_values = [d['median_real'] for d in data_list if d['median_real'] != 0]
                imag_values = [d['median_imag'] for d in data_list if d['median_imag'] != 0]

                if z_values:
                    # 使用中位数作为该频点的基准值
                    final_z_value = statistics.median(z_values)
                    final_real = statistics.median(real_values) if real_values else 0
                    final_imag = statistics.median(imag_values) if imag_values else 0

                    # 使用最新的创建时间
                    latest_time = max([d['created_at'] for d in data_list if d['created_at']], default='')

                    aggregated_data.append({
                        'frequency': frequency,
                        'median_z_value': final_z_value,
                        'median_real': final_real,
                        'median_imag': final_imag,
                        'created_at': latest_time,
                        'sample_count': len(data_list)
                    })

            # 🔧 修复：按频率升序排列
            aggregated_data.sort(key=lambda x: x['frequency'])

            # 填充明细表格
            details_table.setRowCount(len(aggregated_data))

            for row, detail in enumerate(aggregated_data):
                # 频点(Hz) - 显示实际频率值
                frequency = detail['frequency']
                freq_item = QTableWidgetItem(f"{frequency:.3f}")
                details_table.setItem(row, 0, freq_item)

                # Z值(mΩ) - 显示聚合后的中位数Z值
                median_z_value = detail['median_z_value']
                z_value_item = QTableWidgetItem(f"{median_z_value:.3f}")
                details_table.setItem(row, 1, z_value_item)

                # 样本数 - 显示参与计算的样本数量
                sample_count = detail['sample_count']
                count_item = QTableWidgetItem(str(sample_count))
                details_table.setItem(row, 2, count_item)

                # 保存时间
                created_at = detail['created_at']
                if created_at:
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        time_str = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        time_str = created_at[:16] if len(created_at) > 16 else created_at
                else:
                    time_str = '--'
                time_item = QTableWidgetItem(time_str)
                details_table.setItem(row, 3, time_item)

            logger.debug(f"基准明细数据加载完成，共 {len(aggregated_data)} 个唯一频点（原始数据 {len(baseline_details)} 条记录）")

        except Exception as e:
            logger.error(f"加载基准明细数据失败: {e}")

    def _export_selected_baseline(self):
        """导出选中的基准数据"""
        try:
            if 'baseline_table' not in self.widgets:
                return

            table = self.widgets['baseline_table']
            current_row = table.currentRow()

            if current_row < 0:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(None, "警告", "请先选择要导出的基准")
                return

            # 获取选中的基准ID
            name_item = table.item(current_row, 0)
            if not name_item:
                return

            baseline_id = name_item.data(Qt.UserRole)
            baseline_name = name_item.text()

            if not baseline_id:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(None, "警告", "无法获取基准ID")
                return

            # 选择导出文件
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            filename, _ = QFileDialog.getSaveFileName(
                None,
                f"导出基准数据 - {baseline_name}",
                f"baseline_{baseline_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if not filename:
                return

            # 导出基准数据
            self._export_baseline_to_file(baseline_id, filename)

        except Exception as e:
            logger.error(f"导出基准数据失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"导出基准数据失败: {e}")

    def _import_baseline(self):
        """导入基准数据"""
        try:
            # 选择导入文件
            from PyQt5.QtWidgets import QFileDialog
            filename, _ = QFileDialog.getOpenFileName(
                None,
                "导入基准数据",
                "",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if not filename:
                return

            # 导入基准数据
            self._import_baseline_from_file(filename)

        except Exception as e:
            logger.error(f"导入基准数据失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"导入基准数据失败: {e}")

    def _export_baseline_to_file(self, baseline_id: int, filename: str):
        """
        导出基准数据到文件

        Args:
            baseline_id: 基准ID
            filename: 导出文件名
        """
        try:
            from backend.outlier_detection_manager import OutlierDetectionManager
            from datetime import datetime
            import json

            outlier_manager = OutlierDetectionManager()

            # 获取基准信息
            baselines = outlier_manager.get_all_baselines()
            baseline_info = None
            for baseline in baselines:
                if baseline.get('id') == baseline_id:
                    baseline_info = baseline
                    break

            if not baseline_info:
                raise ValueError(f"未找到基准ID: {baseline_id}")

            # 获取基准明细数据
            baseline_details = outlier_manager.get_baseline_details(baseline_id)

            # 构建导出数据结构
            export_data = {
                'baseline_info': {
                    'baseline_name': baseline_info.get('baseline_name'),
                    'description': baseline_info.get('description'),
                    'channel_mode': baseline_info.get('channel_mode'),
                    'created_at': baseline_info.get('created_at'),
                    'created_from_sample_id': baseline_info.get('created_from_sample_id')
                },
                'baseline_details': baseline_details,
                'export_info': {
                    'export_time': datetime.now().isoformat(),
                    'export_version': '1.0',
                    'total_frequency_points': len(baseline_details)
                }
            }

            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            logger.info(f"基准数据导出成功: {filename}")

            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                None, "导出成功",
                f"基准数据已成功导出到:\n{filename}\n\n"
                f"基准名称: {baseline_info.get('baseline_name')}\n"
                f"频率点数: {len(baseline_details)}"
            )

        except Exception as e:
            logger.error(f"导出基准数据到文件失败: {e}")
            raise

    def _import_baseline_from_file(self, filename: str):
        """
        从文件导入基准数据

        Args:
            filename: 导入文件名
        """
        try:
            import json
            from datetime import datetime
            from PyQt5.QtWidgets import QMessageBox, QInputDialog
            from backend.outlier_detection_manager import OutlierDetectionManager

            # 读取文件
            with open(filename, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            # 验证数据格式
            required_fields = ['baseline_info', 'baseline_details']
            for field in required_fields:
                if field not in import_data:
                    raise ValueError(f"导入文件缺少必需字段: {field}")

            baseline_info = import_data['baseline_info']
            baseline_details = import_data['baseline_details']

            if not baseline_details:
                raise ValueError("导入文件中没有基准明细数据")

            # 询问用户是否要修改基准名称
            original_name = baseline_info.get('baseline_name', '导入的基准')
            new_name, ok = QInputDialog.getText(
                None, "导入基准数据",
                f"请输入基准名称:\n(原名称: {original_name})",
                text=f"{original_name}_导入_{datetime.now().strftime('%m%d_%H%M')}"
            )

            if not ok or not new_name.strip():
                logger.debug("用户取消了导入操作")
                return

            # 重构基准明细数据为median_data格式
            median_data = {}
            for detail in baseline_details:
                channel_number = detail.get('channel_number', 1)
                frequency = detail.get('frequency', 0)
                median_z_value = detail.get('median_z_value', 0)
                median_real = detail.get('median_real', 0)
                median_imag = detail.get('median_imag', 0)
                sample_count = detail.get('sample_count', 0)

                if channel_number not in median_data:
                    median_data[channel_number] = {}

                median_data[channel_number][frequency] = {
                    'z_value': median_z_value,
                    'real': median_real,
                    'imag': median_imag,
                    'sample_count': sample_count
                }

            # 保存到数据库
            outlier_manager = OutlierDetectionManager()
            baseline_id = outlier_manager.save_baseline_from_learning(
                baseline_name=new_name.strip(),
                median_data=median_data,
                channel_mode=baseline_info.get('channel_mode', 'average_all'),
                description=f"从文件导入: {baseline_info.get('description', '')}\n原创建时间: {baseline_info.get('created_at', '')}",
                sample_id=f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            # 刷新基准列表
            self.refresh_baseline_list()

            logger.info(f"基准数据导入成功: {new_name} (ID: {baseline_id})")

            QMessageBox.information(
                None, "导入成功",
                f"基准数据已成功导入!\n\n"
                f"基准名称: {new_name}\n"
                f"基准ID: {baseline_id}\n"
                f"频率点数: {len(baseline_details)}"
            )

        except Exception as e:
            logger.error(f"从文件导入基准数据失败: {e}")
            raise

    def _delete_selected_baseline(self):
        """删除选中的基准数据"""
        try:
            if 'baseline_table' not in self.widgets:
                return

            table = self.widgets['baseline_table']
            current_row = table.currentRow()

            if current_row < 0:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(None, "警告", "请先选择要删除的基准")
                return

            # 获取选中的基准信息
            name_item = table.item(current_row, 0)
            if not name_item:
                return

            baseline_id = name_item.data(Qt.UserRole)
            baseline_name = name_item.text()

            if not baseline_id:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(None, "警告", "无法获取基准ID")
                return

            # 检查基准是否正在被使用
            usage_check = self._check_baseline_usage(baseline_id)
            if not usage_check['can_delete']:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(
                    None, "无法删除",
                    f"基准 '{baseline_name}' 正在被使用，无法删除。\n\n"
                    f"使用情况: {usage_check['usage_info']}\n\n"
                    f"请先停用该基准或更换活动基准后再删除。"
                )
                return

            # 显示删除确认对话框
            if self._show_delete_confirmation_dialog(baseline_id, baseline_name):
                # 执行删除操作
                self._perform_baseline_deletion(baseline_id, baseline_name)

        except Exception as e:
            logger.error(f"删除基准数据失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"删除基准数据失败: {e}")

    def _check_baseline_usage(self, baseline_id: int) -> dict:
        """
        检查基准是否正在被使用

        Args:
            baseline_id: 基准ID

        Returns:
            dict: 使用情况检查结果
        """
        try:
            from backend.outlier_detection_manager import OutlierDetectionManager

            outlier_manager = OutlierDetectionManager()

            # 获取离群检测配置
            config = outlier_manager.get_detection_config()

            # 检查是否为活动基准
            active_baseline_id = config.get('active_baseline_id')
            is_active = (active_baseline_id == baseline_id)

            # 检查是否启用了离群检测
            is_detection_enabled = config.get('is_enabled', False)

            if is_active and is_detection_enabled:
                return {
                    'can_delete': False,
                    'usage_info': '当前为活动基准且离群检测功能已启用'
                }
            elif is_active:
                return {
                    'can_delete': False,
                    'usage_info': '当前为活动基准（离群检测功能未启用）'
                }
            else:
                return {
                    'can_delete': True,
                    'usage_info': '未被使用'
                }

        except Exception as e:
            logger.error(f"检查基准使用情况失败: {e}")
            # 出错时保守处理，不允许删除
            return {
                'can_delete': False,
                'usage_info': f'检查失败: {e}'
            }

    def _show_delete_confirmation_dialog(self, baseline_id: int, baseline_name: str) -> bool:
        """
        显示删除确认对话框

        Args:
            baseline_id: 基准ID
            baseline_name: 基准名称

        Returns:
            bool: 用户是否确认删除
        """
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                       QPushButton, QTextEdit, QFrame)
            from backend.outlier_detection_manager import OutlierDetectionManager

            dialog = QDialog()
            dialog.setWindowTitle("删除基准确认")
            dialog.setModal(True)
            dialog.resize(500, 350)

            layout = QVBoxLayout(dialog)

            # 警告标题
            title_label = QLabel("⚠️ 确定要删除选中的基准吗？此操作不可撤销")
            title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #d32f2f; margin: 10px;")
            layout.addWidget(title_label)

            # 分隔线
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            layout.addWidget(line)

            # 基准详细信息
            info_text = QTextEdit()
            info_text.setReadOnly(True)
            info_text.setMaximumHeight(200)

            # 获取基准详细信息
            outlier_manager = OutlierDetectionManager()
            baselines = outlier_manager.get_all_baselines()
            baseline_info = None
            for baseline in baselines:
                if baseline.get('id') == baseline_id:
                    baseline_info = baseline
                    break

            if baseline_info:
                # 获取唯一频点数量
                unique_frequency_count = self._get_unique_frequency_count(baseline_id)

                # 格式化创建时间
                created_at = baseline_info.get('created_at', '')
                if created_at:
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        time_str = created_at
                else:
                    time_str = '未知'

                info_content = f"基准名称: {baseline_name}\n"
                info_content += f"基准ID: {baseline_id}\n"
                info_content += f"频率点数: {unique_frequency_count} 个\n"
                info_content += f"创建时间: {time_str}\n"
                info_content += f"通道模式: {baseline_info.get('channel_mode', '未知')}\n"
                info_content += f"描述: {baseline_info.get('description', '无')}\n\n"
                info_content += "删除操作将会:\n"
                info_content += "• 从数据库中永久删除该基准记录\n"
                info_content += "• 删除所有相关的基准明细数据\n"
                info_content += "• 此操作无法撤销，请谨慎操作"
            else:
                info_content = f"基准名称: {baseline_name}\n基准ID: {baseline_id}\n\n无法获取详细信息"

            info_text.setPlainText(info_content)
            layout.addWidget(info_text)

            # 按钮区域
            button_layout = QHBoxLayout()

            cancel_btn = QPushButton("取消")
            cancel_btn.clicked.connect(lambda: dialog.done(0))
            button_layout.addWidget(cancel_btn)

            delete_btn = QPushButton("确认删除")
            delete_btn.clicked.connect(lambda: dialog.done(1))
            delete_btn.setStyleSheet("background-color: #d32f2f; color: white; font-weight: bold;")
            button_layout.addWidget(delete_btn)

            layout.addLayout(button_layout)

            # 显示对话框
            result = dialog.exec_()
            return result == 1

        except Exception as e:
            logger.error(f"显示删除确认对话框失败: {e}")
            return False

    def _perform_baseline_deletion(self, baseline_id: int, baseline_name: str):
        """
        执行基准删除操作

        Args:
            baseline_id: 基准ID
            baseline_name: 基准名称
        """
        try:
            from backend.outlier_detection_manager import OutlierDetectionManager
            from PyQt5.QtWidgets import QMessageBox

            outlier_manager = OutlierDetectionManager()

            # 执行删除操作
            success = outlier_manager.delete_baseline(baseline_id)

            if success:
                # 刷新基准列表
                self.refresh_baseline_list()

                # 显示成功提示
                QMessageBox.information(
                    None, "删除成功",
                    f"基准 '{baseline_name}' 已成功删除！\n\n"
                    f"基准ID: {baseline_id}\n"
                    f"所有相关数据已从数据库中移除。"
                )

                logger.info(f"基准删除成功: {baseline_name} (ID: {baseline_id})")
            else:
                QMessageBox.critical(
                    None, "删除失败",
                    f"删除基准 '{baseline_name}' 失败！\n\n"
                    f"请检查日志获取详细错误信息。"
                )

        except Exception as e:
            logger.error(f"执行基准删除操作失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                None, "删除失败",
                f"删除基准 '{baseline_name}' 时发生错误:\n{e}"
            )
