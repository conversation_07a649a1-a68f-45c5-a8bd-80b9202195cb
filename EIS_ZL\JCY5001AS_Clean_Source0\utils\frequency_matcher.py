#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JCY5001AS设备频率匹配工具

该模块提供设备预设频点匹配功能，确保用户设置的频率能够正确匹配到设备支持的预设频点。
使用向上查找原则：找到大于等于目标频率的最小预设频点。

优化版本：
- 使用二分查找提升性能
- 添加LRU缓存避免重复计算
- 优化日志输出

作者: <PERSON>
日期: 2024-12-19
更新: 2025-06-21 (性能优化)
"""

import bisect
import logging
from functools import lru_cache
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)


class FrequencyMatcher:
    """JCY5001AS设备频率匹配器（优化版本）"""

    # JCY5001AS设备预设频点表（Hz）
    # 按照设备实际支持的频点，从高频到低频排列
    PRESET_FREQUENCIES = [
        7812.5203, 7751.485, 7690.4497, 7629.4144, 7568.3791, 7507.3438, 7446.3085, 7385.2731,
        7324.2378, 7263.2025, 7202.1672, 7141.1319, 7080.0966, 7019.0612, 6958.0259, 6896.9906,
        6835.9553, 6774.92, 6713.8847, 6652.8494, 6591.814, 6530.7787, 6469.7434, 6408.7081,
        6347.6728, 6286.6375, 6225.6022, 6164.5668, 6103.5315, 6042.4962, 5981.4609, 5920.4256,
        5859.3903, 5798.3549, 5737.3196, 5676.2843, 5615.249, 5554.2137, 5493.1784, 5432.1431,
        5371.1077, 5310.0724, 5249.0371, 5188.0018, 5126.9665, 5065.9312, 5004.8958, 4943.8605,
        4882.8252, 4821.7899, 4760.7546, 4699.7193, 4638.684, 4577.6486, 4516.6133, 4455.578,
        4394.5427, 4333.5074, 4272.4721, 4211.4367, 4150.4014, 4089.3661, 4028.3308, 3967.2955,
        3906.2602, 3875.7425, 3845.2249, 3814.7072, 3784.1895, 3753.6719, 3723.1542, 3692.6366,
        3662.1189, 3631.6013, 3601.0836, 3570.5659, 3540.0483, 3509.5306, 3479.013, 3448.4953,
        3417.9777, 3387.46, 3356.9423, 3326.4247, 3295.907, 3265.3894, 3234.8717, 3204.354,
        3173.8364, 3143.3187, 3112.8011, 3082.2834, 3051.7658, 3021.2481, 2990.7304, 2960.2128,
        2929.6951, 2899.1775, 2868.6598, 2838.1422, 2807.6245, 2777.1068, 2746.5892, 2716.0715,
        2685.5539, 2655.0362, 2624.5186, 2594.0009, 2563.4832, 2532.9656, 2502.4479, 2471.9303,
        2441.4126, 2410.895, 2380.3773, 2349.8596, 2319.342, 2288.8243, 2258.3067, 2227.789,
        2197.2713, 2166.7537, 2136.236, 2105.7184, 2075.2007, 2044.6831, 2014.1654, 1983.6477,
        1953.1301, 1937.8713, 1922.6124, 1907.3536, 1892.0948, 1876.8359, 1861.5771, 1846.3183,
        1831.0595, 1815.8006, 1800.5418, 1785.283, 1770.0241, 1754.7653, 1739.5065, 1724.2477,
        1708.9888, 1693.73, 1678.4712, 1663.2123, 1647.9535, 1632.6947, 1617.4359, 1602.177,
        1586.9182, 1571.6594, 1556.4005, 1541.1417, 1525.8829, 1510.6241, 1495.3652, 1480.1064,
        1464.8476, 1449.5887, 1434.3299, 1419.0711, 1403.8122, 1388.5534, 1373.2946, 1358.0358,
        1342.7769, 1327.5181, 1312.2593, 1297.0004, 1281.7416, 1266.4828, 1251.224, 1235.9651,
        1220.7063, 1205.4475, 1190.1886, 1174.9298, 1159.671, 1144.4122, 1129.1533, 1113.8945,
        1098.6357, 1083.3768, 1068.118, 1052.8592, 1037.6004, 1022.3415, 1007.0827, 991.8239,
        976.565, 968.9356, 961.3062, 953.6768, 946.0474, 938.418, 930.7886, 923.1591, 915.5297,
        907.9003, 900.2709, 892.6415, 885.0121, 877.3827, 869.7532, 862.1238, 854.4944, 846.865,
        839.2356, 831.6062, 823.9768, 816.3473, 808.7179, 801.0885, 793.4591, 785.8297, 778.2003,
        770.5709, 762.9414, 755.312, 747.6826, 740.0532, 732.4238, 724.7944, 717.165, 709.5355,
        701.9061, 694.2767, 686.6473, 679.0179, 671.3885, 663.7591, 656.1296, 648.5002, 640.8708,
        633.2414, 625.612, 617.9826, 610.3532, 602.7237, 595.0943, 587.4649, 579.8355, 572.2061,
        564.5767, 556.9473, 549.3178, 541.6884, 534.059, 526.4296, 518.8002, 511.1708, 503.5414,
        495.9119, 488.2825, 484.4678, 480.6531, 476.8384, 473.0237, 469.209, 465.3943, 461.5796,
        457.7649, 453.9502, 450.1354, 446.3207, 442.506, 438.6913, 434.8766, 431.0619, 427.2472,
        423.4325, 419.6178, 415.8031, 411.9884, 408.1737, 404.359, 400.5443, 396.7295, 392.9148,
        389.1001, 385.2854, 381.4707, 377.656, 373.8413, 370.0266, 366.2119, 362.3972, 358.5825,
        354.7678, 350.9531, 347.1384, 343.3236, 339.5089, 335.6942, 331.8795, 328.0648, 324.2501,
        320.4354, 316.6207, 312.806, 308.9913, 305.1766, 301.3619, 297.5472, 293.7325, 289.9177,
        286.103, 282.2883, 278.4736, 274.6589, 270.8442, 267.0295, 263.2148, 259.4001, 255.5854,
        251.7707, 247.956, 244.1413, 242.2339, 240.3266, 238.4192, 236.5118, 234.6045, 232.6971,
        230.7898, 228.8824, 226.9751, 225.0677, 223.1604, 221.253, 219.3457, 217.4383, 215.531,
        213.6236, 211.7162, 209.8089, 207.9015, 205.9942, 204.0868, 202.1795, 200.2721, 198.3648,
        196.4574, 194.5501, 192.6427, 190.7354, 188.828, 186.9207, 185.0133, 183.1059, 181.1986,
        179.2912, 177.3839, 175.4765, 173.5692, 171.6618, 169.7545, 167.8471, 165.9398, 164.0324,
        162.1251, 160.2177, 158.3103, 156.403, 154.4956, 152.5883, 150.6809, 148.7736, 146.8662,
        144.9589, 143.0515, 141.1442, 139.2368, 137.3295, 135.4221, 133.5148, 131.6074, 129.7,
        127.7927, 125.8853, 123.978, 122.0706, 120.1633, 118.2559, 116.3486, 114.4412, 112.5339,
        110.6265, 108.7192, 106.8118, 104.9044, 102.9971, 101.0897, 99.1824, 97.275, 95.3677,
        93.4603, 91.553, 89.6456, 87.7383, 85.8309, 83.9236, 82.0162, 80.1089, 78.2015, 76.2941,
        74.3868, 72.4794, 70.5721, 68.6647, 66.7574, 64.85, 62.9427, 61.0353, 59.128, 57.2206,
        55.3133, 53.4059, 51.4985, 49.5912, 47.6838, 45.7765, 43.8691, 41.9618, 40.0544, 38.1471,
        36.2397, 34.3324, 32.425, 30.5177, 28.6103, 26.703, 24.7956, 22.8882, 20.9809, 19.0735,
        17.1662, 15.2588, 13.3515, 11.4441, 9.5368, 7.6294, 5.7221, 3.8147, 1.9074, 0.9537,
        0.4768, 0.2384, 0.1192, 0.0596, 0.0298, 0.0149
    ]

    def __init__(self):
        """初始化频率匹配器"""
        # 将频点表排序（从小到大），便于二分查找
        self._sorted_frequencies = sorted(self.PRESET_FREQUENCIES)
        # 缓存最近的匹配结果
        self._last_target = None
        self._last_result = None
        logger.debug(f"频率匹配器初始化完成，共{len(self._sorted_frequencies)}个预设频点")
    
    def find_closest_preset_frequency(self, target_frequency: float) -> float:
        """
        查找最接近的预设频点（向上查找原则）- 优化版本

        使用二分查找和缓存机制提升性能

        Args:
            target_frequency: 目标频率 (Hz)

        Returns:
            匹配的预设频点 (Hz)
        """
        try:
            if target_frequency <= 0:
                logger.warning(f"无效的目标频率: {target_frequency}Hz，使用最小预设频点")
                return self._sorted_frequencies[0]

            # 🚀 优化：检查缓存
            if self._last_target == target_frequency and self._last_result is not None:
                return self._last_result

            # 🚀 优化：使用二分查找（O(log n)而不是O(n)）
            # bisect_left找到插入位置，即第一个大于等于target的位置
            insert_pos = bisect.bisect_left(self._sorted_frequencies, target_frequency)

            # 如果目标频率超过所有预设频点
            if insert_pos >= len(self._sorted_frequencies):
                matched_freq = self._sorted_frequencies[-1]  # 最大频点
                logger.warning(f"目标频率{target_frequency}Hz超出范围，使用最大预设频点{matched_freq}Hz")
            else:
                matched_freq = self._sorted_frequencies[insert_pos]
                # 只在有差异时记录日志，减少日志输出
                if abs(matched_freq - target_frequency) > 0.001:
                    logger.debug(f"频率匹配: {target_frequency}Hz -> {matched_freq}Hz")

            # 🚀 优化：缓存结果
            self._last_target = target_frequency
            self._last_result = matched_freq

            return matched_freq

        except Exception as e:
            logger.error(f"频率匹配失败: {e}")
            return 1000.0  # 默认返回1kHz
    
    def get_frequency_match_info(self, target_frequency: float) -> Tuple[float, float, bool]:
        """
        获取频率匹配信息
        
        Args:
            target_frequency: 目标频率 (Hz)
            
        Returns:
            (匹配频点, 频率差值, 是否精确匹配)
        """
        try:
            matched_freq = self.find_closest_preset_frequency(target_frequency)
            frequency_diff = matched_freq - target_frequency
            is_exact_match = abs(frequency_diff) < 0.001  # 1mHz精度
            
            return matched_freq, frequency_diff, is_exact_match
            
        except Exception as e:
            logger.error(f"获取频率匹配信息失败: {e}")
            return target_frequency, 0.0, True
    
    def validate_frequency_list(self, frequencies: List[float]) -> List[Tuple[float, float]]:
        """
        验证频率列表并返回匹配结果
        
        Args:
            frequencies: 频率列表
            
        Returns:
            [(原始频率, 匹配频率), ...]
        """
        try:
            results = []
            for freq in frequencies:
                matched_freq = self.find_closest_preset_frequency(freq)
                results.append((freq, matched_freq))
            
            return results
            
        except Exception as e:
            logger.error(f"验证频率列表失败: {e}")
            return [(freq, freq) for freq in frequencies]
    
    def is_preset_frequency(self, frequency: float, tolerance: float = 0.001) -> bool:
        """
        检查频率是否为预设频点
        
        Args:
            frequency: 待检查的频率 (Hz)
            tolerance: 容差 (Hz)
            
        Returns:
            是否为预设频点
        """
        try:
            for preset_freq in self._sorted_frequencies:
                if abs(preset_freq - frequency) <= tolerance:
                    return True
            return False
            
        except Exception as e:
            logger.error(f"检查预设频点失败: {e}")
            return False
    
    def get_frequency_range(self) -> Tuple[float, float]:
        """
        获取设备支持的频率范围
        
        Returns:
            (最小频率, 最大频率)
        """
        return min(self._sorted_frequencies), max(self._sorted_frequencies)
    
    def get_preset_frequencies_count(self) -> int:
        """
        获取预设频点数量
        
        Returns:
            预设频点数量
        """
        return len(self._sorted_frequencies)


# 全局频率匹配器实例
frequency_matcher = FrequencyMatcher()
