<?xml version="1.0" ?>
<svg xmlns="http://www.w3.org/2000/svg" width="1400" height="1000" viewBox="0 0 1400 1000">
  <style>
        .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
        .component-text { font-family: Arial, sans-serif; font-size: 16px; text-anchor: middle; }
        .label-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
        .param-text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; font-style: italic; }
        .flow-text { font-family: Arial, sans-serif; font-size: 12px; }
        </style>
  <text x="700" y="50" class="title">图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图</text>
  <rect x="100" y="150" width="200" height="120" fill="#ADD8E6" stroke="black" stroke-width="2" rx="10"/>
  <text x="200.0" y="200.0" class="component-text" font-weight="bold">1-被测电池</text>
  <text x="200.0" y="220.0" class="component-text">1.9V-5.5V</text>
  <rect x="100" y="350" width="200" height="120" fill="#F5DEB3" stroke="black" stroke-width="2" rx="10"/>
  <text x="200.0" y="390.0" class="component-text" font-weight="bold">7-测试夹具</text>
  <text x="200.0" y="410.0" class="component-text">四线制连接</text>
  <text x="200.0" y="430.0" class="component-text">精确测量</text>
  <rect x="450" y="300" width="300" height="200" fill="#90EE90" stroke="black" stroke-width="2" rx="10"/>
  <text x="600.0" y="380.0" class="component-text" font-weight="bold">2-DNB1101BB</text>
  <text x="600.0" y="400.0" class="component-text">EIS测试芯片</text>
  <text x="600.0" y="420.0" class="component-text">0.0075Hz-7800Hz</text>
  <rect x="100" y="600" width="200" height="150" fill="#F08080" stroke="black" stroke-width="2" rx="10"/>
  <text x="200.0" y="655.0" class="component-text" font-weight="bold">4-外部电流源</text>
  <text x="200.0" y="675.0" class="component-text">PMV28UNEA</text>
  <text x="200.0" y="695.0" class="component-text">20Ω/10Ω/6.67Ω/5Ω</text>
  <rect x="850" y="300" width="250" height="200" fill="#FFFFE0" stroke="black" stroke-width="2" rx="10"/>
  <text x="975.0" y="380.0" class="component-text" font-weight="bold">3-STM32F103RCT6</text>
  <text x="975.0" y="400.0" class="component-text">主控制器</text>
  <text x="975.0" y="420.0" class="component-text">72MHz ARM</text>
  <rect x="850" y="600" width="250" height="150" fill="#D3D3D3" stroke="black" stroke-width="2" rx="10"/>
  <text x="975.0" y="655.0" class="component-text" font-weight="bold">5-串口显示屏</text>
  <text x="975.0" y="675.0" class="component-text">实时显示</text>
  <text x="975.0" y="695.0" class="component-text">测试结果</text>
  <rect x="850" y="100" width="250" height="120" fill="#B0C4DE" stroke="black" stroke-width="2" rx="10"/>
  <text x="975.0" y="140.0" class="component-text" font-weight="bold">6-PC上位机</text>
  <text x="975.0" y="160.0" class="component-text">Modbus RTU</text>
  <text x="975.0" y="180.0" class="component-text">数据分析</text>
  <line x1="200" y1="270" x2="200" y2="350" stroke="black" stroke-width="2"/>
  <polygon points="200,350 205.0,340.0 195.0,340.0" fill="black"/>
  <polygon points="200,270 205.0,280.0 195.0,280.0" fill="black"/>
  <text x="200.0" y="300.0" class="label-text" fill="black">电气连接</text>
  <line x1="300" y1="410" x2="450" y2="400" stroke="blue" stroke-width="2"/>
  <polygon points="450,400 439.68955336881504,395.6762643159547 440.3547434740527,405.65411589452077" fill="blue"/>
  <text x="375.0" y="395.0" class="label-text" fill="blue">电压/电流测量信号</text>
  <line x1="750" y1="400" x2="850" y2="400" stroke="purple" stroke-width="2"/>
  <polygon points="850,400 840.0,395.0 840.0,405.0" fill="purple"/>
  <text x="800.0" y="390.0" class="label-text" fill="purple">SPI 1Mbps</text>
  <line x1="975" y1="300" x2="975" y2="220" stroke="red" stroke-width="2"/>
  <polygon points="975,220 970.0,230.0 980.0,230.0" fill="red"/>
  <text x="975.0" y="250.0" class="label-text" fill="red">USB/UART</text>
  <line x1="975" y1="500" x2="975" y2="600" stroke="green" stroke-width="2"/>
  <polygon points="975,600 980.0,590.0 970.0,590.0" fill="green"/>
  <text x="975.0" y="540.0" class="label-text" fill="green">UART 115200bps</text>
  <line x1="500" y1="500" x2="300" y2="650" stroke="orange" stroke-width="2"/>
  <polygon points="300,650 311.0,648.0 305.0,640.0" fill="orange"/>
  <text x="400.0" y="565.0" class="label-text" fill="orange">VSW/VDR控制信号</text>
  <line x1="200" y1="600" x2="200" y2="470" stroke="red" stroke-width="2"/>
  <polygon points="200,470 195.0,480.0 205.0,480.0" fill="red"/>
  <text x="200.0" y="525.0" class="label-text" fill="red">激励电流</text>
  <rect x="400" y="800" width="600" height="120" fill="#FFFACD" stroke="gray" stroke-width="1" rx="5"/>
  <text x="420" y="820" class="flow-text" font-weight="bold">信号流向说明：</text>
  <text x="420" y="835" class="flow-text">1. 电池通过测试夹具连接到系统</text>
  <text x="420" y="850" class="flow-text">2. DNB1101BB芯片测量电池的电压和电流</text>
  <text x="420" y="865" class="flow-text">3. 外部电流源提供EIS测试所需的激励信号</text>
  <text x="420" y="880" class="flow-text">4. STM32控制器处理测试数据和系统控制</text>
  <text x="420" y="895" class="flow-text">5. 测试结果同时显示在本地屏幕和上位机</text>
  <text x="700" y="970" class="param-text">系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C</text>
</svg>