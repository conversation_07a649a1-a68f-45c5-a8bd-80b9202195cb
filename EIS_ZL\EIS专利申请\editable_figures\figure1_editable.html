<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { width: 1400px; height: 1000px; position: relative; border: 1px solid #ccc; }
        .component { position: absolute; border: 2px solid black; border-radius: 10px;
                    display: flex; align-items: center; justify-content: center;
                    text-align: center; font-weight: bold; cursor: move; }
        .title { text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 20px; }
        .battery { background-color: #ADD8E6; left: 100px; top: 150px; width: 200px; height: 120px; }
        .fixture { background-color: #F5DEB3; left: 100px; top: 350px; width: 200px; height: 120px; }
        .chip { background-color: #90EE90; left: 450px; top: 300px; width: 300px; height: 200px; }
        .current-source { background-color: #F08080; left: 100px; top: 600px; width: 200px; height: 150px; }
        .mcu { background-color: #FFFFE0; left: 850px; top: 300px; width: 250px; height: 200px; }
        .display { background-color: #D3D3D3; left: 850px; top: 600px; width: 250px; height: 150px; }
        .pc { background-color: #B0C4DE; left: 850px; top: 100px; width: 250px; height: 120px; }
        .arrow { position: absolute; }
        .flow-description { position: absolute; left: 400px; top: 800px; width: 600px;
                           background-color: #FFFACD; border: 1px solid gray; padding: 10px;
                           border-radius: 5px; }
        .tech-params { position: absolute; left: 400px; top: 970px; width: 600px;
                      text-align: center; font-style: italic; font-weight: bold; }
    </style>
</head>
<body>
    <div class="title">图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图</div>

    <div class="container">
        <div class="component battery">1-被测电池<br>1.9V-5.5V</div>
        <div class="component fixture">7-测试夹具<br>四线制连接<br>精确测量</div>
        <div class="component chip">2-DNB1101BB<br>EIS测试芯片<br>0.0075Hz-7800Hz</div>
        <div class="component current-source">4-外部电流源<br>PMV28UNEA<br>20Ω/10Ω/6.67Ω/5Ω</div>
        <div class="component mcu">3-STM32F103RCT6<br>主控制器<br>72MHz ARM</div>
        <div class="component display">5-串口显示屏<br>实时显示<br>测试结果</div>
        <div class="component pc">6-PC上位机<br>Modbus RTU<br>数据分析</div>

        <!-- 这里可以添加SVG箭头 -->
        <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0; pointer-events: none;">
            <!-- 电池 ↔ 测试夹具 -->
            <line x1="200" y1="270" x2="200" y2="350" stroke="black" stroke-width="2" marker-end="url(#arrowhead)" marker-start="url(#arrowhead)"/>
            <text x="220" y="310" fill="black" font-size="12">电气连接</text>

            <!-- 测试夹具 → DNB1101BB -->
            <line x1="300" y1="410" x2="450" y2="400" stroke="blue" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
            <text x="375" y="395" fill="blue" font-size="12">电压/电流测量信号</text>

            <!-- DNB1101BB → STM32 -->
            <line x1="750" y1="400" x2="850" y2="400" stroke="purple" stroke-width="2" marker-end="url(#arrowhead-purple)"/>
            <text x="800" y="390" fill="purple" font-size="12">SPI 1Mbps</text>

            <!-- STM32 → PC -->
            <line x1="975" y1="300" x2="975" y2="220" stroke="red" stroke-width="2" marker-end="url(#arrowhead-red)"/>
            <text x="985" y="260" fill="red" font-size="12">USB/UART</text>

            <!-- STM32 → 显示屏 -->
            <line x1="975" y1="500" x2="975" y2="600" stroke="green" stroke-width="2" marker-end="url(#arrowhead-green)"/>
            <text x="985" y="550" fill="green" font-size="12">UART 115200bps</text>

            <!-- DNB1101BB → 电流源 -->
            <line x1="500" y1="500" x2="300" y2="650" stroke="orange" stroke-width="2" marker-end="url(#arrowhead-orange)"/>
            <text x="400" y="575" fill="orange" font-size="12">VSW/VDR控制信号</text>

            <!-- 电流源 → 测试夹具 -->
            <line x1="200" y1="600" x2="200" y2="470" stroke="red" stroke-width="2" marker-end="url(#arrowhead-red)"/>
            <text x="220" y="535" fill="red" font-size="12">激励电流</text>

            <!-- 定义箭头标记 -->
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
                </marker>
                <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="blue"/>
                </marker>
                <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="purple"/>
                </marker>
                <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="red"/>
                </marker>
                <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="green"/>
                </marker>
                <marker id="arrowhead-orange" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="orange"/>
                </marker>
            </defs>
        </svg>

        <div class="flow-description">
            <strong>信号流向说明：</strong><br>
            1. 电池通过测试夹具连接到系统<br>
            2. DNB1101BB芯片测量电池的电压和电流<br>
            3. 外部电流源提供EIS测试所需的激励信号<br>
            4. STM32控制器处理测试数据和系统控制<br>
            5. 测试结果同时显示在本地屏幕和上位机
        </div>

        <div class="tech-params">
            系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C
        </div>
    </div>

    <script>
        // 使组件可拖拽
        document.querySelectorAll('.component').forEach(component => {
            component.addEventListener('mousedown', function(e) {
                let isDragging = true;
                let startX = e.clientX - component.offsetLeft;
                let startY = e.clientY - component.offsetTop;

                function onMouseMove(e) {
                    if (isDragging) {
                        component.style.left = (e.clientX - startX) + 'px';
                        component.style.top = (e.clientY - startY) + 'px';
                    }
                }

                function onMouseUp() {
                    isDragging = false;
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                }

                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            });
        });
    </script>
</body>
</html>