# 在LibreOffice Draw中手动创建可编辑专利附图

## 方法一：导入SVG后取消组合

### 步骤：
1. **打开LibreOffice Draw**
2. **文件 → 打开** → 选择SVG文件
3. **选择整个图形**（点击图形）
4. **右键 → 取消组合**（或按 Ctrl+Shift+Alt+G）
5. **再次右键 → 取消组合**（可能需要多次）
6. **现在每个元素都可以单独编辑了**

## 方法二：从空白文档重新创建

### 1. 创建新文档
- 文件 → 新建 → 绘图

### 2. 设置页面
- 格式 → 页面
- 纸张：A4，方向：横向

### 3. 创建组件
使用绘图工具栏：

#### 矩形工具创建组件框：
1. **点击矩形工具** 📐
2. **绘制矩形**
3. **右键 → 区域** → 设置填充颜色
4. **右键 → 线条** → 设置边框

#### 文本工具添加文字：
1. **点击文本工具** 📝
2. **在矩形内点击**
3. **输入文字**
4. **格式 → 字符** → 设置字体

#### 连接线工具创建箭头：
1. **点击连接线工具** ↗️
2. **从起点拖拽到终点**
3. **右键 → 线条** → 设置箭头样式

### 4. 组件坐标参考
```
组件名称          X坐标   Y坐标   宽度   高度   颜色
1-被测电池        2cm     3cm     4cm    2.5cm  浅蓝色
7-测试夹具        2cm     7cm     4cm    2.5cm  小麦色
2-DNB1101BB      9cm     6cm     6cm    4cm    浅绿色
4-外部电流源      2cm     12cm    4cm    3cm    浅红色
3-STM32控制器    17cm    6cm     5cm    4cm    浅黄色
5-串口显示屏      17cm    12cm    5cm    3cm    浅灰色
6-PC上位机       17cm    2cm     5cm    2.5cm  浅蓝灰色
```

### 5. 连接线设置
- 电池 ↔ 测试夹具：双向箭头，黑色
- 测试夹具 → DNB1101BB：单向箭头，蓝色
- DNB1101BB → STM32：单向箭头，紫色
- STM32 → PC：单向箭头，红色
- STM32 → 显示屏：单向箭头，绿色
- DNB1101BB → 电流源：单向箭头，橙色
- 电流源 → 测试夹具：单向箭头，红色

## 方法三：使用宏自动创建

1. **工具 → 宏 → 运行宏**
2. **选择宏文件**：CreatePatentFigure.bas
3. **运行CreatePatentFigure宏**
4. **自动生成可编辑的图形**

## 编辑技巧

### 选择和移动
- **单击选择**单个对象
- **拖拽移动**到新位置
- **Ctrl+点击**选择多个对象

### 编辑文本
- **双击文本区域**进入编辑模式
- **修改文字内容**
- **按Esc退出**编辑模式

### 修改颜色
- **选择对象**
- **右键 → 区域**
- **选择新颜色**

### 调整箭头
- **选择连接线**
- **右键 → 线条**
- **线条末端选项卡**
- **设置起点和终点箭头样式**

### 对齐工具
- **选择多个对象**
- **格式 → 对齐和分布**
- **选择对齐方式**

## 保存和导出

### 保存可编辑版本
- **文件 → 另存为**
- **格式**：ODF绘图(.odg)

### 导出用于专利申请
- **文件 → 导出为PDF**
- **质量**：最高
- **分辨率**：300 DPI

---

**提示**：如果SVG导入后仍然无法编辑，尝试多次"取消组合"操作，直到可以选择单个元素为止。