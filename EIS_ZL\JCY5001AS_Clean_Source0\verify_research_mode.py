#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证研究模式配置脚本
检查频率配置是否正确设置为研究模式的20个频点
"""

import json
import os
import sys

def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'app_config.json')
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return None

def verify_research_mode(config):
    """验证研究模式配置"""
    print("🔍 验证研究模式配置...")
    print("=" * 60)
    
    # 检查预设模式
    preset_mode = config.get('frequency', {}).get('preset_mode', '')
    print(f"📋 预设模式: {preset_mode}")
    
    # 检查frequency.list
    frequency_list = config.get('frequency', {}).get('list', [])
    print(f"📊 frequency.list: {len(frequency_list)}个频点")
    
    # 检查frequency.multi_freq.custom_list
    custom_list = config.get('frequency', {}).get('multi_freq', {}).get('custom_list', [])
    print(f"📊 frequency.multi_freq.custom_list: {len(custom_list)}个频点")
    
    # 检查配置一致性
    print("\n🔧 配置一致性检查:")
    if frequency_list == custom_list:
        print("✅ frequency.list 和 custom_list 配置一致")
    else:
        print("❌ frequency.list 和 custom_list 配置不一致!")
        print(f"   frequency.list: {len(frequency_list)}个频点")
        print(f"   custom_list: {len(custom_list)}个频点")
        return False
    
    # 检查研究模式频点数量
    expected_research_points = 20
    if len(frequency_list) == expected_research_points:
        print(f"✅ 研究模式频点数量正确: {expected_research_points}个")
    else:
        print(f"❌ 研究模式频点数量错误: 期望{expected_research_points}个，实际{len(frequency_list)}个")
        return False
    
    # 显示频点详情
    print(f"\n📋 研究模式频点列表 ({len(frequency_list)}个):")
    for i, freq in enumerate(frequency_list, 1):
        print(f"   {i:2d}. {freq:>8.3f} Hz")
    
    print("\n✅ 研究模式配置验证通过!")
    return True

def main():
    """主函数"""
    print("🚀 研究模式配置验证工具")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    if not config:
        sys.exit(1)
    
    # 验证配置
    if verify_research_mode(config):
        print("\n🎉 所有检查通过，研究模式配置正确!")
        sys.exit(0)
    else:
        print("\n❌ 配置验证失败，请检查配置文件!")
        sys.exit(1)

if __name__ == "__main__":
    main()
