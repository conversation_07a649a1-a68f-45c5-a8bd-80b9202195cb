# -*- coding: utf-8 -*-
"""
测试执行器
负责具体的测试执行逻辑，包括单次测试、连续测试、多频点测试等

从TestFlowController中提取的测试执行功能，遵循单一职责原则

Author: Jack
Date: 2025-01-30
"""

import logging
import time
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable

from .channel_exception_manager import ChannelExceptionManager
from .battery_pre_test_checker import BatteryPreTestChecker

logger = logging.getLogger(__name__)


class TestExecutor:
    """
    测试执行器
    
    职责：
    - 单次测试执行
    - 连续测试执行
    - 多频点测试执行
    - 测试流程控制
    - 测试状态监控
    """
    
    def __init__(self, comm_manager, test_config_manager, device_config_manager,
                 impedance_data_manager, test_result_manager, startup_strategy_manager):
        """
        初始化测试执行器

        Args:
            comm_manager: 通信管理器
            test_config_manager: 测试配置管理器
            device_config_manager: 设备配置管理器
            impedance_data_manager: 阻抗数据管理器
            test_result_manager: 测试结果管理器
            startup_strategy_manager: 启动策略管理器
        """
        self.comm_manager = comm_manager
        self.test_config_manager = test_config_manager
        self.device_config_manager = device_config_manager
        self.impedance_data_manager = impedance_data_manager
        self.test_result_manager = test_result_manager
        self.startup_strategy_manager = startup_strategy_manager

        # 测试控制
        self.stop_event = threading.Event()
        self.progress_callback = None
        self.status_callback = None

        # 🔧 修复：连续测试计数（支持持久化）
        self.continuous_test_count = self._load_continuous_test_count()

        # 连续测试统计数据
        self.continuous_test_statistics = {
            'start_time': None,
            'end_time': None,
            'cycle_times': [],  # 每轮测试时间
            'test_results': [],  # 所有测试结果
            'total_cycles': 0
        }

        # 🔧 修复：添加当前活跃的测试管理器引用
        self._current_staggered_manager = None
        self._current_simultaneous_manager = None

        # 🔧 新增：添加进度管理器用于连续进度计算
        from backend.test_progress_manager import TestProgressManager
        self.progress_manager = TestProgressManager()

        # 🔧 新增：添加通道异常管理器
        self.exception_manager = ChannelExceptionManager(status_callback=self.status_callback)

        # 🔧 新增：添加电池测试前检测器（启用快速模式）
        self.battery_checker = BatteryPreTestChecker(
            device_config_manager=self.device_config_manager,
            exception_manager=self.exception_manager,
            progress_callback=self.progress_callback
        )
        # 🚀 启用快速检测模式，跳过12秒的阻抗响应检测
        self.battery_checker.enable_fast_detection_mode(True)

        # 🔧 新增：回调去重机制
        self._sent_callbacks = set()  # 记录已发送的回调ID
        self._callback_lock = threading.Lock()  # 回调发送锁
        
        logger.info("测试执行器初始化完成")

    def _load_continuous_test_count(self) -> int:
        """
        加载保存的连续测试计数

        Returns:
            保存的计数值，如果没有保存则返回0
        """
        try:
            import os
            import json

            count_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'continuous_test_count.json')

            if os.path.exists(count_file):
                with open(count_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    count = data.get('count', 0)
                    logger.info(f"🔧 加载保存的连续测试计数: {count}")
                    return count
            else:
                logger.info("🔧 未找到保存的连续测试计数文件，从0开始")
                return 0

        except Exception as e:
            logger.error(f"加载连续测试计数失败: {e}")
            return 0

    def _save_continuous_test_count(self):
        """保存当前的连续测试计数"""
        try:
            import os
            import json

            data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
            os.makedirs(data_dir, exist_ok=True)

            count_file = os.path.join(data_dir, 'continuous_test_count.json')

            data = {
                'count': self.continuous_test_count,
                'last_updated': time.strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(count_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.debug(f"🔧 保存连续测试计数: {self.continuous_test_count}")

        except Exception as e:
            logger.error(f"保存连续测试计数失败: {e}")

    def reset_continuous_test_count(self):
        """重置连续测试计数（手动调用）"""
        try:
            self.continuous_test_count = 0
            self._save_continuous_test_count()
            logger.info("🔧 连续测试计数已重置为0")
        except Exception as e:
            logger.error(f"重置连续测试计数失败: {e}")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
        # 同时设置阻抗数据管理器的回调，用于离群率计算
        if hasattr(self.impedance_data_manager, 'set_progress_callback'):
            self.impedance_data_manager.set_progress_callback(callback)
        # 同时设置电池检测器的回调
        if hasattr(self, 'battery_checker'):
            self.battery_checker.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
        # 同时更新异常管理器的状态回调
        if hasattr(self, 'exception_manager'):
            self.exception_manager.status_callback = callback

    def set_stop_event(self, stop_event: threading.Event):
        """设置停止事件"""
        self.stop_event = stop_event
    
    def execute_test(self, test_config: Dict[str, Any], enabled_channels: List[int]) -> bool:
        """
        执行测试
        
        Args:
            test_config: 测试配置
            enabled_channels: 启用的通道列表
            
        Returns:
            是否执行成功
        """
        try:
            logger.info("开始执行测试")

            # 🔧 新增：重置通道异常状态，开始新测试
            self.exception_manager.reset_for_new_test()

            # 🔧 新增：启动测试计时，估算总测试时间
            frequencies = test_config.get('frequencies', [])
            estimated_duration = len(frequencies) * 30.0  # 估算每个频点30秒
            self.progress_manager.start_test_timer(estimated_duration)

            # 记录测试开始时间
            self.test_result_manager.record_test_start(enabled_channels)

            # 准备测试环境
            if not self._prepare_test_environment(test_config):
                logger.error("测试环境准备失败")
                return False
            
            # 检查测试配置
            continuous_mode = test_config.get('continuous_mode', False)
            logger.debug(f"测试配置检查 - continuous_mode: {continuous_mode}")
            logger.debug(f"完整测试配置: {test_config}")

            # 根据测试模式执行不同的测试流程
            if continuous_mode:
                logger.info("开始连续测试模式")
                success = self._execute_continuous_test(test_config, enabled_channels)
            else:
                logger.info("开始单次测试模式")
                success = self._execute_single_test(test_config, enabled_channels)
            
            # 记录测试结束时间
            self.test_result_manager.record_test_end(enabled_channels)
            
            logger.info(f"测试执行完成: {'成功' if success else '失败'}")
            return success
            
        except Exception as e:
            logger.error(f"测试执行失败: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            return False
    
    def _prepare_test_environment(self, test_config: Dict[str, Any]) -> bool:
        """
        准备测试环境

        Args:
            test_config: 测试配置

        Returns:
            是否准备成功
        """
        try:
            logger.info("开始准备测试环境...")

            # 检查测试配置
            logger.debug(f"测试配置检查: {test_config}")
            enabled_channels = test_config.get('enabled_channels', list(range(1, 9)))
            logger.debug(f"启用通道: {enabled_channels}")

            if not enabled_channels:
                logger.error("❌ 启用通道为空，无法进行测试")
                return False

            # 1. 检查设备连接
            logger.debug("检查设备连接...")
            try:
                if not self.device_config_manager.is_device_ready():
                    logger.error("设备未准备就绪")
                    # 尝试重新连接设备
                    logger.debug("尝试重新检查设备连接...")
                    if hasattr(self.device_config_manager, 'check_device_connection'):
                        connection_ok = self.device_config_manager.check_device_connection()
                        logger.debug(f"设备连接重新检查结果: {connection_ok}")
                        if not connection_ok:
                            return False
                    else:
                        return False
                else:
                    logger.debug("设备连接正常")
            except Exception as e:
                logger.error(f"❌ 设备连接检查失败: {e}")
                return False

            # 2. 设备参数配置已在测试控制组件中完成，这里跳过
            logger.debug("设备参数配置已在开始测试时完成，跳过重复配置")

            # 3. 更新电压显示
            logger.debug("更新电压显示...")
            try:
                self.device_config_manager.update_voltage_display(enabled_channels, self.progress_callback)
                logger.debug("电压显示更新完成")
            except Exception as e:
                logger.error(f"电压显示更新失败: {e}")
                # 电压显示失败不应该阻止测试继续
                logger.warning("电压显示更新失败，但测试将继续进行")

            # 4. 等待设备准备
            logger.debug("等待设备准备...")
            time.sleep(0.5)

            logger.info("测试环境准备完成")
            return True

        except Exception as e:
            logger.error(f"❌ 准备测试环境失败: {e}")
            import traceback
            logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
            return False
    
    def _execute_single_test(self, test_config: Dict[str, Any], enabled_channels: List[int]) -> bool:
        """
        执行单次测试
        
        Args:
            test_config: 测试配置
            enabled_channels: 启用的通道列表
            
        Returns:
            是否执行成功
        """
        try:
            # 获取测试配置信息
            frequencies = test_config.get('frequencies', [1.0])
            is_single_mode = test_config.get('is_single_mode', False)
            frequency_order = test_config.get('frequency_order', 'high_to_low')

            # 🔧 新增：设置测试频点到阻抗数据管理器（用于正确的序号生成）
            if hasattr(self.impedance_data_manager, 'set_test_frequencies'):
                self.impedance_data_manager.set_test_frequencies(frequencies)
                logger.debug(f"已设置测试频点到阻抗数据管理器: {len(frequencies)}个频点")

            # 输出测试模式信息
            if is_single_mode:
                logger.info(f"测试模式: 单频点 {frequencies[0]}Hz")
                return self._execute_single_frequency_test(frequencies[0], enabled_channels)
            else:
                order_desc = "高频到低频" if frequency_order == 'high_to_low' else "低频到高频"
                logger.info(f"测试模式: 多频点测试 ({order_desc})")
                logger.info(f"测试频率: {frequencies}")
                return self._execute_multi_frequency_test(frequencies, enabled_channels, test_config)
                
        except Exception as e:
            logger.error(f"单次测试执行失败: {e}")
            return False

    def _pre_test_battery_check(self, enabled_channels: List[int]) -> List[int]:
        """
        测试前电池检测和通道预筛选（优化版）

        Args:
            enabled_channels: 启用的通道列表

        Returns:
            有电池连接的有效通道列表
        """
        try:
            estimated_time = self.battery_checker.get_detection_time_estimate(len(enabled_channels))
            logger.info(f"🔋 开始测试前电池检测，检查通道: {enabled_channels}")
            logger.info(f"🚀 预估检测时间: {estimated_time:.1f}秒（快速模式）")

            # 使用电池检测器进行检测
            return self.battery_checker.get_valid_channels_only(enabled_channels)

        except Exception as e:
            logger.error(f"测试前电池检测失败: {e}")
            # 出错时返回原始通道列表，避免阻塞测试
            return enabled_channels

    def _mark_channel_as_no_battery(self, channel_num: int, reason: str):
        """
        标记通道为无电池状态

        Args:
            channel_num: 通道号
            reason: 无电池的原因
        """
        try:
            # 通过异常管理器标记通道异常
            from backend.channel_exception_manager import ChannelExceptionType, ChannelExceptionInfo
            from datetime import datetime

            exception_info = ChannelExceptionInfo(
                channel_number=channel_num,
                exception_type=ChannelExceptionType.BATTERY_ERROR,
                status_code=0x0003,  # 使用电池错误状态码
                error_message=f"无电池: {reason}",
                detection_time=datetime.now(),
                frequency_when_detected=None,
                should_skip=True
            )

            # 添加到异常管理器
            self.exception_manager.exception_channels[channel_num] = exception_info
            self.exception_manager.skipped_channels.add(channel_num)

            # 通知UI更新状态
            if self.progress_callback:
                self.progress_callback(channel_num, {
                    'state': 'no_battery',
                    'progress': 0,
                    'message': f'无电池: {reason}',
                    'exception_type': 'BATTERY_ERROR',
                    'error_message': f'无电池: {reason}',
                    'voltage': 0.0
                })

            logger.info(f"🚫 通道{channel_num}已标记为无电池: {reason}")

        except Exception as e:
            logger.error(f"标记通道{channel_num}无电池状态失败: {e}")

    def _execute_single_frequency_test(self, frequency: float, enabled_channels: List[int]) -> bool:
        """
        执行单频点测试

        Args:
            frequency: 测试频率
            enabled_channels: 启用的通道列表

        Returns:
            是否执行成功
        """
        try:
            logger.info(f"开始单频点测试: {frequency}Hz，通道{enabled_channels}")

            # 设置频率
            if not self.comm_manager.set_frequency(frequency):
                logger.error(f"设置频率{frequency}Hz失败")
                return False

            # 🔧 修复：实际执行阻抗测量，而不是只设置频率
            # 转换为通道索引（0-7）
            enabled_channel_indices = [ch - 1 for ch in enabled_channels]

            # 通知测试开始
            if self.progress_callback:
                for channel_num in enabled_channels:
                    self.progress_callback(channel_num, {
                        'state': 'testing',
                        'progress': 10,
                        'message': f'单频点测试: {frequency}Hz',
                        'frequency': frequency,
                        'frequency_index': 1,
                        'total_frequencies': 1
                    })

            # 🔧 修复：使用同时启动管理器执行实际的阻抗测量
            logger.info(f"🔧 修复：启动阻抗测量: {frequency}Hz")
            success = self.startup_strategy_manager.start_impedance_measurement(
                enabled_channel_indices, frequency, 'auto'
            )

            if not success:
                logger.error(f"启动阻抗测量失败: {frequency}Hz")
                return False

            # 等待测试完成并收集数据
            logger.debug("等待测试完成...")
            import time
            time.sleep(2)  # 给测试一些时间完成

            # 收集测试数据
            impedance_data = self.impedance_data_manager.get_impedance_data(enabled_channel_indices, frequency)

            if impedance_data and impedance_data.get('channels'):
                logger.info(f"🔧 修复：收集到阻抗数据，通道数: {len(impedance_data['channels'])}")

                # 保存测试数据
                batch_id = self.test_result_manager.current_batch_id
                if batch_id:
                    self.impedance_data_manager.save_impedance_data(frequency, impedance_data, batch_id)
                    logger.debug("单频点测试数据已保存")

                # 通知测试完成
                if self.progress_callback:
                    for channel_num in enabled_channels:
                        self.progress_callback(channel_num, {
                            'state': 'completed',
                            'progress': 100,
                            'message': f'单频点测试完成: {frequency}Hz',
                            'frequency': frequency
                        })
            else:
                logger.warning("未收集到有效的阻抗数据")
                return False

            logger.info(f"单频点测试完成: {frequency}Hz")
            return True

        except Exception as e:
            logger.error(f"单频点测试失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _execute_multi_frequency_test(self, frequencies: List[float], enabled_channels: List[int],
                                    test_config: Dict[str, Any]) -> bool:
        """
        执行多频点测试（支持并行错频模式）

        Args:
            frequencies: 频率列表
            enabled_channels: 启用的通道列表
            test_config: 测试配置

        Returns:
            是否执行成功
        """
        try:
            logger.info(f"开始多频点测试，共{len(frequencies)}个频点")

            # 🔧 新增：测试前电池检测和通道预筛选
            valid_channels = self._pre_test_battery_check(enabled_channels)
            if not valid_channels:
                logger.warning("所有通道都无电池连接，跳过测试")
                return False

            if len(valid_channels) < len(enabled_channels):
                logger.info(f"电池检测完成，有效通道: {valid_channels}，跳过通道: {[ch for ch in enabled_channels if ch not in valid_channels]}")

            enabled_channel_indices = [ch - 1 for ch in valid_channels]  # 转换为0基索引

            # 检查是否启用并行错频模式
            use_staggered_mode = test_config.get('use_parallel_staggered_mode', False)
            critical_frequency = test_config.get('critical_frequency', 10.0)

            print(f"🔍 [测试执行器] 并行错频模式检查: use_staggered_mode={use_staggered_mode}, critical_frequency={critical_frequency}")

            if use_staggered_mode:
                print(f"🎯 [测试执行器] 选择并行错频测试路径")
                logger.info(f"🔄 启用并行错频模式，临界频率: {critical_frequency}Hz")
                return self._execute_parallel_staggered_test(frequencies, enabled_channel_indices, test_config)
            else:
                print(f"🔄 [测试执行器] 选择传统同时启动测试路径")
                logger.info("🚀 使用传统同时启动模式")
                return self._execute_traditional_test(frequencies, enabled_channel_indices, test_config)

        except Exception as e:
            logger.error(f"多频点测试失败: {e}")
            return False

    def _execute_parallel_staggered_test(self, frequencies: List[float], enabled_channel_indices: List[int],
                                       test_config: Dict[str, Any]) -> bool:
        """
        执行并行错频测试

        Args:
            frequencies: 频率列表
            enabled_channel_indices: 启用的通道索引列表（0-7）
            test_config: 测试配置

        Returns:
            是否执行成功
        """
        try:
            from backend.parallel_staggered_test_manager import ParallelStaggeredTestManagerSimplified as ParallelStaggeredTestManager, ParallelStaggeredTestConfig, ParallelStaggeredTestState

            logger.info("开始并行错频测试")

            # 创建并行错频测试配置
            staggered_config = ParallelStaggeredTestConfig()
            staggered_config.enabled_channels = enabled_channel_indices
            staggered_config.frequencies = frequencies
            staggered_config.critical_frequency = test_config.get('critical_frequency', 10.0)
            staggered_config.timeout_seconds = test_config.get('timeout_seconds', 120)
            staggered_config.status_check_interval = test_config.get('status_check_interval', 0.2)

            # 创建并行错频测试管理器
            # 🔧 修复：传递停止事件给并行错频测试管理器
            staggered_manager = ParallelStaggeredTestManager(self.comm_manager)

            # 🔧 修复：确保停止事件正确传递给并行错频测试管理器
            if hasattr(staggered_manager, 'stop_event'):
                staggered_manager.stop_event = self.stop_event
                logger.info("✅ 停止事件已传递给并行错频测试管理器")

            # 🔧 修复：保存管理器引用以便停止时使用
            self._current_staggered_manager = staggered_manager

            # 设置进度回调
            def staggered_progress_callback(progress_info):
                # 整体进度回调
                pass

            def channel_progress_callback(channel_num, progress_data):
                if self.progress_callback:
                    # 修复电压更新问题 - 在并行错频测试进度回调中添加电压信息
                    try:
                        voltage = self.device_config_manager.read_channel_voltage(channel_num)
                        if voltage is not None and voltage > 0:
                            progress_data['voltage'] = voltage
                    except Exception as e:
                        logger.debug(f"获取通道{channel_num}电压失败: {e}")

                    self.progress_callback(channel_num, progress_data)

            # 设置回调函数
            staggered_manager.set_progress_callback(staggered_progress_callback)
            staggered_manager.set_channel_progress_callback(channel_progress_callback)

            logger.debug("并行错频测试管理器回调函数已设置")

            # 启动并行错频测试
            success = staggered_manager.start_test(staggered_config)
            print(f"🚀 [测试执行器] start_parallel_staggered_test返回: {success}")

            if success:
                # 等待测试完成
                while staggered_manager.get_test_state() != ParallelStaggeredTestState.COMPLETED and not self.stop_event.is_set():
                    if staggered_manager.get_test_state() == ParallelStaggeredTestState.ERROR:
                        logger.error("并行错频测试出现错误")
                        break
                    time.sleep(0.1)

                # 获取测试结果
                results = staggered_manager.get_test_results()

                # 保存测试数据
                if results:
                    batch_id = self.test_result_manager.current_batch_id
                    if batch_id:
                        # results是一个字典，键为频率，值为阻抗数据
                        for frequency, freq_data in results.items():
                            if isinstance(frequency, (int, float)) and freq_data:
                                # 转换数据格式：{channel_index: data} -> {'channels': {channel_num: data}}
                                formatted_data = {
                                    'channels': {
                                        channel_index + 1: channel_data  # 转换为1基通道号
                                        for channel_index, channel_data in freq_data.items()
                                    }
                                }
                                self.impedance_data_manager.save_impedance_data(frequency, formatted_data, batch_id)
                        logger.info(f"并行错频测试数据已保存，频点数: {len(results)}")

                # 处理测试完成
                self._process_test_completion(enabled_channel_indices, test_config)

                logger.info("✅ 并行错频测试完成")
                return True
            else:
                logger.error("❌ 并行错频测试启动失败")
                return False

        except Exception as e:
            logger.error(f"并行错频测试失败: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def _execute_traditional_test(self, frequencies: List[float], enabled_channel_indices: List[int],
                                test_config: Dict[str, Any]) -> bool:
        """
        执行传统同时启动测试

        Args:
            frequencies: 频率列表
            enabled_channel_indices: 启用的通道索引列表（0-7）
            test_config: 测试配置

        Returns:
            是否执行成功
        """
        try:
            logger.info("🚀 开始传统同时启动测试")

            enabled_channels = [ch_idx + 1 for ch_idx in enabled_channel_indices]

            # 保存当前测试的频点索引，用于正确显示进度
            self.current_frequency_index = 0

            for i, frequency in enumerate(frequencies, 1):
                if self.stop_event.is_set():
                    logger.info("测试被用户停止")
                    break

                logger.info(f"测试频点 {i}/{len(frequencies)}: {frequency}Hz")

                # 更新当前频点索引
                self.current_frequency_index = i

                # 1. 设置频率
                if not self.comm_manager.set_frequency_broadcast(frequency):
                    logger.error(f"设置频率{frequency}Hz失败")
                    continue

                # 🔧 优化：通知频点更新（为所有启用的通道）- 使用线性进度映射
                if self.progress_callback:
                    # 🔧 使用线性进度映射：每完成一个频点，进度增加 (90% / 总频点数)
                    completed_frequencies = i - 1  # 已完成的频点数（i是当前正在测试的频点索引，从1开始）
                    progress_per_frequency = 90.0 / len(frequencies)  # 每个频点占用的进度范围
                    base_progress = 5 + (completed_frequencies * progress_per_frequency)  # 基础进度：5% + 已完成频点的进度

                    for channel_num in enabled_channels:
                        # 检查通道是否异常
                        if self.exception_manager.is_channel_skipped(channel_num):
                            # 异常通道显示异常状态
                            exception_info = self.exception_manager.get_exception_info(channel_num)
                            if exception_info:
                                self.progress_callback(channel_num, {
                                    'state': 'exception',
                                    'progress': 0,
                                    'message': f'通道异常: {exception_info.error_message}',
                                    'exception_type': exception_info.exception_type.value,
                                    'status_code': exception_info.status_code,
                                    'error_message': exception_info.error_message
                                })
                            continue

                        # 正常通道显示测试进度
                        # 读取当前通道电压
                        voltage = self.device_config_manager.read_channel_voltage(channel_num)
                        if voltage is None:
                            voltage = 0.0

                        # 🔧 修复：使用总测试时间进度而不是频点进度
                        total_test_time = self.progress_manager.get_test_elapsed_time()
                        estimated_total_time = self.progress_manager.estimated_test_duration

                        if total_test_time > 0 and estimated_total_time > 0:
                            # 基于总测试时间的进度
                            time_progress = min(95.0, (total_test_time / estimated_total_time) * 100)
                            progress_value = int(time_progress)
                        else:
                            # 备用：使用基础进度
                            progress_value = int(base_progress)

                        self.progress_callback(channel_num, {
                            'state': 'testing',
                            'progress': progress_value,
                            'message': f'频点测试: {frequency}Hz',
                            'frequency': frequency,
                            'frequency_index': i,  # 从1开始的频点索引
                            'total_frequencies': len(frequencies),
                            'voltage': voltage,  # 添加电压数据
                            'completed_frequencies': completed_frequencies,  # 已完成频点数
                            'progress_calculation': 'time_based',  # 标识使用时间进度
                            'time_based_progress': True
                        })

                        logger.debug(f"通道{channel_num}进度更新: {int(base_progress)}%")

                # 🔧 新增：过滤出正常通道进行测试
                normal_channels = self.exception_manager.get_normal_channels(enabled_channels)
                normal_channel_indices = [ch - 1 for ch in normal_channels]  # 转换为0基索引

                if not normal_channels:
                    logger.warning(f"频点{frequency}Hz所有通道都异常，跳过该频点")
                    continue

                logger.info(f"频点{frequency}Hz正常通道: {normal_channels}")

                # 2. 启动阻抗测量（只对正常通道）
                success = self.comm_manager.start_impedance_measurement_broadcast(normal_channel_indices)

                if not success:
                    logger.error(f"启动阻抗测量失败: {frequency}Hz")
                    continue

                # 3. 等待测量完成（只等待正常通道）
                measurement_completed = self._wait_for_measurement_completion(normal_channel_indices, frequency)
                if not measurement_completed:
                    if self.stop_event.is_set():
                        logger.info(f"频点{frequency}Hz测量被用户停止，尝试读取已完成的数据")
                    else:
                        logger.warning(f"频点{frequency}Hz测量超时")

                # 4. 读取阻抗数据（只读取正常通道的数据）
                logger.debug(f"开始读取频点{frequency}Hz的阻抗数据（正常通道: {normal_channels}）...")
                impedance_data = self.impedance_data_manager.read_impedance_data_for_frequency(
                    frequency, normal_channel_indices
                )

                # 5. 保存测试数据
                if impedance_data:
                    batch_id = self.test_result_manager.current_batch_id
                    if batch_id:
                        self.impedance_data_manager.save_impedance_data(frequency, impedance_data, batch_id)
                        logger.info(f"频点{frequency}Hz阻抗数据已保存，通道数: {len(impedance_data.get('channels', {}))}")
                    else:
                        logger.warning(f"频点{frequency}Hz阻抗数据未保存：批次ID为空")
                else:
                    logger.warning(f"频点{frequency}Hz阻抗数据读取失败或为空")

                # 6. 如果测量被停止且已读取数据，则退出循环
                if self.stop_event.is_set():
                    if impedance_data:
                        logger.info(f"测试被停止，已保存频点{frequency}Hz的阻抗数据，退出测试循环")
                    else:
                        logger.warning(f"测试被停止，频点{frequency}Hz无有效数据")
                    break

                # 7. 如果测量超时且无数据，继续下一个频点
                if not measurement_completed and not impedance_data:
                    continue

                # 🔧 新增：频点完成后的进度更新 - 平滑递进到下一个频点
                if self.progress_callback:
                    # 计算频点完成后的进度：当前频点完成，进度应该到达下一个频点的起始位置
                    completed_frequencies = i  # 当前频点已完成（i是当前频点索引，从1开始）
                    progress_per_frequency = 90.0 / len(frequencies)  # 每个频点占用的进度范围
                    completed_progress = 5 + (completed_frequencies * progress_per_frequency)  # 完成进度：5% + 已完成频点的进度

                    for channel_num in enabled_channels:
                        # 读取当前通道电压
                        voltage = self.device_config_manager.read_channel_voltage(channel_num)
                        if voltage is None:
                            voltage = 0.0

                        # 🔧 发送频点完成进度更新
                        self.progress_callback(channel_num, {
                            'state': 'frequency_completed',
                            'progress': int(completed_progress),
                            'message': f'频点{frequency}Hz完成',
                            'frequency': frequency,
                            'frequency_index': i,  # 当前完成的频点索引
                            'total_frequencies': len(frequencies),
                            'voltage': voltage,
                            'completed_frequencies': completed_frequencies,  # 已完成频点数
                            'progress_calculation': 'frequency_completed'  # 标识为频点完成进度
                        })

                        logger.debug(f"通道{channel_num}频点完成: {frequency}Hz, 进度={int(completed_progress)}%")

            logger.info("传统同时启动测试完成")

            # 6. 测试完成后进行EIS分析和结果显示
            self._process_test_completion(enabled_channel_indices, test_config)

            return True

        except Exception as e:
            logger.error(f"传统同时启动测试失败: {e}")
            return False

    def _execute_continuous_test(self, test_config: Dict[str, Any], enabled_channels: List[int]) -> bool:
        """
        执行连续测试

        Args:
            test_config: 测试配置
            enabled_channels: 启用的通道列表

        Returns:
            是否执行成功
        """
        try:
            logger.info("开始连续测试模式")

            # 获取连续测试配置
            continuous_mode = test_config.get('continuous_mode', False)
            count_limit_enabled = test_config.get('count_limit_enabled', False)
            max_count = test_config.get('max_count', 100)
            interval = test_config.get('interval', 2)

            logger.debug(f"连续测试配置: mode={continuous_mode}, limit={count_limit_enabled}, max={max_count}, interval={interval}s")

            # 🔧 修复：检查连续测试模式是否正确启用
            if not continuous_mode:
                logger.error("❌ 连续测试模式未启用，无法执行连续测试")
                return False

            # 🔧 修复：不重置连续测试计数，保持累计状态
            # self.continuous_test_count 保持之前的值，实现累计功能
            logger.info(f"🔧 连续测试启动，当前累计计数: {self.continuous_test_count}")

            # 重置统计数据（每次启动重新统计）
            self.continuous_test_statistics = {
                'start_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                'end_time': None,
                'cycle_times': [],
                'test_results': [],
                'total_cycles': 0
            }

            # 🔧 修复：发送连续测试启动事件和初始计数更新
            logger.info(f"🔧 发送连续测试启动事件")
            if self.status_callback:
                try:
                    # 发送启动事件
                    self.status_callback({
                        'action': 'continuous_test_started',
                        'count': self.continuous_test_count,
                        'max_count': max_count if count_limit_enabled else 0
                    })
                    logger.info(f"✅ 连续测试启动事件已发送")

                    # 🔧 修复：立即发送初始计数更新事件，确保UI显示当前累计计数
                    self.status_callback({
                        'action': 'continuous_test_count_updated',
                        'count': self.continuous_test_count,
                        'max_count': max_count if count_limit_enabled else 0,
                        'status': 'started'  # 标识连续测试启动
                    })
                    logger.info(f"✅ 连续测试初始计数更新事件已发送: 当前累计{self.continuous_test_count}轮")
                except Exception as e:
                    logger.error(f"❌ 发送连续测试启动事件失败: {e}")

            # 连续测试主循环
            logger.info(f"开始连续测试主循环，最大次数: {max_count if count_limit_enabled else '无限制'}")

            while not self.stop_event.is_set():
                # 🔧 修复：先检查是否达到最大次数限制（在递增前检查）
                if count_limit_enabled and self.continuous_test_count >= max_count:
                    logger.info(f"✅ 连续测试已达到最大次数限制: {max_count}，停止测试")
                    # 完成统计数据收集
                    self.continuous_test_statistics['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
                    # 通知UI测试完成，包含统计数据
                    if self.status_callback:
                        self.status_callback({
                            'action': 'continuous_test_completed',
                            'count': self.continuous_test_count,  # 使用当前计数
                            'max_count': max_count,
                            'statistics': self.continuous_test_statistics
                        })
                    break

                # 🔧 修复：增加测试计数并保存
                self.continuous_test_count += 1
                self._save_continuous_test_count()  # 保存计数
                logger.info(f"🔧 开始第{self.continuous_test_count}轮连续测试")

                # 🔧 修复：立即发送计数更新事件，确保UI显示当前轮次
                logger.info(f"🔧 发送连续测试计数更新事件: count={self.continuous_test_count}, max_count={max_count if count_limit_enabled else 0}")

                if self.status_callback:
                    try:
                        self.status_callback({
                            'action': 'continuous_test_count_updated',
                            'count': self.continuous_test_count,
                            'max_count': max_count if count_limit_enabled else 0,
                            'status': 'cycle_starting'  # 标识轮次开始
                        })
                        logger.info(f"✅ 连续测试计数更新事件已发送: 第{self.continuous_test_count}轮")
                    except Exception as e:
                        logger.error(f"❌ 发送连续测试计数更新事件失败: {e}")
                else:
                    logger.warning("⚠️ status_callback 为空，无法发送计数更新事件")

                # 🔧 修复：在每轮测试开始前进行全面的状态清理
                logger.info(f"🧹 第{self.continuous_test_count}轮测试：开始状态清理")
                if self.status_callback:
                    self.status_callback({
                        'action': 'continuous_test_cycle_cleanup',
                        'count': self.continuous_test_count,
                        'max_count': max_count if count_limit_enabled else 0
                    })

                # 记录本轮测试开始时间
                cycle_start_time = time.time()

                # 🔧 增强：重新检查连续测试模式状态（防止运行时被禁用）
                current_continuous_mode = test_config.get('continuous_mode', False)
                if not current_continuous_mode:
                    logger.info("⚠️ 连续测试模式已被禁用，停止测试")
                    break

                # 执行一轮测试
                logger.info(f"🎯 执行第{self.continuous_test_count}轮测试...")
                success = self._execute_single_test(test_config, enabled_channels)

                # 记录本轮测试结束时间和统计数据
                cycle_end_time = time.time()
                cycle_duration = cycle_end_time - cycle_start_time
                self.continuous_test_statistics['cycle_times'].append(cycle_duration)
                self.continuous_test_statistics['total_cycles'] = self.continuous_test_count

                # 收集本轮测试结果（如果有的话）
                self._collect_cycle_test_results(enabled_channels)

                if success:
                    logger.info(f"✅ 第{self.continuous_test_count}轮测试完成，耗时: {cycle_duration:.2f}秒")
                else:
                    logger.warning(f"⚠️ 第{self.continuous_test_count}轮测试失败，继续下一轮")

                # 🔧 修复：检查是否达到最大次数限制（在执行后检查）
                if count_limit_enabled and self.continuous_test_count >= max_count:
                    logger.info(f"✅ 连续测试已完成所有{max_count}轮测试")
                    # 完成统计数据收集
                    self.continuous_test_statistics['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
                    # 🔧 修复：发送最终计数更新事件
                    if self.status_callback:
                        self.status_callback({
                            'action': 'continuous_test_count_updated',
                            'count': self.continuous_test_count,
                            'max_count': max_count,
                            'status': 'completed'  # 标识测试完成
                        })
                        # 通知UI测试完成，包含统计数据
                        self.status_callback({
                            'action': 'continuous_test_completed',
                            'count': self.continuous_test_count,
                            'max_count': max_count,
                            'statistics': self.continuous_test_statistics
                        })
                    break

                # 等待间隔时间
                logger.info(f"⏱️ 等待{interval}秒后开始下一轮测试...")

                # 🔧 修复：将浮点数转换为整数，分段等待，以便及时响应停止信号
                interval_int = int(interval)  # 转换为整数
                for i in range(interval_int):
                    if self.stop_event.is_set():
                        logger.info("🛑 收到停止信号，退出连续测试")
                        break
                    time.sleep(1)
                    if i % 5 == 0 and i > 0:  # 每5秒记录一次等待状态
                        logger.debug(f"⏳ 等待中... 剩余{interval_int - i}秒")

                # 🔧 修复：如果有小数部分，额外等待
                decimal_part = interval - interval_int
                if decimal_part > 0 and not self.stop_event.is_set():
                    time.sleep(decimal_part)
                    logger.debug(f"⏳ 额外等待{decimal_part:.1f}秒完成")

            # 完成统计数据收集
            self.continuous_test_statistics['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S")

            # 🔧 修复：如果是手动停止且有测试数据，通知UI显示报告
            if self.stop_event.is_set() and self.continuous_test_count > 0:
                logger.info(f"🔍 连续测试被手动停止，但有{self.continuous_test_count}轮测试数据，通知UI显示报告")
                if self.status_callback:
                    self.status_callback({
                        'action': 'continuous_test_stopped',
                        'count': self.continuous_test_count,
                        'max_count': max_count if count_limit_enabled else 0,
                        'statistics': self.continuous_test_statistics
                    })

            logger.info(f"连续测试模式结束，共完成{self.continuous_test_count}轮测试")
            return True

        except Exception as e:
            logger.error(f"连续测试执行失败: {e}")
            return False

    def _collect_cycle_test_results(self, enabled_channels: List[int]):
        """收集本轮测试结果数据"""
        try:
            logger.debug(f"开始收集第{self.continuous_test_count}轮测试结果数据")

            # 从测试结果管理器获取最新的测试结果
            if hasattr(self.test_result_manager, 'get_latest_test_results'):
                logger.debug("调用 get_latest_test_results 方法")
                latest_results = self.test_result_manager.get_latest_test_results(enabled_channels)

                if latest_results:
                    logger.debug(f"获取到 {len(latest_results)} 个通道的真实测试结果")
                    for result in latest_results:
                        result['cycle'] = self.continuous_test_count
                        self.continuous_test_statistics['test_results'].append(result)
                        logger.debug(f"通道{result['channel']} - Rs={result['rs_value']:.3f}mΩ, Rct={result['rct_value']:.3f}mΩ")
                else:
                    logger.debug("get_latest_test_results 返回空结果，使用模拟数据")
                    self._create_mock_test_results(enabled_channels)
            else:
                logger.debug("测试结果管理器没有 get_latest_test_results 方法，使用模拟数据")
                self._create_mock_test_results(enabled_channels)

        except Exception as e:
            logger.error(f"收集测试结果数据失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            # 出错时使用模拟数据
            self._create_mock_test_results(enabled_channels)

    def _create_mock_test_results(self, enabled_channels: List[int]):
        """创建模拟测试结果数据（包含EIS频点数据）"""
        try:
            logger.warning(f"🔍 DEBUG: 创建模拟测试结果数据（包含EIS频点数据）")

            # 定义模拟测试频率
            frequencies = [0.01, 0.1, 1.0, 10.0, 100.0, 1000.0, 7800.0]

            for channel in enabled_channels:
                # 创建频点数据
                frequency_data = []

                for freq in frequencies:
                    # 模拟EIS阻抗数据
                    import random
                    import math

                    # 简化的电池阻抗模型：Rs + Rct/(1 + j*ω*Rct*C)
                    rs = 5.0 + random.gauss(0, 0.2)  # Rs约5mΩ，有小幅波动
                    rct = 10.0 + random.gauss(0, 0.5)  # Rct约10mΩ，有中等波动
                    c = 1e-3  # 电容约1mF

                    omega = 2 * math.pi * freq
                    z_rct = rct / (1 + 1j * omega * rct * c)
                    z_total = rs + z_rct

                    # 添加通道间差异
                    channel_factor = 1.0 + (channel - 4.5) * 0.02  # 通道间2%差异
                    z_total *= channel_factor

                    # 添加测试轮次的微小漂移
                    cycle_drift = 1.0 + (self.continuous_test_count - 3) * 0.005  # 轮次间0.5%漂移
                    z_total *= cycle_drift

                    real_part = z_total.real
                    imag_part = z_total.imag
                    magnitude = abs(z_total)
                    # 🔧 修复：相位角计算 - 不再对虚部取反，因为设备数据已经是正确符号
                    phase = math.degrees(math.atan2(imag_part, real_part))

                    frequency_data.append({
                        'frequency': freq,
                        'impedance_real': real_part,
                        'impedance_imag': imag_part,
                        'impedance_magnitude': magnitude,
                        'impedance_phase': phase
                    })

                # 计算Rs和Rct（从低频和高频阻抗估算）
                low_freq_z = frequency_data[0]['impedance_magnitude']  # 0.01Hz
                high_freq_z = frequency_data[-1]['impedance_magnitude']  # 7800Hz

                rs_value = high_freq_z  # 高频阻抗近似Rs
                rct_value = low_freq_z - high_freq_z  # 低频与高频差值近似Rct

                mock_result = {
                    'cycle': self.continuous_test_count,
                    'channel': channel,
                    'voltage': 3.7,  # 模拟电压
                    'rs_value': rs_value,  # 从阻抗数据计算的Rs值
                    'rct_value': rct_value,  # 从阻抗数据计算的Rct值
                    'rs_grade': 1,
                    'rct_grade': 2,
                    'is_pass': True,
                    'frequency_data': frequency_data,  # 🔧 新增：EIS频点数据
                    'timestamp': f'2025-01-31 10:{self.continuous_test_count:02d}:{channel*5:02d}'
                }
                self.continuous_test_statistics['test_results'].append(mock_result)
                logger.debug(f"模拟数据 - 通道{channel}: Rs={mock_result['rs_value']:.1f}mΩ, Rct={mock_result['rct_value']:.1f}mΩ, 频点数={len(frequency_data)}")
        except Exception as e:
            logger.error(f"创建模拟测试结果失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _wait_for_measurement_completion(self, channel_indices: List[int], frequency: float) -> bool:
        """
        等待测量完成（智能超时优化版本）

        Args:
            channel_indices: 通道索引列表（0-7）
            frequency: 测试频率

        Returns:
            是否测量完成
        """
        try:
            # 🚀 性能优化：使用智能超时管理器
            if not hasattr(self, 'smart_timeout_manager'):
                from utils.smart_timeout_manager import SmartTimeoutManager
                self.smart_timeout_manager = SmartTimeoutManager()

            # 🔧 增强：获取智能超时配置（考虑异常通道）
            # 计算最短超时时间（基于所有通道的状态）
            channel_timeouts = []
            for ch_idx in channel_indices:
                channel_num = ch_idx + 1
                if not self.exception_manager.is_channel_skipped(channel_num):
                    timeout, _ = self.smart_timeout_manager.get_smart_timeout(frequency, channel_num)
                    channel_timeouts.append(timeout)

            # 使用最短的超时时间，确保正常通道不被异常通道拖慢
            if channel_timeouts:
                measurement_timeout = min(channel_timeouts)
                logger.info(f"🎯 频点{frequency}Hz动态超时: {measurement_timeout:.1f}s (基于{len(channel_timeouts)}个正常通道)")
            else:
                # 如果所有通道都异常，使用默认超时
                measurement_timeout, _ = self.smart_timeout_manager.get_smart_timeout(frequency)
                logger.warning(f"🎯 频点{frequency}Hz所有通道异常，使用默认超时: {measurement_timeout:.1f}s")

            _, stability_check_interval = self.smart_timeout_manager.get_smart_timeout(frequency)
            measurement_start_time = time.time()
            last_progress_update = 0

            logger.info(f"🎯 频点{frequency}Hz智能超时: {measurement_timeout:.1f}s (稳定性检查间隔: {stability_check_interval:.1f}s)")

            while time.time() - measurement_start_time < measurement_timeout:
                if self.stop_event.is_set():
                    logger.info("测量被用户停止")
                    # 记录实际测量时间
                    actual_time = time.time() - measurement_start_time
                    self.smart_timeout_manager.record_measurement_time(frequency, actual_time)
                    return False

                # 🚀 性能优化：使用更快的进度更新间隔
                elapsed_time = time.time() - measurement_start_time

                # 每1秒更新一次进度（优化后的频率）
                current_time = int(elapsed_time)
                if current_time > last_progress_update:
                    last_progress_update = current_time
                    if self.progress_callback:
                        enabled_channels = [ch_idx + 1 for ch_idx in channel_indices]
                        for channel_num in enabled_channels:
                            # 🔧 优化：先检查通道是否已被标记为异常，避免不必要的查询
                            if self.exception_manager.is_channel_skipped(channel_num):
                                # 异常通道显示异常状态，不更新进度
                                exception_info = self.exception_manager.get_exception_info(channel_num)
                                if exception_info:
                                    self.progress_callback(channel_num, {
                                        'state': 'exception',
                                        'progress': 0,
                                        'message': f'通道异常: {exception_info.error_message}',
                                        'exception_type': exception_info.exception_type.value,
                                        'status_code': exception_info.status_code,
                                        'error_message': exception_info.error_message,
                                        'frequency': frequency
                                    })
                                continue  # 跳过异常通道的进度更新

                            # 对于正常通道，进行状态检查
                            channel_idx = channel_num - 1
                            status = self.comm_manager.get_measurement_status(channel_idx)

                            # 检查通道异常状态
                            is_channel_normal = self.exception_manager.check_channel_status(
                                channel_num, status, frequency
                            )

                            if not is_channel_normal:
                                # 新检测到的异常通道，跳过进度更新
                                continue

                            # 使用当前测试的频点索引
                            frequency_index = getattr(self, 'current_frequency_index', 1)

                            # 获取总频点数
                            try:
                                frequencies = self.test_config_manager.get_frequencies()
                                total_frequencies = len(frequencies) if frequencies else 20
                            except:
                                total_frequencies = 20

                            # 🔧 修复：使用总测试时间进度而不是频点进度
                            total_test_time = self.progress_manager.get_test_elapsed_time()
                            estimated_total_time = self.progress_manager.estimated_test_duration

                            if total_test_time > 0 and estimated_total_time > 0:
                                # 基于总测试时间的进度
                                time_progress = min(95.0, (total_test_time / estimated_total_time) * 100)
                                continuous_progress = time_progress
                            else:
                                # 备用：使用进度管理器计算
                                if hasattr(self, 'progress_manager') and self.progress_manager:
                                    continuous_progress = self.progress_manager.calculate_channel_progress(
                                        channel_num, frequency_index, total_frequencies,
                                        elapsed_time, measurement_timeout, frequency_completed=False,
                                        total_test_time=total_test_time, estimated_total_time=estimated_total_time
                                    )
                                else:
                                    # 最后备用：基于测量时间的简单进度
                                    if measurement_timeout > 0:
                                        within_freq_progress = min(0.8, elapsed_time / measurement_timeout)
                                        continuous_progress = 5 + within_freq_progress * 90
                                    else:
                                        continuous_progress = 5

                            # 读取当前通道电压
                            voltage = self.device_config_manager.read_channel_voltage(channel_num)
                            if voltage is None:
                                voltage = 0.0

                            self.progress_callback(channel_num, {
                                'state': 'measuring',
                                'progress': int(continuous_progress),
                                'message': f'测量中: {frequency}Hz ({elapsed_time:.1f}s)',
                                'frequency': frequency,
                                'frequency_index': frequency_index,
                                'total_frequencies': total_frequencies,
                                'voltage': voltage,
                                'elapsed_time': elapsed_time,
                                'continuous_progress': True,
                                'time_based_progress': True  # 标记为基于时间的进度
                            })

                            # 调试日志
                            if int(elapsed_time) % 5 == 0:  # 每5秒打印一次
                                logger.debug(f"通道{channel_num}测量进度: {continuous_progress:.1f}% (已用时{elapsed_time:.1f}s)")

                # 🚀 性能优化：检查所有启用通道的测量状态
                all_completed = True
                status_info = []
                current_measurements = []
                normal_channels = []  # 正常通道列表

                for ch_idx in channel_indices:
                    channel_num = ch_idx + 1

                    # 🔧 优化：先检查通道是否已被标记为异常，避免不必要的SCPI查询
                    if self.exception_manager.is_channel_skipped(channel_num):
                        logger.debug(f"通道{channel_num}已被跳过，不查询状态")
                        continue

                    # 🔧 新增：检查智能超时管理器中的异常通道
                    if hasattr(self, 'smart_timeout_manager'):
                        if self.smart_timeout_manager.should_fast_skip_channel(channel_num):
                            logger.warning(f"通道{channel_num}连续异常，快速跳过")
                            self.exception_manager.skipped_channels.add(channel_num)
                            continue

                    status = self.comm_manager.get_measurement_status(ch_idx)
                    status_info.append(f"CH{channel_num}:0x{status:04X}")

                    # 🔧 增强：通道异常检测（包含电压和响应时间信息）
                    voltage = None
                    response_time = None
                    try:
                        voltage = self.device_config_manager.read_channel_voltage(channel_num)
                        response_time = (time.time() - measurement_start_time) * 1000  # 转换为毫秒
                    except Exception as e:
                        logger.debug(f"读取通道{channel_num}额外信息失败: {e}")

                    is_channel_normal = self.exception_manager.check_channel_status(
                        channel_num, status, frequency, voltage, response_time
                    )

                    if not is_channel_normal:
                        # 通道异常，更新智能超时管理器
                        if hasattr(self, 'smart_timeout_manager'):
                            if status == 0x0003:  # 电池错误
                                self.smart_timeout_manager.mark_channel_as_exception(channel_num, "电池异常")
                            elif voltage and (voltage < 2.0 or voltage > 5.0):
                                self.smart_timeout_manager.mark_channel_as_contact_poor(channel_num, "接触不良")
                            else:
                                self.smart_timeout_manager.mark_channel_as_exception(channel_num, f"状态异常(0x{status:04X})")

                        logger.warning(f"通道{channel_num}异常，跳过测试")
                        continue

                    normal_channels.append(ch_idx)

                    if status != 0x0006:  # 0x0006表示测量完成
                        all_completed = False

                # 🛠️ 增强数据验证：只对正常通道收集并验证测量数据
                for ch_idx in normal_channels:
                    try:
                        impedance_data = self.comm_manager.read_impedance_data(ch_idx, frequency)
                        if impedance_data:
                            real_part = impedance_data.get('real', 0)
                            imag_part = impedance_data.get('imag', 0)

                            # 🛠️ 数据有效性检查：确保数据不为0且在合理范围内
                            if (real_part != 0 or imag_part != 0) and abs(real_part) < 1000 and abs(imag_part) < 1000:
                                current_measurements.append(complex(real_part, imag_part))
                            else:
                                logger.warning(f"通道{ch_idx+1}频点{frequency}Hz数据异常: real={real_part}, imag={imag_part}")
                    except Exception as e:
                        logger.warning(f"通道{ch_idx+1}频点{frequency}Hz数据读取失败: {e}")

                # 🔧 增强：检查是否所有正常通道都完成了测试
                if len(normal_channels) == 0:
                    # 所有通道都异常，记录异常信息并返回完成
                    exception_summary = self.exception_manager.get_exception_summary()
                    timeout_summary = self.smart_timeout_manager.get_exception_summary()

                    logger.warning(f"频点{frequency}Hz所有通道都异常，跳过该频点")
                    logger.warning(f"  异常通道: {exception_summary['skipped_channels']}")
                    logger.warning(f"  超时管理器异常通道: {timeout_summary['exception_channels']}")
                    logger.warning(f"  接触不良通道: {timeout_summary['contact_poor_channels']}")

                    # 通知UI更新所有异常通道的状态
                    for channel_num in range(1, 9):
                        if self.exception_manager.is_channel_skipped(channel_num):
                            exception_info = self.exception_manager.get_exception_info(channel_num)
                            if exception_info and self.progress_callback:
                                self.progress_callback(channel_num, {
                                    'state': 'exception',
                                    'progress': 0,
                                    'message': f'异常跳过: {exception_info.error_message}',
                                    'exception_type': exception_info.exception_type.value,
                                    'error_message': exception_info.error_message
                                })

                    return True

                # 🛠️ 暂时禁用早期完成检测，确保数据完整性
                # 注释掉早期完成检测逻辑，等待完整的测量周期
                # if (elapsed_time >= stability_check_interval and
                #     elapsed_time - last_stability_check >= stability_check_interval and
                #     current_measurements):
                #
                #     recent_measurements.extend(current_measurements)
                #     if len(recent_measurements) > 10:  # 保持最近10个测量值
                #         recent_measurements = recent_measurements[-10:]
                #
                #     # 检查稳定性
                #     if self.smart_timeout_manager.check_measurement_stability(frequency, recent_measurements):
                #         logger.info(f"🎯 频点{frequency}Hz提前完成 - 数据稳定 (用时: {elapsed_time:.1f}s)")
                #         actual_time = time.time() - measurement_start_time
                #         self.smart_timeout_manager.record_measurement_time(frequency, actual_time)
                #         return True
                #
                #     last_stability_check = elapsed_time

                # 每3秒打印一次状态信息（优化后的频率）
                if int(elapsed_time) % 3 == 0 and int(elapsed_time) != int(elapsed_time - 0.1):
                    normal_status = [f"CH{ch+1}:0x{self.comm_manager.get_measurement_status(ch):04X}" for ch in normal_channels]
                    logger.debug(f"频点{frequency}Hz正常通道测量状态: {', '.join(normal_status)}")

                if all_completed:
                    normal_status = [f"CH{ch+1}:0x{self.comm_manager.get_measurement_status(ch):04X}" for ch in normal_channels]
                    logger.info(f"频点{frequency}Hz测量完成 - 正常通道状态: {', '.join(normal_status)}")

                    # 🔧 增强：输出详细的异常通道总结
                    exception_summary = self.exception_manager.get_exception_summary()
                    timeout_summary = self.smart_timeout_manager.get_exception_summary()

                    if exception_summary['total_exceptions'] > 0 or timeout_summary['total_exception_channels'] > 0:
                        logger.info(f"频点{frequency}Hz异常通道总结:")
                        logger.info(f"  异常管理器跳过: {exception_summary['skipped_channels']}")
                        logger.info(f"  超时管理器异常: {timeout_summary['exception_channels']}")
                        logger.info(f"  接触不良通道: {timeout_summary['contact_poor_channels']}")

                        # 🔧 新增：为异常通道记录超时时间
                        for channel_num in exception_summary['skipped_channels']:
                            if hasattr(self, 'smart_timeout_manager'):
                                self.smart_timeout_manager.record_measurement_time(frequency, measurement_timeout)

                    # 🔧 增强：只为正常通道记录实际测量时间
                    actual_time = time.time() - measurement_start_time
                    self.smart_timeout_manager.record_measurement_time(frequency, actual_time)

                    # 🔧 新增：更新正常通道的进度为100%
                    for ch_idx in normal_channels:
                        channel_num = ch_idx + 1
                        if self.progress_callback:
                            self.progress_callback(channel_num, {
                                'state': 'measurement_completed',
                                'progress': 100,
                                'message': f'频点{frequency}Hz测量完成',
                                'frequency': frequency,
                                'measurement_time': actual_time
                            })

                    return True

                time.sleep(0.1)  # 🛠️ 保守调整：100ms轮询间隔（确保数据稳定性）

            logger.warning(f"频点{frequency}Hz测量超时 (超时时间: {measurement_timeout:.1f}s)")
            # 记录超时时间
            actual_time = time.time() - measurement_start_time
            self.smart_timeout_manager.record_measurement_time(frequency, actual_time)
            return False

        except Exception as e:
            logger.error(f"等待测量完成失败: {e}")
            return False

    def _process_test_completion(self, channel_indices: List[int], test_config: Dict[str, Any]):
        """
        处理测试完成后的EIS分析和结果显示（修复版）

        Args:
            channel_indices: 通道索引列表（0-7）
            test_config: 测试配置
        """
        try:
            logger.info("🔧 [修复版] 开始处理测试完成后的EIS分析...")

            # 🔧 修复：强制刷新阻抗数据管理器，确保数据最新
            logger.info("🔧 强制刷新阻抗数据...")
            if hasattr(self.impedance_data_manager, 'refresh_data'):
                self.impedance_data_manager.refresh_data()

            # 🔧 增强：只处理正常通道的测试结果
            normal_channels = []
            exception_channels = []

            for ch_idx in channel_indices:
                channel_num = ch_idx + 1
                if self.exception_manager.is_channel_skipped(channel_num):
                    exception_channels.append(channel_num)
                else:
                    normal_channels.append(ch_idx)

            logger.info(f"🔧 测试结果处理: 正常通道{len(normal_channels)}个, 异常通道{len(exception_channels)}个")
            if exception_channels:
                logger.info(f"🔧 异常通道列表: {exception_channels}")

            # 🔧 修复：确保每个正常通道都有完整的测试结果
            results_processed = 0
            for ch_idx in normal_channels:
                channel_num = ch_idx + 1
                
                try:
                    logger.info(f"🔧 开始处理通道{channel_num}的测试结果...")

                    # 🔧 修复：多次尝试计算Rs和Rct值，确保数据可用
                    rs_value, rct_value = None, None
                    for attempt in range(3):  # 最多尝试3次
                        try:
                            rs_value, rct_value = self.test_result_manager.calculate_rs_rct_for_channel(channel_num)
                            if rs_value > 0 and rct_value > 0:
                                logger.info(f"🔧 通道{channel_num} 第{attempt+1}次尝试成功: Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ")
                                break
                            else:
                                logger.warning(f"🔧 通道{channel_num} 第{attempt+1}次尝试得到无效值: Rs={rs_value}, Rct={rct_value}")
                        except Exception as e:
                            logger.warning(f"🔧 通道{channel_num} 第{attempt+1}次计算Rs/Rct失败: {e}")
                        
                        if attempt < 2:  # 不是最后一次尝试
                            import time
                            time.sleep(0.5)  # 等待0.5秒后重试

                    # 如果仍然无法获取有效值，使用默认值
                    if rs_value is None or rs_value <= 0:
                        rs_value = 5.0
                        logger.warning(f"🔧 通道{channel_num} 使用默认Rs值: {rs_value}mΩ")
                    if rct_value is None or rct_value <= 0:
                        rct_value = 10.0
                        logger.warning(f"🔧 通道{channel_num} 使用默认Rct值: {rct_value}mΩ")

                    # 🔧 修复：多次尝试获取真实电压
                    voltage = None
                    for attempt in range(3):
                        try:
                            voltage = self.device_config_manager.read_channel_voltage(channel_num)
                            if voltage is not None and voltage > 0:
                                logger.info(f"🔧 通道{channel_num} 第{attempt+1}次获取电压成功: {voltage:.3f}V")
                                break
                            else:
                                logger.warning(f"🔧 通道{channel_num} 第{attempt+1}次获取电压无效: {voltage}")
                        except Exception as e:
                            logger.warning(f"🔧 通道{channel_num} 第{attempt+1}次获取电压失败: {e}")
                        
                        if attempt < 2:
                            import time
                            time.sleep(0.2)

                    # 如果无法获取电压，使用默认值
                    if voltage is None or voltage <= 0:
                        voltage = 3.7
                        logger.warning(f"🔧 通道{channel_num} 使用默认电压: {voltage}V")

                    # 🔧 修复：确保档位计算正确
                    try:
                        rs_grade, rct_grade = self.test_result_manager.calculate_grades(rs_value, rct_value)
                        logger.info(f"🔧 通道{channel_num} 档位计算成功: Rs档位={rs_grade}, Rct档位={rct_grade}")
                    except Exception as e:
                        logger.error(f"🔧 通道{channel_num} 档位计算失败: {e}")
                        rs_grade, rct_grade = 1, 1  # 使用默认档位

                    # 🔧 修复：获取离群率检测结果
                    outlier_result = None
                    try:
                        # 首先检查离群检测是否启用
                        from backend.outlier_detection_manager import OutlierDetectionManager
                        outlier_manager = OutlierDetectionManager()
                        config = outlier_manager.get_detection_config()

                        if config.get('is_enabled', False):
                            # 离群检测已启用，尝试获取偏差数据
                            if hasattr(self.impedance_data_manager, 'channel_frequency_deviations'):
                                frequency_deviations = self.impedance_data_manager.channel_frequency_deviations.get(channel_num, {})

                                if frequency_deviations:
                                    # 有偏差数据，计算最终离群率结果
                                    threshold = config.get('deviation_threshold', 10.0)
                                    max_deviation = max(frequency_deviations.values())

                                    if max_deviation <= threshold:
                                        outlier_result = "PASS"
                                    else:
                                        outlier_result = f"{max_deviation:.1f}%"

                                    logger.info(f"🔧 通道{channel_num} 离群率检测结果: {outlier_result} (最大偏差: {max_deviation:.1f}%, 阈值: {threshold:.1f}%)")
                                else:
                                    # 没有偏差数据，但离群检测已启用
                                    outlier_result = "无数据"
                                    logger.warning(f"🔧 通道{channel_num} 离群检测已启用但无偏差数据")
                            else:
                                # 阻抗数据管理器没有偏差数据属性
                                outlier_result = "无数据"
                                logger.warning(f"🔧 通道{channel_num} 阻抗数据管理器缺少偏差数据属性")
                        else:
                            # 离群检测未启用
                            outlier_result = "已禁用"
                            logger.debug(f"🔧 通道{channel_num} 离群检测未启用")
                    except Exception as e:
                        logger.error(f"🔧 通道{channel_num} 获取离群率检测结果失败: {e}")
                        outlier_result = "检测失败"

                    # 🔧 修复：确保合格性判断正确（包含离群率检测结果）
                    try:
                        is_pass, fail_items = self.test_result_manager.judge_test_result(voltage, rs_value, rct_value, outlier_result, channel_num)
                        logger.info(f"🔧 通道{channel_num} 合格性判断: {'合格' if is_pass else '不合格'}, 失败项目: {fail_items}, 离群率: {outlier_result}")
                    except Exception as e:
                        logger.error(f"🔧 通道{channel_num} 合格性判断失败: {e}")
                        is_pass, fail_items = True, []  # 默认合格

                    # 🔧 修复：生成失败原因
                    fail_reason = ''
                    if not is_pass and fail_items:
                        fail_reason = self.test_result_manager.generate_fail_reason(fail_items)

                    # 🔧 修复：获取完整的离群率数据用于数据库保存
                    frequency_deviations_for_db = {}
                    baseline_filename_for_db = ""
                    baseline_id_for_db = None
                    max_deviation_percent_for_db = None

                    try:
                        if hasattr(self.impedance_data_manager, 'channel_frequency_deviations'):
                            frequency_deviations_for_db = self.impedance_data_manager.channel_frequency_deviations.get(channel_num, {})

                            # 计算最大偏差百分比
                            if frequency_deviations_for_db:
                                max_deviation_percent_for_db = max(frequency_deviations_for_db.values())

                                # 获取基准信息
                                from backend.outlier_detection_manager import OutlierDetectionManager
                                outlier_manager = OutlierDetectionManager()
                                config = outlier_manager.get_detection_config()

                                if config.get('active_baseline_id'):
                                    baseline_id_for_db = config['active_baseline_id']
                                    baseline_filename_for_db = f"基准_{baseline_id_for_db}"

                                logger.info(f"🔧 通道{channel_num} 离群率数据库保存信息: 最大偏差={max_deviation_percent_for_db:.2f}%, 基准ID={baseline_id_for_db}")
                    except Exception as e:
                        logger.error(f"🔧 通道{channel_num} 获取离群率数据库保存信息失败: {e}")

                    # 🔧 修复：准备完整的结果数据（包含离群率信息）
                    result_data = {
                        'voltage': voltage,
                        'rs_value': rs_value,
                        'rct_value': rct_value,
                        'rs_grade': rs_grade,
                        'rct_grade': rct_grade,
                        'is_pass': is_pass,
                        'fail_items': fail_items,
                        'fail_reason': fail_reason,
                        'outlier_result': outlier_result,  # 🔧 离群率检测结果
                        'baseline_filename': baseline_filename_for_db,  # 🔧 基准文件名
                        'baseline_id': baseline_id_for_db,  # 🔧 基准ID
                        'max_deviation_percent': max_deviation_percent_for_db,  # 🔧 最大偏差百分比
                        'frequency_deviations': frequency_deviations_for_db,  # 🔧 频点偏差数据
                        'state': 'completed',
                        'channel_num': channel_num,
                        'test_timestamp': datetime.now().isoformat()
                    }

                    # 🔧 修复：确保数据库保存成功
                    try:
                        self.test_result_manager.save_test_result(channel_num, result_data, test_config)
                        logger.info(f"🔧 通道{channel_num} 测试结果已保存到数据库")
                    except Exception as e:
                        logger.error(f"🔧 通道{channel_num} 保存测试结果失败: {e}")

                    # 🔧 修复：强制发送结果回调，确保UI更新（包含离群率信息）
                    if self.progress_callback:
                        # 🔧 获取频点偏差数据用于UI显示
                        frequency_deviations = {}
                        if hasattr(self.impedance_data_manager, 'channel_frequency_deviations'):
                            frequency_deviations = self.impedance_data_manager.channel_frequency_deviations.get(channel_num, {})
                            logger.debug(f"🔧 通道{channel_num} 获取频点偏差数据: {len(frequency_deviations)}个频点, 数据: {frequency_deviations}")
                        else:
                            logger.warning(f"🔧 通道{channel_num} 阻抗数据管理器没有channel_frequency_deviations属性")

                        callback_data = {
                            'state': 'completed',
                            'progress': 100,
                            'message': '测试完成',
                            'voltage': voltage,
                            'rs_value': rs_value,
                            'rct_value': rct_value,
                            'rs_grade': rs_grade,
                            'rct_grade': rct_grade,
                            'is_pass': is_pass,
                            'fail_items': fail_items,
                            'fail_reason': fail_reason,  # 🔧 使用生成的失败原因
                            'outlier_result': outlier_result,  # 🔧 添加离群率检测结果
                            'frequency_deviations': frequency_deviations,  # 🔧 添加频点偏差数据
                            'result_data': result_data,
                            'force_update': True,  # 🔧 强制UI更新标志
                            'test_completion': True,  # 🔧 测试完成标志
                            'test_count': getattr(self, 'test_count', 1),  # 🔧 测试次数标志
                            'calculation_completed': True,  # 🔧 新增：标记计算已完成
                            'ready_for_print': True  # 🔧 新增：标记可以打印
                        }

                        logger.info(f"🔧 通道{channel_num} 发送完成回调数据: outlier_result={outlier_result}, frequency_deviations={len(frequency_deviations)}个频点")

                        try:
                            # 🔧 修复：只发送一次completed回调，避免重复统计
                            logger.info(f"🔧 通道{channel_num} 准备发送结果回调: {'合格' if is_pass else '不合格'}, Rs={rs_grade}, Rct={rct_grade}")
                            
                            # 🔧 添加回调去重标记
                            callback_data['callback_id'] = f"test_completion_{channel_num}_{int(datetime.now().timestamp())}"
                            callback_data['single_callback'] = True  # 标记为单次回调
                            
                            self.progress_callback(channel_num, callback_data)
                            logger.info(f"✅ 通道{channel_num} 结果回调发送成功")

                            # 🔧 修复：延迟发送打印就绪信号，使用不同状态避免重复统计
                            import time
                            time.sleep(0.3)  # 稍微延长延迟确保UI处理完成
                            
                            print_ready_callback = {
                                'state': 'print_ready',  # 专用状态，不触发统计
                                'progress': 100,
                                'message': '数据计算完成，可以打印',
                                'print_trigger': True,
                                'callback_id': f"print_ready_{channel_num}_{int(datetime.now().timestamp())}",
                                'no_statistics_update': True,  # 明确标记不更新统计
                                # 包含必要的打印数据
                                'voltage': voltage,
                                'rs_value': rs_value,
                                'rct_value': rct_value,
                                'rs_grade': rs_grade,
                                'rct_grade': rct_grade,
                                'is_pass': is_pass,
                                'outlier_result': outlier_result,
                                'calculation_completed': True,
                                'ready_for_print': True
                            }
                            
                            self.progress_callback(channel_num, print_ready_callback)
                            logger.info(f"✅ 通道{channel_num} 打印就绪信号发送成功（不触发统计更新）")

                        except Exception as e:
                            logger.error(f"🔧 通道{channel_num} 结果回调发送失败: {e}")
                            # 🔧 修复：错误恢复回调使用专用状态，不触发统计
                            try:
                                error_recovery_callback = {
                                    'state': 'error_recovery',  # 专用状态，不触发统计
                                    'progress': 100,
                                    'is_pass': is_pass,
                                    'rs_grade': rs_grade,
                                    'rct_grade': rct_grade,
                                    'calculation_completed': True,
                                    'ready_for_print': True,
                                    'error_recovery': True,
                                    'no_statistics_update': True,  # 明确标记不更新统计
                                    'callback_id': f"error_recovery_{channel_num}_{int(datetime.now().timestamp())}"
                                }
                                self.progress_callback(channel_num, error_recovery_callback)
                                logger.info(f"🔧 通道{channel_num} 错误恢复回调发送成功（不触发统计更新）")
                            except Exception as e2:
                                logger.error(f"🔧 通道{channel_num} 错误恢复回调也失败: {e2}")

                    results_processed += 1
                    logger.info(f"🔧 通道{channel_num}测试结果处理完成: Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ, "
                               f"档位={rs_grade}-{rct_grade}, 结果={'合格' if is_pass else '不合格'}")

                except Exception as e:
                    logger.error(f"🔧 处理通道{channel_num}测试结果时发生错误: {e}")
                    import traceback
                    logger.error(f"🔧 详细错误: {traceback.format_exc()}")

            logger.info(f"🔧 [修复版] 测试结果处理完成，成功处理{results_processed}个通道")

            # 🚫 离群检测功能已暂时禁用
            # 发送最终离群率结果（批量测试完成后）
            # try:
            #     if hasattr(self.impedance_data_manager, 'channel_frequency_deviations'):
            #         frequency_deviations = self.impedance_data_manager.channel_frequency_deviations.get(channel_num, {})
            #
            #         if frequency_deviations:
            #             # 计算最终离群率结果
            #             from backend.outlier_detection_manager import OutlierDetectionManager
            #             outlier_manager = OutlierDetectionManager()
            #             config = outlier_manager.get_detection_config()
            #
            #             if config.get('is_enabled', False):
            #                 threshold = config.get('deviation_threshold', 10.0)
            #                 max_deviation = max(frequency_deviations.values())
            #
            #                 if max_deviation <= threshold:
            #                     final_outlier_result = "PASS"
            #                 else:
            #                     final_outlier_result = f"{max_deviation:.1f}%"
            #
            #                 # 发送最终离群率结果到UI
            #                 callback_data = {
            #                     'state': 'final_outlier_rate_update',
            #                     'outlier_result': final_outlier_result,
            #                     'baseline_filename': f"基准_{config['active_baseline_id']}.json",
            #                     'frequency_deviations': frequency_deviations.copy(),
            #                     'max_deviation': max_deviation,
            #                     'threshold': threshold
            #                 }
            #                 logger.debug(f"通道{channel_num} 发送最终离群率UI更新")
            #                 if self.progress_callback:
            #                     self.progress_callback(channel_num, callback_data)
            #                     logger.debug(f"通道{channel_num} 最终离群率UI更新发送成功")
            #
            # except Exception as e:
            #     logger.error(f"发送通道{channel_num}最终离群率结果失败: {e}")

            # 🔧 新增：处理异常通道的结果显示
            for channel_num in exception_channels:
                try:
                    exception_info = self.exception_manager.get_exception_info(channel_num)
                    if exception_info:
                        # 获取异常原因的中文描述
                        if exception_info.exception_type.value == 'contact_poor':
                            fail_reason = '不合格-接触不良'
                        elif exception_info.exception_type.value == 'battery_error':
                            fail_reason = '不合格-电池异常'
                        else:
                            fail_reason = f'不合格-{exception_info.error_message}'

                        # 准备异常通道的结果数据
                        exception_result_data = {
                            'voltage': exception_info.voltage_when_detected or 0.0,
                            'rs_value': 0.0,
                            'rct_value': 0.0,
                            'rs_grade': '--',
                            'rct_grade': '--',
                            'is_pass': False,
                            'fail_items': ['异常'],
                            'fail_reason': fail_reason,
                            'state': 'exception',
                            'exception_type': exception_info.exception_type.value,
                            'error_message': exception_info.error_message
                        }

                        # 保存异常通道结果到数据库
                        self.test_result_manager.save_test_result(channel_num, exception_result_data, test_config)

                        # 发送异常通道结果回调
                        if self.progress_callback:
                            self.progress_callback(channel_num, {
                                'state': 'completed',
                                'progress': 100,
                                'message': fail_reason,
                                'voltage': exception_result_data['voltage'],
                                'rs_value': 0.0,
                                'rct_value': 0.0,
                                'rs_grade': '--',
                                'rct_grade': '--',
                                'is_pass': False,
                                'fail_items': exception_result_data['fail_items'],
                                'fail_reason': fail_reason,
                                'result_data': exception_result_data
                            })

                        logger.info(f"通道{channel_num}异常结果: {fail_reason}")

                except Exception as e:
                    logger.error(f"处理通道{channel_num}异常结果失败: {e}")

        except Exception as e:
            logger.error(f"处理测试完成失败: {e}")

    
    def _is_callback_already_sent(self, callback_id: str) -> bool:
        """
        检查回调是否已经发送过

        Args:
            callback_id: 回调ID

        Returns:
            是否已发送
        """
        with self._callback_lock:
            if callback_id in self._sent_callbacks:
                return True
            self._sent_callbacks.add(callback_id)
            # 限制记录数量，避免内存泄漏
            if len(self._sent_callbacks) > 1000:
                # 清理一半的记录
                old_callbacks = list(self._sent_callbacks)[:500]
                for old_id in old_callbacks:
                    self._sent_callbacks.discard(old_id)
            return False

    def _send_callback_with_dedup(self, channel_num: int, callback_data: dict):
        """
        发送带去重检查的回调

        Args:
            channel_num: 通道号
            callback_data: 回调数据
        """
        try:
            callback_id = callback_data.get('callback_id')
            if not callback_id:
                # 如果没有ID，生成一个
                callback_id = f"auto_{channel_num}_{callback_data.get('state', 'unknown')}_{int(datetime.now().timestamp())}"
                callback_data['callback_id'] = callback_id

            # 检查是否已发送
            if self._is_callback_already_sent(callback_id):
                logger.warning(f"🔧 通道{channel_num} 回调{callback_id}已发送过，跳过重复发送")
                return False

            # 发送回调
            if self.progress_callback:
                self.progress_callback(channel_num, callback_data)
                logger.debug(f"🔧 通道{channel_num} 回调{callback_id}发送成功")
                return True
            else:
                logger.warning(f"🔧 通道{channel_num} progress_callback为空，无法发送回调")
                return False

        except Exception as e:
            logger.error(f"🔧 通道{channel_num} 发送去重回调失败: {e}")
            return False

    def get_execution_status(self) -> Dict[str, Any]:
        """
        获取执行状态

        Returns:
            执行状态字典
        """
        return {
            'is_stopped': self.stop_event.is_set(),
            'startup_stats': self.startup_strategy_manager.get_startup_stats(),
            'impedance_data_summary': self.impedance_data_manager.get_impedance_data_summary(),
            'batch_info': self.test_result_manager.get_batch_info()
        }

    def stop_execution(self):
        """停止执行（调用增强版本）"""
        self.enhanced_stop_execution()

    def original_stop_execution(self):
        """停止测试执行"""
        try:
            logger.info("🛑 测试执行器开始停止测试...")

            # 🔧 修复1：设置停止事件
            self.stop_event.set()
            logger.info("🛑 测试执行停止信号已发送")

            # 🔧 修复2：立即停止设备测试
            if hasattr(self, 'comm_manager') and self.comm_manager:
                logger.info("🛑 正在停止设备测试...")
                # 停止所有通道的测试
                all_channels = list(range(8))  # 0-7对应通道1-8
                stop_success = self.comm_manager.stop_impedance_measurement(all_channels)
                if stop_success:
                    logger.info("✅ 设备测试已成功停止")
                else:
                    logger.warning("⚠️ 设备测试停止失败，但软件停止信号已发送")
            else:
                logger.warning("⚠️ 通信管理器不可用，仅发送软件停止信号")

            # 🔧 修复3：停止所有测试管理器（如果存在）
            self._stop_active_test_managers()

            logger.info("✅ 测试执行器停止完成")

        except Exception as e:
            logger.error(f"停止测试执行时发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 确保停止事件被设置
            self.stop_event.set()

    def _stop_active_test_managers(self):
        """停止活跃的测试管理器"""
        try:
            logger.debug("🛑 停止活跃的测试管理器...")

            # 停止并行错频测试管理器（如果存在）
            if hasattr(self, '_current_staggered_manager') and self._current_staggered_manager:
                try:
                    self._current_staggered_manager.stop_test()
                    logger.info("✅ 并行错频测试管理器已停止")
                except Exception as e:
                    logger.error(f"停止并行错频测试管理器失败: {e}")

            # 停止同时测试管理器（如果存在）
            if hasattr(self, '_current_simultaneous_manager') and self._current_simultaneous_manager:
                try:
                    self._current_simultaneous_manager.stop_simultaneous_test()
                    logger.info("✅ 同时测试管理器已停止")
                except Exception as e:
                    logger.error(f"停止同时测试管理器失败: {e}")

        except Exception as e:
            logger.error(f"停止测试管理器失败: {e}")

    def reset_execution(self):
        """重置执行状态"""
        self.stop_event.clear()
        self.startup_strategy_manager.reset_startup_stats()
        logger.info("测试执行状态已重置")
    def enhanced_stop_execution(self):
        """增强的停止执行方法"""
        try:
            logger.info("🛑 [增强版] 测试执行器开始停止...")
            
            # 1. 立即设置停止标志
            self.is_testing = False
            if hasattr(self, 'stop_event'):
                self.stop_event.set()
            
            # 2. 强制停止所有活跃的测试管理器
            self._enhanced_stop_active_test_managers()
            
            # 3. 停止设备测试
            self._enhanced_stop_device_test()
            
            # 4. 清理执行状态
            self._enhanced_cleanup_execution_state()
            
            logger.info("✅ [增强版] 测试执行器停止完成")
            
        except Exception as e:
            logger.error(f"❌ [增强版] 测试执行器停止失败: {e}")
    
    def _enhanced_stop_active_test_managers(self):
        """增强的活跃测试管理器停止"""
        try:
            logger.info("🛑 强制停止所有活跃测试管理器...")
            
            # 停止并行错频测试管理器
            if hasattr(self, '_current_staggered_manager') and self._current_staggered_manager:
                try:
                    self._current_staggered_manager.stop_test()
                    logger.info("✅ 并行错频测试管理器已停止")
                except Exception as e:
                    logger.error(f"停止并行错频测试管理器失败: {e}")
                finally:
                    self._current_staggered_manager = None
            
            # 停止同时测试管理器
            if hasattr(self, '_current_simultaneous_manager') and self._current_simultaneous_manager:
                try:
                    self._current_simultaneous_manager.stop_simultaneous_test()
                    logger.info("✅ 同时测试管理器已停止")
                except Exception as e:
                    logger.error(f"停止同时测试管理器失败: {e}")
                finally:
                    self._current_simultaneous_manager = None
            
            # 停止其他可能的管理器
            if hasattr(self, '_active_managers'):
                for manager in list(self._active_managers):
                    try:
                        if hasattr(manager, 'stop_test'):
                            manager.stop_test()
                        elif hasattr(manager, 'stop'):
                            manager.stop()
                        logger.info(f"✅ 管理器 {type(manager).__name__} 已停止")
                    except Exception as e:
                        logger.error(f"停止管理器 {type(manager).__name__} 失败: {e}")
                self._active_managers.clear()
            
        except Exception as e:
            logger.error(f"强制停止活跃测试管理器失败: {e}")
    
    def _enhanced_stop_device_test(self):
        """增强的设备测试停止"""
        try:
            logger.info("🛑 强制停止设备测试...")
            
            if hasattr(self, 'comm_manager') and self.comm_manager:
                # 停止所有通道的测试
                all_channels = list(range(8))
                try:
                    stop_result = self.comm_manager.stop_impedance_measurement(all_channels)
                    logger.info(f"设备测试停止结果: {stop_result}")
                except Exception as e:
                    logger.error(f"停止设备测试失败: {e}")
                
                # 额外的设备停止操作
                try:
                    if hasattr(self.comm_manager, 'emergency_stop'):
                        self.comm_manager.emergency_stop()
                        logger.info("✅ 设备紧急停止完成")
                except Exception as e:
                    logger.debug(f"设备紧急停止失败（可能不支持）: {e}")
            
        except Exception as e:
            logger.error(f"强制停止设备测试失败: {e}")
    
    def _enhanced_cleanup_execution_state(self):
        """增强的执行状态清理"""
        try:
            logger.info("🧹 清理执行状态...")
            
            # 重置所有状态标志
            self.is_testing = False
            if hasattr(self, 'current_state'):
                self.current_state = 'idle'
            
            # 清理管理器引用
            if hasattr(self, '_current_staggered_manager'):
                self._current_staggered_manager = None
            if hasattr(self, '_current_simultaneous_manager'):
                self._current_simultaneous_manager = None
            if hasattr(self, '_active_managers'):
                self._active_managers.clear()
            
            # 清理其他可能的状态
            if hasattr(self, '_test_results'):
                self._test_results.clear()
            if hasattr(self, '_test_progress'):
                self._test_progress.clear()
            
            logger.info("✅ 执行状态清理完成")
            
        except Exception as e:
            logger.error(f"清理执行状态失败: {e}")
