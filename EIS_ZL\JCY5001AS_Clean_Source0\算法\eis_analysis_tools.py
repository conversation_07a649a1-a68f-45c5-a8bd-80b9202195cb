#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EIS电化学阻抗谱数据分析工具
用于对比不同算法的性能和精度
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from scipy import signal
import warnings
warnings.filterwarnings('ignore')

class EISAnalyzer:
    """EIS数据分析器"""
    
    def __init__(self):
        self.data = None
        self.fitted_params = {}
        
    def load_data(self, file_path=None, data=None):
        """加载EIS数据"""
        if file_path:
            # 支持多种文件格式
            if file_path.endswith('.csv'):
                self.data = pd.read_csv(file_path)
            elif file_path.endswith('.xlsx'):
                self.data = pd.read_excel(file_path)
            else:
                raise ValueError("不支持的文件格式")
        elif data is not None:
            self.data = pd.DataFrame(data)
        else:
            raise ValueError("请提供数据文件路径或数据")
            
        # 标准化列名
        self.standardize_columns()
        
    def standardize_columns(self):
        """标准化列名"""
        column_mapping = {
            'frequency': 'freq',
            'freq_hz': 'freq',
            'real': 'z_real',
            'imag': 'z_imag',
            'impedance_real': 'z_real',
            'impedance_imag': 'z_imag',
            'phase': 'phase_deg'
        }
        
        # 转换为小写并映射
        self.data.columns = self.data.columns.str.lower()
        self.data = self.data.rename(columns=column_mapping)
        
    def calculate_impedance_magnitude(self):
        """计算阻抗幅值"""
        if 'z_real' in self.data.columns and 'z_imag' in self.data.columns:
            self.data['z_mag'] = np.sqrt(self.data['z_real']**2 + self.data['z_imag']**2)
            
    def calculate_phase(self):
        """计算相位角"""
        if 'z_real' in self.data.columns and 'z_imag' in self.data.columns:
            self.data['phase_calc'] = np.arctan2(-self.data['z_imag'], self.data['z_real']) * 180 / np.pi
            
    def kramers_kronig_test(self):
        """Kramers-Kronig一致性检验"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        freq = self.data['freq'].values
        z_real = self.data['z_real'].values
        z_imag = self.data['z_imag'].values
        
        # 简化的KK检验
        # 实际应用中需要更复杂的积分计算
        omega = 2 * np.pi * freq
        
        # 计算KK变换的近似值
        z_real_kk = np.zeros_like(z_real)
        z_imag_kk = np.zeros_like(z_imag)
        
        for i, w in enumerate(omega):
            # 简化计算，实际需要更精确的积分
            mask = omega != w
            if np.any(mask):
                z_real_kk[i] = np.trapz(z_imag[mask] * omega[mask] / (omega[mask]**2 - w**2), omega[mask]) * 2/np.pi
                z_imag_kk[i] = -np.trapz(z_real[mask] * w / (omega[mask]**2 - w**2), omega[mask]) * 2/np.pi
        
        # 计算残差
        real_residual = np.abs(z_real - z_real_kk) / np.abs(z_real) * 100
        imag_residual = np.abs(z_imag - z_imag_kk) / np.abs(z_imag) * 100
        
        return {
            'real_residual_percent': real_residual,
            'imag_residual_percent': imag_residual,
            'mean_real_error': np.mean(real_residual),
            'mean_imag_error': np.mean(imag_residual)
        }
    
    def randles_circuit(self, freq, Rs, Rct, CPE_T, CPE_n):
        """Randles等效电路模型"""
        omega = 2 * np.pi * freq
        
        # CPE阻抗
        Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
        
        # 并联阻抗 (Rct || CPE)
        Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
        
        # 总阻抗
        Z_total = Rs + Z_parallel
        
        return Z_total
    
    def fit_randles_circuit(self, initial_guess=None):
        """拟合Randles电路"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        freq = self.data['freq'].values
        z_complex = self.data['z_real'].values + 1j * self.data['z_imag'].values
        
        if initial_guess is None:
            # 自动估算初值
            Rs_guess = np.min(self.data['z_real'])
            Rct_guess = np.max(self.data['z_real']) - Rs_guess
            CPE_T_guess = 1e-6
            CPE_n_guess = 0.8
            initial_guess = [Rs_guess, Rct_guess, CPE_T_guess, CPE_n_guess]
        
        def objective_function(freq, Rs, Rct, CPE_T, CPE_n):
            z_model = self.randles_circuit(freq, Rs, Rct, CPE_T, CPE_n)
            return np.concatenate([z_model.real, z_model.imag])
        
        z_data = np.concatenate([self.data['z_real'].values, self.data['z_imag'].values])
        
        try:
            popt, pcov = curve_fit(objective_function, freq, z_data, p0=initial_guess, maxfev=5000)
            
            # 计算拟合质量
            z_fitted = self.randles_circuit(freq, *popt)
            residuals = np.abs(z_complex - z_fitted)
            rmse = np.sqrt(np.mean(residuals**2))
            
            self.fitted_params['randles'] = {
                'Rs': popt[0],
                'Rct': popt[1], 
                'CPE_T': popt[2],
                'CPE_n': popt[3],
                'rmse': rmse,
                'covariance': pcov
            }
            
            return self.fitted_params['randles']
            
        except Exception as e:
            print(f"拟合失败: {e}")
            return None
    
    def plot_nyquist(self, show_fit=True):
        """绘制Nyquist图"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        plt.figure(figsize=(10, 8))
        
        # 原始数据
        plt.plot(self.data['z_real'], -self.data['z_imag'], 'bo-', label='实验数据', markersize=6)
        
        # 拟合数据
        if show_fit and 'randles' in self.fitted_params:
            freq = self.data['freq'].values
            params = self.fitted_params['randles']
            z_fit = self.randles_circuit(freq, params['Rs'], params['Rct'], 
                                       params['CPE_T'], params['CPE_n'])
            plt.plot(z_fit.real, -z_fit.imag, 'r-', label='Randles拟合', linewidth=2)
        
        plt.xlabel('Z\' (Ω)')
        plt.ylabel('-Z\'\' (Ω)')
        plt.title('Nyquist图')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.axis('equal')
        plt.show()
    
    def plot_bode(self):
        """绘制Bode图"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))
        
        # 幅值图
        self.calculate_impedance_magnitude()
        ax1.loglog(self.data['freq'], self.data['z_mag'], 'bo-', markersize=6)
        ax1.set_xlabel('频率 (Hz)')
        ax1.set_ylabel('|Z| (Ω)')
        ax1.set_title('阻抗幅值 vs 频率')
        ax1.grid(True, alpha=0.3)
        
        # 相位图
        self.calculate_phase()
        ax2.semilogx(self.data['freq'], self.data['phase_calc'], 'ro-', markersize=6)
        ax2.set_xlabel('频率 (Hz)')
        ax2.set_ylabel('相位角 (°)')
        ax2.set_title('相位角 vs 频率')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def algorithm_comparison_report(self):
        """生成算法对比报告"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        report = {
            'data_points': len(self.data),
            'frequency_range': f"{self.data['freq'].min():.2e} - {self.data['freq'].max():.2e} Hz",
            'impedance_range': f"{self.data['z_real'].min():.2f} - {self.data['z_real'].max():.2f} Ω"
        }
        
        # KK检验结果
        try:
            kk_results = self.kramers_kronig_test()
            report['kk_test'] = {
                'mean_real_error': f"{kk_results['mean_real_error']:.2f}%",
                'mean_imag_error': f"{kk_results['mean_imag_error']:.2f}%"
            }
        except:
            report['kk_test'] = "计算失败"
        
        # 拟合结果
        if 'randles' in self.fitted_params:
            params = self.fitted_params['randles']
            report['randles_fit'] = {
                'Rs': f"{params['Rs']:.3f} Ω",
                'Rct': f"{params['Rct']:.3f} Ω", 
                'CPE_T': f"{params['CPE_T']:.2e} F·s^(n-1)",
                'CPE_n': f"{params['CPE_n']:.3f}",
                'RMSE': f"{params['rmse']:.3f} Ω"
            }
        
        return report

# 使用示例
if __name__ == "__main__":
    print("EIS分析工具已准备就绪")
    print("使用方法:")
    print("1. analyzer = EISAnalyzer()")
    print("2. analyzer.load_data('your_data.csv')")
    print("3. analyzer.fit_randles_circuit()")
    print("4. analyzer.plot_nyquist()")
    print("5. report = analyzer.algorithm_comparison_report()")