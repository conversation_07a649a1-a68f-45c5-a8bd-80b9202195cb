#!/usr/bin/env python3
"""
打印机配置修复脚本
用于解决更换打印机后的配置问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.config_manager import ConfigManager
from utils.printer_diagnostic_tool import PrinterDiagnosticTool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主修复流程"""
    print("🔧 打印机配置修复工具")
    print("=" * 50)
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建诊断工具
        diagnostic_tool = PrinterDiagnosticTool(config_manager)
        
        # 运行诊断
        print("🔍 正在诊断打印机配置...")
        results = diagnostic_tool.run_full_diagnostic()
        
        # 显示诊断报告
        print("\n" + diagnostic_tool.print_diagnostic_report())
        
        # 检查是否有可自动修复的问题
        recommendations = results.get('recommendations', [])
        auto_fix_recommendations = [
            r for r in recommendations 
            if r.get('action') in [
                'update_printer_config', 
                'use_niimbot_printer', 
                'configure_available_printer'
            ]
        ]
        
        if not auto_fix_recommendations:
            print("✅ 没有发现可自动修复的问题")
            return
        
        # 显示修复选项
        print("\n🔧 可用的修复选项:")
        for i, rec in enumerate(auto_fix_recommendations, 1):
            print(f"  {i}. {rec['title']}")
            print(f"     {rec['description']}")
            if 'printer_name' in rec:
                print(f"     打印机: {rec['printer_name']}")
            print()
        
        # 询问用户是否应用修复
        while True:
            choice = input("请选择要应用的修复 (输入数字，0=全部应用，q=退出): ").strip().lower()
            
            if choice == 'q':
                print("👋 退出修复工具")
                return
            
            try:
                if choice == '0':
                    # 应用所有修复
                    print("\n🔧 应用所有修复...")
                    success_count = 0
                    
                    for rec in auto_fix_recommendations:
                        action = rec.get('action')
                        printer_name = rec.get('printer_name')
                        
                        if printer_name:
                            success = diagnostic_tool.apply_fix(action, printer_name=printer_name)
                            if success:
                                print(f"✅ {rec['title']} - 修复成功")
                                success_count += 1
                            else:
                                print(f"❌ {rec['title']} - 修复失败")
                        else:
                            print(f"⚠️ {rec['title']} - 缺少打印机名称，跳过")
                    
                    print(f"\n📊 修复完成: {success_count}/{len(auto_fix_recommendations)} 个问题已修复")
                    break
                
                else:
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(auto_fix_recommendations):
                        # 应用单个修复
                        rec = auto_fix_recommendations[choice_idx]
                        action = rec.get('action')
                        printer_name = rec.get('printer_name')
                        
                        print(f"\n🔧 应用修复: {rec['title']}")
                        
                        if printer_name:
                            success = diagnostic_tool.apply_fix(action, printer_name=printer_name)
                            if success:
                                print(f"✅ 修复成功")
                            else:
                                print(f"❌ 修复失败")
                        else:
                            print(f"⚠️ 缺少打印机名称，无法修复")
                        
                        # 询问是否继续
                        continue_choice = input("\n是否继续修复其他问题? (y/n): ").strip().lower()
                        if continue_choice != 'y':
                            break
                    else:
                        print("❌ 无效的选择，请重新输入")
                        continue
            
            except ValueError:
                print("❌ 请输入有效的数字")
                continue
        
        # 运行验证诊断
        print("\n🔍 验证修复结果...")
        verification_results = diagnostic_tool.run_full_diagnostic()
        
        # 检查是否还有问题
        remaining_issues = [
            r for r in verification_results.get('recommendations', [])
            if r.get('level') in ['error', 'warning']
        ]
        
        if remaining_issues:
            print(f"\n⚠️ 仍有 {len(remaining_issues)} 个问题需要手动处理:")
            for issue in remaining_issues:
                level_icon = {"error": "🔴", "warning": "🟡"}.get(issue['level'], "ℹ️")
                print(f"  {level_icon} {issue['title']}")
                print(f"     {issue['description']}")
        else:
            print("\n✅ 所有问题已解决！打印机配置正常")
        
        # 显示当前配置
        current_printer = config_manager.get('printer.name', '')
        if current_printer:
            print(f"\n📋 当前配置的打印机: {current_printer}")
        else:
            print("\n⚠️ 当前没有配置打印机")
        
    except Exception as e:
        logger.error(f"修复工具运行失败: {e}")
        import traceback
        traceback.print_exc()
        print(f"\n❌ 修复工具运行失败: {e}")
        print("请检查日志文件获取详细错误信息")


def show_manual_steps():
    """显示手动修复步骤"""
    print("\n📖 手动修复步骤:")
    print("=" * 30)
    print("1. 检查打印机连接:")
    print("   - 确认USB线连接正常")
    print("   - 检查打印机电源是否开启")
    print("   - 验证打印机在Windows设备管理器中显示正常")
    print()
    print("2. 更新打印机驱动:")
    print("   - 下载最新的NIIMBOT打印机驱动")
    print("   - 卸载旧驱动后重新安装")
    print("   - 重启计算机")
    print()
    print("3. 检查打印机设置:")
    print("   - 在Windows设置中将打印机设为默认")
    print("   - 测试打印Windows测试页")
    print("   - 确认打印机状态为'就绪'")
    print()
    print("4. 重新配置系统:")
    print("   - 运行此修复工具")
    print("   - 或在系统设置中手动选择正确的打印机")
    print()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        show_manual_steps()
