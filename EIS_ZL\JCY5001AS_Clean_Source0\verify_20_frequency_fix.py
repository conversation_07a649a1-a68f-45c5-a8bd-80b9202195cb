#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证20频点修复脚本
确认研究模式能够测试所有20个频点
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.test_config_manager import TestConfigManager

def verify_config_file():
    """验证配置文件"""
    print("🔍 1. 验证配置文件...")
    print("-" * 60)
    
    try:
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置
        test_mode = config.get('test_params', {}).get('test_mode', 'N/A')
        preset_mode = config.get('frequency', {}).get('preset_mode', 'N/A')
        frequency_list = config.get('frequency', {}).get('list', [])
        use_parallel_staggered = config.get('test', {}).get('use_parallel_staggered_mode', False)
        
        print(f"   • test_params.test_mode: {test_mode}")
        print(f"   • frequency.preset_mode: {preset_mode}")
        print(f"   • frequency.list: {len(frequency_list)}个频点")
        print(f"   • test.use_parallel_staggered_mode: {use_parallel_staggered}")
        
        # 验证关键配置
        issues = []
        if test_mode != 'simultaneous':
            issues.append(f"test_mode应该是'simultaneous'，当前是'{test_mode}'")
        
        if preset_mode != '研究模式':
            issues.append(f"preset_mode应该是'研究模式'，当前是'{preset_mode}'")
        
        if len(frequency_list) != 20:
            issues.append(f"frequency.list应该有20个频点，当前有{len(frequency_list)}个")
        
        if use_parallel_staggered:
            issues.append("use_parallel_staggered_mode应该是false")
        
        if issues:
            print("   ❌ 配置文件存在问题:")
            for issue in issues:
                print(f"      - {issue}")
            return False
        else:
            print("   ✅ 配置文件验证通过")
            return True
            
    except Exception as e:
        print(f"   ❌ 读取配置文件失败: {e}")
        return False

def verify_config_manager():
    """验证ConfigManager"""
    print("\n🔍 2. 验证ConfigManager...")
    print("-" * 60)
    
    try:
        config_manager = ConfigManager()
        
        # 检查关键配置
        test_mode = config_manager.get('test_params.test_mode', 'N/A')
        preset_mode = config_manager.get('frequency.preset_mode', 'N/A')
        frequency_list = config_manager.get('frequency.list', [])
        use_parallel_staggered = config_manager.get('test.use_parallel_staggered_mode', False)
        
        print(f"   • test_params.test_mode: {test_mode}")
        print(f"   • frequency.preset_mode: {preset_mode}")
        print(f"   • frequency.list: {len(frequency_list)}个频点")
        print(f"   • test.use_parallel_staggered_mode: {use_parallel_staggered}")
        
        # 验证关键配置
        issues = []
        if test_mode != 'simultaneous':
            issues.append(f"test_mode应该是'simultaneous'，当前是'{test_mode}'")
        
        if preset_mode != '研究模式':
            issues.append(f"preset_mode应该是'研究模式'，当前是'{preset_mode}'")
        
        if len(frequency_list) != 20:
            issues.append(f"frequency.list应该有20个频点，当前有{len(frequency_list)}个")
        
        if use_parallel_staggered:
            issues.append("use_parallel_staggered_mode应该是false")
        
        if issues:
            print("   ❌ ConfigManager存在问题:")
            for issue in issues:
                print(f"      - {issue}")
            return False
        else:
            print("   ✅ ConfigManager验证通过")
            return True
            
    except Exception as e:
        print(f"   ❌ ConfigManager验证失败: {e}")
        return False

def verify_test_config_manager():
    """验证TestConfigManager"""
    print("\n🔍 3. 验证TestConfigManager...")
    print("-" * 60)
    
    try:
        config_manager = ConfigManager()
        test_config_manager = TestConfigManager(config_manager)
        
        # 加载测试配置
        test_config = test_config_manager.load_test_config()
        
        # 检查关键配置
        test_mode = test_config.get('test_mode', 'N/A')
        frequencies = test_config.get('frequencies', [])
        use_parallel_staggered = test_config.get('use_parallel_staggered_mode', False)
        
        print(f"   • test_mode: {test_mode}")
        print(f"   • frequencies: {len(frequencies)}个频点")
        print(f"   • use_parallel_staggered_mode: {use_parallel_staggered}")
        
        # 验证关键配置
        issues = []
        if test_mode != '研究模式':
            issues.append(f"test_mode应该是'研究模式'，当前是'{test_mode}'")
        
        if len(frequencies) != 20:
            issues.append(f"frequencies应该有20个频点，当前有{len(frequencies)}个")
        
        if use_parallel_staggered:
            issues.append("use_parallel_staggered_mode应该是false")
        
        if issues:
            print("   ❌ TestConfigManager存在问题:")
            for issue in issues:
                print(f"      - {issue}")
            return False
        else:
            print("   ✅ TestConfigManager验证通过")
            print(f"   📊 频点列表前5个: {frequencies[:5]}")
            print(f"   📊 频点列表后5个: {frequencies[-5:]}")
            return True
            
    except Exception as e:
        print(f"   ❌ TestConfigManager验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 验证20频点修复")
    print("=" * 80)
    
    # 执行验证
    config_file_ok = verify_config_file()
    config_manager_ok = verify_config_manager()
    test_config_manager_ok = verify_test_config_manager()
    
    # 总结
    print("\n🎯 验证结果总结")
    print("=" * 80)
    
    if config_file_ok and config_manager_ok and test_config_manager_ok:
        print("🎉 所有验证通过！")
        print("✅ 研究模式现在应该能够测试所有20个频点")
        print("✅ 并行错频模式已正确禁用")
        print("✅ 配置一致性检查通过")
        print("\n📋 下一步:")
        print("   1. 重新启动应用程序")
        print("   2. 选择研究模式进行测试")
        print("   3. 验证是否测试了所有20个频点")
        print("   4. 测试时间应该约为3-4分钟")
        return True
    else:
        print("❌ 验证失败！")
        print("   请检查上述错误信息并修复配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
