#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析提供的EIS数据
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 提供的数据
data_raw = """Frequency (Hz)	虚部mΩ	虚部mΩ
100.000030517578	2.20E-01	-4.55E-02
79.4328994750977	2.25E-01	-2.91E-02
63.0956497192383	2.31E-01	-1.60E-02
50.1187973022461	2.37E-01	-6.84E-03
39.810676574707	2.43E-01	-7.44E-04
31.622766494751	2.48E-01	3.78E-03
25.1188793182373	2.52E-01	7.34E-03
19.9526309967041	2.56E-01	9.10E-03
15.8489456176758	2.59E-01	9.80E-03
12.5892715454102	2.62E-01	1.03E-02
10.0000076293945	2.65E-01	1.07E-02
7.94327640533447	2.67E-01	1.08E-02
6.30957412719727	2.69E-01	1.05E-02
5.0118727684021	2.71E-01	1.07E-02
3.98107433319092	2.72E-01	1.07E-02
3.16227889060974	2.74E-01	1.05E-02
2.51188802719116	2.76E-01	1.05E-02
1.99526083469391	2.77E-01	1.08E-02
1.58489120006561	2.78E-01	1.10E-02
1.25892543792725	2.80E-01	1.17E-02
1.00000083446503	2.81E-01	1.18E-02
0.794327080249786	2.83E-01	1.21E-02
0.630957663059235	2.85E-01	1.30E-02
0.501187026500702	2.87E-01	1.35E-02
0.398107141256332	2.89E-01	1.43E-02
0.31622776389122	2.91E-01	1.55E-02
0.251188218593597	2.93E-01	1.66E-02
0.199526086449623	2.96E-01	1.86E-02
0.158489406108856	2.99E-01	2.03E-02
0.125892534852028	3.02E-01	2.24E-02
0.100000008940697	3.06E-01	2.50E-02"""

def parse_data():
    """解析提供的数据"""
    lines = data_raw.strip().split('\n')
    
    # 解析数据
    freq_list = []
    real_list = []
    imag_list = []
    
    for line in lines[1:]:  # 跳过标题行
        parts = line.split('\t')
        if len(parts) == 3:
            freq = float(parts[0])
            real_mohm = float(parts[1])  # mΩ
            imag_mohm = float(parts[2])  # mΩ
            
            # 转换为Ω
            real_ohm = real_mohm / 1000
            imag_ohm = imag_mohm / 1000
            
            freq_list.append(freq)
            real_list.append(real_ohm)
            imag_list.append(imag_ohm)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'freq': freq_list,
        'z_real': real_list,
        'z_imag': imag_list
    })
    
    # 计算衍生参数
    df['z_mag'] = np.sqrt(df['z_real']**2 + df['z_imag']**2)
    df['phase_deg'] = np.arctan2(-df['z_imag'], df['z_real']) * 180 / np.pi
    df['omega'] = 2 * np.pi * df['freq']
    
    return df

def randles_circuit_model(freq, Rs, Rct, CPE_T, CPE_n):
    """Randles等效电路模型"""
    omega = 2 * np.pi * freq
    
    # CPE阻抗: Z_CPE = 1 / (T * (jω)^n)
    Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
    
    # 并联阻抗: Rct || CPE
    Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
    
    # 总阻抗: Rs + (Rct || CPE)
    Z_total = Rs + Z_parallel
    
    return Z_total

def fit_equivalent_circuit(df):
    """拟合等效电路"""
    freq = df['freq'].values
    z_complex = df['z_real'].values + 1j * df['z_imag'].values
    
    # 初值估算
    Rs_guess = np.min(df['z_real'])
    Rct_guess = np.max(df['z_real']) - Rs_guess
    CPE_T_guess = 0.001  # 调整初值
    CPE_n_guess = 0.8
    initial_guess = [Rs_guess, Rct_guess, CPE_T_guess, CPE_n_guess]
    
    print(f"初始猜测值:")
    print(f"Rs = {Rs_guess:.6f} Ω")
    print(f"Rct = {Rct_guess:.6f} Ω")
    print(f"CPE_T = {CPE_T_guess:.6f}")
    print(f"CPE_n = {CPE_n_guess:.6f}")
    
    def objective_function(freq, Rs, Rct, CPE_T, CPE_n):
        z_model = randles_circuit_model(freq, Rs, Rct, CPE_T, CPE_n)
        return np.concatenate([z_model.real, z_model.imag])
    
    z_data = np.concatenate([df['z_real'].values, df['z_imag'].values])
    
    try:
        # 设置参数边界
        bounds = ([0, 0, 1e-6, 0.1], [1, 1, 1, 1])
        
        popt, pcov = curve_fit(objective_function, freq, z_data, 
                             p0=initial_guess, maxfev=10000, bounds=bounds)
        
        # 计算拟合质量
        z_fitted = randles_circuit_model(freq, *popt)
        residuals = np.abs(z_complex - z_fitted)
        rmse = np.sqrt(np.mean(residuals**2))
        
        # 计算R²
        ss_res = np.sum(residuals**2)
        ss_tot = np.sum(np.abs(z_complex - np.mean(z_complex))**2)
        r_squared = 1 - (ss_res / ss_tot)
        
        # 计算参数标准误差
        param_errors = np.sqrt(np.diag(pcov))
        
        results = {
            'Rs': popt[0],
            'Rct': popt[1],
            'CPE_T': popt[2],
            'CPE_n': popt[3],
            'rmse': rmse,
            'r_squared': r_squared,
            'fitted_impedance': z_fitted,
            'param_errors': param_errors,
            'covariance': pcov
        }
        
        print(f"\n=== 等效电路拟合结果 ===")
        print(f"Rs (溶液电阻): {popt[0]:.6f} ± {param_errors[0]:.6f} Ω")
        print(f"Rct (电荷转移电阻): {popt[1]:.6f} ± {param_errors[1]:.6f} Ω")
        print(f"CPE_T (常相位元件): {popt[2]:.6e} ± {param_errors[2]:.6e} F·s^(n-1)")
        print(f"CPE_n (常相位指数): {popt[3]:.6f} ± {param_errors[3]:.6f}")
        print(f"RMSE: {rmse:.6f} Ω")
        print(f"R²: {r_squared:.6f}")
        
        # 计算总阻抗和时间常数
        total_resistance = popt[0] + popt[1]
        time_constant = popt[1] * popt[2]
        
        print(f"\n=== 衍生参数 ===")
        print(f"总阻抗 (Rs + Rct): {total_resistance:.6f} Ω")
        print(f"时间常数 (Rct × CPE_T): {time_constant:.6e} s")
        
        # 找到特征频率（虚部最大值对应的频率）
        max_imag_idx = np.argmax(-df['z_imag'])
        characteristic_freq = df['freq'].iloc[max_imag_idx]
        print(f"特征频率: {characteristic_freq:.3f} Hz")
        
        return results
        
    except Exception as e:
        print(f"拟合失败: {e}")
        return None

def plot_nyquist(df, fit_results=None):
    """绘制Nyquist图"""
    plt.figure(figsize=(10, 8))
    
    # 原始数据
    plt.plot(df['z_real'], -df['z_imag'], 'bo-', markersize=6, label='实验数据')
    
    # 拟合数据
    if fit_results:
        z_fit = fit_results['fitted_impedance']
        plt.plot(z_fit.real, -z_fit.imag, 'r-', linewidth=2, label='Randles拟合')
    
    plt.xlabel('Z\' (Ω)')
    plt.ylabel('-Z\'\' (Ω)')
    plt.title('EIS Nyquist图')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.axis('equal')
    
    # 添加频率标注
    for i in [0, len(df)//2, len(df)-1]:
        plt.annotate(f'{df.iloc[i]["freq"]:.2f} Hz', 
                    (df.iloc[i]['z_real'], -df.iloc[i]['z_imag']),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    plt.tight_layout()
    plt.show()

def plot_bode(df):
    """绘制Bode图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))
    
    # 幅值图
    ax1.loglog(df['freq'], df['z_mag'], 'bo-', markersize=6)
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('|Z| (Ω)')
    ax1.set_title('阻抗幅值 vs 频率')
    ax1.grid(True, alpha=0.3)
    
    # 相位图
    ax2.semilogx(df['freq'], df['phase_deg'], 'ro-', markersize=6)
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('相位角 (°)')
    ax2.set_title('相位角 vs 频率')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def analyze_data_quality(df):
    """分析数据质量"""
    print(f"\n=== 数据质量分析 ===")
    print(f"数据点数: {len(df)}")
    print(f"频率范围: {df['freq'].min():.3f} - {df['freq'].max():.3f} Hz")
    print(f"阻抗实部范围: {df['z_real'].min():.6f} - {df['z_real'].max():.6f} Ω")
    print(f"阻抗虚部范围: {df['z_imag'].min():.6f} - {df['z_imag'].max():.6f} Ω")
    print(f"阻抗幅值范围: {df['z_mag'].min():.6f} - {df['z_mag'].max():.6f} Ω")
    print(f"相位角范围: {df['phase_deg'].min():.2f} - {df['phase_deg'].max():.2f} °")
    
    # 检查数据单调性
    freq_monotonic = df['freq'].is_monotonic_decreasing
    print(f"频率单调递减: {freq_monotonic}")
    
    # 检查是否有负实部（非物理）
    negative_real = (df['z_real'] < 0).any()
    print(f"存在负实部: {negative_real}")
    
    # 计算数据密度
    freq_log_spacing = np.diff(np.log10(df['freq']))
    avg_log_spacing = np.mean(np.abs(freq_log_spacing))
    print(f"平均对数频率间距: {avg_log_spacing:.3f}")

def kramers_kronig_test(df):
    """简化的Kramers-Kronig一致性检验"""
    print(f"\n=== Kramers-Kronig一致性检验 ===")
    
    freq = df['freq'].values
    z_real = df['z_real'].values
    z_imag = df['z_imag'].values
    omega = 2 * np.pi * freq
    
    # 简化的KK变换（仅作为示例）
    # 实际应用需要更精确的积分方法
    
    # 计算实部的相对变化
    real_variation = (np.max(z_real) - np.min(z_real)) / np.mean(z_real) * 100
    
    # 计算虚部的相对变化
    imag_variation = (np.max(z_imag) - np.min(z_imag)) / np.mean(np.abs(z_imag)) * 100
    
    print(f"实部相对变化: {real_variation:.2f}%")
    print(f"虚部相对变化: {imag_variation:.2f}%")
    
    # 检查高频和低频行为
    high_freq_real = z_real[0]  # 最高频率的实部
    low_freq_real = z_real[-1]  # 最低频率的实部
    
    print(f"高频实部 (100 Hz): {high_freq_real:.6f} Ω")
    print(f"低频实部 (0.1 Hz): {low_freq_real:.6f} Ω")
    print(f"实部增加: {(low_freq_real - high_freq_real):.6f} Ω")

def main():
    """主分析函数"""
    print("=== EIS数据分析开始 ===")
    
    # 解析数据
    df = parse_data()
    print(f"成功解析 {len(df)} 个数据点")
    
    # 显示数据概览
    print(f"\n数据概览:")
    print(df.head())
    print(f"\n数据统计:")
    print(df.describe())
    
    # 数据质量分析
    analyze_data_quality(df)
    
    # Kramers-Kronig检验
    kramers_kronig_test(df)
    
    # 等效电路拟合
    fit_results = fit_equivalent_circuit(df)
    
    # 绘图
    print(f"\n绘制Nyquist图...")
    plot_nyquist(df, fit_results)
    
    print(f"\n绘制Bode图...")
    plot_bode(df)
    
    return df, fit_results

if __name__ == "__main__":
    df, results = main()
    print(f"\n=== 分析完成 ===")
    print(f"数据已保存在变量 df 中")
    print(f"拟合结果已保存在变量 results 中")