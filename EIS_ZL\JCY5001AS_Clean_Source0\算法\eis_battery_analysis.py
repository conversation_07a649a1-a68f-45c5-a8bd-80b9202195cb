#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
314d电芯和7430mAh电芯EIS数据分析对比
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from scipy import signal
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BatteryEISComparison:
    """电池EIS数据对比分析器"""
    
    def __init__(self):
        self.data_314d = None
        self.data_7430mah = None
        self.analysis_results = {}
        
    def load_battery_data(self, file_314d=None, file_7430mah=None, data_314d=None, data_7430mah=None):
        """加载两种电芯的EIS数据"""
        
        if file_314d:
            try:
                self.data_314d = pd.read_excel(file_314d)
                print(f"成功加载314d电芯数据: {file_314d}")
                print(f"数据形状: {self.data_314d.shape}")
                print(f"列名: {list(self.data_314d.columns)}")
            except Exception as e:
                print(f"加载314d数据失败: {e}")
                
        if file_7430mah:
            try:
                self.data_7430mah = pd.read_excel(file_7430mah)
                print(f"成功加载7430mAh电芯数据: {file_7430mah}")
                print(f"数据形状: {self.data_7430mah.shape}")
                print(f"列名: {list(self.data_7430mah.columns)}")
            except Exception as e:
                print(f"加载7430mAh数据失败: {e}")
                
        # 如果直接提供数据
        if data_314d is not None:
            self.data_314d = pd.DataFrame(data_314d)
        if data_7430mah is not None:
            self.data_7430mah = pd.DataFrame(data_7430mah)
            
        # 标准化列名
        self._standardize_columns()
        
    def _standardize_columns(self):
        """标准化列名"""
        column_mapping = {
            'frequency': 'freq',
            'freq_hz': 'freq',
            'frequency_hz': 'freq',
            'real': 'z_real',
            'imag': 'z_imag',
            'impedance_real': 'z_real',
            'impedance_imag': 'z_imag',
            'real_ohm': 'z_real',
            'imag_ohm': 'z_imag',
            'phase': 'phase_deg',
            'phase_degree': 'phase_deg'
        }
        
        for data in [self.data_314d, self.data_7430mah]:
            if data is not None:
                # 转换为小写
                data.columns = data.columns.str.lower()
                # 应用映射
                data.rename(columns=column_mapping, inplace=True)
    
    def calculate_derived_parameters(self):
        """计算衍生参数"""
        for name, data in [('314d', self.data_314d), ('7430mAh', self.data_7430mah)]:
            if data is not None:
                # 阻抗幅值
                data['z_mag'] = np.sqrt(data['z_real']**2 + data['z_imag']**2)
                
                # 相位角（如果没有的话）
                if 'phase_deg' not in data.columns:
                    data['phase_deg'] = np.arctan2(-data['z_imag'], data['z_real']) * 180 / np.pi
                
                # 角频率
                data['omega'] = 2 * np.pi * data['freq']
                
                print(f"{name}电芯衍生参数计算完成")
    
    def randles_circuit_model(self, freq, Rs, Rct, CPE_T, CPE_n):
        """Randles等效电路模型"""
        omega = 2 * np.pi * freq
        
        # CPE阻抗: Z_CPE = 1 / (T * (jω)^n)
        Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
        
        # 并联阻抗: Rct || CPE
        Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
        
        # 总阻抗: Rs + (Rct || CPE)
        Z_total = Rs + Z_parallel
        
        return Z_total
    
    def fit_equivalent_circuit(self, battery_type='314d'):
        """拟合等效电路"""
        data = self.data_314d if battery_type == '314d' else self.data_7430mah
        
        if data is None:
            print(f"没有{battery_type}电芯数据")
            return None
            
        freq = data['freq'].values
        z_complex = data['z_real'].values + 1j * data['z_imag'].values
        
        # 初值估算
        Rs_guess = np.min(data['z_real'])
        Rct_guess = np.max(data['z_real']) - Rs_guess
        CPE_T_guess = 1e-6
        CPE_n_guess = 0.8
        initial_guess = [Rs_guess, Rct_guess, CPE_T_guess, CPE_n_guess]
        
        def objective_function(freq, Rs, Rct, CPE_T, CPE_n):
            z_model = self.randles_circuit_model(freq, Rs, Rct, CPE_T, CPE_n)
            return np.concatenate([z_model.real, z_model.imag])
        
        z_data = np.concatenate([data['z_real'].values, data['z_imag'].values])
        
        try:
            popt, pcov = curve_fit(objective_function, freq, z_data, 
                                 p0=initial_guess, maxfev=5000)
            
            # 计算拟合质量
            z_fitted = self.randles_circuit_model(freq, *popt)
            residuals = np.abs(z_complex - z_fitted)
            rmse = np.sqrt(np.mean(residuals**2))
            r_squared = 1 - np.sum(residuals**2) / np.sum(np.abs(z_complex - np.mean(z_complex))**2)
            
            results = {
                'battery_type': battery_type,
                'Rs': popt[0],
                'Rct': popt[1],
                'CPE_T': popt[2],
                'CPE_n': popt[3],
                'rmse': rmse,
                'r_squared': r_squared,
                'fitted_impedance': z_fitted,
                'covariance': pcov
            }
            
            self.analysis_results[battery_type] = results
            
            print(f"\n{battery_type}电芯等效电路拟合结果:")
            print(f"Rs (溶液电阻): {popt[0]:.4f} Ω")
            print(f"Rct (电荷转移电阻): {popt[1]:.4f} Ω")
            print(f"CPE_T (常相位元件): {popt[2]:.2e} F·s^(n-1)")
            print(f"CPE_n (常相位指数): {popt[3]:.4f}")
            print(f"RMSE: {rmse:.4f} Ω")
            print(f"R²: {r_squared:.4f}")
            
            return results
            
        except Exception as e:
            print(f"{battery_type}电芯拟合失败: {e}")
            return None
    
    def compare_algorithms_performance(self):
        """对比不同算法性能"""
        if not self.analysis_results:
            print("请先进行等效电路拟合")
            return
            
        print("\n=== 算法性能对比分析 ===")
        
        comparison = {}
        
        for battery_type, results in self.analysis_results.items():
            # 计算特征频率
            data = self.data_314d if battery_type == '314d' else self.data_7430mah
            
            # 找到阻抗虚部最大值对应的频率（特征频率）
            max_imag_idx = np.argmax(-data['z_imag'])
            characteristic_freq = data['freq'].iloc[max_imag_idx]
            
            # 计算时间常数
            tau = results['Rct'] * results['CPE_T']
            
            # 计算总阻抗
            total_resistance = results['Rs'] + results['Rct']
            
            comparison[battery_type] = {
                'Rs': results['Rs'],
                'Rct': results['Rct'],
                'total_R': total_resistance,
                'CPE_T': results['CPE_T'],
                'CPE_n': results['CPE_n'],
                'tau': tau,
                'char_freq': characteristic_freq,
                'rmse': results['rmse'],
                'r_squared': results['r_squared']
            }
        
        # 创建对比表格
        df_comparison = pd.DataFrame(comparison).T
        print("\n电芯参数对比:")
        print(df_comparison.round(6))
        
        # 性能差异分析
        if '314d' in comparison and '7430mAh' in comparison:
            print("\n=== 性能差异分析 ===")
            
            # 电阻比较
            rs_ratio = comparison['7430mAh']['Rs'] / comparison['314d']['Rs']
            rct_ratio = comparison['7430mAh']['Rct'] / comparison['314d']['Rct']
            
            print(f"溶液电阻比值 (7430mAh/314d): {rs_ratio:.3f}")
            print(f"电荷转移电阻比值 (7430mAh/314d): {rct_ratio:.3f}")
            
            # 容量相关分析
            print(f"\n容量相关分析:")
            print(f"314d电芯总阻抗: {comparison['314d']['total_R']:.4f} Ω")
            print(f"7430mAh电芯总阻抗: {comparison['7430mAh']['total_R']:.4f} Ω")
            
            # 时间常数比较
            tau_ratio = comparison['7430mAh']['tau'] / comparison['314d']['tau']
            print(f"时间常数比值 (7430mAh/314d): {tau_ratio:.3f}")
        
        return comparison
    
    def plot_nyquist_comparison(self):
        """绘制Nyquist图对比"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        
        colors = {'314d': 'blue', '7430mAh': 'red'}
        
        # 子图1: 314d电芯
        if self.data_314d is not None:
            ax1.plot(self.data_314d['z_real'], -self.data_314d['z_imag'], 
                    'o-', color=colors['314d'], markersize=4, label='实验数据')
            
            if '314d' in self.analysis_results:
                z_fit = self.analysis_results['314d']['fitted_impedance']
                ax1.plot(z_fit.real, -z_fit.imag, '-', color='orange', 
                        linewidth=2, label='Randles拟合')
            
            ax1.set_xlabel('Z\' (Ω)')
            ax1.set_ylabel('-Z\'\' (Ω)')
            ax1.set_title('314d电芯 Nyquist图')
            ax1.grid(True, alpha=0.3)
            ax1.legend()
            ax1.axis('equal')
        
        # 子图2: 7430mAh电芯
        if self.data_7430mah is not None:
            ax2.plot(self.data_7430mah['z_real'], -self.data_7430mah['z_imag'], 
                    'o-', color=colors['7430mAh'], markersize=4, label='实验数据')
            
            if '7430mAh' in self.analysis_results:
                z_fit = self.analysis_results['7430mAh']['fitted_impedance']
                ax2.plot(z_fit.real, -z_fit.imag, '-', color='orange', 
                        linewidth=2, label='Randles拟合')
            
            ax2.set_xlabel('Z\' (Ω)')
            ax2.set_ylabel('-Z\'\' (Ω)')
            ax2.set_title('7430mAh电芯 Nyquist图')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            ax2.axis('equal')
        
        # 子图3: 对比图
        if self.data_314d is not None:
            ax3.plot(self.data_314d['z_real'], -self.data_314d['z_imag'], 
                    'o-', color=colors['314d'], markersize=4, label='314d电芯')
        
        if self.data_7430mah is not None:
            ax3.plot(self.data_7430mah['z_real'], -self.data_7430mah['z_imag'], 
                    's-', color=colors['7430mAh'], markersize=4, label='7430mAh电芯')
        
        ax3.set_xlabel('Z\' (Ω)')
        ax3.set_ylabel('-Z\'\' (Ω)')
        ax3.set_title('两种电芯对比')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        ax3.axis('equal')
        
        plt.tight_layout()
        plt.show()
    
    def plot_bode_comparison(self):
        """绘制Bode图对比"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        colors = {'314d': 'blue', '7430mAh': 'red'}
        
        # 幅值图
        for name, data in [('314d', self.data_314d), ('7430mAh', self.data_7430mah)]:
            if data is not None:
                ax1.loglog(data['freq'], data['z_mag'], 'o-', 
                          color=colors[name], markersize=4, label=f'{name}电芯')
        
        ax1.set_xlabel('频率 (Hz)')
        ax1.set_ylabel('|Z| (Ω)')
        ax1.set_title('阻抗幅值对比')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 相位图
        for name, data in [('314d', self.data_314d), ('7430mAh', self.data_7430mah)]:
            if data is not None:
                ax2.semilogx(data['freq'], data['phase_deg'], 'o-', 
                            color=colors[name], markersize=4, label=f'{name}电芯')
        
        ax2.set_xlabel('频率 (Hz)')
        ax2.set_ylabel('相位角 (°)')
        ax2.set_title('相位角对比')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 实部对比
        for name, data in [('314d', self.data_314d), ('7430mAh', self.data_7430mah)]:
            if data is not None:
                ax3.loglog(data['freq'], data['z_real'], 'o-', 
                          color=colors[name], markersize=4, label=f'{name}电芯')
        
        ax3.set_xlabel('频率 (Hz)')
        ax3.set_ylabel('Z\' (Ω)')
        ax3.set_title('阻抗实部对比')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 虚部对比
        for name, data in [('314d', self.data_314d), ('7430mAh', self.data_7430mah)]:
            if data is not None:
                ax4.loglog(data['freq'], np.abs(data['z_imag']), 'o-', 
                          color=colors[name], markersize=4, label=f'{name}电芯')
        
        ax4.set_xlabel('频率 (Hz)')
        ax4.set_ylabel('|Z\'\'| (Ω)')
        ax4.set_title('阻抗虚部对比')
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        
        plt.tight_layout()
        plt.show()
    
    def generate_analysis_report(self):
        """生成分析报告"""
        report = {
            'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_summary': {},
            'circuit_parameters': {},
            'performance_comparison': {},
            'algorithm_evaluation': {}
        }
        
        # 数据摘要
        for name, data in [('314d', self.data_314d), ('7430mAh', self.data_7430mah)]:
            if data is not None:
                report['data_summary'][name] = {
                    'data_points': len(data),
                    'frequency_range': f"{data['freq'].min():.2e} - {data['freq'].max():.2e} Hz",
                    'impedance_real_range': f"{data['z_real'].min():.4f} - {data['z_real'].max():.4f} Ω",
                    'impedance_imag_range': f"{data['z_imag'].min():.4f} - {data['z_imag'].max():.4f} Ω"
                }
        
        # 电路参数
        for battery_type, results in self.analysis_results.items():
            report['circuit_parameters'][battery_type] = {
                'Rs': f"{results['Rs']:.4f} Ω",
                'Rct': f"{results['Rct']:.4f} Ω",
                'CPE_T': f"{results['CPE_T']:.2e} F·s^(n-1)",
                'CPE_n': f"{results['CPE_n']:.4f}",
                'fitting_quality': f"RMSE={results['rmse']:.4f}Ω, R²={results['r_squared']:.4f}"
            }
        
        # 算法评估
        report['algorithm_evaluation'] = {
            'randles_circuit': {
                'description': 'Rs + (Rct || CPE)等效电路模型',
                'advantages': ['物理意义明确', '参数可解释性强', '适用于大多数电池系统'],
                'limitations': ['假设单一时间常数', '可能无法描述复杂界面过程'],
                'fitting_method': 'Levenberg-Marquardt非线性最小二乘法'
            }
        }
        
        return report

# 使用示例和模拟数据
def create_sample_data():
    """创建示例EIS数据用于演示"""
    
    # 频率范围
    freq = np.logspace(-2, 5, 50)  # 0.01 Hz to 100 kHz
    omega = 2 * np.pi * freq
    
    # 314d电芯参数 (假设)
    Rs_314d = 0.02
    Rct_314d = 0.15
    CPE_T_314d = 0.001
    CPE_n_314d = 0.85
    
    # 7430mAh电芯参数 (假设)
    Rs_7430 = 0.015
    Rct_7430 = 0.08
    CPE_T_7430 = 0.002
    CPE_n_7430 = 0.90
    
    def calculate_impedance(freq, Rs, Rct, CPE_T, CPE_n):
        omega = 2 * np.pi * freq
        Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
        Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
        Z_total = Rs + Z_parallel
        return Z_total
    
    # 计算阻抗
    Z_314d = calculate_impedance(freq, Rs_314d, Rct_314d, CPE_T_314d, CPE_n_314d)
    Z_7430 = calculate_impedance(freq, Rs_7430, Rct_7430, CPE_T_7430, CPE_n_7430)
    
    # 添加噪声
    noise_level = 0.002
    Z_314d += noise_level * (np.random.randn(len(freq)) + 1j * np.random.randn(len(freq)))
    Z_7430 += noise_level * (np.random.randn(len(freq)) + 1j * np.random.randn(len(freq)))
    
    # 创建DataFrame
    data_314d = pd.DataFrame({
        'freq': freq,
        'z_real': Z_314d.real,
        'z_imag': Z_314d.imag
    })
    
    data_7430mah = pd.DataFrame({
        'freq': freq,
        'z_real': Z_7430.real,
        'z_imag': Z_7430.imag
    })
    
    return data_314d, data_7430mah

if __name__ == "__main__":
    print("=== EIS电池数据分析工具 ===")
    print("用于314d电芯和7430mAh电芯的对比分析")
    
    # 创建分析器实例
    analyzer = BatteryEISComparison()
    
    # 如果没有实际数据，使用模拟数据演示
    print("\n生成模拟数据进行演示...")
    data_314d, data_7430mah = create_sample_data()
    
    # 加载数据
    analyzer.load_battery_data(data_314d=data_314d, data_7430mah=data_7430mah)
    
    # 计算衍生参数
    analyzer.calculate_derived_parameters()
    
    # 拟合等效电路
    print("\n开始等效电路拟合...")
    analyzer.fit_equivalent_circuit('314d')
    analyzer.fit_equivalent_circuit('7430mAh')
    
    # 算法性能对比
    comparison = analyzer.compare_algorithms_performance()
    
    # 生成报告
    report = analyzer.generate_analysis_report()
    
    print("\n=== 分析完成 ===")
    print("可以调用以下方法查看结果:")
    print("- analyzer.plot_nyquist_comparison()  # Nyquist图对比")
    print("- analyzer.plot_bode_comparison()     # Bode图对比")
    print("- analyzer.generate_analysis_report() # 生成详细报告")