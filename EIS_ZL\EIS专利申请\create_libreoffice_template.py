#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建LibreOffice Draw模板文件
生成ODG格式的可编辑专利附图

Author: Augment Agent
Date: 2025-01-09
"""

import os
import zipfile
import xml.etree.ElementTree as ET
from xml.dom import minidom

class LibreOfficeDrawGenerator:
    """LibreOffice Draw文件生成器"""
    
    def __init__(self, output_dir="editable_figures"):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def create_odg_figure1(self):
        """创建ODG格式的图1"""
        # 创建临时目录
        temp_dir = os.path.join(self.output_dir, "temp_odg")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 创建ODG文件结构
        self._create_manifest(temp_dir)
        self._create_meta(temp_dir)
        self._create_styles(temp_dir)
        self._create_content(temp_dir)
        
        # 打包成ODG文件
        odg_path = os.path.join(self.output_dir, "figure1_system_architecture.odg")
        with zipfile.ZipFile(odg_path, 'w', zipfile.ZIP_DEFLATED) as odg_zip:
            # 添加mimetype文件（必须是第一个，且不压缩）
            odg_zip.writestr("mimetype", "application/vnd.oasis.opendocument.graphics", compress_type=zipfile.ZIP_STORED)
            
            # 添加其他文件
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, temp_dir)
                    odg_zip.write(file_path, arc_path)
        
        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir)
        
        print(f"ODG文件已生成: {odg_path}")
    
    def _create_manifest(self, temp_dir):
        """创建manifest.xml"""
        manifest = ET.Element('manifest:manifest')
        manifest.set('xmlns:manifest', 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0')
        manifest.set('manifest:version', '1.2')
        
        # 添加文件条目
        entries = [
            ('/', 'application/vnd.oasis.opendocument.graphics'),
            ('content.xml', 'text/xml'),
            ('styles.xml', 'text/xml'),
            ('meta.xml', 'text/xml')
        ]
        
        for path, media_type in entries:
            entry = ET.SubElement(manifest, 'manifest:file-entry')
            entry.set('manifest:full-path', path)
            entry.set('manifest:media-type', media_type)
        
        # 保存manifest.xml
        meta_inf_dir = os.path.join(temp_dir, "META-INF")
        os.makedirs(meta_inf_dir, exist_ok=True)
        self._save_xml_file(manifest, os.path.join(meta_inf_dir, "manifest.xml"))
    
    def _create_meta(self, temp_dir):
        """创建meta.xml"""
        office_doc = ET.Element('office:document-meta')
        office_doc.set('xmlns:office', 'urn:oasis:names:tc:opendocument:xmlns:office:1.0')
        office_doc.set('xmlns:meta', 'urn:oasis:names:tc:opendocument:xmlns:meta:1.0')
        office_doc.set('xmlns:dc', 'http://purl.org/dc/elements/1.1/')
        office_doc.set('office:version', '1.2')
        
        meta = ET.SubElement(office_doc, 'office:meta')
        
        # 添加元数据
        title = ET.SubElement(meta, 'dc:title')
        title.text = '图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图'
        
        creator = ET.SubElement(meta, 'dc:creator')
        creator.text = 'Patent Figure Generator'
        
        subject = ET.SubElement(meta, 'dc:subject')
        subject.text = 'DNB1101BB专利附图'
        
        self._save_xml_file(office_doc, os.path.join(temp_dir, "meta.xml"))
    
    def _create_styles(self, temp_dir):
        """创建styles.xml"""
        office_doc = ET.Element('office:document-styles')
        office_doc.set('xmlns:office', 'urn:oasis:names:tc:opendocument:xmlns:office:1.0')
        office_doc.set('xmlns:style', 'urn:oasis:names:tc:opendocument:xmlns:style:1.0')
        office_doc.set('xmlns:fo', 'urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0')
        office_doc.set('office:version', '1.2')
        
        # 添加基本样式
        styles = ET.SubElement(office_doc, 'office:styles')
        
        # 默认样式
        default_style = ET.SubElement(styles, 'style:default-style')
        default_style.set('style:family', 'graphic')
        
        graphic_props = ET.SubElement(default_style, 'style:graphic-properties')
        graphic_props.set('draw:stroke', 'solid')
        graphic_props.set('draw:stroke-width', '0.05cm')
        graphic_props.set('draw:fill', 'solid')
        graphic_props.set('draw:fill-color', '#ffffff')
        
        self._save_xml_file(office_doc, os.path.join(temp_dir, "styles.xml"))
    
    def _create_content(self, temp_dir):
        """创建content.xml"""
        office_doc = ET.Element('office:document-content')
        office_doc.set('xmlns:office', 'urn:oasis:names:tc:opendocument:xmlns:office:1.0')
        office_doc.set('xmlns:style', 'urn:oasis:names:tc:opendocument:xmlns:style:1.0')
        office_doc.set('xmlns:text', 'urn:oasis:names:tc:opendocument:xmlns:text:1.0')
        office_doc.set('xmlns:draw', 'urn:oasis:names:tc:opendocument:xmlns:drawing:1.0')
        office_doc.set('xmlns:fo', 'urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0')
        office_doc.set('xmlns:svg', 'urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0')
        office_doc.set('office:version', '1.2')
        
        # 自动样式
        automatic_styles = ET.SubElement(office_doc, 'office:automatic-styles')
        
        # 页面样式
        page_layout = ET.SubElement(automatic_styles, 'style:page-layout')
        page_layout.set('style:name', 'PM1')
        
        page_props = ET.SubElement(page_layout, 'style:page-layout-properties')
        page_props.set('fo:margin-top', '2cm')
        page_props.set('fo:margin-bottom', '2cm')
        page_props.set('fo:margin-left', '2cm')
        page_props.set('fo:margin-right', '2cm')
        page_props.set('fo:page-width', '29.7cm')
        page_props.set('fo:page-height', '21cm')
        page_props.set('style:print-orientation', 'landscape')
        
        # 主体内容
        body = ET.SubElement(office_doc, 'office:body')
        drawing = ET.SubElement(body, 'office:drawing')
        
        # 页面
        page = ET.SubElement(drawing, 'draw:page')
        page.set('draw:name', 'page1')
        page.set('draw:master-page-name', 'Default')
        
        # 标题
        title_frame = ET.SubElement(page, 'draw:text-box')
        title_frame.set('draw:style-name', 'gr1')
        title_frame.set('svg:width', '25cm')
        title_frame.set('svg:height', '2cm')
        title_frame.set('svg:x', '2cm')
        title_frame.set('svg:y', '1cm')
        
        title_p = ET.SubElement(title_frame, 'text:p')
        title_p.set('text:style-name', 'P1')
        title_p.text = '图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图'
        
        # 组件定义
        components = [
            {'name': '1-被测电池\n1.9V-5.5V', 'x': '3cm', 'y': '4cm', 'width': '5cm', 'height': '3cm', 'color': '#ADD8E6'},
            {'name': '7-测试夹具\n四线制连接\n精确测量', 'x': '3cm', 'y': '8cm', 'width': '5cm', 'height': '3cm', 'color': '#F5DEB3'},
            {'name': '2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz', 'x': '10cm', 'y': '6cm', 'width': '7cm', 'height': '5cm', 'color': '#90EE90'},
            {'name': '4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω', 'x': '3cm', 'y': '12cm', 'width': '5cm', 'height': '3.5cm', 'color': '#F08080'},
            {'name': '3-STM32F103RCT6\n主控制器\n72MHz ARM', 'x': '19cm', 'y': '6cm', 'width': '6cm', 'height': '5cm', 'color': '#FFFFE0'},
            {'name': '5-串口显示屏\n实时显示\n测试结果', 'x': '19cm', 'y': '12cm', 'width': '6cm', 'height': '3.5cm', 'color': '#D3D3D3'},
            {'name': '6-PC上位机\nModbus RTU\n数据分析', 'x': '19cm', 'y': '2cm', 'width': '6cm', 'height': '3cm', 'color': '#B0C4DE'}
        ]
        
        # 添加组件
        for i, comp in enumerate(components):
            # 矩形框
            rect = ET.SubElement(page, 'draw:rect')
            rect.set('draw:style-name', f'gr{i+2}')
            rect.set('svg:width', comp['width'])
            rect.set('svg:height', comp['height'])
            rect.set('svg:x', comp['x'])
            rect.set('svg:y', comp['y'])
            rect.set('draw:fill', 'solid')
            rect.set('draw:fill-color', comp['color'])
            rect.set('draw:stroke', 'solid')
            rect.set('draw:stroke-width', '0.1cm')
            
            # 文本
            text_box = ET.SubElement(rect, 'draw:text-box')
            text_p = ET.SubElement(text_box, 'text:p')
            text_p.set('text:style-name', 'P2')
            text_p.text = comp['name']
        
        # 添加连接线（简化版本）
        lines = [
            {'x1': '5.5cm', 'y1': '7cm', 'x2': '5.5cm', 'y2': '8cm'},  # 电池到夹具
            {'x1': '8cm', 'y1': '9.5cm', 'x2': '10cm', 'y2': '8.5cm'},  # 夹具到芯片
            {'x1': '17cm', 'y1': '8.5cm', 'x2': '19cm', 'y2': '8.5cm'},  # 芯片到STM32
            {'x1': '22cm', 'y1': '6cm', 'x2': '22cm', 'y2': '5cm'},  # STM32到PC
            {'x1': '22cm', 'y1': '11cm', 'x2': '22cm', 'y2': '12cm'},  # STM32到显示屏
        ]
        
        for i, line in enumerate(lines):
            line_elem = ET.SubElement(page, 'draw:line')
            line_elem.set('draw:style-name', f'line{i+1}')
            line_elem.set('svg:x1', line['x1'])
            line_elem.set('svg:y1', line['y1'])
            line_elem.set('svg:x2', line['x2'])
            line_elem.set('svg:y2', line['y2'])
            line_elem.set('draw:stroke', 'solid')
            line_elem.set('draw:stroke-width', '0.1cm')
        
        self._save_xml_file(office_doc, os.path.join(temp_dir, "content.xml"))
    
    def _save_xml_file(self, element, filepath):
        """保存XML文件"""
        rough_string = ET.tostring(element, 'unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_string = reparsed.toprettyxml(indent="  ")
        
        # 移除空行
        lines = [line for line in pretty_string.split('\n') if line.strip()]
        pretty_string = '\n'.join(lines)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(pretty_string)

if __name__ == "__main__":
    generator = LibreOfficeDrawGenerator()
    generator.create_odg_figure1()
    print("LibreOffice Draw ODG文件生成完成！")
    print("您可以使用LibreOffice Draw打开并编辑这个文件。")
