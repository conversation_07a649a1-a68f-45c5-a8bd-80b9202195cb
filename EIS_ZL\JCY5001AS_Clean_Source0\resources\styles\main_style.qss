/* JCY5001A产线界面主样式表 */

/* 全局样式 */
* {
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
    font-size: 9pt;
}

/* 主窗口样式 */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* 分组框样式 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 10px;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #2c3e50;
    font-size: 10pt;
}

/* 按钮样式 */
QPushButton {
    background-color: #3498db;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: bold;
    min-width: 80px;
    min-height: 32px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* 开始按钮特殊样式 */
QPushButton#startButton {
    background-color: #27ae60;
}

QPushButton#startButton:hover {
    background-color: #229954;
}

/* 停止按钮特殊样式 */
QPushButton#stopButton {
    background-color: #e74c3c;
}

QPushButton#stopButton:hover {
    background-color: #c0392b;
}

/* 警告按钮样式 */
QPushButton#warningButton {
    background-color: #f39c12;
}

QPushButton#warningButton:hover {
    background-color: #e67e22;
}

/* 标签样式 */
QLabel {
    color: #2c3e50;
    background-color: transparent;
}

/* 大标题样式 */
QLabel#titleLabel {
    font-size: 16pt;
    font-weight: bold;
    color: #2c3e50;
}

/* 子标题样式 */
QLabel#subtitleLabel {
    font-size: 12pt;
    font-weight: bold;
    color: #34495e;
}

/* 数值显示标签 */
QLabel#valueLabel {
    font-size: 11pt;
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 4px 8px;
}

/* 状态标签样式 */
QLabel#statusGood {
    background-color: #d5f4e6;
    color: #27ae60;
    border: 1px solid #27ae60;
    border-radius: 4px;
    padding: 4px 8px;
    font-weight: bold;
}

QLabel#statusWarning {
    background-color: #fef9e7;
    color: #f39c12;
    border: 1px solid #f39c12;
    border-radius: 4px;
    padding: 4px 8px;
    font-weight: bold;
}

QLabel#statusError {
    background-color: #fadbd8;
    color: #e74c3c;
    border: 1px solid #e74c3c;
    border-radius: 4px;
    padding: 4px 8px;
    font-weight: bold;
}

/* 输入框样式 */
QLineEdit {
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 6px 10px;
    background-color: white;
    selection-background-color: #3498db;
    font-size: 9pt;
}

QLineEdit:focus {
    border-color: #3498db;
}

QLineEdit:disabled {
    background-color: #ecf0f1;
    color: #7f8c8d;
}

/* 组合框样式 */
QComboBox {
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 6px 10px;
    background-color: white;
    min-width: 100px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(resources/icons/arrow_down.png);
    width: 12px;
    height: 12px;
}

/* 进度条样式 */
QProgressBar {
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    text-align: center;
    background-color: #ecf0f1;
    color: #2c3e50;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 4px;
}

/* 表格样式 */
QTableWidget {
    gridline-color: #bdc3c7;
    background-color: white;
    alternate-background-color: #f8f9fa;
    selection-background-color: #3498db;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
}

/* 选项卡样式 */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    background-color: white;
}

QTabBar::tab {
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom-color: white;
}

QTabBar::tab:hover {
    background-color: #d5dbdb;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #34495e;
    color: white;
    border-top: 1px solid #2c3e50;
}

QStatusBar::item {
    border: none;
}

/* 菜单样式 */
QMenuBar {
    background-color: #34495e;
    color: white;
    border-bottom: 1px solid #2c3e50;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
}

QMenuBar::item:selected {
    background-color: #2c3e50;
}

QMenu {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: white;
}

/* 工具提示样式 */
QToolTip {
    background-color: #2c3e50;
    color: white;
    border: 1px solid #34495e;
    border-radius: 4px;
    padding: 6px;
    font-size: 9pt;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #bdc3c7;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* 复选框样式 */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #bdc3c7;
    border-radius: 3px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
    image: url(resources/icons/check.png);
}

/* 单选按钮样式 */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    background-color: white;
}

QRadioButton::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
}

/* 数字输入框样式 */
QSpinBox, QDoubleSpinBox {
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 6px 10px;
    background-color: white;
    min-width: 80px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3498db;
}

/* 滑块样式 */
QSlider::groove:horizontal {
    border: 1px solid #bdc3c7;
    height: 6px;
    background-color: #ecf0f1;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #3498db;
    border: 1px solid #2980b9;
    width: 16px;
    margin: -6px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background-color: #2980b9;
}

/* 测试结果状态样式 */
QLabel#resultPass {
    color: #27ae60;
    font-weight: bold;
    font-size: 14px;
}

QLabel#resultFail {
    color: #e74c3c;
    font-weight: bold;
    font-size: 14px;
}

QLabel#resultTesting {
    color: #f39c12;
    font-weight: bold;
    font-size: 14px;
}

QLabel#resultWaiting {
    color: #3498db;
    font-weight: bold;
    font-size: 14px;
}

QLabel#resultDisabled {
    color: #95a5a6;
    font-weight: bold;
    font-size: 14px;
}

QLabel#resultFailed {
    color: #e74c3c;
    font-weight: bold;
    font-size: 14px;
}

/* 通道异常状态样式 */
QLabel#resultChannelError {
    color: #e67e22;
    font-weight: bold;
    font-size: 14px;
    background-color: #fdf2e9;
    border: 1px solid #e67e22;
    border-radius: 3px;
    padding: 2px 4px;
}

QLabel#resultBatteryError {
    color: #8e44ad;
    font-weight: bold;
    font-size: 14px;
    background-color: #f4ecf7;
    border: 1px solid #8e44ad;
    border-radius: 3px;
    padding: 2px 4px;
}

QLabel#resultHardwareError {
    color: #c0392b;
    font-weight: bold;
    font-size: 14px;
    background-color: #fadbd8;
    border: 1px solid #c0392b;
    border-radius: 3px;
    padding: 2px 4px;
}

QLabel#resultSkipped {
    color: #7f8c8d;
    font-weight: bold;
    font-size: 14px;
    background-color: #ecf0f1;
    border: 1px solid #7f8c8d;
    border-radius: 3px;
    padding: 2px 4px;
}

/* 档位异常状态样式 */
QLabel#gradeChannelError {
    color: #e67e22;
    font-weight: bold;
    font-size: 14px;
    background-color: #fdf2e9;
    border: 1px solid #e67e22;
    border-radius: 3px;
    padding: 2px 4px;
}

QLabel#gradeBatteryError {
    color: #8e44ad;
    font-weight: bold;
    font-size: 14px;
    background-color: #f4ecf7;
    border: 1px solid #8e44ad;
    border-radius: 3px;
    padding: 2px 4px;
}

QLabel#gradeHardwareError {
    color: #c0392b;
    font-weight: bold;
    font-size: 14px;
    background-color: #fadbd8;
    border: 1px solid #c0392b;
    border-radius: 3px;
    padding: 2px 4px;
}

QLabel#gradeSkipped {
    color: #7f8c8d;
    font-weight: bold;
    font-size: 14px;
    background-color: #ecf0f1;
    border: 1px solid #7f8c8d;
    border-radius: 3px;
    padding: 2px 4px;
}
