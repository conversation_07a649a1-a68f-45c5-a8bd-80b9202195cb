#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急配置修复脚本
强制修复运行时配置覆盖问题
"""

import json
import sys
import os

def emergency_fix():
    """紧急修复配置"""
    print("🚨 紧急配置修复")
    print("=" * 80)
    
    config_path = "config/app_config.json"
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("🔧 强制设置所有相关配置...")
        
        # 确保所有节点存在
        if 'test' not in config:
            config['test'] = {}
        if 'test_params' not in config:
            config['test_params'] = {}
        if 'frequency' not in config:
            config['frequency'] = {}
        if 'multi_freq' not in config['frequency']:
            config['frequency']['multi_freq'] = {}
        
        # 强制设置关键配置 - 多个位置确保覆盖
        config['test']['count_limit_enabled'] = False
        config['test']['max_count'] = 100
        config['test']['use_parallel_staggered_mode'] = False
        config['test']['critical_frequency'] = 15.0
        config['test']['timeout_seconds'] = 150
        config['test']['max_retries'] = 5
        config['test']['error_recovery'] = True
        
        # 测试参数强制设置
        config['test_params']['test_mode'] = 'simultaneous'
        config['test_params']['gain'] = '1'
        config['test_params']['average_times'] = 1
        config['test_params']['resistance_range'] = '10R'
        
        # 频率配置强制设置
        config['frequency']['preset_mode'] = '研究模式'
        config['frequency']['mode'] = 'multi'
        config['frequency']['single_mode'] = False
        config['frequency']['frequency_order'] = 'high_to_low'
        
        # 研究模式20个频点
        research_frequencies = [
            1007.083, 625.612, 389.1, 242.234, 150.681, 
            93.46, 57.221, 36.24, 22.888, 13.351, 
            11.444, 9.537, 7.629, 5.722, 3.815, 
            1.907, 0.954, 0.477, 0.238, 0.119
        ]
        
        config['frequency']['list'] = research_frequencies
        config['frequency']['multi_freq']['custom_list'] = research_frequencies
        config['frequency']['multi_freq']['points'] = 20
        
        # 额外的安全设置
        config['test']['enabled_channels'] = [1, 2, 3, 4, 5, 6, 7, 8]
        config['test']['auto_detect'] = False
        config['test']['continuous_mode'] = False
        config['test']['interval'] = 3
        config['test']['timeout'] = 8
        config['test']['retry_count'] = 1
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 紧急配置修复完成")
        print(f"   • count_limit_enabled: {config['test']['count_limit_enabled']}")
        print(f"   • max_count: {config['test']['max_count']}")
        print(f"   • use_parallel_staggered_mode: {config['test']['use_parallel_staggered_mode']}")
        print(f"   • test_mode: {config['test_params']['test_mode']}")
        print(f"   • preset_mode: {config['frequency']['preset_mode']}")
        print(f"   • frequency count: {len(config['frequency']['list'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 紧急修复失败: {e}")
        return False

def create_runtime_override_protection():
    """创建运行时覆盖保护"""
    print("\n🛡️ 创建运行时覆盖保护...")
    
    # 创建一个配置保护文件
    protection_code = '''# 配置保护代码
# 在test_config_manager.py中添加此代码来防止运行时覆盖

def _protect_critical_config(self, config):
    """保护关键配置不被运行时覆盖"""
    # 强制设置关键配置
    config['count_limit_enabled'] = False
    config['max_count'] = 100
    config['use_parallel_staggered_mode'] = False
    
    # 记录保护日志
    logger.info("🛡️ 配置保护：强制禁用计数限制和并行错频模式")
    
    return config
'''
    
    try:
        with open('config_protection.txt', 'w', encoding='utf-8') as f:
            f.write(protection_code)
        print("✅ 配置保护代码已生成: config_protection.txt")
        return True
    except Exception as e:
        print(f"❌ 创建保护代码失败: {e}")
        return False

def backup_original_config():
    """备份原始配置"""
    print("\n💾 备份原始配置...")
    
    try:
        import shutil
        import datetime
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"config/app_config_backup_{timestamp}.json"
        
        shutil.copy("config/app_config.json", backup_path)
        print(f"✅ 配置已备份到: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def main():
    """主函数"""
    print("🚨 紧急配置修复工具")
    print("=" * 80)
    print("此工具将强制修复所有配置问题")
    print("=" * 80)
    
    # 1. 备份原始配置
    backup_success = backup_original_config()
    
    # 2. 紧急修复
    fix_success = emergency_fix()
    
    # 3. 创建保护机制
    protection_success = create_runtime_override_protection()
    
    # 总结
    print("\n🎯 紧急修复总结")
    print("=" * 80)
    
    if fix_success:
        print("✅ 配置文件已强制修复")
        print("✅ 所有关键配置已重置")
        print("✅ 研究模式20个频点已设置")
    else:
        print("❌ 配置修复失败")
    
    if backup_success:
        print("✅ 原始配置已备份")
    
    if protection_success:
        print("✅ 保护机制已创建")
    
    print("\n📋 下一步操作:")
    print("1. 立即重启应用程序")
    print("2. 不要修改任何设置")
    print("3. 直接开始研究模式测试")
    print("4. 观察是否测试20个频点")
    
    if fix_success:
        print("\n⚠️ 重要提醒:")
        print("• 如果问题仍然存在，说明有代码在运行时强制覆盖配置")
        print("• 需要修改源代码来彻底解决问题")
        print("• 请提供测试结果以便进一步诊断")
    
    return fix_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
