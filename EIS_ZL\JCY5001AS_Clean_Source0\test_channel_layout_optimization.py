#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JCY5001AS通道显示组件右列布局优化效果
验证移除不需要的显示项后的布局改进

Author: Assistant
Date: 2025-01-27
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QLineEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ChannelLayoutOptimizationTestWindow(QMainWindow):
    """通道布局优化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 通道右列布局优化测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        self._add_description(main_layout)
        
        # 创建对比测试
        self._create_comparison_test(main_layout)
    
    def _add_description(self, layout):
        """添加说明文字"""
        desc_label = QLabel("JCY5001AS 通道右列布局优化测试")
        desc_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 12px;
                background-color: #ecf0f1;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(desc_label)
        
        # 添加优化说明
        changes_label = QLabel("""
        <b>主要优化内容：</b><br>
        • <b>移除不需要的显示项</b>：删除"离群率"和"容量"预测显示区域<br>
        • <b>电池码输入框优化</b>：最小宽度120px → 150px，权重1 → 2<br>
        • <b>Rs和Rct显示优化</b>：标题宽度60px → 70px，数值宽度80px → 100px<br>
        • <b>字体大小提升</b>：Rs/Rct标题10pt → 11pt，数值12pt → 13pt<br>
        • <b>布局间距优化</b>：增加各组件间距，改善视觉效果
        """)
        changes_label.setFont(QFont("Microsoft YaHei", 10))
        changes_label.setStyleSheet("""
            QLabel {
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                line-height: 1.6;
            }
        """)
        layout.addWidget(changes_label)
    
    def _create_comparison_test(self, layout):
        """创建对比测试"""
        comparison_widget = QWidget()
        comparison_layout = QHBoxLayout(comparison_widget)
        comparison_layout.setSpacing(20)
        
        # 优化前模拟
        before_widget = self._create_channel_demo("优化前布局", {
            "has_outlier": True,
            "has_capacity": True,
            "battery_width": "120px",
            "rs_title_width": "60px",
            "rs_value_width": "80px",
            "rs_title_font": "10pt",
            "rs_value_font": "12pt",
            "spacing": "6px"
        })
        comparison_layout.addWidget(before_widget)
        
        # 优化后效果
        after_widget = self._create_channel_demo("优化后布局", {
            "has_outlier": False,
            "has_capacity": False,
            "battery_width": "150px",
            "rs_title_width": "70px",
            "rs_value_width": "100px",
            "rs_title_font": "11pt",
            "rs_value_font": "13pt",
            "spacing": "8px"
        })
        comparison_layout.addWidget(after_widget)
        
        layout.addWidget(comparison_widget)
    
    def _create_channel_demo(self, title, config):
        """创建通道演示"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout(demo_widget)
        demo_layout.setContentsMargins(10, 10, 10, 10)
        demo_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                background-color: #3498db;
                padding: 8px;
                border-radius: 4px;
                font-size: 12pt;
            }}
        """)
        demo_layout.addWidget(title_label)
        
        # 通道模拟框
        channel_frame = QFrame()
        channel_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
        """)
        channel_layout = QVBoxLayout(channel_frame)
        
        # 通道标题
        channel_title = QLabel("通道 1")
        channel_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        channel_layout.addWidget(channel_title)
        
        # 主内容区域
        main_content = QFrame()
        main_content_layout = QHBoxLayout(main_content)
        
        # 左列
        left_column = self._create_left_column_demo(config)
        main_content_layout.addLayout(left_column, 2)  # 左列2份权重
        
        # 右列
        right_column = self._create_right_column_demo(config)
        main_content_layout.addLayout(right_column, 3)  # 右列3份权重
        
        channel_layout.addWidget(main_content)
        
        # 添加配置信息
        info_text = f"""
        电池码输入宽度: {config['battery_width']}
        Rs标题宽度: {config['rs_title_width']} | Rs数值宽度: {config['rs_value_width']}
        Rs标题字体: {config['rs_title_font']} | Rs数值字体: {config['rs_value_font']}
        组件间距: {config['spacing']}
        """
        
        if config['has_outlier'] or config['has_capacity']:
            extra_items = []
            if config['has_outlier']:
                extra_items.append("离群率")
            if config['has_capacity']:
                extra_items.append("容量预测")
            info_text += f"额外显示项: {', '.join(extra_items)}"
        else:
            info_text += "额外显示项: 已移除"
        
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Microsoft YaHei", 8))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        demo_layout.addWidget(info_label)
        
        demo_layout.addWidget(channel_frame)
        return demo_widget
    
    def _create_left_column_demo(self, config):
        """创建左列演示"""
        left_layout = QVBoxLayout()
        
        # 测试计数
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("测试计数:"))
        count_layout.addWidget(QLabel("45"))
        count_layout.addStretch()
        left_layout.addLayout(count_layout)
        
        # 电池码输入
        battery_layout = QHBoxLayout()
        battery_label = QLabel("电池码:")
        battery_layout.addWidget(battery_label)
        
        battery_input = QLineEdit()
        battery_input.setText("JCY-20250705-6274")
        battery_input.setReadOnly(True)
        battery_input.setStyleSheet(f"""
            QLineEdit {{
                border: 1px solid #bdc3c7;
                padding: 6px 10px;
                background-color: white;
                min-width: {config['battery_width']};
                font-size: 10pt;
            }}
        """)
        battery_layout.addWidget(battery_input, 2 if config['battery_width'] == "150px" else 1)
        left_layout.addLayout(battery_layout)
        
        # 电压显示
        voltage_layout = QHBoxLayout()
        voltage_layout.addWidget(QLabel("电压(V):"))
        voltage_layout.addWidget(QLabel("3.984"))
        voltage_layout.addStretch()
        left_layout.addLayout(voltage_layout)
        
        left_layout.addStretch()
        return left_layout
    
    def _create_right_column_demo(self, config):
        """创建右列演示"""
        right_layout = QVBoxLayout()
        right_layout.setSpacing(int(config['spacing'].replace('px', '')))
        
        # Rs显示
        rs_layout = QHBoxLayout()
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet(f"""
            font-size: {config['rs_title_font']};
            color: #7f8c8d;
            font-weight: bold;
            min-width: {config['rs_title_width']};
        """)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("17.807mΩ")
        rs_value.setStyleSheet(f"""
            QLabel {{
                font-size: {config['rs_value_font']};
                font-weight: bold;
                padding: 4px 8px;
                border: 1px solid #ecf0f1;
                border-radius: 5px;
                background-color: #f8f9fa;
                min-width: {config['rs_value_width']};
                color: #2c3e50;
            }}
        """)
        rs_layout.addWidget(rs_value)
        rs_layout.addStretch()
        right_layout.addLayout(rs_layout)
        
        # Rct显示
        rct_layout = QHBoxLayout()
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet(f"""
            font-size: {config['rs_title_font']};
            color: #7f8c8d;
            font-weight: bold;
            min-width: {config['rs_title_width']};
        """)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("2.224mΩ")
        rct_value.setStyleSheet(f"""
            QLabel {{
                font-size: {config['rs_value_font']};
                font-weight: bold;
                padding: 4px 8px;
                border: 1px solid #ecf0f1;
                border-radius: 5px;
                background-color: #f8f9fa;
                min-width: {config['rs_value_width']};
                color: #2c3e50;
            }}
        """)
        rct_layout.addWidget(rct_value)
        rct_layout.addStretch()
        right_layout.addLayout(rct_layout)
        
        # 条件显示离群率和容量
        if config['has_outlier']:
            outlier_layout = QHBoxLayout()
            outlier_layout.addWidget(QLabel("离群率:"))
            outlier_layout.addWidget(QLabel("--"))
            outlier_layout.addStretch()
            right_layout.addLayout(outlier_layout)
        
        if config['has_capacity']:
            capacity_layout = QHBoxLayout()
            capacity_layout.addWidget(QLabel("容量:"))
            capacity_layout.addWidget(QLabel("--"))
            capacity_layout.addStretch()
            right_layout.addLayout(capacity_layout)
        
        right_layout.addStretch()
        return right_layout


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        
        # 设置应用程序样式
        app.setStyle('Fusion')
        
        # 创建测试窗口
        window = ChannelLayoutOptimizationTestWindow()
        window.show()
        
        logger.info("通道右列布局优化测试窗口已启动")
        logger.info("主要优化：")
        logger.info("• 移除离群率和容量预测显示区域")
        logger.info("• 电池码输入框宽度：120px → 150px")
        logger.info("• Rs/Rct显示区域：标题70px，数值100px")
        logger.info("• 字体大小提升：标题11pt，数值13pt")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
