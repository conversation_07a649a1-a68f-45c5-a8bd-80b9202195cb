#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Nuitka打包JCY5001A V0.80.05软件
生成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def clean_build_dirs():
    """清理之前的构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [
        'main.dist',
        'main.build',
        'main.onefile-build',
        '__pycache__',
        'build',
        'dist'
    ]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"  ✅ 已删除: {dir_name}")
            except Exception as e:
                print(f"  ⚠️ 删除失败 {dir_name}: {e}")

def build_with_nuitka():
    """使用Nuitka构建可执行文件"""
    print("🚀 开始使用Nuitka构建 V0.80.05...")
    
    # 构建命令 - 优化版本，减少包含的模块
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",  # 独立模式
        "--enable-plugin=pyqt5",  # 启用PyQt5插件
        "--include-data-dir=config=config",  # 包含配置目录
        "--include-data-dir=resources=resources",  # 包含资源目录
        "--include-data-dir=templates=templates",  # 包含模板目录
        "--include-data-dir=data=data",  # 包含数据目录
        "--include-package=PyQt5",  # 包含PyQt5包
        "--include-package=serial",  # 包含串口通信包
        "--include-package=numpy",  # 包含numpy
        "--include-package=scipy",  # 包含scipy
        "--include-package=matplotlib",  # 包含matplotlib
        "--include-package=openpyxl",  # 包含Excel处理包
        "--include-package=xlsxwriter",  # 包含Excel写入包
        "--include-package=PIL",  # 包含图像处理包
        "--include-package=qrcode",  # 包含二维码包
        "--include-package=reportlab",  # 包含PDF生成包
        "--windows-console-mode=disable",  # 新的控制台禁用选项
        "--windows-icon-from-ico=resources/icons/app_icon.ico",  # 设置图标
        "--output-dir=dist",  # 输出目录
        "--output-filename=JCY5001A_V0.80.05.exe",  # 输出文件名
        "--company-name=JingCeYun",  # 公司名称（英文避免编码问题）
        "--product-name=JCY5001A Battery Impedance Tester",  # 产品名称（英文避免编码问题）
        "--file-version=********",  # 文件版本
        "--product-version=********",  # 产品版本
        "--file-description=JCY5001A Battery Impedance Testing System",  # 文件描述（英文避免编码问题）
        "--copyright=Copyright (C) 2025 JingCeYun",  # 版权信息（英文避免编码问题）
        "--assume-yes-for-downloads",  # 自动确认下载
        "--show-progress",  # 显示进度
        "--show-memory",  # 显示内存使用
        "--jobs=4",  # 使用4个并行作业
        "--low-memory",  # 低内存模式
        "main.py"  # 主文件
    ]
    
    print("📋 构建命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行构建
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("✅ Nuitka构建完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka构建失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到输出目录"""
    print("📁 复制额外文件...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        print(f"❌ 输出目录不存在: {dist_dir}")
        return False
    
    # 需要复制的文件和目录
    files_to_copy = [
        ("README.md", "README.md"),
        ("requirements.txt", "requirements.txt"),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(dist_dir, dst)
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
            try:
                if os.path.isdir(src):
                    shutil.copytree(src, dst_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(src, dst_path)
                print(f"  ✅ 已复制: {src} -> {dst}")
            except Exception as e:
                print(f"  ⚠️ 复制失败 {src}: {e}")
    
    return True

def create_installer_info():
    """创建安装信息文件"""
    print("📝 创建安装信息...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        return False
    
    info_content = f"""JCY5001A鲸测云8路EIS阻抗筛选仪 V0.80.05
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
构建工具: Nuitka

安装说明:
1. 解压所有文件到目标目录
2. 运行 JCY5001A_V0.80.05.exe 启动程序
3. 首次运行会创建配置文件

系统要求:
- Windows 10/11 (64位)
- 至少4GB内存
- 至少1GB磁盘空间

技术支持:
- 公司: 鲸测云
- 版本: V0.80.05
- 构建日期: {datetime.now().strftime('%Y-%m-%d')}

更新内容:
- 版本号更新至V0.80.05
- 主界面产品标题同步显示新版本号
- 优化系统稳定性和性能
- 修复已知问题
"""
    
    try:
        with open(os.path.join(dist_dir, "安装说明.txt"), "w", encoding="utf-8") as f:
            f.write(info_content)
        print("  ✅ 安装说明已创建")
        return True
    except Exception as e:
        print(f"  ❌ 创建安装说明失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 JCY5001A V0.80.05 Nuitka打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    # 步骤1: 清理构建目录
    clean_build_dirs()
    print()
    
    # 步骤2: 使用Nuitka构建
    if not build_with_nuitka():
        print("❌ 构建失败，停止打包")
        return False
    print()
    
    # 步骤3: 复制额外文件
    if not copy_additional_files():
        print("⚠️ 复制额外文件失败，但构建已完成")
    print()
    
    # 步骤4: 创建安装信息
    if not create_installer_info():
        print("⚠️ 创建安装信息失败，但构建已完成")
    print()
    
    # 完成
    print("🎉 打包完成!")
    print(f"📦 输出目录: dist/main.dist/")
    print(f"🚀 可执行文件: dist/main.dist/JCY5001A_V0.80.05.exe")
    print()
    print("💡 提示:")
    print("  - 整个 dist/main.dist/ 目录包含了所有必需文件")
    print("  - 可以将整个目录复制到目标机器上运行")
    print("  - 首次运行会自动创建配置文件")
    print("  - 版本号V0.80.05将显示在主界面标题区域")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
