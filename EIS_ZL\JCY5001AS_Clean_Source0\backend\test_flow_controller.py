# -*- coding: utf-8 -*-
"""
重构后的测试流程控制器
使用组合模式集成各个专门的管理器，遵循单一职责原则

Author: Jack
Date: 2025-01-30
"""

import logging
import threading
from typing import Dict, List, Optional, Callable, Union

# 导入各个管理器
from backend.test_config_manager import TestConfigManager
from backend.device_config_manager import DeviceConfigManager
from backend.impedance_data_manager import ImpedanceDataManager
from backend.test_result_manager import TestResultManager
from backend.startup_strategy_manager import StartupStrategyManager
from backend.test_executor import TestExecutor

logger = logging.getLogger(__name__)


class TestState:
    """测试状态枚举"""
    IDLE = "idle"
    PREPARING = "preparing"
    TESTING = "testing"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"


class TestFlowController:
    """
    重构后的测试流程控制器
    
    职责：
    - 测试流程的启动和停止控制
    - 各个管理器的协调和集成
    - 测试状态管理
    - 回调函数管理
    """
    
    def __init__(self, config_manager, comm_manager, progress_callback=None, status_callback=None):
        """
        初始化重构后的测试流程控制器
        
        Args:
            config_manager: 配置管理器
            comm_manager: 通信管理器
            progress_callback: 进度回调函数
            status_callback: 状态回调函数
        """
        self.config_manager = config_manager
        self.comm_manager = comm_manager
        self.progress_callback = progress_callback
        self.status_callback = status_callback
        
        # 测试控制状态
        self.is_testing = False
        self.test_thread = None
        self.stop_event = threading.Event()
        self.current_state = TestState.IDLE
        
        # 兼容性属性（保持与原始接口的兼容性）
        self.test_config = {}
        self.current_batch_id = None
        self.batch_info = {}
        self.impedance_data_storage = {}
        self.test_start_times = {}
        self.test_end_times = {}
        self.battery_codes = []
        
        # 初始化各个管理器
        self._initialize_managers()
        
        logger.info("重构后的测试流程控制器初始化完成")
    
    def _initialize_managers(self):
        """初始化各个管理器"""
        try:
            # 1. 测试配置管理器
            self.test_config_manager = TestConfigManager(self.config_manager)
            
            # 2. 设备配置管理器
            self.device_config_manager = DeviceConfigManager(self.comm_manager, self.config_manager)
            
            # 3. 阻抗数据管理器
            self.impedance_data_manager = ImpedanceDataManager(self.comm_manager)
            
            # 4. 测试结果管理器
            self.test_result_manager = TestResultManager(self.config_manager, self.impedance_data_manager)
            
            # 5. 启动策略管理器
            self.startup_strategy_manager = StartupStrategyManager(self.comm_manager, self.config_manager)
            
            # 6. 测试执行器
            self.test_executor = TestExecutor(
                self.comm_manager,
                self.test_config_manager,
                self.device_config_manager,
                self.impedance_data_manager,
                self.test_result_manager,
                self.startup_strategy_manager
            )
            
            # 设置回调函数
            self.test_executor.set_progress_callback(self.progress_callback)
            self.test_executor.set_status_callback(self.status_callback)
            self.test_executor.set_stop_event(self.stop_event)
            
            # 同步兼容性属性
            self._sync_compatibility_attributes()
            
            logger.info("所有管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化管理器失败: {e}")
            raise
    
    def _sync_compatibility_attributes(self):
        """同步兼容性属性"""
        try:
            # 同步测试配置
            self.test_config = self.test_config_manager.get_config()

            # 同步批次信息
            batch_info = self.test_result_manager.get_batch_info()
            self.current_batch_id = batch_info.get('current_batch_id')
            self.batch_info = batch_info.get('batch_info', {})
            self.battery_codes = batch_info.get('battery_codes', [])

            # 同步电池码到阻抗数据管理器（修复电池码保存逻辑）
            self.impedance_data_manager.set_battery_codes(self.battery_codes)

            # 同步阻抗数据
            self.impedance_data_storage = self.impedance_data_manager.get_all_impedance_data()

        except Exception as e:
            logger.error(f"同步兼容性属性失败: {e}")
    
    def start_batch_test(self, batch_info: Optional[Dict] = None, battery_codes: Optional[List[str]] = None) -> bool:
        """
        开始批次测试

        Args:
            batch_info: 批次信息（可选，如果为None则使用默认值）
            battery_codes: 电池码列表（可选，如果为None则自动生成）

        Returns:
            是否启动成功
        """
        try:
            if self.is_testing:
                logger.warning("测试已在进行中")
                return False

            # 🔧 修复：改进设备连接检查
            logger.info("🔧 检查设备连接状态...")
            if not self.comm_manager.is_device_connected():
                logger.error("❌ 设备未连接")
                # 🔧 修复：尝试重新连接
                logger.info("🔧 尝试重新连接设备...")
                try:
                    if hasattr(self.comm_manager, 'reconnect'):
                        reconnect_success = self.comm_manager.reconnect()
                        logger.info(f"🔧 设备重连结果: {reconnect_success}")
                        if not reconnect_success:
                            return False
                    else:
                        return False
                except Exception as e:
                    logger.error(f"❌ 设备重连失败: {e}")
                    return False
            else:
                logger.info("✅ 设备连接正常")

            # 🔧 修复：提供默认批次信息
            if batch_info is None:
                import time
                batch_info = {
                    'batch_number': f"AUTO_{int(time.time())}",
                    'product_name': '默认产品',
                    'operator': '系统',
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                }
                logger.info(f"🔧 使用默认批次信息: {batch_info}")

            # 🔧 修复：提供默认电池码
            if battery_codes is None:
                # 获取启用的通道数量
                enabled_channels = self.config_manager.get('test.enabled_channels', list(range(1, 9)))
                battery_codes = [f"AUTO_BATTERY_{i:02d}" for i in enabled_channels]
                logger.info(f"🔧 自动生成电池码: {battery_codes}")

            # 清理和初始化测试数据
            logger.info("🧹 开始清理和初始化测试数据...")
            self._clear_and_initialize_test_data()

            # 重新加载测试配置
            test_config = self.test_config_manager.get_config()
            logger.info(f"🔧 测试配置已重新加载:")
            logger.info(f"  - 连续测试模式: {test_config.get('continuous_mode', False)}")
            logger.info(f"  - 启用通道: {test_config.get('enabled_channels', [])}")
            logger.info(f"  - 测试频率: {test_config.get('frequencies', [])}")

            # 🔧 修复：验证关键配置
            if not test_config.get('enabled_channels'):
                logger.error("❌ 没有启用的通道，无法进行测试")
                return False

            if not test_config.get('frequencies'):
                logger.error("❌ 没有配置测试频率，无法进行测试")
                return False

            # 设置新批次
            try:
                batch_id = self.test_result_manager.setup_new_batch(batch_info, battery_codes)
                logger.info(f"✅ 批次设置成功，批次ID: {batch_id}")
            except Exception as e:
                logger.error(f"❌ 批次设置失败: {e}")
                return False

            # 同步兼容性属性
            self._sync_compatibility_attributes()

            # 启动测试线程
            self.is_testing = True
            self.stop_event.clear()
            self.current_state = TestState.PREPARING

            self.test_thread = threading.Thread(
                target=self._test_worker,
                args=(test_config,),
                daemon=True
            )
            self.test_thread.start()

            logger.info(f"✅ 批次测试启动成功: {batch_info.get('batch_number', 'Unknown')}")
            logger.info(f"📊 新批次ID: {batch_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 启动批次测试失败: {e}")
            import traceback
            logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
            return False
    
    def stop_test(self):
        """停止测试（增强版）"""
        try:
            logger.info("🛑 [增强版] 开始停止测试...")

            # 🔧 增强修复1：立即设置停止标志，防止新的测试启动
            self.is_testing = False
            self.stop_event.set()
            self.current_state = TestState.STOPPED
            logger.info("✅ 停止标志已设置")

            # 🔧 增强修复2：多重设备停止机制
            self._enhanced_stop_device()

            # 🔧 增强修复3：强制停止测试执行器
            self._enhanced_stop_test_executor()

            # 🔧 增强修复4：强制停止所有测试管理器
            self._enhanced_stop_all_test_managers()

            # 🔧 增强修复5：强制终止测试线程
            self._enhanced_stop_test_thread()

            # 🔧 增强修复6：彻底清理测试状态
            self._enhanced_cleanup_test_state()

            self.current_state = TestState.IDLE
            logger.info("✅ [增强版] 测试已完全停止")

        except Exception as e:
            logger.error(f"❌ [增强版] 停止测试失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 确保状态重置
            self.is_testing = False
            self.current_state = TestState.IDLE

    def _enhanced_stop_device(self):
        """增强的设备停止机制"""
        try:
            logger.info("🛑 [增强版] 停止设备测试...")

            if hasattr(self, 'comm_manager') and self.comm_manager:
                # 方法1：停止所有通道的阻抗测量
                all_channels = list(range(8))  # 0-7对应通道1-8
                try:
                    stop_success = self.comm_manager.stop_impedance_measurement(all_channels)
                    logger.info(f"设备阻抗测量停止结果: {stop_success}")
                except Exception as e:
                    logger.error(f"停止阻抗测量失败: {e}")

                # 方法2：发送设备复位命令（如果支持）
                try:
                    if hasattr(self.comm_manager, 'reset_device'):
                        reset_success = self.comm_manager.reset_device()
                        logger.info(f"设备复位结果: {reset_success}")
                except Exception as e:
                    logger.debug(f"设备复位失败（可能不支持）: {e}")

                # 方法3：清空设备命令队列（如果支持）
                try:
                    if hasattr(self.comm_manager, 'clear_command_queue'):
                        self.comm_manager.clear_command_queue()
                        logger.info("设备命令队列已清空")
                except Exception as e:
                    logger.debug(f"清空命令队列失败（可能不支持）: {e}")

                logger.info("✅ 设备停止操作完成")
            else:
                logger.warning("⚠️ 通信管理器不可用，无法停止设备")

        except Exception as e:
            logger.error(f"增强设备停止失败: {e}")

    def _enhanced_stop_test_executor(self):
        """增强的测试执行器停止机制"""
        try:
            logger.info("🛑 [增强版] 停止测试执行器...")

            if hasattr(self, 'test_executor') and self.test_executor:
                # 方法1：调用标准停止方法
                try:
                    self.test_executor.stop_execution()
                    logger.info("✅ 测试执行器标准停止完成")
                except Exception as e:
                    logger.error(f"测试执行器标准停止失败: {e}")

                # 方法2：设置执行器内部停止标志
                try:
                    if hasattr(self.test_executor, 'stop_event'):
                        self.test_executor.stop_event.set()
                    if hasattr(self.test_executor, 'is_testing'):
                        self.test_executor.is_testing = False
                    logger.info("✅ 测试执行器内部标志已设置")
                except Exception as e:
                    logger.error(f"设置执行器内部标志失败: {e}")

                # 方法3：强制停止活跃的测试管理器
                try:
                    if hasattr(self.test_executor, '_stop_active_test_managers'):
                        self.test_executor._stop_active_test_managers()
                        logger.info("✅ 活跃测试管理器已停止")
                except Exception as e:
                    logger.error(f"停止活跃测试管理器失败: {e}")

            else:
                logger.warning("⚠️ 测试执行器不存在")

        except Exception as e:
            logger.error(f"增强测试执行器停止失败: {e}")

    def _enhanced_stop_all_test_managers(self):
        """增强的测试管理器停止机制"""
        try:
            logger.info("🛑 [增强版] 停止所有测试管理器...")

            # 通过测试执行器停止管理器
            if hasattr(self, 'test_executor') and self.test_executor:
                try:
                    # 停止当前的并行错频测试管理器
                    if hasattr(self.test_executor, '_current_staggered_manager') and self.test_executor._current_staggered_manager:
                        self.test_executor._current_staggered_manager.stop_test()
                        logger.info("✅ 并行错频测试管理器已停止")

                    # 停止当前的同时测试管理器
                    if hasattr(self.test_executor, '_current_simultaneous_manager') and self.test_executor._current_simultaneous_manager:
                        self.test_executor._current_simultaneous_manager.stop_simultaneous_test()
                        logger.info("✅ 同时测试管理器已停止")

                    # 停止其他可能的管理器
                    if hasattr(self.test_executor, '_active_managers'):
                        for manager in self.test_executor._active_managers:
                            try:
                                if hasattr(manager, 'stop_test'):
                                    manager.stop_test()
                                elif hasattr(manager, 'stop'):
                                    manager.stop()
                            except Exception as e:
                                logger.error(f"停止管理器失败: {e}")

                except Exception as e:
                    logger.error(f"通过测试执行器停止管理器失败: {e}")

            # 直接停止可能存在的管理器实例（备用方法）
            self._force_stop_known_managers()

        except Exception as e:
            logger.error(f"增强测试管理器停止失败: {e}")

    def _force_stop_known_managers(self):
        """强制停止已知的管理器类型"""
        try:
            logger.info("🛑 强制停止已知管理器...")

            # 尝试停止可能存在的全局管理器实例
            # 注意：这是一个备用方法，因为我们没有直接的实例引用

            # 可以在这里添加更多的强制停止逻辑
            # 例如：通过单例模式获取管理器实例并停止

            logger.info("✅ 强制停止已知管理器完成")

        except Exception as e:
            logger.error(f"强制停止已知管理器失败: {e}")

    def _enhanced_stop_test_thread(self):
        """增强的测试线程停止机制"""
        try:
            logger.info("🛑 [增强版] 停止测试线程...")

            if self.test_thread and self.test_thread.is_alive():
                logger.info("⏳ 等待测试线程结束...")

                # 方法1：正常等待（短时间）
                self.test_thread.join(timeout=2.0)

                if self.test_thread.is_alive():
                    logger.warning("⚠️ 测试线程未能在2秒内结束，尝试强制停止...")

                    # 方法2：再次设置停止事件并等待
                    self.stop_event.set()
                    self.test_thread.join(timeout=3.0)

                    if self.test_thread.is_alive():
                        logger.warning("⚠️ 测试线程仍在运行，但设备已停止，继续执行...")
                        # 注意：Python中无法强制杀死线程，但设备已停止，测试不会继续
                    else:
                        logger.info("✅ 测试线程已正常结束")
                else:
                    logger.info("✅ 测试线程已正常结束")

            # 清理线程引用
            self.test_thread = None

        except Exception as e:
            logger.error(f"增强测试线程停止失败: {e}")

    def _enhanced_cleanup_test_state(self):
        """增强的测试状态清理机制"""
        try:
            logger.info("🧹 [增强版] 清理测试状态...")

            # 清理各个管理器的数据
            if hasattr(self, 'impedance_data_manager'):
                try:
                    self.impedance_data_manager.clear_impedance_data()
                    logger.debug("阻抗数据管理器已清理")
                except Exception as e:
                    logger.error(f"清理阻抗数据管理器失败: {e}")

            if hasattr(self, 'test_result_manager'):
                try:
                    self.test_result_manager.clear_test_data()
                    logger.debug("测试结果管理器已清理")
                except Exception as e:
                    logger.error(f"清理测试结果管理器失败: {e}")

            if hasattr(self, 'startup_strategy_manager'):
                try:
                    self.startup_strategy_manager.reset_startup_stats()
                    logger.debug("启动策略管理器已重置")
                except Exception as e:
                    logger.error(f"重置启动策略管理器失败: {e}")

            # 重置测试执行器
            if hasattr(self, 'test_executor') and self.test_executor:
                try:
                    if hasattr(self.test_executor, 'reset_execution'):
                        self.test_executor.reset_execution()
                    logger.debug("测试执行器已重置")
                except Exception as e:
                    logger.error(f"重置测试执行器失败: {e}")

            # 清理兼容性属性
            self.test_config = {}
            self.current_batch_id = None
            self.batch_info = {}
            self.impedance_data_storage = {}
            self.test_start_times = {}
            self.test_end_times = {}
            self.battery_codes = []

            # 强制垃圾回收
            import gc
            gc.collect()

            logger.info("✅ [增强版] 测试状态清理完成")

        except Exception as e:
            logger.error(f"增强测试状态清理失败: {e}")

    def _stop_device_immediately(self):
        """立即停止设备测试"""
        try:
            logger.info("🛑 立即停止设备测试...")

            if hasattr(self, 'comm_manager') and self.comm_manager:
                # 停止所有通道的测试
                all_channels = list(range(8))  # 0-7对应通道1-8
                stop_success = self.comm_manager.stop_impedance_measurement(all_channels)
                if stop_success:
                    logger.info("✅ 设备测试已成功停止")
                else:
                    logger.warning("⚠️ 设备测试停止失败，但停止信号已发送")
            else:
                logger.warning("⚠️ 通信管理器不可用，无法停止设备")

        except Exception as e:
            logger.error(f"停止设备测试失败: {e}")

    def _stop_all_test_managers(self):
        """停止所有测试管理器"""
        try:
            logger.info("🛑 停止所有测试管理器...")

            # 通过测试执行器停止管理器（这是主要的停止路径）
            if hasattr(self, 'test_executor') and self.test_executor:
                try:
                    # 调用测试执行器的停止活跃管理器方法
                    if hasattr(self.test_executor, '_stop_active_test_managers'):
                        self.test_executor._stop_active_test_managers()
                        logger.info("✅ 通过测试执行器停止管理器完成")

                    # 如果测试执行器有管理器引用，也停止它们
                    if hasattr(self.test_executor, '_current_staggered_manager') and self.test_executor._current_staggered_manager:
                        self.test_executor._current_staggered_manager.stop_test()
                        logger.info("✅ 当前并行错频测试管理器已停止")

                    if hasattr(self.test_executor, '_current_simultaneous_manager') and self.test_executor._current_simultaneous_manager:
                        self.test_executor._current_simultaneous_manager.stop_simultaneous_test()
                        logger.info("✅ 当前同时测试管理器已停止")

                except Exception as e:
                    logger.error(f"通过测试执行器停止管理器失败: {e}")

            # 备用停止路径：直接停止可能存在的管理器实例
            try:
                # 尝试导入并停止并行错频测试管理器
                from backend.parallel_staggered_test_manager import ParallelStaggeredTestManagerSimplified
                # 这里无法直接停止，因为我们没有实例引用
                logger.debug("备用停止路径：无法直接停止并行错频测试管理器（无实例引用）")

                # 尝试导入并停止同时测试管理器
                from backend.simultaneous_test_manager import SimultaneousTestManager
                # 这里无法直接停止，因为我们没有实例引用
                logger.debug("备用停止路径：无法直接停止同时测试管理器（无实例引用）")

            except Exception as e:
                logger.debug(f"备用停止路径失败: {e}")

        except Exception as e:
            logger.error(f"停止测试管理器失败: {e}")

    def _cleanup_test_state(self):
        """清理测试状态"""
        try:
            logger.debug("🧹 清理测试状态...")

            # 清理各个管理器的数据
            if hasattr(self, 'impedance_data_manager'):
                self.impedance_data_manager.clear_impedance_data()

            if hasattr(self, 'test_result_manager'):
                self.test_result_manager.clear_test_data()

            if hasattr(self, 'startup_strategy_manager'):
                self.startup_strategy_manager.reset_startup_stats()

            # 重置测试执行器
            if hasattr(self, 'test_executor') and self.test_executor:
                if hasattr(self.test_executor, 'reset_execution'):
                    self.test_executor.reset_execution()

            # 清理兼容性属性
            self.test_config = {}
            self.current_batch_id = None
            self.batch_info = {}
            self.impedance_data_storage = {}
            self.test_start_times = {}
            self.test_end_times = {}
            self.battery_codes = []

            logger.debug("✅ 测试状态清理完成")

        except Exception as e:
            logger.error(f"清理测试状态失败: {e}")
    
    def _test_worker(self, test_config: Dict):
        """测试工作线程"""
        try:
            logger.info("🔄 测试工作线程启动")
            
            # 获取启用的通道列表
            enabled_channels = self.config_manager.get('test.enabled_channels', list(range(1, 9)))
            test_config['enabled_channels'] = enabled_channels

            # 检查通道状态码
            logger.info("🔍 检查通道状态码...")
            status_check_result = self.comm_manager.check_channels_status(enabled_channels)

            if not status_check_result.get('can_start_test', False):
                error_channels = status_check_result.get('error_channels', [])
                if error_channels:
                    error_msgs = [f"通道{ch}: {msg}" for ch, msg in error_channels]
                    logger.error(f"❌ 发现异常通道，无法启动测试: {error_msgs}")

                    # 通知UI显示异常状态
                    if self.progress_callback:
                        for channel_num, error_msg in error_channels:
                            self.progress_callback(channel_num, {
                                'state': 'channel_error',
                                'progress': 0,
                                'message': error_msg,
                                'voltage': 0.0,
                                'error': True
                            })

                    self.current_state = TestState.FAILED
                    return
                else:
                    logger.error("❌ 没有可用的测试通道")
                    self.current_state = TestState.FAILED
                    return

            # 更新可用通道列表
            available_channels = status_check_result.get('available_channels', [])
            test_config['available_channels'] = available_channels

            if len(available_channels) < len(enabled_channels):
                skipped_channels = set(enabled_channels) - set(available_channels)
                logger.warning(f"⚠️ 部分通道将被跳过: {list(skipped_channels)}")

                # 通知UI显示跳过的通道
                if self.progress_callback:
                    error_channels = status_check_result.get('error_channels', [])
                    error_channel_dict = dict(error_channels)

                    for channel_num in skipped_channels:
                        error_msg = error_channel_dict.get(channel_num, "通道状态异常")
                        self.progress_callback(channel_num, {
                            'state': 'skipped',
                            'progress': 0,
                            'message': error_msg,
                            'voltage': 0.0,
                            'skipped': True
                        })

            logger.info(f"✅ 状态检查完成，可用通道: {available_channels}")

            # 更新状态
            self.current_state = TestState.TESTING
            
            # 执行测试
            success = self.test_executor.execute_test(test_config, enabled_channels)
            
            # 更新最终状态
            if success and not self.stop_event.is_set():
                self.current_state = TestState.COMPLETED
                logger.info("🎉 测试工作线程正常完成")
            else:
                self.current_state = TestState.FAILED if not self.stop_event.is_set() else TestState.STOPPED
                logger.info("⚠️ 测试工作线程异常结束或被停止")
            
            # 同步兼容性属性
            self._sync_compatibility_attributes()
            
        except Exception as e:
            logger.error(f"❌ 测试工作线程异常: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            self.current_state = TestState.FAILED
            
        finally:
            self.is_testing = False
            logger.info("🔚 测试工作线程清理完成")
    
    def _clear_and_initialize_test_data(self):
        """清理和初始化测试数据"""
        try:
            logger.info("🚀 初始化新的测试会话...")
            
            # 1. 清理各个管理器的数据
            self.impedance_data_manager.clear_impedance_data()
            self.test_result_manager.clear_test_data()
            self.startup_strategy_manager.reset_startup_stats()
            
            # 2. 重置测试执行器
            self.test_executor.reset_execution()
            
            # 3. 重置控制状态
            self.is_testing = False
            self.stop_event.clear()
            self.current_state = TestState.IDLE
            
            # 4. 清理兼容性属性
            self.test_config = {}
            self.current_batch_id = None
            self.batch_info = {}
            self.impedance_data_storage = {}
            self.test_start_times = {}
            self.test_end_times = {}
            self.battery_codes = []
            
            # 5. 通知UI重置通道状态
            if self.progress_callback:
                enabled_channels = self.config_manager.get('test.enabled_channels', list(range(1, 9)))
                for channel_num in enabled_channels:
                    self.progress_callback(channel_num, {
                        'state': 'reset',
                        'progress': 0,
                        'message': '准备测试',
                        'voltage': 0.0
                    })
            
            logger.info("✅ 新测试会话初始化完成")
            
        except Exception as e:
            logger.error(f"初始化新测试会话失败: {e}")
    
    def get_test_status(self) -> Dict:
        """获取测试状态"""
        return {
            'is_testing': self.is_testing,
            'current_state': self.current_state,
            'test_config': self.test_config,
            'execution_status': self.test_executor.get_execution_status() if hasattr(self, 'test_executor') else {},
            'batch_info': self.batch_info
        }
    
    def update_test_config(self, config_updates: Dict):
        """更新测试配置"""
        try:
            self.test_config_manager.update_config(config_updates)
            self._sync_compatibility_attributes()
            logger.info(f"测试配置已更新: {config_updates}")
        except Exception as e:
            logger.error(f"更新测试配置失败: {e}")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
        if hasattr(self, 'test_executor') and self.test_executor:
            self.test_executor.set_progress_callback(callback)
    
    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
        if hasattr(self, 'test_executor') and self.test_executor:
            self.test_executor.set_status_callback(callback)
    
    def get_manager(self, manager_name: str):
        """
        获取指定的管理器实例
        
        Args:
            manager_name: 管理器名称
            
        Returns:
            管理器实例或None
        """
        managers = {
            'test_config': getattr(self, 'test_config_manager', None),
            'device_config': getattr(self, 'device_config_manager', None),
            'impedance_data': getattr(self, 'impedance_data_manager', None),
            'test_result': getattr(self, 'test_result_manager', None),
            'startup_strategy': getattr(self, 'startup_strategy_manager', None),
            'test_executor': getattr(self, 'test_executor', None)
        }
        return managers.get(manager_name)
    
    # ===== 兼容性方法 =====
    # 为了保持与原始接口的兼容性，添加一些常用的方法
    
    def _load_test_config(self):
        """兼容性方法：加载测试配置"""
        if hasattr(self, 'test_config_manager'):
            self.test_config = self.test_config_manager.get_config()
    
    def _get_test_frequencies(self) -> List[float]:
        """兼容性方法：获取测试频率"""
        if hasattr(self, 'test_config_manager'):
            return self.test_config_manager.get_frequencies()
        return [1.0]
    
    def _sort_frequencies(self, frequencies: List[float], order: str) -> List[float]:
        """兼容性方法：频率排序"""
        if hasattr(self, 'test_config_manager'):
            return self.test_config_manager._sort_frequencies(frequencies, order)
        return frequencies
    
    def _setup_new_batch(self, batch_info: Dict, battery_codes: List[str]):
        """兼容性方法：设置新批次"""
        if hasattr(self, 'test_result_manager'):
            self.current_batch_id = self.test_result_manager.setup_new_batch(batch_info, battery_codes)
            self.batch_info = batch_info.copy()
            self.battery_codes = battery_codes.copy()
    
    def _clear_test_data(self):
        """兼容性方法：清理测试数据"""
        self._clear_and_initialize_test_data()
    
    def _initialize_new_test_session(self):
        """兼容性方法：初始化新测试会话"""
        # 这个方法的功能已经包含在 _clear_and_initialize_test_data 中
        pass