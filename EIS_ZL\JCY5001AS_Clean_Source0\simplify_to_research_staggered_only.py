#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化系统：只保留研究模式+错频测试
移除所有其他模式，避免配置冲突
"""

import json
import sys
import os

def simplify_config():
    """简化配置，只保留研究模式+错频测试"""
    print("🔧 简化系统配置")
    print("=" * 80)
    print("只保留：研究模式 + 错频测试")
    print("移除：生产模式、快速模式、同时启动模式等")
    print("=" * 80)
    
    config_path = "config/app_config.json"
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("🔧 1. 设置频率配置...")
        
        # 研究模式20个频点
        research_frequencies = [
            1007.083, 625.612, 389.1, 242.234, 150.681, 
            93.46, 57.221, 36.24, 22.888, 13.351, 
            11.444, 9.537, 7.629, 5.722, 3.815, 
            1.907, 0.954, 0.477, 0.238, 0.119
        ]
        
        # 确保频率配置节点存在
        if 'frequency' not in config:
            config['frequency'] = {}
        if 'multi_freq' not in config['frequency']:
            config['frequency']['multi_freq'] = {}
        
        # 强制设置为研究模式
        config['frequency']['preset_mode'] = '研究模式'
        config['frequency']['mode'] = 'multi'
        config['frequency']['single_mode'] = False
        config['frequency']['list'] = research_frequencies
        config['frequency']['custom_list'] = research_frequencies
        config['frequency']['multi_freq']['custom_list'] = research_frequencies
        config['frequency']['multi_freq']['points'] = 20
        config['frequency']['frequency_order'] = 'high_to_low'
        
        print(f"   ✅ 频率配置：研究模式，{len(research_frequencies)}个频点")
        
        print("🔧 2. 设置测试模式...")
        
        # 确保测试配置节点存在
        if 'test' not in config:
            config['test'] = {}
        if 'test_params' not in config:
            config['test_params'] = {}
        
        # 强制设置为错频模式
        config['test_params']['test_mode'] = 'staggered'  # 错频模式
        config['test']['use_parallel_staggered_mode'] = True  # 启用并行错频
        config['test']['critical_frequency'] = 15.0  # 临界频率15Hz
        
        # 禁用其他模式
        config['test']['continuous_mode'] = False  # 禁用连续模式
        config['test']['auto_detect'] = False  # 禁用自动检测
        config['test']['count_limit_enabled'] = False  # 禁用计数限制
        config['test']['max_count'] = 100  # 设置大的最大计数
        
        # 基本测试参数
        config['test']['timeout'] = 8
        config['test']['retry_count'] = 1
        config['test']['interval'] = 3
        config['test']['timeout_seconds'] = 150
        config['test']['max_retries'] = 5
        config['test']['error_recovery'] = True
        config['test']['enabled_channels'] = [1, 2, 3, 4, 5, 6, 7, 8]
        
        # 测试参数
        config['test_params']['gain'] = '1'
        config['test_params']['average_times'] = 1
        config['test_params']['resistance_range'] = '10R'
        
        print("   ✅ 测试模式：错频模式（staggered）")
        print("   ✅ 并行错频：启用")
        print("   ✅ 临界频率：15.0Hz")
        print("   ✅ 其他模式：全部禁用")
        
        print("🔧 3. 移除不需要的配置...")
        
        # 移除可能导致冲突的配置
        config_keys_to_remove = []
        
        # 检查并移除不需要的配置
        if 'production_mode' in config.get('frequency', {}):
            del config['frequency']['production_mode']
            config_keys_to_remove.append('frequency.production_mode')
        
        if 'quick_mode' in config.get('frequency', {}):
            del config['frequency']['quick_mode']
            config_keys_to_remove.append('frequency.quick_mode')
        
        # 移除演示模式配置
        if 'demo' in config:
            config['demo']['enabled'] = False
            config['demo']['device_demo_mode'] = False
        
        if config_keys_to_remove:
            print(f"   ✅ 移除配置：{', '.join(config_keys_to_remove)}")
        else:
            print("   ✅ 无需移除额外配置")
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("\n✅ 配置简化完成")
        print(f"   • 模式：研究模式 + 错频测试")
        print(f"   • 频点：{len(research_frequencies)}个")
        print(f"   • 临界频率：{config['test']['critical_frequency']}Hz")
        print(f"   • 测试模式：{config['test_params']['test_mode']}")
        print(f"   • 并行错频：{config['test']['use_parallel_staggered_mode']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置简化失败: {e}")
        return False

def create_mode_lock():
    """创建模式锁定，防止意外修改"""
    print("\n🔒 创建模式锁定...")
    
    lock_content = """# 模式锁定文件
# 系统已简化为：研究模式 + 错频测试
# 
# 锁定配置：
# - preset_mode: 研究模式
# - test_mode: staggered (错频)
# - use_parallel_staggered_mode: true
# - critical_frequency: 15.0Hz
# - frequencies: 20个研究模式频点
#
# 警告：请勿修改这些配置，否则可能导致测试异常
"""
    
    try:
        with open('.mode_lock', 'w', encoding='utf-8') as f:
            f.write(lock_content)
        print("✅ 模式锁定文件已创建: .mode_lock")
        return True
    except Exception as e:
        print(f"❌ 创建模式锁定失败: {e}")
        return False

def verify_simplified_config():
    """验证简化后的配置"""
    print("\n🔍 验证简化后的配置...")
    
    try:
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置
        frequency_config = config.get('frequency', {})
        test_config = config.get('test', {})
        test_params = config.get('test_params', {})
        
        preset_mode = frequency_config.get('preset_mode', 'N/A')
        frequency_count = len(frequency_config.get('list', []))
        test_mode = test_params.get('test_mode', 'N/A')
        use_staggered = test_config.get('use_parallel_staggered_mode', False)
        critical_freq = test_config.get('critical_frequency', 0)
        
        print(f"   • 预设模式: {preset_mode}")
        print(f"   • 频点数量: {frequency_count}个")
        print(f"   • 测试模式: {test_mode}")
        print(f"   • 并行错频: {'启用' if use_staggered else '禁用'}")
        print(f"   • 临界频率: {critical_freq}Hz")
        
        # 验证配置正确性
        issues = []
        
        if preset_mode != '研究模式':
            issues.append(f"预设模式应该是'研究模式'，当前是'{preset_mode}'")
        
        if frequency_count != 20:
            issues.append(f"频点数量应该是20个，当前是{frequency_count}个")
        
        if test_mode != 'staggered':
            issues.append(f"测试模式应该是'staggered'，当前是'{test_mode}'")
        
        if not use_staggered:
            issues.append("并行错频模式应该启用")
        
        if critical_freq != 15.0:
            issues.append(f"临界频率应该是15.0Hz，当前是{critical_freq}Hz")
        
        if issues:
            print("\n❌ 发现配置问题:")
            for issue in issues:
                print(f"   • {issue}")
            return False
        else:
            print("\n✅ 配置验证通过")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 JCY5001AS 系统简化工具")
    print("=" * 80)
    print("此工具将系统简化为：研究模式 + 错频测试")
    print("移除所有其他模式，避免配置冲突")
    print("=" * 80)
    
    # 1. 简化配置
    config_success = simplify_config()
    
    # 2. 创建锁定
    lock_success = create_mode_lock()
    
    # 3. 验证配置
    verify_success = verify_simplified_config()
    
    # 总结
    print("\n🎯 简化总结")
    print("=" * 80)
    
    if config_success and verify_success:
        print("✅ 系统简化成功")
        print("✅ 配置验证通过")
        print("✅ 现在只有：研究模式 + 错频测试")
        
        print("\n📋 简化后的系统特点:")
        print("• 🎯 单一模式：研究模式（20个频点）")
        print("• 🔄 单一测试方式：错频测试")
        print("• 📊 高频点（>15Hz）：错频测试")
        print("• 📊 低频点（≤15Hz）：同时测试")
        print("• 🚫 移除：生产模式、快速模式、同时启动等")
        
        print("\n📋 下一步操作:")
        print("1. 重启应用程序")
        print("2. 开始研究模式测试")
        print("3. 系统将自动使用错频测试")
        print("4. 测试时间约3-4分钟")
        
    else:
        print("❌ 系统简化失败")
        print("请检查错误信息并重试")
    
    if lock_success:
        print("✅ 模式锁定已启用")
    
    return config_success and verify_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
