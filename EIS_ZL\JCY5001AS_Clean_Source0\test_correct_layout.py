#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的统计框布局
验证数值框紧跟标签的效果
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QGroupBox, QFrame, QPushButton, QGridLayout)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class CorrectLayoutTest(QMainWindow):
    """正确布局测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 正确统计框布局测试")
        self.setGeometry(100, 100, 1200, 600)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("统计框布局修正测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建对比区域
        self._create_comparison_area(main_layout)
        
        # 添加说明
        info_label = QLabel("""
        🎯 修正内容：
        • 添加第三列用于占据剩余空间
        • 让数值框紧跟在标签后面，不被拉伸到右边
        • 形成"标签: [数值]"的紧凑布局
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #d4edda; 
            padding: 10px; 
            border-radius: 5px; 
            border: 1px solid #c3e6cb;
        """)
        main_layout.addWidget(info_label)
    
    def _create_comparison_area(self, layout):
        """创建对比区域"""
        comparison_frame = QFrame()
        comparison_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        comparison_layout = QHBoxLayout(comparison_frame)
        
        # 错误布局（原来的问题）
        wrong_widget = self._create_wrong_layout()
        comparison_layout.addWidget(wrong_widget)
        
        # 正确布局（修正后的效果）
        correct_widget = self._create_correct_layout()
        comparison_layout.addWidget(correct_widget)
        
        layout.addWidget(comparison_frame)
    
    def _create_wrong_layout(self):
        """创建错误布局演示（问题布局）"""
        wrong_group = QGroupBox("❌ 错误布局 - 数值框被拉伸到右边")
        wrong_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 5px;
                margin-top: 0.2ex;
                padding-top: 2px;
                background-color: white;
            }
            QGroupBox::title {
                color: #e74c3c;
                font-size: 12pt;
                font-weight: bold;
            }
        """)
        
        layout = QGridLayout(wrong_group)
        layout.setSpacing(4)
        layout.setHorizontalSpacing(5)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加统计项
        items = [
            ("总测试数:", "474", "#ecf0f1", "#bdc3c7"),
            ("合格数:", "265", "#d5f4e6", "#27ae60"),
            ("不合格数:", "209", "#fadbd8", "#e74c3c"),
            ("良率:", "55.9%", "#ebf3fd", "#3498db")
        ]
        
        for i, (label_text, value_text, bg_color, border_color) in enumerate(items):
            label = QLabel(label_text)
            layout.addWidget(label, i, 0)
            
            value = QLabel(value_text)
            value.setStyleSheet(f"""
                background-color: {bg_color}; border: 1px solid {border_color};
                border-radius: 4px; padding: 4px 6px;
                min-width: 48px; max-width: 48px; text-align: center;
                font-weight: bold; margin: 0px;
            """)
            value.setAlignment(Qt.AlignCenter)
            layout.addWidget(value, i, 1)
        
        # 错误设置：第二列被拉伸，导致数值框跑到右边
        layout.setColumnStretch(0, 0)
        layout.setColumnStretch(1, 1)  # 这里拉伸导致问题
        
        return wrong_group
    
    def _create_correct_layout(self):
        """创建正确布局演示（修正后的效果）"""
        correct_group = QGroupBox("✅ 正确布局 - 数值框紧跟标签")
        correct_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 5px;
                margin-top: 0.2ex;
                padding-top: 2px;
                background-color: white;
            }
            QGroupBox::title {
                color: #27ae60;
                font-size: 12pt;
                font-weight: bold;
            }
        """)
        
        layout = QGridLayout(correct_group)
        layout.setSpacing(4)
        layout.setHorizontalSpacing(5)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加统计项
        items = [
            ("总测试数:", "474", "#ecf0f1", "#bdc3c7"),
            ("合格数:", "265", "#d5f4e6", "#27ae60"),
            ("不合格数:", "209", "#fadbd8", "#e74c3c"),
            ("良率:", "55.9%", "#ebf3fd", "#3498db")
        ]
        
        for i, (label_text, value_text, bg_color, border_color) in enumerate(items):
            label = QLabel(label_text)
            layout.addWidget(label, i, 0)
            
            value = QLabel(value_text)
            value.setStyleSheet(f"""
                background-color: {bg_color}; border: 1px solid {border_color};
                border-radius: 4px; padding: 4px 6px;
                min-width: 48px; max-width: 48px; text-align: center;
                font-weight: bold; margin: 0px;
            """)
            value.setAlignment(Qt.AlignCenter)
            layout.addWidget(value, i, 1)
            
            # 添加空白占位符到第三列
            spacer = QWidget()
            layout.addWidget(spacer, i, 2)
        
        # 正确设置：第三列拉伸，让数值框紧跟标签
        layout.setColumnStretch(0, 0)  # 标签列固定
        layout.setColumnStretch(1, 0)  # 数值框列固定
        layout.setColumnStretch(2, 1)  # 第三列拉伸占据剩余空间
        
        return correct_group

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = CorrectLayoutTest()
    window.show()
    
    print("🚀 统计框布局修正测试启动")
    print("=" * 50)
    print("📋 对比内容:")
    print("   ❌ 错误布局：数值框被拉伸到右边")
    print("   ✅ 正确布局：数值框紧跟标签")
    print("=" * 50)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
