#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触发测试脚本
模拟界面操作，自动启动测试并监控结果
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.test_config_manager import TestConfigManager

def trigger_automated_test():
    """触发自动化测试"""
    print("🚀 JCY5001AS 自动测试触发器")
    print("=" * 60)
    
    try:
        # 1. 加载配置
        print("📋 加载测试配置...")
        config_manager = ConfigManager()
        test_config_manager = TestConfigManager(config_manager)
        test_config = test_config_manager.load_test_config()
        
        # 显示关键配置
        print(f"   • 测试模式: {test_config.get('test_mode', 'unknown')}")
        print(f"   • 频点数量: {len(test_config.get('frequencies', []))}")
        print(f"   • 计数限制: {'禁用' if not test_config.get('count_limit_enabled', True) else '启用'}")
        print(f"   • 并行错频: {'启用' if test_config.get('use_parallel_staggered_mode', False) else '禁用'}")
        print(f"   • 超时时间: {test_config.get('timeout', 0)}秒")
        
        # 2. 准备测试数据
        print(f"\n🔧 准备测试数据...")
        battery_codes = [
            "TEST-001", "TEST-002", "TEST-003", "TEST-004",
            "TEST-005", "TEST-006", "TEST-007", "TEST-008"
        ]
        
        print(f"   • 电池编码: {battery_codes}")
        
        # 3. 尝试导入并启动测试引擎
        print(f"\n⚙️ 启动测试引擎...")
        
        try:
            from backend.test_engine_adapter import TestEngineAdapter
            from backend.communication_manager import CommunicationManager
            from data.database_manager import DatabaseManager

            # 创建通信管理器
            comm_manager = CommunicationManager(config_manager)

            # 尝试连接设备
            print("🔌 尝试连接设备...")
            if comm_manager.connect():
                print("✅ 设备连接成功")

                # 创建数据库管理器
                db_path = config_manager.get('database.path', 'data/test_results.db')
                db_manager = DatabaseManager(db_path)

                # 创建测试引擎
                test_engine = TestEngineAdapter(config_manager, db_manager, comm_manager)
                
                # 设置进度监控
                progress_data = {'completed_frequencies': 0, 'total_frequencies': 0}
                
                def progress_callback(channel, data):
                    freq_index = data.get('frequency_index', 0)
                    total_freq = data.get('total_frequencies', 0)
                    frequency = data.get('frequency', 0)
                    
                    if freq_index > progress_data['completed_frequencies']:
                        progress_data['completed_frequencies'] = freq_index
                        progress_data['total_frequencies'] = total_freq
                        print(f"📊 测试进度: {freq_index}/{total_freq} - 频点: {frequency:.3f}Hz")
                
                def status_callback(data):
                    action = data.get('action', 'unknown')
                    if action == 'test_completed':
                        print("✅ 测试完成通知")
                    elif action == 'frequency_completed':
                        frequency = data.get('frequency', 0)
                        print(f"🎯 频点完成: {frequency:.3f}Hz")
                
                test_engine.set_progress_callback(progress_callback)
                test_engine.set_status_callback(status_callback)
                
                # 启动测试
                print(f"\n🚀 启动批量测试...")
                start_time = time.time()
                print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
                
                success = test_engine.start_batch_test(battery_codes, "AUTO-TEST-BATCH")
                
                if success:
                    print("✅ 测试启动成功")
                    
                    # 监控测试进度
                    print(f"\n👀 监控测试进度...")
                    timeout = 300  # 5分钟超时
                    check_interval = 3  # 每3秒检查一次
                    
                    elapsed = 0
                    while elapsed < timeout:
                        time.sleep(check_interval)
                        elapsed += check_interval
                        
                        # 显示进度
                        if progress_data['total_frequencies'] > 0:
                            progress_percent = (progress_data['completed_frequencies'] / progress_data['total_frequencies']) * 100
                            print(f"📈 当前进度: {progress_data['completed_frequencies']}/{progress_data['total_frequencies']} ({progress_percent:.1f}%) - 已用时: {elapsed}秒")
                        
                        # 检查是否完成
                        if progress_data['completed_frequencies'] >= progress_data['total_frequencies'] and progress_data['total_frequencies'] > 0:
                            break
                    
                    end_time = time.time()
                    total_time = end_time - start_time
                    
                    print(f"\n📊 测试结果:")
                    print(f"   • 总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
                    print(f"   • 完成频点: {progress_data['completed_frequencies']}/{progress_data['total_frequencies']}")
                    
                    if progress_data['completed_frequencies'] >= 20:
                        print("🎉 测试成功！所有20个频点都已测试完成！")
                    else:
                        print(f"⚠️ 测试不完整，只完成了{progress_data['completed_frequencies']}个频点")
                    
                else:
                    print("❌ 测试启动失败")
                
                # 断开连接
                comm_manager.disconnect()
                print("🔌 设备连接已断开")
                
            else:
                print("❌ 设备连接失败")
                
        except Exception as e:
            print(f"❌ 测试引擎异常: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 测试触发异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        trigger_automated_test()
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 主程序异常: {e}")

if __name__ == "__main__":
    main()
