#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JCY5001AS通道显示组件布局压缩优化效果
验证左列压缩和右列扩展的效果

Author: Assistant
Date: 2025-07-05
"""

import sys
import os
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QFrame, QLineEdit, QGroupBox,
                             QScrollArea, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ChannelLayoutCompressionTestWindow(QMainWindow):
    """通道布局压缩测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 通道布局压缩优化测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        self._add_description(main_layout)
        
        # 创建对比测试
        self._create_comparison_test(main_layout)
        
        logger.info("通道布局压缩优化测试窗口已启动")
        logger.info("主要优化：")
        logger.info("• 左右列权重调整：从2:3调整为1.5:3.5")
        logger.info("• 左列组件压缩：减少标签文字、字体大小、组件宽度")
        logger.info("• 右列扩展：Rs/Rct标题80px，数值120px，字体14pt")
        logger.info("• 整体紧凑化：减少间距和内边距")
    
    def _add_description(self, layout):
        """添加说明文字"""
        desc_label = QLabel("""
        <h2>JCY5001AS 通道布局压缩优化测试</h2>
        <p><b>优化目标：</b>缩短左列宽度，为右列Rs/Rct阻抗值显示释放更多空间</p>
        <p><b>主要改进：</b></p>
        <ul>
        <li><b>布局权重调整：</b>从2:3调整为1.5:3.5，右列获得更多空间</li>
        <li><b>左列压缩：</b>简化标签文字、减小字体、压缩组件宽度</li>
        <li><b>右列扩展：</b>Rs/Rct标题80px，数值120px，字体增大到14pt</li>
        <li><b>整体优化：</b>减少间距和内边距，提高空间利用率</li>
        </ul>
        """)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 11pt;
            }
        """)
        layout.addWidget(desc_label)
    
    def _create_comparison_test(self, layout):
        """创建对比测试"""
        comparison_frame = QFrame()
        comparison_layout = QHBoxLayout(comparison_frame)
        comparison_layout.setSpacing(20)
        
        # 优化前的布局
        before_demo = self._create_demo_channel("优化前布局", {
            'left_weight': 2,
            'right_weight': 3,
            'count_label': '测试计数:',
            'time_label': '测试用时:',
            'battery_label': '电池码:',
            'voltage_label': '电压(V):',
            'battery_width': '150px',
            'rs_title_width': '70px',
            'rs_value_width': '100px',
            'rs_title_font': '11pt',
            'rs_value_font': '13pt',
            'spacing': '8px',
            'font_size': '10pt'
        })
        comparison_layout.addWidget(before_demo)
        
        # 优化后的布局
        after_demo = self._create_demo_channel("优化后布局", {
            'left_weight': 15,  # 1.5份权重
            'right_weight': 35,  # 3.5份权重
            'count_label': '计数:',
            'time_label': '用时:',
            'battery_label': '电池:',
            'voltage_label': '电压:',
            'battery_width': '120px',
            'rs_title_width': '80px',
            'rs_value_width': '120px',
            'rs_title_font': '12pt',
            'rs_value_font': '14pt',
            'spacing': '4px',
            'font_size': '9pt'
        })
        comparison_layout.addWidget(after_demo)
        
        layout.addWidget(comparison_frame)
    
    def _create_demo_channel(self, title, config):
        """创建演示通道"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout(demo_widget)
        demo_layout.setContentsMargins(10, 10, 10, 10)
        demo_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        demo_layout.addWidget(title_label)
        
        # 通道组框
        channel_frame = QGroupBox("通道 1")
        channel_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        
        channel_layout = QVBoxLayout(channel_frame)
        channel_layout.setContentsMargins(6, 8, 6, 6)
        channel_layout.setSpacing(3)
        
        # 主内容区域
        main_content = QFrame()
        main_content_layout = QHBoxLayout(main_content)
        main_content_layout.setSpacing(int(config['spacing'].replace('px', '')))
        main_content_layout.setContentsMargins(4, 4, 4, 4)
        
        # 左列
        left_column = self._create_left_column_demo(config)
        main_content_layout.addLayout(left_column, config['left_weight'])
        
        # 右列
        right_column = self._create_right_column_demo(config)
        main_content_layout.addLayout(right_column, config['right_weight'])
        
        channel_layout.addWidget(main_content)
        
        # 添加配置信息
        info_text = f"""
        权重比例: {config['left_weight']}:{config['right_weight']}
        电池码宽度: {config['battery_width']}
        Rs标题: {config['rs_title_width']} | Rs数值: {config['rs_value_width']}
        字体大小: {config['font_size']} | Rs数值字体: {config['rs_value_font']}
        组件间距: {config['spacing']}
        """
        
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Microsoft YaHei", 8))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        demo_layout.addWidget(info_label)
        demo_layout.addWidget(channel_frame)
        
        return demo_widget
    
    def _create_left_column_demo(self, config):
        """创建左列演示"""
        left_layout = QVBoxLayout()
        left_layout.setSpacing(2)
        
        # 测试计数和时间
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(int(config['spacing'].replace('px', '')))
        
        count_label = QLabel(config['count_label'])
        count_label.setStyleSheet(f"font-size: {config['font_size']}; color: #7f8c8d; font-weight: bold;")
        count_time_layout.addWidget(count_label)
        
        count_value = QLabel("73")
        count_value.setStyleSheet(f"font-size: {config['font_size']}; color: #27ae60; font-weight: bold;")
        count_time_layout.addWidget(count_value)
        
        separator = QLabel("|")
        count_time_layout.addWidget(separator)
        
        time_label = QLabel(config['time_label'])
        time_label.setStyleSheet(f"font-size: {config['font_size']}; color: #7f8c8d; font-weight: bold;")
        count_time_layout.addWidget(time_label)
        
        time_value = QLabel("00:05:23")
        time_value.setStyleSheet(f"font-size: {config['font_size']}; color: #2c3e50; font-weight: bold;")
        count_time_layout.addWidget(time_value)
        
        count_time_layout.addStretch()
        left_layout.addLayout(count_time_layout)
        
        # 电池码输入
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(int(config['spacing'].replace('px', '')))
        
        battery_label = QLabel(config['battery_label'])
        battery_label.setStyleSheet(f"font-size: {config['font_size']}; color: #7f8c8d; font-weight: bold;")
        battery_layout.addWidget(battery_label)
        
        battery_edit = QLineEdit("JCY-20250705-6274")
        battery_edit.setStyleSheet(f"""
            QLineEdit {{
                font-size: {config['font_size']};
                padding: 4px 6px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
                min-width: {config['battery_width']};
                min-height: 24px;
            }}
        """)
        battery_layout.addWidget(battery_edit)
        
        left_layout.addLayout(battery_layout)
        
        # 电压显示
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(int(config['spacing'].replace('px', '')))
        
        voltage_label = QLabel(config['voltage_label'])
        voltage_label.setStyleSheet(f"font-size: {config['font_size']}; color: #7f8c8d; font-weight: bold;")
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel("3.247")
        voltage_value.setStyleSheet(f"font-size: {config['font_size']}; color: #2c3e50; font-weight: bold;")
        voltage_layout.addWidget(voltage_value)
        voltage_layout.addStretch()
        
        left_layout.addLayout(voltage_layout)
        left_layout.addStretch()
        
        return left_layout
    
    def _create_right_column_demo(self, config):
        """创建右列演示"""
        right_layout = QVBoxLayout()
        right_layout.setSpacing(4)
        
        # Rs显示
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(int(config['spacing'].replace('px', '')) * 2)
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet(f"""
            font-size: {config['rs_title_font']}; 
            color: #7f8c8d; 
            font-weight: bold;
            min-width: {config['rs_title_width']};
        """)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("17.807")
        rs_value.setStyleSheet(f"""
            QLabel {{
                font-size: {config['rs_value_font']};
                font-weight: bold;
                color: #2c3e50;
                background-color: #f8f9fa;
                padding: 6px 12px;
                border: 1px solid #ecf0f1;
                border-radius: 6px;
                min-width: {config['rs_value_width']};
                text-align: left;
            }}
        """)
        rs_layout.addWidget(rs_value)
        rs_layout.addStretch()
        
        right_layout.addLayout(rs_layout)
        
        # Rct显示
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(int(config['spacing'].replace('px', '')) * 2)
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet(f"""
            font-size: {config['rs_title_font']}; 
            color: #7f8c8d; 
            font-weight: bold;
            min-width: {config['rs_title_width']};
        """)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("2.224")
        rct_value.setStyleSheet(f"""
            QLabel {{
                font-size: {config['rs_value_font']};
                font-weight: bold;
                color: #2c3e50;
                background-color: #f8f9fa;
                padding: 6px 12px;
                border: 1px solid #ecf0f1;
                border-radius: 6px;
                min-width: {config['rs_value_width']};
                text-align: left;
            }}
        """)
        rct_layout.addWidget(rct_value)
        rct_layout.addStretch()
        
        right_layout.addLayout(rct_layout)
        right_layout.addStretch()
        
        return right_layout


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = ChannelLayoutCompressionTestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
