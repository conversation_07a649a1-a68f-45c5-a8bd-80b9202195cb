# 数据上传功能说明

## 功能概述

数据上传功能允许JCY5001A电池测试系统自动将测试结果上传到远程服务器，实现数据的集中管理和分析。

## 主要特性

### 1. 自动上传
- 测试完成后自动上传结果
- 支持单个结果和批量结果上传
- 异步上传，不影响测试流程

### 2. 可靠传输
- 支持重试机制
- 网络异常处理
- 上传队列管理

### 3. 灵活配置
- 可配置服务器地址和端点
- 支持多种认证方式
- 可调整超时和重试参数

### 4. 安全认证
- Bearer Token认证
- API Key认证
- Basic认证
- 可选择不使用认证

## 配置说明

### 配置文件位置
```
config/app_config.json
```

### 配置参数

```json
{
  "data_upload": {
    "enabled": true,                          // 是否启用数据上传
    "server_url": "http://192.168.101.101:5002",  // 服务器地址
    "endpoint": "/api/test-results",          // API端点
    "timeout": 30,                            // 超时时间（秒）
    "retry_count": 3,                         // 重试次数
    "retry_delay": 1.0,                       // 重试延时（秒）
    "batch_size": 10,                         // 批量大小
    "device_id": "JCY5001A_001",             // 设备ID
    "auto_upload": true,                      // 自动上传
    "upload_on_test_complete": true,          // 测试完成后上传
    "upload_batch_results": false,            // 批量上传
    "auth_token": "",                         // 认证令牌
    "auth_type": "bearer"                     // 认证类型
  }
}
```

## 数据格式

### 单个测试结果上传格式

```json
{
  "upload_type": "single_result",
  "device_info": {
    "device_id": "JCY5001A_001",
    "software_version": "V0.80.08",
    "upload_time": "2025-06-26T15:30:00"
  },
  "batch_info": {
    "batch_id": 123,
    "batch_number": "B20250626001",
    "operator": "张三",
    "battery_type": "磷酸铁锂",
    "battery_spec": "3.2V 100Ah"
  },
  "test_result": {
    "channel_number": 1,
    "battery_code": "BAT001",
    "voltage": 3.25,
    "rs_value": 1.234,
    "rct_value": 5.678,
    "rs_grade": 1,
    "rct_grade": 2,
    "is_pass": true,
    "fail_reason": null,
    "test_start_time": "2025-06-26T15:25:00",
    "test_end_time": "2025-06-26T15:28:00",
    "test_duration": 180.5,
    "test_mode": "生产模式",
    "frequency_list": [1.907, 11.444, 101.09, 1007.083],
    "operator": "张三",
    "battery_type": "磷酸铁锂",
    "battery_spec": "21700",
    "batch_number": "B20250626001"
  }
}
```

### 批量测试结果上传格式

```json
{
  "upload_type": "batch_results",
  "device_info": {
    "device_id": "JCY5001A_001",
    "software_version": "V0.80.08",
    "upload_time": "2025-06-26T15:30:00"
  },
  "batch_info": {
    "batch_id": 123,
    "batch_number": "B20250626001",
    "operator": "张三",
    "battery_type": "磷酸铁锂",
    "battery_spec": "3.2V 100Ah"
  },
  "test_results": [
    // 多个测试结果对象
  ],
  "result_count": 8
}
```

## 服务器端要求

### API端点
- **URL**: `POST {server_url}{endpoint}`
- **Content-Type**: `application/json`
- **认证**: 根据配置的认证类型

### 响应格式
成功响应（HTTP 200）：
```json
{
  "status": "success",
  "message": "数据接收成功",
  "timestamp": "2025-06-26T15:30:00"
}
```

错误响应：
```json
{
  "error": "错误类型",
  "message": "错误描述",
  "status_code": 400
}
```

## 使用方法

### 1. 启用数据上传
在配置文件中设置：
```json
{
  "data_upload": {
    "enabled": true
  }
}
```

### 2. 配置服务器地址
```json
{
  "data_upload": {
    "server_url": "http://your-server.com:5002",
    "endpoint": "/api/test-results"
  }
}
```

### 3. 配置认证（可选）
```json
{
  "data_upload": {
    "auth_type": "bearer",
    "auth_token": "your-auth-token"
  }
}
```

### 4. 测试连接
可以通过配置界面或代码测试服务器连接：
```python
from backend.data_upload_manager import DataUploadManager

upload_manager = DataUploadManager(config)
success = upload_manager.test_connection()
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器地址是否正确
   - 确认网络连接正常
   - 验证防火墙设置

2. **认证失败**
   - 检查认证令牌是否正确
   - 确认认证类型设置
   - 验证令牌是否过期

3. **上传超时**
   - 增加超时时间设置
   - 检查网络稳定性
   - 减少批量大小

### 日志查看
数据上传相关日志会记录在应用日志中，可以通过以下方式查看：
- 日志级别：INFO、WARNING、ERROR
- 日志内容包括：连接状态、上传进度、错误信息

## 技术实现

### 核心组件
1. **DataUploadManager**: 数据上传管理器
2. **TestResultManager**: 测试结果管理器集成
3. **DataUploadConfigDialog**: 配置界面

### 工作流程
1. 测试完成后，TestResultManager保存结果到数据库
2. 如果启用上传，调用DataUploadManager上传数据
3. DataUploadManager将数据添加到上传队列
4. 后台线程处理上传队列，发送HTTP请求
5. 处理服务器响应和错误重试

### 线程安全
- 使用队列进行线程间通信
- 后台线程处理网络请求
- 不阻塞主测试流程

## 版本信息
- 功能版本：V1.0
- 兼容软件版本：V0.80.08+
- 更新日期：2025-06-26
