#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证统计显示区域的修改是否正确
"""

import sys
import os

def verify_statistics_widget_changes():
    """验证statistics_widget.py的修改"""
    
    file_path = "ui/components/statistics_widget.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    checks = [
        # 1. 检查档位范围向左移动的修改
        ("档位范围左对齐", "setAlignment(Qt.AlignmentFlag.AlignLeft)" in content),
        
        # 2. 检查表格边框修改
        ("表格边框设置", "border: 2px solid #34495e !important" in content),
        ("表格网格线", "gridline-color: #34495e !important" in content),
        ("单元格边框", "border: 1px solid #34495e !important" in content),
        
        # 3. 检查表格高度修改
        ("表格最小高度", "min-height: 120px !important" in content),
        ("表格行高", "setDefaultSectionSize(30)" in content),
        
        # 4. 检查档位范围标签样式
        ("档位范围最小宽度", "min-width: 350px" in content),
        ("档位范围字体大小", "font-size: 10pt" in content),
        
        # 5. 检查表格显示设置
        ("显示网格线", "setShowGrid(True)" in content),
        ("边框样式", "setFrameStyle" in content),
    ]
    
    print("🔍 验证统计显示区域修改:")
    print("=" * 50)
    
    all_passed = True
    for check_name, condition in checks:
        if condition:
            print(f"✅ {check_name}: 通过")
        else:
            print(f"❌ {check_name}: 失败")
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有修改验证通过！")
        print("\n📋 修改总结:")
        print("1. ✅ RS档位和RCT档位已设置为向左对齐")
        print("2. ✅ 表格边框已完善，包括底部边框")
        print("3. ✅ 表格高度已增加，确保3x3表格完整显示")
        print("4. ✅ 档位范围标签宽度已增加，确保文字完整显示")
        print("5. ✅ 表格网格线和边框样式已优化")
    else:
        print("⚠️  部分修改可能未正确应用")
    
    return all_passed

def main():
    """主函数"""
    print("🚀 JCY5001AS 统计显示区域修改验证")
    print("📝 验证内容:")
    print("   1. RS档位和RCT档位向左移动")
    print("   2. 表格边框完整显示（包括底部边框）")
    print("   3. 档位范围文字完整显示")
    print("   4. 表格高度和样式优化")
    print()
    
    success = verify_statistics_widget_changes()
    
    if success:
        print("\n🎯 建议测试步骤:")
        print("1. 运行主程序查看统计显示区域")
        print("2. 检查RS档位和RCT档位是否向左移动")
        print("3. 检查表格是否有完整的边框")
        print("4. 检查档位范围文字是否完整显示")
        print("5. 检查3x3表格是否完整显示所有数据")
    
    return success

if __name__ == "__main__":
    main()
