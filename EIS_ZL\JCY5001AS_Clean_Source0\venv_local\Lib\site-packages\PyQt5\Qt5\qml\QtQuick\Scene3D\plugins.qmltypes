import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Scene3D 2.15'

Module {
    dependencies: ["Qt3D.Core 2.0", "QtQuick 2.0"]
    Component {
        name: "Qt3DRender::Scene3DItem"
        defaultProperty: "entity"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Scene3D/Scene3D 2.0",
            "QtQuick.Scene3D/Scene3D 2.14"
        ]
        exportMetaObjectRevisions: [0, 14]
        Enum {
            name: "CameraAspectRatioMode"
            values: {
                "AutomaticAspectRatio": 0,
                "UserAspectRatio": 1
            }
        }
        Enum {
            name: "CompositingMode"
            values: {
                "FBO": 0,
                "Underlay": 1
            }
        }
        Property { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        Property { name: "aspects"; type: "QStringList" }
        Property { name: "multisample"; type: "bool" }
        Property { name: "cameraAspectRatioMode"; type: "CameraAspectRatioMode" }
        Property { name: "hoverEnabled"; type: "bool" }
        Property { name: "compositingMode"; revision: 14; type: "CompositingMode" }
        Signal {
            name: "cameraAspectRatioModeChanged"
            Parameter { name: "mode"; type: "CameraAspectRatioMode" }
        }
        Method {
            name: "setAspects"
            Parameter { name: "aspects"; type: "QStringList" }
        }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setCameraAspectRatioMode"
            Parameter { name: "mode"; type: "CameraAspectRatioMode" }
        }
        Method {
            name: "setHoverEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setCompositingMode"
            Parameter { name: "mode"; type: "CompositingMode" }
        }
        Method {
            name: "setItemAreaAndDevicePixelRatio"
            Parameter { name: "area"; type: "QSize" }
            Parameter { name: "devicePixelRatio"; type: "double" }
        }
    }
    Component {
        name: "Qt3DRender::Scene3DView"
        defaultProperty: "entity"
        prototype: "QQuickItem"
        exports: ["QtQuick.Scene3D/Scene3DView 2.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        Property { name: "scene3D"; type: "Qt3DRender::Scene3DItem"; isPointer: true }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setScene3D"
            Parameter { name: "scene3D"; type: "Scene3DItem"; isPointer: true }
        }
    }
}
