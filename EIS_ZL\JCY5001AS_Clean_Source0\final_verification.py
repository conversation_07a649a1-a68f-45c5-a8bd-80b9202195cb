#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本
确认所有配置都正确设置，能够测试20个频点
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.test_config_manager import TestConfigManager

def verify_final_config():
    """最终配置验证"""
    print("🎯 最终配置验证")
    print("=" * 80)
    
    try:
        # 1. 验证配置文件
        print("📋 1. 配置文件验证:")
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        test_config = config.get('test', {})
        test_params = config.get('test_params', {})
        frequency_config = config.get('frequency', {})
        
        print(f"   • test.count_limit_enabled: {test_config.get('count_limit_enabled', 'N/A')}")
        print(f"   • test.max_count: {test_config.get('max_count', 'N/A')}")
        print(f"   • test.use_parallel_staggered_mode: {test_config.get('use_parallel_staggered_mode', 'N/A')}")
        print(f"   • test_params.test_mode: {test_params.get('test_mode', 'N/A')}")
        print(f"   • frequency.preset_mode: {frequency_config.get('preset_mode', 'N/A')}")
        print(f"   • frequency.list: {len(frequency_config.get('list', []))}个频点")
        
        # 2. 验证ConfigManager
        print("\n📋 2. ConfigManager验证:")
        config_manager = ConfigManager()
        
        cm_count_limit = config_manager.get('test.count_limit_enabled', 'N/A')
        cm_max_count = config_manager.get('test.max_count', 'N/A')
        cm_parallel_staggered = config_manager.get('test.use_parallel_staggered_mode', 'N/A')
        cm_test_mode = config_manager.get('test_params.test_mode', 'N/A')
        cm_preset_mode = config_manager.get('frequency.preset_mode', 'N/A')
        cm_frequency_list = config_manager.get('frequency.list', [])
        
        print(f"   • test.count_limit_enabled: {cm_count_limit}")
        print(f"   • test.max_count: {cm_max_count}")
        print(f"   • test.use_parallel_staggered_mode: {cm_parallel_staggered}")
        print(f"   • test_params.test_mode: {cm_test_mode}")
        print(f"   • frequency.preset_mode: {cm_preset_mode}")
        print(f"   • frequency.list: {len(cm_frequency_list)}个频点")
        
        # 3. 验证TestConfigManager
        print("\n📋 3. TestConfigManager验证:")
        test_config_manager = TestConfigManager(config_manager)
        test_config = test_config_manager.load_test_config()
        
        tcm_count_limit = test_config.get('count_limit_enabled', 'N/A')
        tcm_max_count = test_config.get('max_count', 'N/A')
        tcm_parallel_staggered = test_config.get('use_parallel_staggered_mode', 'N/A')
        tcm_test_mode = test_config.get('test_mode', 'N/A')
        tcm_frequencies = test_config.get('frequencies', [])
        
        print(f"   • count_limit_enabled: {tcm_count_limit}")
        print(f"   • max_count: {tcm_max_count}")
        print(f"   • use_parallel_staggered_mode: {tcm_parallel_staggered}")
        print(f"   • test_mode: {tcm_test_mode}")
        print(f"   • frequencies: {len(tcm_frequencies)}个频点")
        
        # 4. 问题检查
        print("\n🔍 4. 问题检查:")
        issues = []
        
        if tcm_count_limit != False:
            issues.append(f"count_limit_enabled应该是False，当前是{tcm_count_limit}")
        
        if tcm_max_count != 100:
            issues.append(f"max_count应该是100，当前是{tcm_max_count}")
        
        if tcm_parallel_staggered != False:
            issues.append(f"use_parallel_staggered_mode应该是False，当前是{tcm_parallel_staggered}")
        
        if tcm_test_mode != '研究模式':
            issues.append(f"test_mode应该是'研究模式'，当前是'{tcm_test_mode}'")
        
        if len(tcm_frequencies) != 20:
            issues.append(f"frequencies应该有20个频点，当前有{len(tcm_frequencies)}个")
        
        if issues:
            print("❌ 发现问题:")
            for issue in issues:
                print(f"   • {issue}")
            return False
        else:
            print("✅ 所有配置检查通过")
            
        # 5. 显示频点列表
        print(f"\n📊 5. 研究模式频点列表 ({len(tcm_frequencies)}个):")
        for i, freq in enumerate(tcm_frequencies, 1):
            print(f"   {i:2d}. {freq:>8.3f} Hz")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = verify_final_config()
    
    print("\n🎯 最终验证结果")
    print("=" * 80)
    
    if success:
        print("🎉 所有验证通过！")
        print("✅ 研究模式配置正确")
        print("✅ 20个频点配置正确")
        print("✅ 并行错频模式已禁用")
        print("✅ 计数限制已禁用")
        print("✅ 最大计数设置为100")
        
        print("\n📋 现在可以进行测试:")
        print("1. 在应用程序中开始研究模式测试")
        print("2. 应该测试所有20个频点")
        print("3. 测试时间约3-4分钟")
        print("4. 不会因为计数限制而提前停止")
        
    else:
        print("❌ 验证失败！")
        print("请检查上述错误信息并修复配置")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
