#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证配置加载工具
检查配置是否正确加载到系统中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.test_config_manager import TestConfigManager

def verify_config_loading():
    """验证配置加载"""
    print("🔍 验证配置加载状态")
    print("=" * 80)
    
    # 1. 检查基础配置管理器
    print("📋 基础配置管理器:")
    config_manager = ConfigManager()
    
    # 检查关键配置项
    count_limit_enabled = config_manager.get('test.count_limit_enabled', None)
    max_count = config_manager.get('test.max_count', None)
    timeout = config_manager.get('test.timeout', None)
    use_parallel_staggered = config_manager.get('test.use_parallel_staggered_mode', None)
    
    print(f"   • count_limit_enabled: {count_limit_enabled}")
    print(f"   • max_count: {max_count}")
    print(f"   • timeout: {timeout}")
    print(f"   • use_parallel_staggered_mode: {use_parallel_staggered}")
    
    # 2. 检查测试配置管理器
    print("\n📋 测试配置管理器:")
    test_config_manager = TestConfigManager(config_manager)
    test_config = test_config_manager.load_test_config()
    
    print(f"   • count_limit_enabled: {test_config.get('count_limit_enabled', 'N/A')}")
    print(f"   • max_count: {test_config.get('max_count', 'N/A')}")
    print(f"   • timeout: {test_config.get('timeout', 'N/A')}")
    print(f"   • use_parallel_staggered_mode: {test_config.get('use_parallel_staggered_mode', 'N/A')}")
    print(f"   • frequencies count: {len(test_config.get('frequencies', []))}")
    
    # 3. 检查频率配置
    print("\n📊 频率配置:")
    frequencies = test_config.get('frequencies', [])
    print(f"   • 频点数量: {len(frequencies)}")
    if frequencies:
        print(f"   • 频率范围: {frequencies[0]:.3f}Hz ~ {frequencies[-1]:.3f}Hz")
        print(f"   • 前5个频点: {[f'{f:.3f}' for f in frequencies[:5]]}")
        print(f"   • 后5个频点: {[f'{f:.3f}' for f in frequencies[-5:]]}")
    
    # 4. 验证配置一致性
    print("\n🔧 配置一致性检查:")
    
    # 检查计数限制
    if count_limit_enabled == False and test_config.get('count_limit_enabled') == False:
        print("✅ 计数限制配置一致: 已禁用")
    else:
        print(f"❌ 计数限制配置不一致!")
        print(f"   基础配置: {count_limit_enabled}")
        print(f"   测试配置: {test_config.get('count_limit_enabled')}")
    
    # 检查最大测试次数
    if max_count == 100 and test_config.get('max_count') == 100:
        print("✅ 最大测试次数配置一致: 100")
    else:
        print(f"❌ 最大测试次数配置不一致!")
        print(f"   基础配置: {max_count}")
        print(f"   测试配置: {test_config.get('max_count')}")
    
    # 检查超时时间
    if timeout == 8 and test_config.get('timeout') == 8:
        print("✅ 超时时间配置一致: 8秒")
    else:
        print(f"❌ 超时时间配置不一致!")
        print(f"   基础配置: {timeout}")
        print(f"   测试配置: {test_config.get('timeout')}")
    
    # 检查并行错频模式
    if use_parallel_staggered == True and test_config.get('use_parallel_staggered_mode') == True:
        print("✅ 并行错频模式配置一致: 已启用")
    else:
        print(f"❌ 并行错频模式配置不一致!")
        print(f"   基础配置: {use_parallel_staggered}")
        print(f"   测试配置: {test_config.get('use_parallel_staggered_mode')}")
    
    # 检查频点数量
    if len(frequencies) == 20:
        print("✅ 频点数量正确: 20个")
    else:
        print(f"❌ 频点数量不正确: {len(frequencies)}个")
    
    # 5. 总结
    print("\n🎯 总结:")
    print("=" * 80)
    
    all_correct = (
        count_limit_enabled == False and
        max_count == 100 and
        timeout == 8 and
        use_parallel_staggered == True and
        len(frequencies) == 20
    )
    
    if all_correct:
        print("✅ 所有配置都正确加载！")
        print("📊 预期测试行为:")
        print("   • 测试所有20个频点")
        print("   • 使用并行错频模式")
        print("   • 每个频点超时8秒")
        print("   • 无测试次数限制")
        print("   • 预估测试时间: 约1-2分钟")
    else:
        print("❌ 配置存在问题，需要进一步检查")
        print("🔧 建议:")
        print("   • 重启应用程序")
        print("   • 检查配置文件是否正确保存")
        print("   • 清除可能的配置缓存")

def main():
    """主函数"""
    try:
        verify_config_loading()
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
