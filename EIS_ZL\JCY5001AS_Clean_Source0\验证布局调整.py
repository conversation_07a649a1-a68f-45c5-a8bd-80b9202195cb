#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证JCY5001AS上层区域布局调整效果
检查关键布局参数是否已正确应用

Author: Assistant
Date: 2025-01-27
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def verify_layout_adjustments():
    """验证布局调整是否正确应用"""
    print("🔍 验证JCY5001AS上层区域布局调整")
    print("=" * 50)
    
    try:
        # 1. 检查窗口布局管理器
        print("\n1. 检查窗口布局管理器...")
        from ui.main_window_managers.window_layout_manager import WindowLayoutManager
        
        # 创建临时配置管理器
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        
        # 检查关键参数
        print("✅ 窗口布局管理器导入成功")
        
        # 2. 检查批次信息组件
        print("\n2. 检查批次信息组件...")
        from ui.components.batch_info_widget import BatchInfoWidget
        print("✅ 批次信息组件导入成功")
        
        # 3. 检查统计信息组件
        print("\n3. 检查统计信息组件...")
        from ui.components.statistics_widget import StatisticsWidget
        print("✅ 统计信息组件导入成功")
        
        # 4. 检查测试控制组件
        print("\n4. 检查测试控制组件...")
        from ui.components.test_control_widget import TestControlWidget
        print("✅ 测试控制组件导入成功")
        
        # 5. 验证布局参数
        print("\n5. 验证关键布局参数...")
        
        # 检查源代码中的关键参数
        layout_file = os.path.join(project_root, 'ui', 'main_window_managers', 'window_layout_manager.py')
        if os.path.exists(layout_file):
            with open(layout_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查上层区域高度
                if 'setFixedHeight(280)' in content:
                    print("✅ 上层区域高度已调整为280px")
                else:
                    print("❌ 上层区域高度调整未找到")
                
                # 检查分割器尺寸
                if 'setSizes([54, 280, 746])' in content:
                    print("✅ 分割器尺寸比例已更新")
                else:
                    print("❌ 分割器尺寸比例未更新")
                
                # 检查边距设置
                if 'setContentsMargins(8, 8, 8, 8)' in content:
                    print("✅ 上层区域边距已增加")
                else:
                    print("❌ 上层区域边距未调整")
        
        # 6. 检查组件样式调整
        print("\n6. 检查组件样式调整...")
        
        # 检查批次信息组件
        batch_file = os.path.join(project_root, 'ui', 'components', 'batch_info_widget.py')
        if os.path.exists(batch_file):
            with open(batch_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'setContentsMargins(12, 12, 12, 12)' in content:
                    print("✅ 批次信息组件边距已调整")
                if 'font-size: 12pt' in content:
                    print("✅ 批次信息组件字体已优化")
        
        # 检查统计信息组件
        stats_file = os.path.join(project_root, 'ui', 'components', 'statistics_widget.py')
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'setContentsMargins(8, 8, 8, 8)' in content:
                    print("✅ 统计信息组件边距已调整")
                if 'font-size: 16pt' in content:
                    print("✅ 统计信息组件字体已优化")
        
        # 检查测试控制组件
        control_file = os.path.join(project_root, 'ui', 'components', 'test_control_widget.py')
        if os.path.exists(control_file):
            with open(control_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'setMinimumHeight(45)' in content:
                    print("✅ 测试控制按钮高度已调整")
                if 'font-size: 14pt' in content:
                    print("✅ 测试控制组件字体已优化")
        
        print("\n" + "=" * 50)
        print("🎉 布局调整验证完成！")
        print("\n📋 调整总结:")
        print("• 上层区域高度: 203px → 280px (+77px)")
        print("• 布局比例: 标题5% + 统计30% + 通道65%")
        print("• 批次信息: 边距和字体优化")
        print("• 统计信息: 边距和字体优化")
        print("• 测试控制: 按钮高度和字体优化")
        print("\n✨ 所有调整已成功应用，界面显示空间更加充足！")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = verify_layout_adjustments()
    
    if success:
        print("\n🚀 建议:")
        print("1. 运行主程序查看实际效果: python main.py")
        print("2. 运行布局对比测试: python test_layout_adjustment.py")
        print("3. 查看详细说明文档: 布局调整说明.md")
    else:
        print("\n❌ 验证失败，请检查代码调整是否正确")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
