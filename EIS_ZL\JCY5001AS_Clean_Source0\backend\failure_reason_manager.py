"""
失败原因详细化管理器

职责：
- 生成详细的失败原因描述
- 包含具体的超标范围信息
- 支持电压、Rs、Rct等参数的失败原因分析
"""

import logging
from typing import List, Optional

logger = logging.getLogger(__name__)


class FailureReasonManager:
    """
    失败原因详细化管理器
    
    职责：
    - 生成详细的失败原因描述
    - 包含具体的超标范围信息
    - 支持多种参数的失败原因分析
    """
    
    def __init__(self, config_manager):
        """
        初始化失败原因管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        
        logger.debug("失败原因详细化管理器初始化完成")
    
    def generate_detailed_failure_reason(self, voltage: float, rs_value: float, rct_value: float, 
                                       outlier_result: Optional[str] = None) -> str:
        """
        生成详细的失败原因描述
        
        Args:
            voltage: 电压值 (V)
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            outlier_result: 离群率检测结果
            
        Returns:
            详细的失败原因描述
        """
        try:
            failure_reasons = []
            
            # 检查电压失败原因
            voltage_reason = self._check_voltage_failure(voltage)
            if voltage_reason:
                failure_reasons.append(voltage_reason)
            
            # 检查Rs失败原因
            rs_reason = self._check_rs_failure(rs_value)
            if rs_reason:
                failure_reasons.append(rs_reason)
            
            # 检查Rct失败原因
            rct_reason = self._check_rct_failure(rct_value)
            if rct_reason:
                failure_reasons.append(rct_reason)
            
            # 检查离群率失败原因
            outlier_reason = self._check_outlier_failure(outlier_result)
            if outlier_reason:
                failure_reasons.append(outlier_reason)
            
            # 组合失败原因
            if failure_reasons:
                return "; ".join(failure_reasons)
            else:
                return ""
                
        except Exception as e:
            logger.error(f"生成详细失败原因失败: {e}")
            return "失败原因获取异常"
    
    def _check_voltage_failure(self, voltage: float) -> Optional[str]:
        """
        检查电压失败原因
        
        Args:
            voltage: 电压值 (V)
            
        Returns:
            电压失败原因描述，如果合格则返回None
        """
        try:
            # 获取电压范围配置
            voltage_min = self.config_manager.get('test_params.voltage_range.min', 2.889)
            voltage_max = self.config_manager.get('test_params.voltage_range.max', 3.531)
            
            if voltage < voltage_min:
                return f"电压超标，范围{voltage_min:.3f}V~{voltage_max:.3f}V（实测{voltage:.3f}V，过低）"
            elif voltage > voltage_max:
                return f"电压超标，范围{voltage_min:.3f}V~{voltage_max:.3f}V（实测{voltage:.3f}V，过高）"
            else:
                return None
                
        except Exception as e:
            logger.error(f"检查电压失败原因失败: {e}")
            return f"电压检查异常（{voltage:.3f}V）"
    
    def _check_rs_failure(self, rs_value: float) -> Optional[str]:
        """
        检查Rs失败原因
        
        Args:
            rs_value: Rs值 (mΩ)
            
        Returns:
            Rs失败原因描述，如果合格则返回None
        """
        try:
            # 获取Rs范围配置
            rs_min = self.config_manager.get('impedance.rs_min', 0.5)
            rs_max = self.config_manager.get('impedance.rs_grade3_max', 50.0)
            
            if rs_value < rs_min:
                return f"Rs超标，范围{rs_min:.3f}mΩ~{rs_max:.3f}mΩ（实测{rs_value:.3f}mΩ，过低）"
            elif rs_value > rs_max:
                return f"Rs超标，范围{rs_min:.3f}mΩ~{rs_max:.3f}mΩ（实测{rs_value:.3f}mΩ，过高）"
            else:
                return None
                
        except Exception as e:
            logger.error(f"检查Rs失败原因失败: {e}")
            return f"Rs检查异常（{rs_value:.3f}mΩ）"
    
    def _check_rct_failure(self, rct_value: float) -> Optional[str]:
        """
        检查Rct失败原因
        
        Args:
            rct_value: Rct值 (mΩ)
            
        Returns:
            Rct失败原因描述，如果合格则返回None
        """
        try:
            # 获取Rct范围配置
            rct_min = self.config_manager.get('impedance.rct_min', 0.5)
            rct_max = self.config_manager.get('impedance.rct_grade3_max', 100.0)
            
            if rct_value < rct_min:
                return f"Rct超标，范围{rct_min:.3f}mΩ~{rct_max:.3f}mΩ（实测{rct_value:.3f}mΩ，过低）"
            elif rct_value > rct_max:
                return f"Rct超标，范围{rct_min:.3f}mΩ~{rct_max:.3f}mΩ（实测{rct_value:.3f}mΩ，过高）"
            else:
                return None
                
        except Exception as e:
            logger.error(f"检查Rct失败原因失败: {e}")
            return f"Rct检查异常（{rct_value:.3f}mΩ）"
    
    def _check_outlier_failure(self, outlier_result: Optional[str]) -> Optional[str]:
        """
        检查离群率失败原因
        
        Args:
            outlier_result: 离群率检测结果
            
        Returns:
            离群率失败原因描述，如果合格则返回None
        """
        try:
            if not outlier_result or outlier_result == "PASS":
                return None
            
            # 如果离群率检测失败，返回具体的偏差信息
            if outlier_result != "PASS":
                try:
                    # 尝试解析偏差百分比
                    deviation = float(outlier_result.replace('%', ''))
                    threshold = self.config_manager.get('outlier_deviation_threshold', 10.0)
                    return f"离群率超标，阈值{threshold:.1f}%（实测偏差{deviation:.1f}%）"
                except:
                    return f"离群率超标（{outlier_result}）"
            
            return None
            
        except Exception as e:
            logger.error(f"检查离群率失败原因失败: {e}")
            return f"离群率检查异常（{outlier_result}）"
    
    def get_failure_items_list(self, voltage: float, rs_value: float, rct_value: float,
                              outlier_result: Optional[str] = None) -> List[str]:
        """
        获取失败项目列表（按优先级顺序检查）

        优先级顺序：
        1. 电压检测（电压范围不合格）
        2. 离群率检测（偏差超过阈值）
        3. Rs档位判断
        4. Rct档位判断

        Args:
            voltage: 电压值 (V)
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            outlier_result: 离群率检测结果

        Returns:
            失败项目列表（按优先级排序）
        """
        try:
            fail_items = []

            # 第一优先级：电压检测
            if self._check_voltage_failure(voltage):
                fail_items.append("电压")
                return fail_items  # 电压不合格，直接返回，不检查其他项目

            # 第二优先级：离群率检测
            if self._check_outlier_failure(outlier_result):
                fail_items.append("离群率")
                return fail_items  # 离群率不合格，直接返回，不检查Rs/Rct

            # 第三优先级：Rs检测
            if self._check_rs_failure(rs_value):
                fail_items.append("Rs")

            # 第四优先级：Rct检测
            if self._check_rct_failure(rct_value):
                fail_items.append("Rct")

            return fail_items

        except Exception as e:
            logger.error(f"获取失败项目列表失败: {e}")
            return ["系统错误"]
    
    def is_test_passed(self, voltage: float, rs_value: float, rct_value: float, 
                      outlier_result: Optional[str] = None) -> bool:
        """
        判断测试是否通过
        
        Args:
            voltage: 电压值 (V)
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            outlier_result: 离群率检测结果
            
        Returns:
            是否通过测试
        """
        try:
            fail_items = self.get_failure_items_list(voltage, rs_value, rct_value, outlier_result)
            return len(fail_items) == 0
            
        except Exception as e:
            logger.error(f"判断测试通过状态失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            logger.debug("失败原因详细化管理器资源清理完成")
            
        except Exception as e:
            logger.error(f"失败原因详细化管理器清理失败: {e}")
