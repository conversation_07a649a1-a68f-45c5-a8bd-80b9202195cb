#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储监控管理器
负责监控数据库和日志文件的存储使用情况，提供警告和清理建议

功能：
1. 监控数据库文件大小
2. 监控日志文件大小
3. 检查存储使用情况
4. 提供存储警告
5. 计算存储使用趋势

作者：Jack
日期：2025-01-31
"""

import os
import logging
import glob
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class StorageMonitor(QObject):
    """存储监控管理器"""
    
    # 信号定义
    storage_warning = pyqtSignal(dict)  # 存储警告信号
    storage_critical = pyqtSignal(dict)  # 存储严重警告信号
    storage_status_updated = pyqtSignal(dict)  # 存储状态更新信号
    
    def __init__(self, config_manager=None):
        """
        初始化存储监控管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._check_storage_usage)
        
        # 存储路径配置
        self.database_path = "data/test_results.db"
        self.log_directory = "logs"
        self.temp_directory = "temp"
        self.cache_directory = "cache"
        
        # 存储状态缓存
        self.last_check_time = None
        self.storage_status = {}
        
        self.logger.info("存储监控管理器初始化完成")
    
    def start_monitoring(self):
        """开始存储监控"""
        try:
            if not self.config_manager:
                self.logger.warning("配置管理器未设置，无法启动存储监控")
                return
            
            # 获取监控配置
            monitoring_enabled = self.config_manager.get('storage_management.storage_monitoring_enabled', True)
            if not monitoring_enabled:
                self.logger.info("存储监控已禁用")
                return
            
            # 获取监控间隔
            interval_minutes = self.config_manager.get('storage_management.monitoring_interval_minutes', 30)
            interval_ms = interval_minutes * 60 * 1000  # 转换为毫秒
            
            # 启动定时器
            self.monitor_timer.start(interval_ms)
            
            # 立即执行一次检查
            self._check_storage_usage()
            
            self.logger.info(f"存储监控已启动，检查间隔: {interval_minutes}分钟")
            
        except Exception as e:
            self.logger.error(f"启动存储监控失败: {e}")
    
    def stop_monitoring(self):
        """停止存储监控"""
        try:
            self.monitor_timer.stop()
            self.logger.info("存储监控已停止")
            
        except Exception as e:
            self.logger.error(f"停止存储监控失败: {e}")
    
    def get_storage_status(self) -> Dict[str, Any]:
        """
        获取当前存储状态
        
        Returns:
            存储状态信息字典
        """
        try:
            status = {
                'database': self._get_database_status(),
                'logs': self._get_logs_status(),
                'temp_files': self._get_temp_files_status(),
                'cache_files': self._get_cache_files_status(),
                'total_usage': 0,
                'last_check_time': datetime.now(),
                'warnings': [],
                'recommendations': []
            }
            
            # 计算总使用量
            status['total_usage'] = (
                status['database']['size_mb'] +
                status['logs']['total_size_mb'] +
                status['temp_files']['total_size_mb'] +
                status['cache_files']['total_size_mb']
            )
            
            # 检查警告条件
            self._check_warnings(status)
            
            # 生成建议
            self._generate_recommendations(status)
            
            # 缓存状态
            self.storage_status = status
            self.last_check_time = status['last_check_time']
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取存储状态失败: {e}")
            return {}
    
    def _get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        try:
            if not os.path.exists(self.database_path):
                return {
                    'exists': False,
                    'size_bytes': 0,
                    'size_mb': 0,
                    'limit_mb': self.config_manager.get('storage_management.database_size_limit_mb', 500),
                    'usage_percent': 0,
                    'last_modified': None
                }
            
            size_bytes = os.path.getsize(self.database_path)
            size_mb = size_bytes / (1024 * 1024)
            limit_mb = self.config_manager.get('storage_management.database_size_limit_mb', 500)
            usage_percent = (size_mb / limit_mb) * 100 if limit_mb > 0 else 0
            
            return {
                'exists': True,
                'size_bytes': size_bytes,
                'size_mb': round(size_mb, 2),
                'limit_mb': limit_mb,
                'usage_percent': round(usage_percent, 1),
                'last_modified': datetime.fromtimestamp(os.path.getmtime(self.database_path))
            }
            
        except Exception as e:
            self.logger.error(f"获取数据库状态失败: {e}")
            return {}
    
    def _get_logs_status(self) -> Dict[str, Any]:
        """获取日志文件状态"""
        try:
            if not os.path.exists(self.log_directory):
                return {
                    'total_files': 0,
                    'total_size_bytes': 0,
                    'total_size_mb': 0,
                    'limit_mb': self.config_manager.get('storage_management.log_size_limit_mb', 100),
                    'usage_percent': 0,
                    'files': []
                }
            
            log_files = glob.glob(os.path.join(self.log_directory, "*.log"))
            total_size_bytes = 0
            files_info = []
            
            for log_file in log_files:
                if os.path.exists(log_file):
                    size_bytes = os.path.getsize(log_file)
                    total_size_bytes += size_bytes
                    
                    files_info.append({
                        'path': log_file,
                        'name': os.path.basename(log_file),
                        'size_bytes': size_bytes,
                        'size_mb': round(size_bytes / (1024 * 1024), 2),
                        'last_modified': datetime.fromtimestamp(os.path.getmtime(log_file))
                    })
            
            total_size_mb = total_size_bytes / (1024 * 1024)
            limit_mb = self.config_manager.get('storage_management.log_size_limit_mb', 100)
            usage_percent = (total_size_mb / limit_mb) * 100 if limit_mb > 0 else 0
            
            return {
                'total_files': len(log_files),
                'total_size_bytes': total_size_bytes,
                'total_size_mb': round(total_size_mb, 2),
                'limit_mb': limit_mb,
                'usage_percent': round(usage_percent, 1),
                'files': files_info
            }
            
        except Exception as e:
            self.logger.error(f"获取日志文件状态失败: {e}")
            return {}
    
    def _get_temp_files_status(self) -> Dict[str, Any]:
        """获取临时文件状态"""
        try:
            if not os.path.exists(self.temp_directory):
                return {
                    'total_files': 0,
                    'total_size_bytes': 0,
                    'total_size_mb': 0,
                    'files': []
                }
            
            temp_files = glob.glob(os.path.join(self.temp_directory, "*"))
            total_size_bytes = 0
            files_info = []
            
            for temp_file in temp_files:
                if os.path.isfile(temp_file):
                    size_bytes = os.path.getsize(temp_file)
                    total_size_bytes += size_bytes
                    
                    files_info.append({
                        'path': temp_file,
                        'name': os.path.basename(temp_file),
                        'size_bytes': size_bytes,
                        'size_mb': round(size_bytes / (1024 * 1024), 2),
                        'last_modified': datetime.fromtimestamp(os.path.getmtime(temp_file))
                    })
            
            total_size_mb = total_size_bytes / (1024 * 1024)
            
            return {
                'total_files': len(files_info),
                'total_size_bytes': total_size_bytes,
                'total_size_mb': round(total_size_mb, 2),
                'files': files_info
            }
            
        except Exception as e:
            self.logger.error(f"获取临时文件状态失败: {e}")
            return {}
    
    def _get_cache_files_status(self) -> Dict[str, Any]:
        """获取缓存文件状态"""
        try:
            if not os.path.exists(self.cache_directory):
                return {
                    'total_files': 0,
                    'total_size_bytes': 0,
                    'total_size_mb': 0,
                    'files': []
                }
            
            cache_files = glob.glob(os.path.join(self.cache_directory, "*"))
            total_size_bytes = 0
            files_info = []
            
            for cache_file in cache_files:
                if os.path.isfile(cache_file):
                    size_bytes = os.path.getsize(cache_file)
                    total_size_bytes += size_bytes
                    
                    files_info.append({
                        'path': cache_file,
                        'name': os.path.basename(cache_file),
                        'size_bytes': size_bytes,
                        'size_mb': round(size_bytes / (1024 * 1024), 2),
                        'last_modified': datetime.fromtimestamp(os.path.getmtime(cache_file))
                    })
            
            total_size_mb = total_size_bytes / (1024 * 1024)
            
            return {
                'total_files': len(files_info),
                'total_size_bytes': total_size_bytes,
                'total_size_mb': round(total_size_mb, 2),
                'files': files_info
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存文件状态失败: {e}")
            return {}
    
    def _check_storage_usage(self):
        """检查存储使用情况（定时器回调）"""
        try:
            status = self.get_storage_status()
            
            # 发送状态更新信号
            self.storage_status_updated.emit(status)
            
            # 检查警告条件
            warning_threshold = self.config_manager.get('storage_management.warning_threshold_percent', 80)
            
            # 检查数据库使用率
            if status.get('database', {}).get('usage_percent', 0) >= warning_threshold:
                self.storage_warning.emit({
                    'type': 'database',
                    'usage_percent': status['database']['usage_percent'],
                    'size_mb': status['database']['size_mb'],
                    'limit_mb': status['database']['limit_mb'],
                    'message': f"数据库文件大小已达到 {status['database']['usage_percent']:.1f}%"
                })
            
            # 检查数据库是否超限
            if status.get('database', {}).get('usage_percent', 0) >= 100:
                self.storage_critical.emit({
                    'type': 'database',
                    'usage_percent': status['database']['usage_percent'],
                    'size_mb': status['database']['size_mb'],
                    'limit_mb': status['database']['limit_mb'],
                    'message': f"数据库文件大小已超过限制！当前: {status['database']['size_mb']:.1f}MB"
                })
            
            # 检查日志使用率
            if status.get('logs', {}).get('usage_percent', 0) >= warning_threshold:
                self.storage_warning.emit({
                    'type': 'logs',
                    'usage_percent': status['logs']['usage_percent'],
                    'size_mb': status['logs']['total_size_mb'],
                    'limit_mb': status['logs']['limit_mb'],
                    'message': f"日志文件大小已达到 {status['logs']['usage_percent']:.1f}%"
                })
            
            self.logger.debug(f"存储使用检查完成: 数据库 {status.get('database', {}).get('usage_percent', 0):.1f}%, 日志 {status.get('logs', {}).get('usage_percent', 0):.1f}%")
            
        except Exception as e:
            self.logger.error(f"检查存储使用情况失败: {e}")
    
    def _check_warnings(self, status: Dict[str, Any]):
        """检查警告条件"""
        try:
            warnings = []
            warning_threshold = self.config_manager.get('storage_management.warning_threshold_percent', 80)
            
            # 检查数据库警告
            db_usage = status.get('database', {}).get('usage_percent', 0)
            if db_usage >= warning_threshold:
                warnings.append({
                    'type': 'database_warning',
                    'level': 'critical' if db_usage >= 100 else 'warning',
                    'message': f"数据库文件使用率: {db_usage:.1f}%",
                    'recommendation': "建议清理历史测试数据或增加存储限制"
                })
            
            # 检查日志警告
            log_usage = status.get('logs', {}).get('usage_percent', 0)
            if log_usage >= warning_threshold:
                warnings.append({
                    'type': 'logs_warning',
                    'level': 'critical' if log_usage >= 100 else 'warning',
                    'message': f"日志文件使用率: {log_usage:.1f}%",
                    'recommendation': "建议清理旧日志文件或启用日志轮转"
                })
            
            status['warnings'] = warnings
            
        except Exception as e:
            self.logger.error(f"检查警告条件失败: {e}")
    
    def _generate_recommendations(self, status: Dict[str, Any]):
        """生成清理建议"""
        try:
            recommendations = []
            
            # 临时文件建议
            temp_size = status.get('temp_files', {}).get('total_size_mb', 0)
            if temp_size > 10:  # 超过10MB
                recommendations.append({
                    'type': 'temp_cleanup',
                    'priority': 'high',
                    'message': f"发现 {temp_size:.1f}MB 临时文件",
                    'action': "清理临时文件",
                    'estimated_space': f"{temp_size:.1f}MB"
                })
            
            # 缓存文件建议
            cache_size = status.get('cache_files', {}).get('total_size_mb', 0)
            if cache_size > 50:  # 超过50MB
                recommendations.append({
                    'type': 'cache_cleanup',
                    'priority': 'medium',
                    'message': f"发现 {cache_size:.1f}MB 缓存文件",
                    'action': "清理缓存文件",
                    'estimated_space': f"{cache_size:.1f}MB"
                })
            
            # 日志轮转建议
            log_files = status.get('logs', {}).get('total_files', 0)
            if log_files > 10:
                recommendations.append({
                    'type': 'log_rotation',
                    'priority': 'medium',
                    'message': f"发现 {log_files} 个日志文件",
                    'action': "启用日志轮转",
                    'estimated_space': "可节省大量空间"
                })
            
            status['recommendations'] = recommendations
            
        except Exception as e:
            self.logger.error(f"生成清理建议失败: {e}")


# 全局存储监控管理器实例
_storage_monitor: Optional[StorageMonitor] = None


def get_storage_monitor() -> Optional[StorageMonitor]:
    """获取全局存储监控管理器实例"""
    return _storage_monitor


def initialize_storage_monitor(config_manager) -> StorageMonitor:
    """
    初始化全局存储监控管理器
    
    Args:
        config_manager: 配置管理器实例
        
    Returns:
        存储监控管理器实例
    """
    global _storage_monitor
    
    if _storage_monitor is None:
        _storage_monitor = StorageMonitor(config_manager)
        
        logger = logging.getLogger(__name__)
        logger.info("✅ 全局存储监控管理器初始化完成")
    
    return _storage_monitor
