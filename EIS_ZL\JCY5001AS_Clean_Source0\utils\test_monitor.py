# -*- coding: utf-8 -*-
"""
测试过程监控器
监控电池阻抗测试过程中的关键步骤和状态

Author: Jack
Date: 2025-01-27
"""

import time
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TestStep:
    """测试步骤数据类"""
    step_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: str = "running"  # running, completed, failed
    details: Dict[str, Any] = None
    error_message: str = ""


@dataclass
class ChannelStatus:
    """通道状态数据类"""
    channel_num: int
    battery_code: str = ""
    state: str = "idle"
    current_frequency: float = 0.0
    frequency_index: int = 0
    total_frequencies: int = 0
    progress: int = 0
    last_update: datetime = None
    error_count: int = 0


class TestMonitor(QObject):
    """测试过程监控器"""

    # 信号定义
    step_started = pyqtSignal(str, str)  # 步骤名称, 详细信息
    step_completed = pyqtSignal(str, float, str)  # 步骤名称, 耗时, 状态
    step_failed = pyqtSignal(str, str)  # 步骤名称, 错误信息
    frequency_progress = pyqtSignal(int, float, int, int)  # 通道, 频率, 当前, 总数
    channel_status_changed = pyqtSignal(int, str, dict)  # 通道, 状态, 详细信息
    performance_update = pyqtSignal(dict)  # 性能指标更新

    def __init__(self):
        """初始化测试监控器"""
        super().__init__()

        self.is_monitoring = False
        self.test_start_time: Optional[datetime] = None
        self.current_steps: Dict[str, TestStep] = {}
        self.completed_steps: List[TestStep] = []
        self.channel_status: Dict[int, ChannelStatus] = {}

        # 性能监控
        self.performance_data = {
            'test_duration': 0.0,
            'total_frequencies': 0,
            'completed_frequencies': 0,
            'active_channels': 0,
            'error_count': 0,
            'average_frequency_time': 0.0
        }

        # 监控回调
        self.step_callback: Optional[Callable] = None
        self.frequency_callback: Optional[Callable] = None

        # 定时器用于性能监控
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._update_performance)

        # 初始化通道状态
        for i in range(1, 9):  # 8个通道
            self.channel_status[i] = ChannelStatus(
                channel_num=i,
                last_update=datetime.now()
            )

        logger.info("测试过程监控器初始化完成")

    def start_monitoring(self):
        """开始监控测试过程"""
        if self.is_monitoring:
            logger.warning("测试监控已经在运行")
            return

        try:
            self.is_monitoring = True
            self.test_start_time = datetime.now()

            # 重置状态
            self.current_steps.clear()
            self.completed_steps.clear()

            # 重置性能数据
            self.performance_data = {
                'test_duration': 0.0,
                'total_frequencies': 0,
                'completed_frequencies': 0,
                'active_channels': 0,
                'error_count': 0,
                'average_frequency_time': 0.0
            }

            # 🚀 性能优化：启动性能监控定时器（降低更新频率）
            self.performance_timer.start(2000)  # 每2秒更新一次（优化后）

            logger.info("测试过程监控已启动")
            self.log_step("测试监控启动", "开始监控测试过程")

        except Exception as e:
            logger.error(f"启动测试监控失败: {e}")

    def stop_monitoring(self):
        """停止监控测试过程"""
        if not self.is_monitoring:
            return

        try:
            self.is_monitoring = False

            # 停止性能监控定时器
            self.performance_timer.stop()

            # 完成所有未完成的步骤
            for step_name, step in self.current_steps.items():
                if step.status == "running":
                    self.complete_step(step_name, "interrupted")

            logger.info("测试过程监控已停止")
            self.log_step("测试监控停止", "监控过程结束")

        except Exception as e:
            logger.error(f"停止测试监控失败: {e}")

    def start_step(self, step_name: str, details: str = ""):
        """
        开始一个测试步骤

        Args:
            step_name: 步骤名称
            details: 步骤详细信息
        """
        try:
            if not self.is_monitoring:
                return

            step = TestStep(
                step_name=step_name,
                start_time=datetime.now(),
                status="running",
                details={'description': details}
            )

            self.current_steps[step_name] = step

            logger.info(f"📋 开始步骤: {step_name} - {details}")
            self.step_started.emit(step_name, details)

            if self.step_callback:
                self.step_callback('started', step_name, details)

        except Exception as e:
            logger.error(f"开始步骤失败: {e}")

    def complete_step(self, step_name: str, status: str = "completed", error_message: str = ""):
        """
        完成一个测试步骤

        Args:
            step_name: 步骤名称
            status: 完成状态 (completed/failed/interrupted)
            error_message: 错误信息
        """
        try:
            if not self.is_monitoring or step_name not in self.current_steps:
                return

            step = self.current_steps[step_name]
            step.end_time = datetime.now()
            step.status = status
            step.error_message = error_message

            # 计算耗时
            duration = (step.end_time - step.start_time).total_seconds()

            # 移动到已完成列表
            self.completed_steps.append(step)
            del self.current_steps[step_name]

            if status == "completed":
                logger.info(f"✅ 完成步骤: {step_name} (耗时: {duration:.2f}s)")
                self.step_completed.emit(step_name, duration, status)
            elif status == "failed":
                logger.error(f"❌ 步骤失败: {step_name} - {error_message} (耗时: {duration:.2f}s)")
                self.step_failed.emit(step_name, error_message)
                self.performance_data['error_count'] += 1
            else:
                logger.warning(f"⚠️ 步骤中断: {step_name} (耗时: {duration:.2f}s)")

            if self.step_callback:
                self.step_callback(status, step_name, error_message)

        except Exception as e:
            logger.error(f"完成步骤失败: {e}")

    def update_frequency_progress(self, channel_num: int, frequency: float,
                                current_index: int, total_count: int, status: str = "testing"):
        """
        更新频点测试进度

        Args:
            channel_num: 通道号
            frequency: 当前频率
            current_index: 当前频点索引
            total_count: 总频点数
            status: 测试状态
        """
        try:
            if not self.is_monitoring:
                return

            if channel_num in self.channel_status:
                channel = self.channel_status[channel_num]
                channel.current_frequency = frequency
                channel.frequency_index = current_index
                channel.total_frequencies = total_count
                channel.state = status
                channel.last_update = datetime.now()

                # 计算进度（修复跳跃问题）
                if total_count > 0:
                    # 修复进度计算：使用(current_index - 1)作为基础，避免跳跃
                    base_progress = ((current_index - 1) / total_count) * 100
                    freq_progress_range = 100 / total_count

                    if status == "starting":
                        # 启动阶段：基础进度 + 10%的频点进度
                        channel.progress = int(base_progress + freq_progress_range * 0.1)
                    elif status == "completed":
                        # 完成阶段：基础进度 + 100%的频点进度
                        channel.progress = int(base_progress + freq_progress_range)
                    else:
                        # 测试中阶段：基础进度 + 50%的频点进度
                        channel.progress = int(base_progress + freq_progress_range * 0.5)

                # 进度计算详情
                logger.debug(f"通道{channel_num}进度: {frequency}Hz ({current_index}/{total_count}) {status} -> {channel.progress}%")

                logger.debug(f"📊 通道{channel_num}频点进度: {frequency}Hz ({current_index}/{total_count}) - {status}")

                # 发送信号
                self.frequency_progress.emit(channel_num, frequency, current_index, total_count)

                # 更新性能数据
                if status == "completed":
                    self.performance_data['completed_frequencies'] += 1

                if self.frequency_callback:
                    self.frequency_callback(channel_num, frequency, current_index, total_count, status)

        except Exception as e:
            logger.error(f"更新频点进度失败: {e}")

    def update_channel_status(self, channel_num: int, state: str, details: Dict[str, Any] = None):
        """
        更新通道状态

        Args:
            channel_num: 通道号
            state: 通道状态
            details: 详细信息
        """
        try:
            if not self.is_monitoring:
                return

            if channel_num in self.channel_status:
                channel = self.channel_status[channel_num]
                old_state = channel.state
                channel.state = state
                channel.last_update = datetime.now()

                if details:
                    if 'battery_code' in details:
                        channel.battery_code = details['battery_code']
                    if 'progress' in details:
                        channel.progress = details['progress']
                    if 'error' in details:
                        channel.error_count += 1

                logger.debug(f"🔄 通道{channel_num}状态变更: {old_state} -> {state}")

                # 发送信号
                self.channel_status_changed.emit(channel_num, state, details or {})

        except Exception as e:
            logger.error(f"更新通道状态失败: {e}")

    def log_step(self, step_name: str, message: str):
        """
        记录步骤日志

        Args:
            step_name: 步骤名称
            message: 日志消息
        """
        try:
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            logger.info(f"[{timestamp}] {step_name}: {message}")

        except Exception as e:
            logger.error(f"记录步骤日志失败: {e}")

    def _update_performance(self):
        """更新性能指标"""
        try:
            if not self.is_monitoring or not self.test_start_time:
                return

            # 计算测试持续时间
            self.performance_data['test_duration'] = (
                datetime.now() - self.test_start_time
            ).total_seconds()

            # 统计活跃通道数
            active_channels = sum(
                1 for channel in self.channel_status.values()
                if channel.state in ['testing', 'preparing', 'waiting']
            )
            self.performance_data['active_channels'] = active_channels

            # 计算总频点数
            total_frequencies = sum(
                channel.total_frequencies for channel in self.channel_status.values()
            )
            self.performance_data['total_frequencies'] = total_frequencies

            # 计算平均频点测试时间
            if self.performance_data['completed_frequencies'] > 0:
                self.performance_data['average_frequency_time'] = (
                    self.performance_data['test_duration'] /
                    self.performance_data['completed_frequencies']
                )

            # 发送性能更新信号
            self.performance_update.emit(self.performance_data.copy())

        except Exception as e:
            logger.error(f"更新性能指标失败: {e}")

    def get_status_summary(self) -> Dict[str, Any]:
        """
        获取状态摘要

        Returns:
            状态摘要字典
        """
        try:
            return {
                'is_monitoring': self.is_monitoring,
                'test_start_time': self.test_start_time,
                'current_steps': len(self.current_steps),
                'completed_steps': len(self.completed_steps),
                'performance_data': self.performance_data.copy(),
                'channel_status': {
                    num: {
                        'state': channel.state,
                        'progress': channel.progress,
                        'current_frequency': channel.current_frequency,
                        'error_count': channel.error_count
                    }
                    for num, channel in self.channel_status.items()
                }
            }
        except Exception as e:
            logger.error(f"获取状态摘要失败: {e}")
            return {}

    def set_step_callback(self, callback: Callable):
        """设置步骤回调函数"""
        self.step_callback = callback

    def set_frequency_callback(self, callback: Callable):
        """设置频点回调函数"""
        self.frequency_callback = callback


# 全局测试监控器实例
_global_test_monitor: Optional[TestMonitor] = None


def get_global_test_monitor() -> TestMonitor:
    """获取全局测试监控器实例"""
    global _global_test_monitor
    if _global_test_monitor is None:
        _global_test_monitor = TestMonitor()
    return _global_test_monitor
