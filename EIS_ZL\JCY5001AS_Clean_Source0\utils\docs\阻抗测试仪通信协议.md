# 阻抗测试仪通信协议文档

## 1. 基本信息

- **通信协议类型**：Modbus RTU
- **命令类型**：
  - 03H：读取数据
  - 06H：设置/写入单个寄存器数据
  - 10H：设置/写入多个寄存器数据

## 2. 设置ZM频率(位小数)

### 基本参数
- **频率范围**：0.001 ~ 7813.000Hz
- **地址范围**：4200H ~ 427FH (128个通道)
- **群发地址**：4F07H ~ 4F08H
- **通道地址计算**：通道地址 = 4200H + (通道号-1) * 2

### 命令格式

#### 读取命令 (03H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 03H   | 42H     | 00H     | 00H          | 02H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 字节数 | 数据1 | 数据2 | 数据3 | 数据4 | CRC高 | CRC低 |
|------|-------|-------|-------|------|------|------|------|------|------|
| 示例 | 01H   | 03H   | 04H   | xxH  | xxH  | xxH  | xxH  | xxH  | xxH  |

#### 设置命令 (10H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | 字节数 | 数据1 | 数据2 | 数据3 | 数据4 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|------|------|------|------|------|
| 示例 | 01H   | 10H   | 42H     | 00H     | 00H          | 02H          | 04H  | xxH  | xxH  | xxH  | xxH  | xxH  | xxH  |

#### 设置返回帧
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 10H   | 42H     | 00H     | 00H          | 02H          | xxH  | xxH  |

### 数据格式
- 频率值采用32位(4字节)表示，需要根据小数位数进行换算
- 例如：频率1.234Hz，小数点后3位，则传输值为1234
- 数据以高字节在前，低字节在后的顺序传输

### 具体例子

#### 例1：一次性读取多个通道的频率值
```
发送命令：01 03 42 00 00 10 xx xx  (从通道1开始读取8个通道的频率，xx为CRC校验码)
返回数据：01 03 20 00 00 29 04 00 00 29 04 00 00 29 04 00 00 29 04 00 00 29 04 00 00 29 04 00 00 29 04 00 00 29 04 xx xx
(返回8个通道的频率数据，每个通道4字节，所有通道均为10.500Hz，xx为CRC校验码)
```

#### 例2：使用群发命令设置所有通道的频率为10.500Hz
```
发送命令：01 10 4F 07 00 02 04 00 00 29 04 xx xx  (10500 = 0x00002904，小数点后3位)
返回数据：01 10 4F 07 00 02 xx xx           (xx为CRC校验码)
```

### 批量读取与设置性能测试

通过实际测试，我们发现批量读取和群发设置相比单独操作每个通道有显著的性能优势：

#### 性能测试结果
- **单独读取8个通道平均耗时**: 1607.15 毫秒
- **一次性读取8个通道平均耗时**: 100.82 毫秒
- **读取速度提升**: 15.94倍
- **单独设置4个通道平均耗时**: 804.04 毫秒
- **群发设置所有通道平均耗时**: 100.87 毫秒
- **设置速度提升**: 7.97倍

#### 批量读取命令
可以使用以下命令一次性读取多个通道的频率：
```
发送命令：01 03 42 00 00 10 xx xx  (从通道1开始读取8个通道的频率)
返回数据：01 03 20 [32字节数据] xx xx  (返回8个通道的频率数据)
```

#### 群发设置命令
可以使用以下命令同时设置所有通道的频率：
```
发送命令：01 10 4F 07 00 02 04 [4字节频率数据] xx xx
返回数据：01 10 4F 07 00 02 xx xx
```

#### 实际应用建议
- **推荐使用批量操作**：在所有场景中，应优先使用批量读取和群发设置命令，不建议使用单独读取/设置指令
- **提高刷新率**：批量操作可以将刷新率从约0.6Hz提高到约10Hz，显著提升实时监控性能
- **减少通信负担**：批量操作减少了通信次数，降低了串口通信的负担
- **简化代码实现**：使用批量操作可以简化代码实现，减少循环和重复代码

## 3. 设置ZM增益

### 基本参数
- **地址范围**：4280H ~ 42BFH (通道1~64)，每个通道占用2个寄存器
- **群发地址**：4F09H（注意：实际测试表明，此地址只能设置通道1的增益，无法真正实现群发功能）
- **数据类型**：16位无符号整数
- **有效增益值**：仅支持1、4、16三个值

### 命令格式

#### 读取命令 (03H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 03H   | 42H     | 80H     | 00H          | 01H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|-------|------|------|------|------|
| 示例 | 01H   | 03H   | 02H   | xxH  | xxH  | xxH  | xxH  |

#### 设置命令 (10H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|------|------|------|
| 示例 | 01H   | 10H   | 42H     | 80H     | 00H          | 01H          | 02H  | xxH  | xxH  | xxH  | xxH  |

#### 设置返回帧
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 10H   | 42H     | 80H     | 00H          | 01H          | xxH  | xxH  |

### 具体例子

#### 例1：读取第一个通道的增益值
```
发送命令：01 03 42 80 00 01 xx xx  (xx为CRC校验码)
返回数据：01 03 02 00 04 xx xx     (返回增益值4，xx为CRC校验码)
```

#### 例2：设置第一个通道的增益为4
```
发送命令：01 10 42 80 00 01 02 00 04 xx xx  (增益值为4)
返回数据：01 10 42 80 00 01 xx xx           (xx为CRC校验码)
```

#### 例3：使用群发地址设置增益
```
发送命令：01 10 4F 09 00 01 02 00 04 xx xx  (设置增益为4)
返回数据：01 10 4F 09 00 01 xx xx           (xx为CRC校验码)
```

注意：实际测试表明，使用群发地址(4F09H)发送的命令只会影响通道1的增益，不会影响其他通道。

## 4. 命令测试结果

### 06命令与10命令对比测试

通过实际测试，我们发现06命令和10命令在设置阻抗测试仪增益方面的行为如下：

1. **单个通道设置**：
   - 06命令可以成功设置通道1-5的增益，通道6-8设置失败
   - 10命令也可以成功设置通道1-5的增益，通道6-8设置失败
   - 两种命令在设置单个通道时效果相同

2. **群发设置**：
   - 使用群发地址(4F09H)的06命令只能设置通道1的增益，不影响其他通道
   - 使用群发地址(4F09H)的10命令也只能设置通道1的增益，不影响其他通道
   - 两种命令在使用群发地址时效果相同，都无法真正实现群发功能

3. **批量读取**：
   - 可以使用03H命令一次性读取所有8个通道的增益值，但只能从通道1开始读取
   - 命令格式：`01 03 42 80 00 08 xx xx`（从通道1开始读取8个通道）
   - 尝试从通道5开始批量读取会失败，必须从通道1开始批量读取

### 增益值测试

测试确认阻抗测试仪只支持以下三个增益值：
- 1
- 4
- 16

所有这三个值都可以成功设置，并且可以通过读取命令验证设置已生效。

## 5. 设置ZM平均次数

### 基本参数
- **地址范围**：4040H ~ 407FH (通道1~8)，每个通道占用2个寄存器
- **群发地址**：4F01H
- **数据类型**：16位无符号整数

### 命令格式

#### 读取命令 (03H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 03H   | 40H     | 40H     | 00H          | 01H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|-------|------|------|------|------|
| 示例 | 01H   | 03H   | 02H   | xxH  | xxH  | xxH  | xxH  |

#### 设置命令 (06H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 数据高位 | 数据低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 06H   | 40H     | 40H     | 00H     | 04H     | xxH  | xxH  |

#### 设置返回帧 (06H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 数据高位 | 数据低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 06H   | 40H     | 40H     | 00H     | 04H     | xxH  | xxH  |

#### 设置命令 (10H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|------|------|------|
| 示例 | 01H   | 10H   | 40H     | 40H     | 00H          | 01H          | 02H  | xxH  | xxH  | xxH  | xxH  |

#### 设置返回帧 (10H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 10H   | 40H     | 40H     | 00H          | 01H          | xxH  | xxH  |

### 具体例子

#### 例1：读取第一个通道的平均次数
```
发送命令：01 03 40 40 00 01 xx xx  (xx为CRC校验码)
返回数据：01 03 02 00 04 xx xx     (返回平均次数4，xx为CRC校验码)
```

#### 例1-2：一次性读取所有8个通道的平均次数
```
发送命令：01 03 40 40 00 08 xx xx  (xx为CRC校验码)
返回数据：01 03 10 00 02 00 04 00 06 00 08 00 0A 00 08 00 08 00 08 xx xx  (返回8个通道的平均次数，分别是2,4,6,8,10,8,8,8，xx为CRC校验码)
```

注意：批量读取必须从通道1开始，尝试从其他通道开始批量读取会失败。

#### 例2：使用06H命令设置第一个通道的平均次数为4
```
发送命令：01 06 40 40 00 04 xx xx  (平均次数为4)
返回数据：01 06 40 40 00 04 xx xx  (xx为CRC校验码)
```

#### 例3：使用10H命令设置第一个通道的平均次数为4
```
发送命令：01 10 40 40 00 01 02 00 04 xx xx  (平均次数为4)
返回数据：01 10 40 40 00 01 xx xx           (xx为CRC校验码)
```

#### 例4：使用06H命令和群发地址设置平均次数
```
发送命令：01 06 4F 01 00 04 xx xx  (设置平均次数为4)
返回数据：01 06 4F 01 00 04 xx xx  (xx为CRC校验码)
```

#### 例5：使用10H命令和群发地址设置平均次数
```
发送命令：01 10 4F 01 00 01 02 00 04 xx xx  (设置平均次数为4)
返回数据：01 10 4F 01 00 01 xx xx           (xx为CRC校验码)
```

### 测试结果

通过实际测试，我们发现：

1. **03H命令**：
   - 单通道读取：成功读取通道1-4的平均次数值，通道5-8单独读取失败。
   - 批量读取：可以一次性读取所有8个通道的平均次数值，但只能从通道1开始读取。
   - 命令格式：`01 03 40 40 00 08 xx xx`（从通道1开始读取8个通道）
   - 注意：尝试从通道5开始批量读取4个通道会失败。

2. **06H命令**：成功设置通道1-5的平均次数值，通道6-8设置失败。

3. **10H命令**：成功设置通道1-5的平均次数值，通道6-8设置失败。

4. **群发功能**：
   - 使用群发地址(4F01H)的06H命令成功设置了通道1-4的平均次数为8。
   - 群发命令对所有支持的通道都有效。

5. **平均次数设置**：
   - 测试表明，设备支持设置不同的平均次数值（2, 4, 6, 8, 10等）。
   - 成功设置通道1为2，通道2为4，通道3为6，通道4为8，通道5为10。
   - 批量读取验证了设置的值已正确应用。

6. **通道支持**：
   - 单通道读取：设备支持读取通道1-4的平均次数。
   - 批量读取：设备支持从通道1开始一次性读取所有8个通道的平均次数。
   - 设置：设备可以设置通道1-5的平均次数，通道6-8无法设置（返回错误代码01 86 02）。

## 6. 启动阻抗测量

### 基本参数
- **地址范围**：0000H ~ 003FH (通道1~64)
- **数据类型**：通道状态（启动/停止）
- **功能码**：05H（控制单个通道）或0FH（控制多个通道）

### 命令格式

#### 控制单个通道命令 (05H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 数据高位 | 数据低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 05H   | 00H     | 00H     | FFH     | 00H     | xxH  | xxH  |

#### 控制单个通道返回帧 (05H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 数据高位 | 数据低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 05H   | 00H     | 00H     | FFH     | 00H     | xxH  | xxH  |

#### 控制多个通道命令 (0FH)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 通道数量高位 | 通道数量低位 | 字节数 | 数据1 | ... | CRC高 | CRC低 |
|------|-------|-------|---------|---------|------------|------------|------|------|-----|------|------|
| 示例 | 01H   | 0FH   | 00H     | 00H     | 00H        | 02H        | 01H  | 03H  | ... | xxH  | xxH  |

#### 控制多个通道返回帧 (0FH)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 通道数量高位 | 通道数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|------------|------------|------|------|
| 示例 | 01H   | 0FH   | 00H     | 00H     | 00H        | 02H        | xxH  | xxH  |

### 具体例子

#### 例1：启动第一个通道的阻抗测量 (05H)
```
发送命令：01 05 00 00 FF 00 8C 3A  (8C 3A为CRC校验码)
返回数据：01 05 00 00 FF 00 8C 3A  (8C 3A为CRC校验码)
```

#### 例2：停止第一个通道的阻抗测量 (05H)
```
发送命令：01 05 00 00 00 00 CD CA  (CD CA为CRC校验码)
返回数据：01 05 00 00 00 00 CD CA  (CD CA为CRC校验码)
```

#### 例3：同时启动通道1和通道2的阻抗测量 (0FH)
```
发送命令：01 0F 00 00 00 02 01 03 9E 96  (9E 96为CRC校验码)
返回数据：01 0F 00 00 00 02 D4 0A        (D4 0A为CRC校验码)
```

#### 例4：同时停止通道1和通道2的阻抗测量 (0FH)
```
发送命令：01 0F 00 00 00 02 01 00 DE 97  (DE 97为CRC校验码)
返回数据：01 0F 00 00 00 02 D4 0A        (D4 0A为CRC校验码)
```

### 数据格式说明

- **05H命令**：
  - 数据高位为FFH，数据低位为00H表示启动通道
  - 数据高位为00H，数据低位为00H表示停止通道

- **0FH命令**：
  - 字节数：表示后续数据的字节数，计算方式为(通道数量+7)//8
  - 数据字节：每个位表示一个通道的状态，1表示启动，0表示停止
  - 通道1对应最低位(bit 0)，通道8对应最高位(bit 7)
  - 例如：
    - 01H (00000001) 表示只启动通道1
    - 02H (00000010) 表示只启动通道2
    - 03H (00000011) 表示同时启动通道1和通道2
    - 07H (00000111) 表示同时启动通道1、2和3
    - E0H (11100000) 表示同时启动通道6、7和8
    - FFH (11111111) 表示启动所有8个通道

### 测试结果

通过实际测试，我们发现：

1. **05H命令**：
   - 成功用于启动/停止单个通道的阻抗测量
   - 命令格式：`01 05 00 XX FF 00 xx xx`（启动）或 `01 05 00 XX 00 00 xx xx`（停止）
   - 其中XX是通道号（从00开始）
   - 测试表明设备支持8个通道（通道0到通道7）

2. **0FH命令**：
   - 成功用于同时启动/停止多个通道的阻抗测量
   - 命令格式：`01 0F 00 00 00 08 01 ZZ xx xx`
   - 其中ZZ是数据字节，表示要启动的通道
   - 测试表明可以同时控制8个通道
   - 例如：
     - 启动通道1、2和3：`01 0F 00 00 00 08 01 07 xx xx`
     - 启动通道6、7和8：`01 0F 00 00 00 08 01 E0 xx xx`
     - 启动所有通道：`01 0F 00 00 00 08 01 FF xx xx`

3. **群发地址**：
   - 使用05H功能码和群发地址(0F00H)的测试失败，设备不支持这种方式的群发
   - 建议使用0FH功能码同时控制多个通道

4. **通道位映射**：
   - 在0FH命令的数据字节中，通道1对应最低位(bit 0)，通道8对应最高位(bit 7)
   - 这意味着数据字节中的每个位从右到左依次对应通道1到通道8
   - 例如，要启动通道1、2和3，数据字节应为07H (00000111)
   - 要启动通道6、7和8，数据字节应为E0H (11100000)

5. **代码实现**：
   ```python
   def build_start_zm_measurement_selected_channels_cmd(channels: list) -> bytes:
       """
       构建启动指定通道阻抗测量命令

       Args:
           channels: 要启动的通道列表，例如[0,1,2]表示启动通道1、2和3

       Returns:
           命令字节序列
       """
       device_addr = 0x01  # 设备地址
       start_addr = 0x0000  # 起始地址
       coil_count = 0x0008  # 线圈数量(8个通道)
       byte_count = 0x01    # 字节数

       # 创建线圈状态字节
       coil_value = 0
       for channel in channels:
           if 0 <= channel < 8:  # 确保通道号有效
               coil_value |= (1 << channel)  # 设置对应位为1

       cmd = bytearray([
           0x0F,  # 功能码：控制多个通道
           (start_addr >> 8) & 0xFF,  # 起始地址高字节
           start_addr & 0xFF,         # 起始地址低字节
           (coil_count >> 8) & 0xFF,  # 线圈数量高字节
           coil_count & 0xFF,         # 线圈数量低字节
           byte_count,                # 字节数
           coil_value                 # 线圈状态字节
       ])

       # 计算CRC
       crc = calculate_crc16(bytearray([device_addr]) + cmd)
       cmd += bytearray([crc & 0xFF, (crc >> 8) & 0xFF])

       return bytearray([device_addr]) + cmd
   ```

## 7. 获取设备通道数

### 基本参数
- **地址**：3E00H
- **功能码**：04H（读输入寄存器）
- **数据类型**：16位无符号整数

### 命令格式

#### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 3EH     | 00H     | 00H          | 01H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|-------|------|------|------|------|
| 示例 | 01H   | 04H   | 02H   | xxH  | xxH  | xxH  | xxH  |

### 具体例子

#### 例1：获取设备通道数
```
发送命令：01 04 3E 00 00 01 3C 22  (3C 22为CRC校验码)
返回数据：01 04 02 00 02 38 F1     (返回通道数2，38 F1为CRC校验码)
```

### 测试结果

通过实际测试，我们发现：

1. **04H命令**：成功读取设备通道数。
2. **返回值**：返回的通道数为2，表示设备有2个通道。

## 8. ZM测量期间数据更新标志

### 基本参数
- **地址范围**：1000H ~ 103FH (通道1~64)，地址步进1
- **群发地址**：无
- **数据类型**：1Bit，范围0~1
- **功能说明**：ZM工作期间阻抗数据更新后该标志位置1，读取RE和IM数据后自动清0

### 命令格式

#### 读取命令 (02H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 02H   | 10H        | 00H        | 00H          | 0CH          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据1 | 数据2 | CRCH | CRCL |
|------|-------|-------|---------|------|------|------|------|
| 示例 | 01H   | 02H   | 02H     | 55H  | 05H  | xxH  | xxH  |

### 具体例子

#### 例1：读取1~12通道的数据更新标志
```
发送命令：01 02 10 00 00 0C xx xx  (xx为CRC校验码)
返回数据：01 02 02 55 05 xx xx     (返回数据55H 05H表示CH1=1,CH2=0,CH3=1,CH4=0,CH5=1,CH6=0,CH7=1,CH8=0,CH9=1,CH10=0,CH11=1,CH12=0，xx为CRC校验码)
```

### 测试结果

通过实际测试，我们发现：

1. **02H命令**：成功读取通道的数据更新标志。
2. **数据格式**：返回的数据每个位表示一个通道的更新状态，1表示数据已更新，0表示数据未更新。
3. **自动清零**：读取RE和IM数据后，对应通道的更新标志会自动清零。
4. **应用场景**：可用于判断测量数据是否已更新，避免重复读取相同的数据。

## 9. 获取阻抗测量数据

阻抗测量数据分为RE（实部）、IM（虚部）、Zreal（阻抗实部）、Zimag（阻抗虚部）和VZM（电压）五部分，需要分别读取。

### 9.1 获取RE阻抗数据

#### 基本参数
- **地址范围**：3000H ~ 307FH (通道1~32)，地址步进4
- **群发地址**：无
- **数据类型**：64位有符号定点5位小数，数据格式(HG,FE,DC,BA)
- **数据范围**：0.00001~00000000000000001H 到 -0.00001~FFFFFFFFFFFFFFFFH

#### 命令格式

##### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 30H        | 00H        | 00H          | 08H          | xxH  | xxH  |

##### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据1高8位 | 数据1低8位 | 数据2高8位 | 数据2低8位 | 数据3高8位 | 数据3低8位 | 数据4高8位 | 数据4低8位 | 数据5高8位 | 数据5低8位 | 数据6高8位 | 数据6低8位 | 数据7高8位 | 数据7低8位 | 数据8高8位 | 数据8低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 10H     | 00H     | 00H     | 00H     | 00H     | 49H     | 96H     | 02H     | D2H     | FFH     | FFH     | FFH     | FFH     | FDH     | ABH     | F4H     | 6EH     | xxH  | xxH  |

#### 具体例子

##### 例1：读取1~2通道的RE阻抗数据
```
发送命令：01 04 30 00 00 08 xx xx  (xx为CRC校验码)
返回数据：01 04 10 00 00 00 00 49 96 02 D2 FF FF FF FF FD AB F4 6E xx xx
(返回数据表示CH1=12345.67890，CH2=-99999.78900，xx为CRC校验码)
```

### 9.2 获取IM阻抗数据

#### 基本参数
- **地址范围**：3080H ~ 30FFH (通道1~32)，地址步进4
- **群发地址**：无
- **数据类型**：64位有符号定点5位小数，数据格式(HG,FE,DC,BA)
- **数据范围**：0.00001~00000000000000001H 到 -0.00001~FFFFFFFFFFFFFFFFH

#### 命令格式

##### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 30H        | 80H        | 00H          | 08H          | xxH  | xxH  |

##### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据1高8位 | 数据1低8位 | 数据2高8位 | 数据2低8位 | 数据3高8位 | 数据3低8位 | 数据4高8位 | 数据4低8位 | 数据5高8位 | 数据5低8位 | 数据6高8位 | 数据6低8位 | 数据7高8位 | 数据7低8位 | 数据8高8位 | 数据8低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 10H     | 00H     | 00H     | 00H     | 00H     | 49H     | 96H     | 02H     | D2H     | FFH     | FFH     | FFH     | FFH     | FDH     | ABH     | F4H     | 6EH     | xxH  | xxH  |

#### 具体例子

##### 例1：读取1~2通道的IM阻抗数据
```
发送命令：01 04 30 80 00 08 xx xx  (xx为CRC校验码)
返回数据：01 04 10 00 00 00 00 49 96 02 D2 FF FF FF FF FD AB F4 6E xx xx
(返回数据表示CH1=12345.67890，CH2=-99999.78900，xx为CRC校验码)
```

### 9.3 获取Zreal阻抗实部数据

#### 基本参数
- **地址范围**：3100H ~ 317FH (通道1~32)，地址步进4
- **群发地址**：无
- **数据类型**：64位有符号定点5位小数，数据格式(HG,FE,DC,BA)

#### 命令格式
```
读取1~2通道的Zreal阻抗数据：
发送数据：01 04 31 00 00 08 xx xx (xx为CRC校验码)

返回数据：01 04 10 00 00 00 00 49 96 02 D2 FF FF FF FF FD AB F4 6E xx xx
(返回数据表示CH1=12345.67890，CH2=-99999.78900，xx为CRC校验码)
```

### 9.4 获取Zimag阻抗虚部数据

#### 基本参数
- **地址范围**：3180H ~ 31FFH (通道1~32)，地址步进4
- **群发地址**：无
- **数据类型**：64位有符号定点5位小数，数据格式(HG,FE,DC,BA)

#### 命令格式
```
读取1~2通道的Zimag阻抗数据：
发送数据：01 04 31 80 00 08 xx xx (xx为CRC校验码)

返回数据：01 04 10 00 00 00 00 49 96 02 D2 FF FF FF FF FD AB F4 6E xx xx
(返回数据表示CH1=12345.67890，CH2=-99999.78900，xx为CRC校验码)
```

### 9.5 获取VZM电压数据

#### 基本参数
- **地址范围**：3200H ~ 327FH (通道1~64)，地址步进2
- **群发地址**：无
- **数据类型**：32位无符号定点7位小数，数据格式(DC,BA)

#### 命令格式
```
读取1~3通道的VZM电压数据：
发送数据：01 04 32 00 00 06 xx xx (xx为CRC校验码)

返回数据：01 04 0C 00 00 00 01 00 00 00 01 00 00 00 01 xx xx
(返回数据表示CH1=0.0000001，CH2=0.0000001，CH3=0.0000001，xx为CRC校验码)
```

#### 具体例子

##### 例1：读取1~3通道的VZM电压数据
```
发送命令：01 04 32 00 00 06 xx xx  (xx为CRC校验码)
返回数据：01 04 0C 00 00 00 01 00 00 00 01 00 00 00 01 xx xx
```

返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据1高8位 | 数据1低8位 | 数据2高8位 | 数据2低8位 | 数据3高8位 | 数据3低8位 | 数据4高8位 | 数据4低8位 | 数据5高8位 | 数据5低8位 | 数据6高8位 | 数据6低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 0CH     | 00H     | 00H     | 00H     | 01H     | 00H     | 00H     | 00H     | 01H     | 00H     | 00H     | 00H     | 01H     | xxH  | xxH  |

返回数据解析：
- CH1=3.9563589V (对应数据：02H 5BH B1H 45H)
- CH2=2.5478958V (对应数据：18H 4CH 72H 0EH)
- CH3=3.2584133V (对应数据：1FH 13H 1CH 5H)

### 9.6 阻抗数据读取说明

1. **数据类型**：
   - RE、IM、Zreal和Zimag数据采用64位有符号定点数表示，小数点后5位，可以是正值或负值
   - VZM电压数据采用32位无符号定点数表示，小数点后7位，只能是正值

2. **数据格式**：
   - 64位数据按照(HG,FE,DC,BA)的顺序传输，即高字节在前，低字节在后
   - 32位数据按照(DC,BA)的顺序传输，即高字节在前，低字节在后

3. **符号位**：
   - 64位数据的最高位（第64位）为符号位，0表示正数，1表示负数
   - 32位VZM数据没有符号位，只能表示正值

4. **数据范围**：
   - RE、IM、Zreal和Zimag：
     - 正数范围：0.00001 到 很大的正数（接近2^63-1除以10^5）
     - 负数范围：-0.00001 到 很大的负数（接近-2^63除以10^5）
   - VZM电压：
     - 范围：0.0000001 到 很大的正数（接近2^32-1除以10^7）

5. **同时读取**：在实际应用中，可能需要同时读取RE、IM、Zreal、Zimag和VZM数据才能完整表示阻抗特性。

6. **数据更新**：读取阻抗数据后，对应通道的数据更新标志会自动清零。

7. **数据解析示例**：
   - 64位数据：
     - 返回数据`00 00 00 00 49 96 02 D2`表示阻抗值为+12345.67890
     - 返回数据`FF FF FF FF FD AB F4 6E`表示阻抗值为-99999.78900
   - 32位数据：
     - 返回数据`00 00 00 01`表示电压值为0.0000001V
     - 返回数据`02 5B B1 45`表示电压值为3.9563589V

   解析方法：
   - 对于64位正数：直接将8字节数据转换为64位整数，然后除以10^5
   - 对于64位负数：使用二进制补码表示，需要先识别符号位，然后进行相应转换
   - 对于32位数据：直接将4字节数据转换为32位整数，然后除以10^7

8. **数据格式详解**：
   - 64位数据：
     - 数据1高8位和数据1低8位组成第1个字节
     - 数据2高8位和数据2低8位组成第2个字节
     - 以此类推，8个字节组成一个完整的64位数据
     - 例如：CH1=12345.67890对应的数据为00H 00H 00H 00H 49H 96H 02H D2H
     - 例如：CH2=-99999.78900对应的数据为FFH FFH FFH FFH FDH ABH F4H 6EH
   - 32位数据：
     - 数据1高8位和数据1低8位组成第1个字节
     - 数据2高8位和数据2低8位组成第2个字节
     - 4个字节组成一个完整的32位数据
     - 例如：CH1=3.9563589V对应的数据为02H 5BH B1H 45H

## 10. 获取电池电压数据

### 基本参数
- **地址范围**：3340H ~ 337FH (通道1~64)，地址步进1
- **群发地址**：无
- **数据类型**：16位无符号定点4位小数，数据格式(BA)，示例数据4.1234(V) = 41234 = A112H

### 命令格式

#### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 33H        | 40H        | 00H          | 03H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据1高8位 | 数据1低8位 | 数据2高8位 | 数据2低8位 | 数据3高8位 | 数据3低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 06H     | A1H     | 12H     | 77H     | 4AH     | 48H     | C0H     | xxH  | xxH  |

### 具体例子

#### 例1：读取1~3通道的电池电压数据
```
发送命令：01 04 33 40 00 03 xx xx  (xx为CRC校验码)
返回数据：01 04 06 A1 12 77 4A 48 C0 xx xx
```

返回数据解析：
- CH1=4.1234V (对应数据：A1H 12H)
- CH2=3.0538V (对应数据：77H 4AH)
- CH3=1.8624V (对应数据：48H C0H)

## 11. 获取通道数量

### 基本参数
- **地址范围**：3E00H
- **群发地址**：无
- **数据类型**：16位无符号整数，数据格式(BA)

### 命令格式

#### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 3EH        | 00H        | 00H          | 01H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据高8位 | 数据低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 02H     | 00H     | 08H     | xxH  | xxH  |

### 具体例子

#### 例1：读取设备通道数量
```
发送命令：01 04 3E 00 00 01 xx xx  (xx为CRC校验码)
返回数据：01 04 02 00 08 xx xx
```

返回数据解析：
- 通道数量=8 (对应数据：00H 08H)

## 12. 获取软件版本号

### 基本参数
- **地址范围**：3E01H，地址步进2
- **群发地址**：无
- **数据类型**：16位无符号整数，数据格式DC,BA(压缩BCD码格式)

### 命令格式

#### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 3EH        | 01H        | 00H          | 02H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 版本1高8位 | 版本1低8位 | 版本2高8位 | 版本2低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 04H     | 01H     | 23H     | 12H     | 26H     | xxH  | xxH  |

### 具体例子

#### 例1：读取软件版本号
```
发送命令：01 04 3E 01 00 02 xx xx  (xx为CRC校验码)
返回数据：01 04 04 01 23 12 26 xx xx
```

返回数据解析：
- 软件版本号=V1.23.12.26 (对应数据：01H 23H 12H 26H)

## 注意事项

### 通用注意事项
- 所有命令均需计算并附加CRC校验码
- 批量读取必须从通道1开始，尝试从其他通道开始批量读取会失败
- 地址范围内可选择多个不同通道

### 性能优化建议
- **优先使用批量读取**：一次性读取多个通道数据比单独读取每个通道快约16倍
- **优先使用群发设置**：群发设置所有通道比单独设置每个通道快约8倍
- **避免单独读取**：不建议使用单独读取指令，应使用批量读取指令
- **提高刷新率**：使用批量操作可将刷新率从约0.6Hz提高到约10Hz

### 频率设置
- 频率值的小数位数为3位，范围0.001~7813.000Hz
- 设置命令(10H)需要先将频率值乘以1000转换为整数后再传输
- 通道地址计算：通道地址 = 4200H + (通道号-1) * 2
- 批量读取频率命令格式：`01 03 42 00 00 10 xx xx`（读取8个通道）
- 群发设置频率命令格式：`01 10 4F 07 00 02 04 [频率数据] xx xx`

### 增益设置
- 增益值只支持1、4、16三个值
- 设备可以设置通道1-5的增益，通道6-8设置失败（返回错误代码01 86 02）
- **增益群发特殊行为**：
  - 使用06H命令和群发地址4F09H设置增益为1时：可以同时设置所有通道的增益为1
  - 使用06H命令和群发地址4F09H设置增益为4或16时：只会设置通道1的增益
  - 使用10H命令和群发地址4F09H设置增益时：只会设置通道1的增益
- 批量读取增益命令格式：`01 03 42 80 00 08 xx xx`（读取8个通道）
- **增益设置最佳实践**：
  - 建议使用命令`01 06 4F 09 00 01 xx xx`将所有通道的增益统一设置为1
  - 单独设置通道增益可能导致通道间相互干扰，不建议在实际应用中使用
  - 如果必须使用不同增益值，建议先将所有通道设置为1，然后再单独设置需要的通道

### 平均次数设置
- 设备支持设置不同的平均次数值（2, 4, 6, 8, 10等）
- 平均次数可以使用06H或10H命令设置，两种命令都有效
- 设备可以设置通道1-5的平均次数，通道6-8无法单独设置（返回错误代码01 86 02）
- **平均次数群发功能**：
  - 使用06H命令和群发地址4F01H可以同时设置所有8个通道的平均次数
  - 使用10H命令和群发地址4F01H也可以同时设置所有8个通道的平均次数
  - 群发命令对所有通道都有效，无论设置什么值
- 批量读取平均次数命令格式：`01 03 40 40 00 08 xx xx`（读取8个通道）
- **平均次数设置最佳实践**：
  - 建议使用群发命令设置所有通道的平均次数为相同值
  - 单独设置通道平均次数可能导致通道间相互干扰，读取结果与设置值不一致
  - 如果必须使用不同平均次数值，建议先使用群发命令设置基础值，然后再单独设置需要的通道

### 阻抗测量控制
- 启动阻抗测量使用05H命令（单通道）或0FH命令（多通道）
- 05H命令格式：`01 05 00 XX FF 00 xx xx`（启动）或 `01 05 00 XX 00 00 xx xx`（停止）
- 0FH命令格式：`01 0F 00 00 00 08 01 ZZ xx xx`（ZZ是数据字节，表示要启动的通道）
- 数据字节中的位映射：通道1对应最低位(bit 0)，通道8对应最高位(bit 7)
- 例如：
  - 启动通道1、2和3：`01 0F 00 00 00 08 01 07 xx xx`（07H = 00000111）
  - 启动通道6、7和8：`01 0F 00 00 00 08 01 E0 xx xx`（E0H = 11100000）
  - 启动所有通道：`01 0F 00 00 00 08 01 FF xx xx`（FFH = 11111111）
- ZM测量期间数据更新标志使用02H命令读取，地址范围1000H~103FH
- 数据更新标志为1表示数据已更新，读取RE和IM数据后自动清零

### 其他功能
- 使用04H命令可以获取设备的通道数（命令格式：`01 04 3E 00 00 01 xx xx`）

## 13. 获取状态码

### 基本参数
- **地址范围**：3380H ~ 338FH (通道1~64)，地址步进1
- **群发地址**：无
- **数据类型**：16位无符号整数，数据格式(BA)，状态码以以下表格

### 命令格式

#### 读取命令 (04H)
| 字段 | 起始位 | 命令字 | 起始地址高8位 | 起始地址低8位 | 寄存器数量高8位 | 寄存器数量低8位 | CRCH | CRCL |
|------|-------|-------|------------|------------|--------------|--------------|------|------|
| 示例 | 01H   | 04H   | 33H        | 80H        | 00H          | 03H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 返回字节数 | 数据1高8位 | 数据1低8位 | 数据2高8位 | 数据2低8位 | 数据3高8位 | 数据3低8位 | CRCH | CRCL |
|------|-------|-------|---------|---------|---------|---------|---------|---------|---------|------|------|
| 示例 | 01H   | 04H   | 06H     | 00H     | 00H     | 00H     | 00H     | 00H     | 00H     | xxH  | xxH  |

### 具体例子

#### 例1：读取1~3通道的状态码
```
发送命令：01 04 33 80 00 03 xx xx  (xx为CRC校验码)
返回数据：01 04 06 00 00 00 00 00 00 xx xx
```

返回数据解析：
- CH1=0000H (空闲)
- CH2=0000H (空闲)
- CH3=0000H (空闲)

### 状态码说明

| 状态码 | 说明 |
|-------|------|
| 0000H | 空闲 |
| 0001H | ZM测量中 |
| 0002H | 平衡功能运行中 |
| 0003H | 电池电压低或电池未安装 |
| 0004H | 设置错误 |
| 0005H | 硬件错误/ADC错误 |
| 0006H | 测量完成 |

## 14. 设置ZM采样电阻档位

### 基本参数
- **地址范围**：40C0H ~ 40FFH (通道1~64)，地址步进1
- **群发地址**：4F03H
- **数据类型**：16位无符号整数
- **有效档位值**：
  - 00H：1R档位
  - 01H：5R档位
  - 02H：10R档位

### 命令格式

#### 读取命令 (03H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 03H   | 40H     | C0H     | 00H          | 01H          | xxH  | xxH  |

#### 返回帧
| 字段 | 起始位 | 命令字 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|-------|------|------|------|------|
| 示例 | 01H   | 03H   | 02H   | 00H  | 01H  | xxH  | xxH  |

#### 设置命令 (10H)
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | 字节数 | 数据1 | 数据2 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|------|------|------|
| 示例 | 01H   | 10H   | 40H     | C0H     | 00H          | 01H          | 02H  | 00H  | 01H  | xxH  | xxH  |

#### 设置返回帧
| 字段 | 起始位 | 命令字 | 地址高位 | 地址低位 | 寄存器数量高位 | 寄存器数量低位 | CRC高 | CRC低 |
|------|-------|-------|---------|---------|--------------|--------------|------|------|
| 示例 | 01H   | 10H   | 40H     | C0H     | 00H          | 01H          | xxH  | xxH  |

### 具体例子

#### 例1：读取第一个通道的采样电阻档位
```
发送命令：01 03 40 C0 00 01 xx xx  (xx为CRC校验码)
返回数据：01 03 02 00 01 xx xx     (返回档位值01H，表示5R档位，xx为CRC校验码)
```

#### 例2：设置第一个通道的采样电阻档位为01H（5R）
```
发送命令：01 10 40 C0 00 01 02 00 01 xx xx  (档位值为01H，表示5R档位)
返回数据：01 10 40 C0 00 01 xx xx           (xx为CRC校验码)
```

#### 例3：使用群发地址设置所有通道的采样电阻档位为01H（5R）
```
发送命令：01 10 4F 03 00 01 02 00 01 xx xx  (设置档位值为01H，表示5R档位)
返回数据：01 10 4F 03 00 01 xx xx           (xx为CRC校验码)
```

### 测试结果

通过实际测试，我们发现：

1. **采样电阻档位值**：
   - 00H：对应1R档位
   - 01H：对应5R档位
   - 02H：对应10R档位
   - 不支持其他值

2. **群发功能**：
   - 使用群发地址(4F03H)的10H命令可以成功设置所有通道的采样电阻档位
   - 群发命令对所有通道都有效

3. **批量读取**：
   - 可以使用03H命令一次性读取所有通道的采样电阻档位
   - 命令格式：`01 03 40 C0 00 08 xx xx`（从通道1开始读取8个通道）

4. **应用建议**：
   - 根据被测电池的阻抗范围选择合适的采样电阻档位
   - 1R档位（00H）适用于阻抗较小的电池（约1-5mΩ）
   - 5R档位（01H）适用于阻抗中等的电池（约5-10mΩ）
   - 10R档位（02H）适用于阻抗较大的电池（约10-20mΩ）
   - 设置采样电阻档位后，建议验证设置是否生效

