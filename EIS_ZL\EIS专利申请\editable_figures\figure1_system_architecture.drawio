<?xml version="1.0" ?>
<mxfile host="app.diagrams.net" modified="2025-01-09T00:00:00.000Z" agent="Patent Figure Generator">
  <diagram id="figure1" name="图1-系统架构图">
    <mxGraphModel dx="1426" dy="827" grid="1" gridSize="10">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="1-被测电池&#10;1.9V-5.5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ADD8E6;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="100" y="150" width="200" height="120" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="7-测试夹具&#10;四线制连接&#10;精确测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5DEB3;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="100" y="350" width="200" height="120" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="2-DNB1101BB&#10;EIS测试芯片&#10;0.0075Hz-7800Hz" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#90EE90;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="450" y="300" width="300" height="200" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="4-外部电流源&#10;PMV28UNEA&#10;20Ω/10Ω/6.67Ω/5Ω" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F08080;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="100" y="600" width="200" height="150" as="geometry"/>
        </mxCell>
        <mxCell id="6" value="3-STM32F103RCT6&#10;主控制器&#10;72MHz ARM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFE0;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="850" y="300" width="250" height="200" as="geometry"/>
        </mxCell>
        <mxCell id="7" value="5-串口显示屏&#10;实时显示&#10;测试结果" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D3D3D3;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="850" y="600" width="250" height="150" as="geometry"/>
        </mxCell>
        <mxCell id="8" value="6-PC上位机&#10;Modbus RTU&#10;数据分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B0C4DE;strokeColor=#000000;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="850" y="100" width="250" height="120" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
