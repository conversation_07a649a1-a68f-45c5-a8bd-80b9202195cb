#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A电池阻抗测试系统 PyInstaller打包脚本

使用PyInstaller将Python项目打包成可执行文件
作者：Jack
日期：2025-01-31
版本：1.0
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def clean_previous_builds():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")
    
    dirs_to_clean = ["dist", "build", "__pycache__"]
    files_to_clean = ["*.spec"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                if os.path.isdir(dir_name):
                    shutil.rmtree(dir_name)
                    print(f"✅ 已清理目录: {dir_name}")
                else:
                    os.remove(dir_name)
                    print(f"✅ 已清理文件: {dir_name}")
            except Exception as e:
                print(f"⚠️  清理 {dir_name} 时出错: {e}")
    
    # 清理spec文件
    for file in os.listdir("."):
        if file.endswith(".spec"):
            try:
                os.remove(file)
                print(f"✅ 已清理spec文件: {file}")
            except Exception as e:
                print(f"⚠️  清理 {file} 时出错: {e}")
    
    print("✅ 构建环境清理完成")

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        result = subprocess.run(["pyinstaller", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"✅ PyInstaller版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装或无法运行")
        print("💡 正在尝试安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            print("💡 请手动安装: pip install pyinstaller")
            return False

def build_executable():
    """使用PyInstaller构建可执行文件"""
    
    print("🚀 开始使用PyInstaller打包JCY5001A电池阻抗测试系统...")
    
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 输出目录名
    output_name = f"JCY5001A_EIS_System_{timestamp}"
    
    # PyInstaller命令
    pyinstaller_cmd = [
        "pyinstaller",
        "--onedir",                       # 生成文件夹模式
        "--windowed",                     # Windows下不显示控制台
        "--name", output_name,            # 输出文件名
        
        # 添加数据文件
        "--add-data", "resources;resources",
        "--add-data", "config;config", 
        "--add-data", "templates;templates",
        "--add-data", "requirements.txt;.",
        
        # 隐藏导入（确保包含关键模块）
        "--hidden-import", "PyQt5",
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui", 
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "serial",
        "--hidden-import", "openpyxl",
        "--hidden-import", "xlsxwriter",
        "--hidden-import", "PIL",
        "--hidden-import", "psutil",
        "--hidden-import", "sqlalchemy",
        "--hidden-import", "win32api",
        "--hidden-import", "win32print",
        "--hidden-import", "pywintypes",
        
        # 排除不需要的模块
        "--exclude-module", "pytest",
        "--exclude-module", "setuptools",
        "--exclude-module", "distutils",
        "--exclude-module", "tkinter",
        
        # 其他选项
        "--clean",                        # 清理临时文件
        "--noconfirm",                    # 不确认覆盖
        
        # 主入口文件
        "main.py"
    ]
    
    # 如果存在图标文件，添加图标
    icon_path = os.path.join("resources", "icons", "app_icon.ico")
    if os.path.exists(icon_path):
        pyinstaller_cmd.extend(["--icon", icon_path])
        print(f"✅ 找到应用图标: {icon_path}")
    
    print("📦 PyInstaller编译命令:")
    print(" ".join(pyinstaller_cmd))
    print()
    
    try:
        # 执行PyInstaller编译
        print("⏳ 开始编译，这可能需要5-15分钟时间...")
        print("💡 PyInstaller会自动检测和包含依赖")
        print()
        
        result = subprocess.run(pyinstaller_cmd, check=True)
        
        if result.returncode == 0:
            print(f"✅ 编译成功！")
            
            # 检查输出目录
            output_dir = os.path.join("dist", output_name)
            if os.path.exists(output_dir):
                print(f"📁 输出目录: {output_dir}")
                
                # 检查主执行文件
                exe_file = os.path.join(output_dir, output_name + ".exe")
                if os.path.exists(exe_file):
                    file_size = os.path.getsize(exe_file) / (1024 * 1024)
                    print(f"📊 主执行文件大小: {file_size:.1f} MB")
                
                # 计算整个目录大小
                total_size = 0
                for dirpath, _, filenames in os.walk(output_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                
                total_size_mb = total_size / (1024 * 1024)
                print(f"📊 总目录大小: {total_size_mb:.1f} MB")

                # 创建启动脚本
                create_startup_script(output_dir, output_name)

                print("\n🎉 打包完成！")
                print(f"💡 运行方式: 双击 {output_dir}\\{output_name}.exe")
                print(f"💡 或使用启动脚本: {output_dir}\\启动程序.bat")
                print("💡 整个文件夹可以复制到其他电脑上运行")
            
        else:
            print(f"❌ 编译失败，返回码: {result.returncode}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller编译失败: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 打包过程中发生错误: {e}")
        return False
    
    return True

def create_startup_script(output_dir, exe_name):
    """创建启动脚本"""
    try:
        startup_script = f"""@echo off
echo 正在启动JCY5001A电池阻抗测试系统...
echo.
cd /d "%~dp0"
"{exe_name}.exe"
if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查：
    echo 1. 确保所有文件完整
    echo 2. 检查系统兼容性
    echo 3. 联系技术支持
    pause
)
"""

        script_path = os.path.join(output_dir, "启动程序.bat")
        with open(script_path, 'w', encoding='gbk') as f:
            f.write(startup_script)

        print(f"✅ 已创建启动脚本: 启动程序.bat")
        return True

    except Exception as e:
        print(f"⚠️  创建启动脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("JCY5001A电池阻抗测试系统 - PyInstaller打包工具 v1.0")
    print("=" * 70)
    print("🔧 特性:")
    print("  • 使用PyInstaller自动检测依赖")
    print("  • 生成独立可执行文件夹")
    print("  • 包含所有必要的资源文件")
    print("  • 自动创建启动脚本")
    print("=" * 70)

    # 清理之前的构建
    clean_previous_builds()
    print()

    # 检查PyInstaller
    if not check_pyinstaller():
        sys.exit(1)

    print()

    # 开始打包
    if build_executable():
        print("\n🎯 打包任务完成！")
        print("\n📝 使用说明:")
        print("1. 生成的文件夹包含所有运行时依赖")
        print("2. 可以将整个文件夹复制到其他Windows电脑上运行")
        print("3. 不需要在目标电脑上安装Python或其他依赖")
        print("4. 首次运行可能需要几秒钟初始化时间")
        print("5. 使用'启动程序.bat'可以更方便地启动软件")
        print("\n⚡ 性能提示:")
        print("• 首次运行会进行初始化，后续启动会更快")
        print("• 建议将整个文件夹放在SSD上以获得最佳性能")
        print("• 如遇到问题，请检查Windows Defender等杀毒软件设置")
    else:
        print("\n💥 打包任务失败！")
        print("💡 常见解决方案:")
        print("1. 确保所有依赖包已正确安装")
        print("2. 检查磁盘空间是否充足（建议至少2GB）")
        print("3. 关闭杀毒软件后重试")
        print("4. 使用管理员权限运行")
        sys.exit(1)

if __name__ == "__main__":
    main()
