#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数值框缩短修改是否正确
"""

import sys
import os

def verify_number_box_shrink_changes():
    """验证数值框缩短修改"""
    
    files_to_check = [
        "ui/components/channel_display_widget.py",
        "ui/components/statistics_widget.py"
    ]
    
    all_passed = True
    
    print("🔍 验证数值框缩短修改:")
    print("=" * 60)
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            all_passed = False
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n📁 检查文件: {file_path}")
        
        if file_path == "ui/components/channel_display_widget.py":
            # 检查通道显示组件的修改
            checks = [
                ("电压框最小宽度", "setMinimumWidth(32)" in content and "电压数值" in content),
                ("电压框最大宽度", "setMaximumWidth(32)" in content and "电压数值" in content),
                ("阻抗值框最小宽度", "setMinimumWidth(32)" in content and "数值标签" in content),
                ("阻抗值框最大宽度", "setMaximumWidth(32)" in content and "数值标签" in content),
                ("刚好显示4个字符注释", "刚好显示4个字符" in content),
                ("去除多余空间注释", "去除多余空间" in content),
            ]
            
        elif file_path == "ui/components/statistics_widget.py":
            # 检查统计组件的修改
            checks = [
                ("总测试数框宽度", "min-width: 32px !important" in content and "valueLabel" in content),
                ("合格数框宽度", "min-width: 32px !important" in content and "passLabel" in content),
                ("不合格数框宽度", "min-width: 32px !important" in content and "failLabel" in content),
                ("良率框宽度", "min-width: 32px !important" in content and "yieldLabel" in content),
                ("统一最大宽度", "max-width: 32px !important" in content),
                ("减少内边距", "padding: 4px 6px !important" in content),
                ("缩短至刚好显示注释", "缩短至刚好显示4个字符" in content),
            ]
        
        for check_name, condition in checks:
            if condition:
                print(f"  ✅ {check_name}: 通过")
            else:
                print(f"  ❌ {check_name}: 失败")
                all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有修改验证通过！")
        print("\n📋 修改总结:")
        print("1. ✅ 电压显示框缩短")
        print("   - 最小宽度：20px → 32px")
        print("   - 最大宽度：30px → 32px")
        print("   - 刚好显示'0.000'四个字符")
        
        print("\n2. ✅ RS/RCT阻抗值显示框缩短")
        print("   - 最小宽度：35px → 32px")
        print("   - 最大宽度：40px → 32px")
        print("   - 刚好显示'0.000'四个字符")
        
        print("\n3. ✅ 统计数值框缩短")
        print("   - 所有统计框宽度：60px → 32px")
        print("   - 内边距减少：12px → 6px")
        print("   - 固定宽度，去除多余空间")
        
        print("\n🎯 预期效果:")
        print("• 所有显示'0.000'或数字的框明显变窄")
        print("• 框的宽度刚好适配显示的内容")
        print("• 去除右侧多余的空白区域")
        print("• 数字内容仍能正常显示，不被截断")
        print("• 为其他组件释放更多空间")
        
    else:
        print("⚠️  部分修改可能未正确应用")
    
    return all_passed

def main():
    """主函数"""
    print("🚀 JCY5001AS 数值框缩短修改验证")
    print("📝 验证内容:")
    print("   缩短显示'0.000'数值的框至刚好能显示4个字符")
    print()
    
    success = verify_number_box_shrink_changes()
    
    if success:
        print("\n🎯 建议测试步骤:")
        print("1. 运行主程序或测试程序")
        print("2. 观察所有显示数值的框是否明显变窄")
        print("3. 确认框的宽度是否刚好适配显示内容")
        print("4. 确认是否去除了右侧多余空白区域")
        print("5. 确认数字内容是否仍能正常显示")
    
    return success

if __name__ == "__main__":
    main()
