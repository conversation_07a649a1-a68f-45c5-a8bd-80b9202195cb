# JCY5001AS 厂家端授权管理工具说明

## 概述

JCY5001AS 电池测试系统提供了两个独立的厂家端授权管理工具，用于管理客户软件的授权和解锁。

## 工具列表

### 1. 完整授权管理器 (`license_manager_app.py`)

**功能特点：**
- 多选项卡界面，功能全面
- 解锁码生成功能
- 授权状态查看和管理
- 试用期管理
- 详细的授权日志记录

**适用场景：**
- 需要全面管理客户授权的场景
- 需要查看授权历史和状态的场景
- 需要管理试用期的场景

**启动方式：**
```bash
python license_manager_app.py
# 或者
start_license_manager.bat
```

### 2. 简化解锁码生成工具 (`unlock_code_generator.py`)

**功能特点：**
- 专门用于生成解锁码
- 界面简洁，操作快速
- 支持完整解锁和临时解锁
- 一键复制解锁码功能

**适用场景：**
- 只需要快速生成解锁码的场景
- 客服人员日常使用
- 紧急解锁需求

**启动方式：**
```bash
python unlock_code_generator.py
```

## 统一启动器

为了方便使用，提供了统一的启动脚本：

```bash
start_manufacturer_tools.bat
```

运行此脚本后，可以选择启动哪个工具。

## 使用流程

### 客户申请解锁流程

1. **客户端操作：**
   - 客户在软件中按 `Ctrl+U` 打开解锁对话框
   - 复制硬件指纹发送给厂家

2. **厂家端操作：**
   - 启动厂家端工具（推荐使用简化工具快速处理）
   - 输入管理员密码：`JCY5001-ADMIN`
   - 粘贴客户硬件指纹
   - 选择解锁类型（完整解锁/临时解锁）
   - 生成解锁码并发送给客户

3. **客户端验证：**
   - 客户输入收到的解锁码
   - 系统验证并完成解锁

## 安全注意事项

⚠️ **重要提醒：**

1. **工具保密：** 厂家端工具仅供内部使用，严禁分发给客户
2. **密码保护：** 管理员密码 `JCY5001-ADMIN` 请妥善保管
3. **安全传输：** 解锁码请通过安全渠道发送给客户
4. **日志记录：** 所有操作都有日志记录，请定期检查

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `license_manager_app.py` | 完整授权管理器主程序 |
| `unlock_code_generator.py` | 简化解锁码生成工具 |
| `start_license_manager.bat` | 授权管理器启动脚本 |
| `start_manufacturer_tools.bat` | 统一工具启动器 |
| `ui/dialogs/license_manager_dialog.py` | 授权管理器UI界面 |
| `utils/license_manager.py` | 授权管理核心逻辑 |

## 日志文件

- `license_manager.log` - 完整授权管理器日志
- `unlock_generator.log` - 解锁码生成工具日志
- `data/license_operations.log` - 授权操作详细日志

## 故障排除

### 常见问题

1. **启动失败：**
   - 检查Python环境是否正确安装
   - 确认依赖库（PyQt5, cryptography）已安装
   - 查看对应的日志文件获取详细错误信息

2. **解锁码生成失败：**
   - 确认管理员密码正确
   - 检查硬件指纹格式是否正确
   - 查看日志文件确认具体错误

3. **界面显示异常：**
   - 确认系统支持中文显示
   - 检查字体设置是否正确

### 技术支持

如遇到技术问题，请联系开发团队并提供：
- 详细的错误描述
- 相关的日志文件
- 操作系统和Python版本信息

---

**版本：** 1.0.0  
**作者：** Jack  
**更新日期：** 2025-06-08
