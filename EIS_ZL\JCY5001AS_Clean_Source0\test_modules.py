#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A 应用程序模块测试脚本
测试各个核心模块是否能正常导入和工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_modules():
    """测试各个核心模块"""
    print('=' * 50)
    print('JCY5001A 应用程序模块测试')
    print('=' * 50)
    
    test_results = []
    
    # 测试配置管理器
    try:
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        app_name = config.get("app.name", "未知")
        app_version = config.get("app.version", "未知")
        print(f'✅ 配置管理器: 正常')
        print(f'   应用名称: {app_name}')
        print(f'   版本号: {app_version}')
        test_results.append(('配置管理器', True, None))
    except Exception as e:
        print(f'❌ 配置管理器: {e}')
        test_results.append(('配置管理器', False, str(e)))
    
    # 测试数据库管理器
    try:
        from data.database_manager import initialize_database_manager
        db_manager = initialize_database_manager()
        print('✅ 数据库管理器: 正常')
        test_results.append(('数据库管理器', True, None))
    except Exception as e:
        print(f'❌ 数据库管理器: {e}')
        test_results.append(('数据库管理器', False, str(e)))
    
    # 测试通信管理器
    try:
        from backend.communication_manager import CommunicationManager
        print('✅ 通信管理器: 导入正常')
        test_results.append(('通信管理器', True, None))
    except Exception as e:
        print(f'❌ 通信管理器: {e}')
        test_results.append(('通信管理器', False, str(e)))
    
    # 测试EIS分析器
    try:
        from backend.eis_analyzer import EISAnalyzer
        print('✅ EIS分析器: 导入正常')
        test_results.append(('EIS分析器', True, None))
    except Exception as e:
        print(f'❌ EIS分析器: {e}')
        test_results.append(('EIS分析器', False, str(e)))
    
    # 测试测试引擎
    try:
        from backend.test_engine import TestEngine
        print('✅ 测试引擎: 导入正常')
        test_results.append(('测试引擎', True, None))
    except Exception as e:
        print(f'❌ 测试引擎: {e}')
        test_results.append(('测试引擎', False, str(e)))
    
    # 测试主窗口
    try:
        from ui.main_window import MainWindow
        print('✅ 主窗口: 导入正常')
        test_results.append(('主窗口', True, None))
    except Exception as e:
        print(f'❌ 主窗口: {e}')
        test_results.append(('主窗口', False, str(e)))
    
    # 测试授权管理器
    try:
        from utils.license_manager import LicenseManager
        print('✅ 授权管理器: 导入正常')
        test_results.append(('授权管理器', True, None))
    except Exception as e:
        print(f'❌ 授权管理器: {e}')
        test_results.append(('授权管理器', False, str(e)))
    
    print('=' * 50)
    print('测试结果汇总:')
    print('=' * 50)
    
    success_count = 0
    for module_name, success, error in test_results:
        if success:
            print(f'✅ {module_name}: 通过')
            success_count += 1
        else:
            print(f'❌ {module_name}: 失败 - {error}')
    
    total_count = len(test_results)
    print(f'\n总计: {success_count}/{total_count} 个模块测试通过')
    
    if success_count == total_count:
        print('🎉 所有模块测试通过！应用程序准备就绪。')
        return True
    else:
        print('⚠️ 部分模块测试失败，需要修复后再进行打包。')
        return False

if __name__ == "__main__":
    success = test_modules()
    sys.exit(0 if success else 1)
