#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A电池阻抗测试系统 Nuitka 快速打包脚本

用于快速测试打包功能，排除大型依赖
作者：Jack
日期：2025-01-31
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def clean_previous_builds():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")
    
    dirs_to_clean = ["dist", "main.build", "main.dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                if os.path.isdir(dir_name):
                    shutil.rmtree(dir_name)
                    print(f"✅ 已清理目录: {dir_name}")
                else:
                    os.remove(dir_name)
                    print(f"✅ 已清理文件: {dir_name}")
            except Exception as e:
                print(f"⚠️  清理 {dir_name} 时出错: {e}")
    
    print("✅ 构建环境清理完成")

def build_executable():
    """使用Nuitka构建standalone可执行文件（快速版本）"""
    
    print("🚀 开始使用Nuitka快速打包JCY5001A电池阻抗测试系统...")
    
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 输出目录名
    output_name = f"JCY5001A_EIS_System_Quick_{timestamp}"
    
    # Nuitka编译命令 - 快速版本（排除大型依赖）
    nuitka_cmd = [
        "python", "-m", "nuitka",
        "--standalone",                    # 独立模式，生成文件夹
        "--windows-console-mode=disable", # Windows下禁用控制台窗口
        "--enable-plugin=pyqt5",          # 启用PyQt5插件
        
        # 核心依赖包（精简版）
        "--include-package=PyQt5",        # PyQt5 GUI框架
        "--include-package=numpy",        # 数值计算
        "--include-package=serial",       # 串口通信
        "--include-package=openpyxl",     # Excel文件处理
        "--include-package=xlsxwriter",   # Excel写入
        "--include-package=PIL",          # 图像处理
        "--include-package=psutil",       # 系统信息
        "--include-package=sqlalchemy",   # 数据库ORM
        
        # Windows特定依赖
        "--include-package=win32api",     # Windows API
        "--include-package=win32print",   # Windows打印API
        "--include-package=pywintypes",   # Windows类型
        
        # 数据文件
        "--include-data-dir=resources=resources",     # 资源文件夹
        "--include-data-dir=config=config",           # 配置文件夹
        "--include-data-dir=templates=templates",     # 模板文件夹
        
        # 排除大型包以加快编译
        "--nofollow-import-to=scipy",     # 排除scipy（大型科学计算包）
        "--nofollow-import-to=matplotlib", # 排除matplotlib（大型绘图包）
        "--nofollow-import-to=pandas",    # 排除pandas（大型数据分析包）
        "--nofollow-import-to=pytest",    # 排除测试框架
        "--nofollow-import-to=setuptools", # 排除安装工具
        "--nofollow-import-to=distutils", # 排除分发工具
        "--nofollow-import-to=tkinter",   # 排除Tkinter
        
        # 输出设置
        f"--output-dir=dist",             # 输出目录
        f"--output-filename={output_name}",  # 输出文件名
        
        # 编译选项
        "--assume-yes-for-downloads",     # 自动确认下载
        "--show-progress",                # 显示进度
        "--remove-output",                # 清理之前的输出
        "--jobs=2",                       # 并行编译（2个进程，减少内存使用）
        
        # 主入口文件
        "main.py"
    ]
    
    # 如果存在图标文件，添加图标
    icon_path = os.path.join("resources", "icons", "app_icon.ico")
    if os.path.exists(icon_path):
        nuitka_cmd.insert(-1, f"--windows-icon-from-ico={icon_path}")
        print(f"✅ 找到应用图标: {icon_path}")
    
    print("📦 Nuitka快速编译命令:")
    print(" ".join(nuitka_cmd))
    print()
    
    try:
        # 执行Nuitka编译
        print("⏳ 开始快速编译，预计需要5-10分钟...")
        print("💡 快速模式排除了scipy、matplotlib、pandas等大型包")
        print()
        
        result = subprocess.run(nuitka_cmd, check=True, capture_output=False)
        
        if result.returncode == 0:
            print(f"✅ 快速编译成功！")
            
            # 检查输出目录
            output_dir = os.path.join("dist", output_name + ".dist")
            if os.path.exists(output_dir):
                print(f"📁 输出目录: {output_dir}")
                
                # 检查主执行文件
                exe_file = os.path.join(output_dir, output_name + ".exe")
                if os.path.exists(exe_file):
                    file_size = os.path.getsize(exe_file) / (1024 * 1024)
                    print(f"📊 主执行文件大小: {file_size:.1f} MB")
                
                # 计算整个目录大小
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(output_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                
                total_size_mb = total_size / (1024 * 1024)
                print(f"📊 总目录大小: {total_size_mb:.1f} MB")
                
                print("\n🎉 快速打包完成！")
                print(f"💡 运行方式: 双击 {output_dir}\\{output_name}.exe")
                print("💡 整个文件夹可以复制到其他电脑上运行")
                print("\n⚠️  注意：快速版本可能缺少某些功能（如图表绘制）")
            
        else:
            print(f"❌ 编译失败，返回码: {result.returncode}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka编译失败: {e}")
        return False
        
    except FileNotFoundError:
        print("❌ 找不到Nuitka命令")
        print("💡 请先安装Nuitka: pip install nuitka")
        return False
        
    except Exception as e:
        print(f"❌ 打包过程中发生错误: {e}")
        return False
    
    return True

def check_nuitka():
    """检查Nuitka是否安装"""
    try:
        result = subprocess.run(["python", "-m", "nuitka", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Nuitka版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Nuitka未安装或无法运行")
        print("💡 请先安装Nuitka: pip install nuitka")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("JCY5001A电池阻抗测试系统 - Nuitka 快速打包工具")
    print("=" * 60)
    print("🔧 快速模式特性:")
    print("  • 排除大型依赖包以加快编译速度")
    print("  • 适用于功能测试和基本部署")
    print("  • 编译时间约5-10分钟")
    print("=" * 60)
    
    # 清理之前的构建
    clean_previous_builds()
    print()
    
    # 检查Nuitka
    if not check_nuitka():
        sys.exit(1)
    
    print()
    
    # 开始打包
    if build_executable():
        print("\n🎯 快速打包任务完成！")
        print("\n📝 使用说明:")
        print("1. 生成的文件夹包含基本运行时依赖")
        print("2. 可以将整个文件夹复制到其他Windows电脑上运行")
        print("3. 快速版本可能缺少某些高级功能")
        print("4. 如需完整功能，请使用 build_exe_standalone.py")
    else:
        print("\n💥 快速打包任务失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
