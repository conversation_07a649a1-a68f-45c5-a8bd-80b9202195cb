# JCY5001AS 授权管理系统

## 📋 概述

JCY5001AS 授权管理系统包含两个部分：
1. **客户端解锁申请功能**：集成在主软件中，用于客户申请解锁
2. **厂家端授权管理工具**：独立软件，用于厂家生成和管理解锁码

## 🔄 工作流程

### 完整的授权工作流程

```
客户端                           厂家端
   ↓                              ↓
1. 试用期到期/需要正式授权    →   收到客户申请
   ↓                              ↓
2. 打开"申请解锁"功能         →   打开授权管理工具
   ↓                              ↓
3. 显示硬件指纹              →   输入客户硬件指纹
   ↓                              ↓
4. 复制并发送给厂家          →   选择解锁类型
   ↓                              ↓
5. 等待厂家回复解锁码        →   生成解锁码
   ↓                              ↓
6. 输入解锁码               →   发送解锁码给客户
   ↓                              ↓
7. 验证并解锁成功           →   记录操作日志
```

## 🚀 功能特性

### 👥 客户端功能（主软件集成）

#### 📋 申请解锁
- **硬件指纹显示**: 自动获取并显示本机硬件指纹
- **一键复制**: 方便复制硬件指纹发送给厂家
- **联系信息**: 显示厂家联系方式
- **申请指导**: 详细的申请流程说明

#### 🔑 解锁码输入
- **4段式输入**: 标准化的解锁码输入格式
- **自动验证**: 实时验证解锁码格式和有效性
- **多种解锁类型**: 支持永久授权、试用期延长、临时授权
- **状态反馈**: 清晰的解锁结果显示

### 🏭 厂家端功能（独立管理工具）

#### 📊 授权状态管理
- **实时状态显示**: 显示当前授权状态、试用期信息、剩余天数
- **试用期进度**: 可视化试用期进度条和倒计时
- **功能状态**: 显示已启用的软件功能列表
- **授权历史**: 查看授权操作历史记录

#### 🔑 解锁码生成
- **客户指纹输入**: 输入客户发送的硬件指纹
- **解锁类型选择**: 永久授权、试用期延长、临时授权
- **天数设置**: 灵活设置延长天数（1-3650天）
- **安全绑定**: 解锁码与客户硬件指纹绑定
- **操作日志**: 记录所有生成操作

#### 🖥️ 硬件信息
- **硬件指纹**: 显示和复制硬件指纹
- **系统信息**: 显示操作系统、处理器、内存等信息
- **硬件详情**: 显示CPU、内存、磁盘、网络等详细信息

#### ⚙️ 高级管理（管理员功能）
- **试用期重置**: 重置试用期天数
- **授权状态恢复**: 将已授权状态恢复为试用状态
- **快速测试**: 设置试用期快速到期（用于测试）
- **模拟模式**: 模拟试用期到期状态

## 🔧 安装和运行

### 系统要求
- Windows 10/11
- Python 3.7+
- PyQt5
- cryptography

### 运行方式

#### 方式1：使用批处理文件（推荐）
```bash
双击运行 start_license_manager.bat
```

#### 方式2：直接运行Python脚本
```bash
python license_manager_app.py
```

## 👥 用户角色

### 操作员模式
- 查看授权状态
- 输入解锁码
- 查看硬件信息
- 查看操作历史

### 管理员模式
- 所有操作员功能
- 生成解锁码
- 重置试用期
- 恢复授权状态
- 快速测试功能
- 模拟到期模式

**默认管理员密码**: `admin123`

## 📖 使用说明

### 🎯 客户端使用说明（主软件）

#### 1. 申请软件解锁
当软件试用期到期或需要正式授权时：

1. **打开申请解锁功能**
   - 在主软件菜单栏选择：`帮助` → `申请解锁`
   - 或使用快捷键：`Ctrl+U`

2. **获取硬件指纹**
   - 在"申请解锁"选项卡中查看硬件指纹
   - 点击"📋 复制硬件指纹"按钮
   - 硬件指纹已复制到剪贴板

3. **联系厂家**
   - 将硬件指纹发送给软件供应商
   - 联系方式：
     - 邮箱：<EMAIL>
     - 电话：400-xxx-xxxx
     - QQ：123456789

4. **输入解锁码**
   - 收到厂家提供的解锁码后
   - 切换到"输入解锁码"选项卡
   - 在4个输入框中输入解锁码
   - 点击"🔓 验证并解锁"按钮

#### 2. 解锁码格式
- 标准格式：`XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX`
- 每段8个字符，共4段，用"-"分隔
- 支持自动跳转到下一个输入框

### 🏭 厂家端使用说明（独立工具）

#### 1. 启动授权管理工具
- 双击 `start_license_manager.bat` 启动软件
- 软件会显示启动画面，然后打开主界面

#### 2. 生成解锁码
1. **切换到管理员模式**
   - 点击"切换到管理员模式"按钮
   - 输入管理员密码（默认：admin123）

2. **输入客户信息**
   - 在"🔑 解锁管理"选项卡中
   - 粘贴客户发送的硬件指纹
   - 输入客户ID（如：CUSTOMER_001）

3. **选择解锁类型**
   - **永久授权 (full)**：软件永久解锁
   - **试用期延长 (trial_extend)**：延长试用期
   - **临时授权 (temp)**：临时授权指定天数

4. **生成并发送解锁码**
   - 点击"生成解锁码"按钮
   - 复制生成的解锁码
   - 发送给客户

#### 3. 查看授权状态
- 在 **📊 授权状态** 选项卡中查看当前授权状态
- 绿色表示已授权，橙色表示试用期，红色表示已过期

#### 4. 查看硬件信息
- 在 **🖥️ 硬件信息** 选项卡中查看硬件指纹和系统信息
- 可以复制硬件指纹用于对比验证

#### 5. 高级管理功能
- 在 **⚙️ 高级管理** 选项卡中使用管理员功能
- 包括试用期重置、状态恢复、测试功能等

## 🔐 安全说明

### 硬件指纹
- 硬件指纹基于计算机硬件生成，用于唯一标识设备
- 更换主要硬件（主板、CPU等）可能导致硬件指纹变化
- 硬件指纹用于生成和验证解锁码

### 解锁码格式
- 解锁码格式：`XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX`
- 每个解锁码与特定硬件指纹绑定
- 解锁码包含客户信息和有效期验证

### 管理员权限
- 管理员功能需要密码验证
- 建议定期更改管理员密码
- 管理员功能仅用于授权管理和测试

## 📝 日志文件

软件运行时会生成日志文件：
- **license_manager.log**: 授权管理器运行日志
- 日志包含启动信息、操作记录、错误信息等

## ❓ 常见问题

### Q: 软件无法启动
A: 检查Python环境和依赖库是否正确安装

### Q: 解锁码无效
A: 确认解锁码格式正确，检查硬件指纹是否匹配

### Q: 忘记管理员密码
A: 联系软件供应商重置密码

### Q: 硬件指纹变化
A: 更换硬件后需要重新生成解锁码

## 📞 技术支持

如有问题，请联系：
- **开发者**: Jack
- **邮箱**: <EMAIL>
- **电话**: 400-xxx-xxxx

## 📄 版本信息

- **版本**: 1.0.0
- **发布日期**: 2025-06-08
- **兼容性**: JCY5001AS 8路EIS阻抗筛选仪

---

© 2025 鲸测云科技. 保留所有权利.
