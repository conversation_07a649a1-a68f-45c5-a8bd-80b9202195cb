# 电池阻抗测试系统云端服务 - 快速启动指南

## 🚀 快速开始

### 1. 环境要求
- Python 3.8+ (推荐 3.11)
- pip (Python包管理器)

### 2. 安装和启动

#### 步骤1: 解压项目
```bash
# 解压下载的项目包
unzip battery_impedance_api_package.zip
cd battery_impedance_api_package
```

#### 步骤2: 安装依赖
```bash
# 创建虚拟环境 (推荐)
python -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖包
pip install -r requirements.txt
```

#### 步骤3: 启动服务
```bash
python src/main.py
```

### 3. 访问服务
- **API服务**: http://localhost:5002
- **API文档**: http://localhost:5002/api
- **健康检查**: http://localhost:5002/health

### 4. 默认管理员账号
- **用户名**: admin
- **密码**: Admin123!

## 📋 主要功能

### ✅ 核心API功能
1. **用户认证** - JWT令牌认证
2. **设备管理** - JCY5001设备注册和管理
3. **电池管理** - 电池信息管理
4. **测试管理** - 测试批次和结果管理
5. **数据处理** - Excel导入导出
6. **机器学习预测** - 容量、寿命、性能预测
7. **管理员功能** - 系统管理和监控

### 🤖 智能预测功能
- **容量预测**: 基于Rs、Rct预测电池容量
- **寿命预测**: 预测电池循环寿命
- **性能分析**: 温度性能影响分析
- **电池匹配**: 智能电池匹配推荐

## 🧪 快速测试

### 1. 登录获取令牌
```bash
curl -X POST http://localhost:5002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"Admin123!"}'
```

### 2. 测试预测API
```bash
# 使用上面获取的token替换 <your-token>
curl -X POST http://localhost:5002/api/predict/performance \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "rs": 0.05,
    "rct": 0.02,
    "voltage": 3.2,
    "temperature": 25,
    "battery_type": "LFP"
  }'
```

## 📚 详细文档
- 查看 `complete_system_delivery.pdf` 获取完整文档
- 查看 `requirements_completion_checklist.md` 了解功能清单

## 🔧 故障排除

### 常见问题
1. **端口被占用**: 修改 `.env` 文件中的端口
2. **依赖安装失败**: 确保Python版本正确
3. **权限问题**: 使用虚拟环境

### 技术支持
- 查看项目文档获取详细信息
- 所有API都有完整的错误处理和日志

## 🎯 生产部署

### 使用Gunicorn (推荐)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5002 src.main:app
```

### 使用Docker
```bash
docker build -t battery-api .
docker run -p 5002:5002 battery-api
```

---
**项目版本**: v2.0.0  
**完成度**: 100%  
**状态**: 生产就绪 ✅

