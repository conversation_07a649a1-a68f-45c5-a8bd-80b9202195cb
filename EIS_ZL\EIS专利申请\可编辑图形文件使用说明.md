# 可编辑专利附图文件使用说明

## 概述

为了解决matplotlib生成图片的字体乱码问题，并方便您进行二次编辑，我已经为您生成了多种可编辑格式的专利附图文件。这些文件可以在不同的软件中打开和编辑，避免了字体显示问题。

## 生成的文件列表

### 📁 editable_figures 文件夹包含：

1. **figure1_system_architecture.svg** - SVG矢量图形文件
2. **figure1_system_architecture.drawio** - Draw.io流程图文件
3. **figure1_editable.html** - HTML可编辑网页版本
4. **figure1_powerpoint_template.xml** - PowerPoint模板文件
5. **figure1_visio_template.xml** - Visio模板文件
6. **figure1_editing_guide.txt** - 详细编辑指南

## 各格式使用方法

### 🎨 1. SVG文件 (推荐)
**文件**: `figure1_system_architecture.svg`

**支持的软件**:
- **LibreOffice Draw** (免费) - 完全支持编辑
- **Inkscape** (免费) - 专业矢量图形编辑器
- **Adobe Illustrator** (付费) - 专业设计软件
- **CorelDRAW** (付费) - 矢量图形软件

**使用方法**:
1. 双击SVG文件，选择用LibreOffice Draw打开
2. 可以直接编辑文本、移动组件、修改颜色
3. 导出为PNG或PDF格式用于专利申请

**优点**: 矢量格式，无损缩放，文字清晰

### 🌐 2. Draw.io文件
**文件**: `figure1_system_architecture.drawio`

**使用方法**:
1. 访问 https://app.diagrams.net/
2. 点击"打开现有图表"
3. 选择上传drawio文件
4. 在线编辑后导出为PNG/PDF

**优点**: 在线编辑，无需安装软件，专业流程图工具

### 💻 3. HTML文件
**文件**: `figure1_editable.html`

**使用方法**:
1. 用浏览器打开HTML文件
2. 组件可以拖拽移动
3. 右键查看源代码可以编辑文本和样式
4. 使用浏览器的打印功能导出为PDF

**优点**: 跨平台，可视化编辑，容易修改

### 📊 4. PowerPoint模板
**文件**: `figure1_powerpoint_template.xml`

**使用方法**:
1. 打开Microsoft PowerPoint或LibreOffice Impress
2. 导入XML文件或复制内容到新幻灯片
3. 按照XML中的坐标和样式重建图形
4. 导出为高质量图片

**优点**: 熟悉的界面，易于编辑文本和格式

### 📈 5. Visio模板
**文件**: `figure1_visio_template.xml`

**使用方法**:
1. 打开Microsoft Visio或其他流程图软件
2. 导入XML模板
3. 根据模板重建图形
4. 导出为所需格式

**优点**: 专业的流程图和系统架构图工具

### 📝 6. 编辑指南
**文件**: `figure1_editing_guide.txt`

**内容包括**:
- 所有组件的精确坐标和尺寸
- 颜色代码和样式信息
- 连接关系和箭头样式
- 文本内容和格式要求

**使用方法**:
1. 在任何绘图软件中创建新文档
2. 按照指南中的坐标创建矩形框
3. 设置相应的颜色和文本
4. 添加箭头连接线

## 推荐的编辑流程

### 方案一：LibreOffice Draw (免费，推荐)
1. 安装LibreOffice (免费办公套件)
2. 用LibreOffice Draw打开SVG文件
3. 直接编辑组件位置、文本、颜色
4. 导出为PNG (300 DPI) 用于专利申请

### 方案二：在线Draw.io (无需安装)
1. 访问 https://app.diagrams.net/
2. 上传drawio文件
3. 在线编辑
4. 导出为PNG/PDF

### 方案三：HTML + 浏览器 (简单快捷)
1. 用浏览器打开HTML文件
2. 拖拽调整组件位置
3. 编辑HTML源代码修改文本
4. 截图或打印为PDF

## 解决字体问题的方法

### 在LibreOffice Draw中：
1. 选择所有文本
2. 字体 → 选择"Arial"或"SimHei"
3. 确保字体大小合适
4. 导出时选择"嵌入字体"

### 在SVG编辑器中：
1. 文本使用标准字体（Arial, Times New Roman）
2. 避免使用系统特定字体
3. 导出时转换文本为路径

## 导出建议

### 用于专利申请的最佳设置：
- **格式**: PNG或PDF
- **分辨率**: 300 DPI
- **尺寸**: A4横向 (297×210mm)
- **字体**: 清晰可读，建议12pt以上
- **线条**: 粗细适中，确保打印清晰

## 常见问题解决

### Q: 文字显示为方框或乱码
**A**: 更换为标准字体（Arial, Times New Roman），或在导出时选择"嵌入字体"

### Q: 箭头方向不对
**A**: 在编辑软件中重新绘制箭头，或调整箭头的起点和终点

### Q: 颜色显示不正确
**A**: 使用标准的十六进制颜色代码，如#ADD8E6（浅蓝色）

### Q: 导出的图片模糊
**A**: 确保导出分辨率设置为300 DPI，选择PNG或PDF格式

## 技术支持

如果您在使用过程中遇到问题：
1. 查看编辑指南文件中的详细说明
2. 尝试不同的编辑软件
3. 使用在线Draw.io作为备选方案

## 文件备份

建议您：
1. 备份原始的可编辑文件
2. 保存编辑过程中的中间版本
3. 最终版本导出为多种格式备用

---

**注意**: 所有生成的文件都避免了matplotlib的字体问题，可以正常显示中文内容。选择最适合您的编辑软件进行修改即可。
