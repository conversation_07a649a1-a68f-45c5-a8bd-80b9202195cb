<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专利附图简单编辑器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .editor-container { 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title { 
            text-align: center; 
            font-size: 24px; 
            font-weight: bold; 
            margin-bottom: 20px; 
            color: #333;
        }
        .canvas { 
            width: 1200px; 
            height: 800px; 
            position: relative; 
            border: 2px solid #ccc; 
            margin: 20px auto;
            background: white;
            overflow: hidden;
        }
        .component { 
            position: absolute; 
            border: 2px solid black; 
            border-radius: 8px; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            text-align: center; 
            font-weight: bold; 
            cursor: move; 
            user-select: none;
            font-size: 12px;
            line-height: 1.2;
        }
        .component:hover {
            box-shadow: 0 0 10px rgba(0,0,255,0.3);
            transform: scale(1.02);
        }
        .component.selected {
            border-color: #ff0000;
            box-shadow: 0 0 15px rgba(255,0,0,0.5);
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.danger:hover {
            background: #da190b;
        }
        .property-panel {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 250px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .property-panel h3 {
            margin-top: 0;
            color: #333;
        }
        .property-panel input, .property-panel textarea {
            width: 100%;
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .color-picker {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <div class="title">图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图</div>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ul>
                <li><strong>拖拽移动</strong>：点击组件可以拖拽移动位置</li>
                <li><strong>编辑属性</strong>：点击组件后在右侧面板修改文本和颜色</li>
                <li><strong>导出图片</strong>：点击"导出为图片"按钮保存</li>
                <li><strong>重置位置</strong>：点击"重置布局"恢复默认位置</li>
            </ul>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="exportAsImage()">导出为图片</button>
            <button class="btn" onclick="resetLayout()">重置布局</button>
            <button class="btn" onclick="addArrows()">显示/隐藏连接线</button>
            <button class="btn danger" onclick="printPage()">打印/保存PDF</button>
        </div>
        
        <div class="canvas" id="canvas">
            <!-- 组件将通过JavaScript动态创建 -->
        </div>
        
        <div class="property-panel" id="propertyPanel" style="display: none;">
            <h3>组件属性</h3>
            <label>文本内容：</label>
            <textarea id="componentText" rows="3"></textarea>
            
            <label>背景颜色：</label>
            <input type="color" id="componentColor" class="color-picker">
            
            <label>宽度：</label>
            <input type="number" id="componentWidth" min="100" max="400">
            
            <label>高度：</label>
            <input type="number" id="componentHeight" min="80" max="300">
            
            <button class="btn" onclick="applyChanges()">应用更改</button>
            <button class="btn danger" onclick="closePanel()">关闭</button>
        </div>
    </div>

    <script>
        let selectedComponent = null;
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let showArrows = false;

        // 组件数据
        const components = [
            { id: 'battery', text: '1-被测电池\n1.9V-5.5V', x: 80, y: 120, width: 180, height: 100, color: '#ADD8E6' },
            { id: 'fixture', text: '7-测试夹具\n四线制连接\n精确测量', x: 80, y: 280, width: 180, height: 100, color: '#F5DEB3' },
            { id: 'chip', text: '2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz', x: 350, y: 200, width: 280, height: 160, color: '#90EE90' },
            { id: 'current_source', text: '4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω', x: 80, y: 450, width: 180, height: 120, color: '#F08080' },
            { id: 'mcu', text: '3-STM32F103RCT6\n主控制器\n72MHz ARM', x: 720, y: 200, width: 220, height: 160, color: '#FFFFE0' },
            { id: 'display', text: '5-串口显示屏\n实时显示\n测试结果', x: 720, y: 450, width: 220, height: 120, color: '#D3D3D3' },
            { id: 'pc', text: '6-PC上位机\nModbus RTU\n数据分析', x: 720, y: 50, width: 220, height: 100, color: '#B0C4DE' }
        ];

        // 初始化画布
        function initCanvas() {
            const canvas = document.getElementById('canvas');
            canvas.innerHTML = '';
            
            components.forEach(comp => {
                const element = document.createElement('div');
                element.className = 'component';
                element.id = comp.id;
                element.style.left = comp.x + 'px';
                element.style.top = comp.y + 'px';
                element.style.width = comp.width + 'px';
                element.style.height = comp.height + 'px';
                element.style.backgroundColor = comp.color;
                element.innerHTML = comp.text.replace(/\n/g, '<br>');
                
                // 添加事件监听器
                element.addEventListener('mousedown', startDrag);
                element.addEventListener('click', selectComponent);
                
                canvas.appendChild(element);
            });
        }

        // 开始拖拽
        function startDrag(e) {
            e.preventDefault();
            isDragging = true;
            selectedComponent = e.target;
            
            const rect = selectedComponent.getBoundingClientRect();
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
            
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
        }

        // 拖拽中
        function drag(e) {
            if (!isDragging || !selectedComponent) return;
            
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
            const newX = e.clientX - canvasRect.left - dragOffset.x;
            const newY = e.clientY - canvasRect.top - dragOffset.y;
            
            // 边界检查
            const maxX = canvasRect.width - selectedComponent.offsetWidth;
            const maxY = canvasRect.height - selectedComponent.offsetHeight;
            
            selectedComponent.style.left = Math.max(0, Math.min(newX, maxX)) + 'px';
            selectedComponent.style.top = Math.max(0, Math.min(newY, maxY)) + 'px';
        }

        // 停止拖拽
        function stopDrag() {
            isDragging = false;
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);
        }

        // 选择组件
        function selectComponent(e) {
            if (isDragging) return;
            
            // 清除之前的选择
            document.querySelectorAll('.component').forEach(comp => {
                comp.classList.remove('selected');
            });
            
            // 选择当前组件
            e.target.classList.add('selected');
            selectedComponent = e.target;
            
            // 显示属性面板
            showPropertyPanel();
        }

        // 显示属性面板
        function showPropertyPanel() {
            if (!selectedComponent) return;
            
            const panel = document.getElementById('propertyPanel');
            const comp = components.find(c => c.id === selectedComponent.id);
            
            document.getElementById('componentText').value = comp.text;
            document.getElementById('componentColor').value = comp.color;
            document.getElementById('componentWidth').value = comp.width;
            document.getElementById('componentHeight').value = comp.height;
            
            panel.style.display = 'block';
        }

        // 应用更改
        function applyChanges() {
            if (!selectedComponent) return;
            
            const text = document.getElementById('componentText').value;
            const color = document.getElementById('componentColor').value;
            const width = document.getElementById('componentWidth').value;
            const height = document.getElementById('componentHeight').value;
            
            selectedComponent.innerHTML = text.replace(/\n/g, '<br>');
            selectedComponent.style.backgroundColor = color;
            selectedComponent.style.width = width + 'px';
            selectedComponent.style.height = height + 'px';
            
            // 更新数据
            const comp = components.find(c => c.id === selectedComponent.id);
            comp.text = text;
            comp.color = color;
            comp.width = parseInt(width);
            comp.height = parseInt(height);
        }

        // 关闭属性面板
        function closePanel() {
            document.getElementById('propertyPanel').style.display = 'none';
            document.querySelectorAll('.component').forEach(comp => {
                comp.classList.remove('selected');
            });
            selectedComponent = null;
        }

        // 重置布局
        function resetLayout() {
            initCanvas();
            closePanel();
        }

        // 导出为图片
        function exportAsImage() {
            // 使用html2canvas库（需要引入）
            alert('请使用浏览器的截图功能，或者右键选择"打印"然后保存为PDF');
        }

        // 打印页面
        function printPage() {
            window.print();
        }

        // 显示/隐藏连接线
        function addArrows() {
            showArrows = !showArrows;
            // 这里可以添加SVG箭头的显示/隐藏逻辑
            alert(showArrows ? '连接线已显示' : '连接线已隐藏');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCanvas();
        });

        // 点击空白区域取消选择
        document.getElementById('canvas').addEventListener('click', function(e) {
            if (e.target.id === 'canvas') {
                closePanel();
            }
        });
    </script>
</body>
</html>
