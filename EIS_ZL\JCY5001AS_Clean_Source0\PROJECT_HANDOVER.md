# JCY5001A 电池测试系统 - 项目移交文档

## 📋 **项目基本信息**

### **项目概述**
- **项目名称**: JCY5001A 电池交流阻抗测试系统
- **项目类型**: 电池管理系统(BMS)上位机软件
- **开发语言**: Python 3.10 + PyQt5
- **当前版本**: V0.80.10 (stable-v0.80.10分支)
- **开发者**: <PERSON> (张海)
- **联系方式**: <EMAIL>

### **项目功能**
- **电池检测**: 自动检测电池连接状态
- **阻抗测试**: 多频点交流阻抗测试(0.01Hz-7.8kHz)
- **参数计算**: Rs、Rct、Rsei、W阻抗等参数分析
- **数据分析**: EIS分析、离群检测、统计分析
- **报告生成**: Excel导出、标签打印、数据可视化
- **多通道支持**: 8通道并行测试

## 🏗️ **系统架构**

### **技术栈**
```
前端界面: PyQt5
后端逻辑: Python 3.10
数据库: SQLite3
通信协议: Modbus RTU (串口)
数据分析: NumPy, SciPy, Matplotlib
报表生成: XlsxWriter
打包工具: Nuitka
```

### **核心模块**
```
├── backend/                   # 后端业务逻辑
│   ├── communication_manager.py      # 设备通信
│   ├── test_executor.py             # 测试执行
│   ├── eis_analyzer.py              # EIS分析
│   ├── impedance_data_manager.py    # 数据管理
│   └── test_result_manager.py       # 结果处理
├── ui/                        # 用户界面
│   ├── main_window.py               # 主窗口
│   ├── components/                  # UI组件
│   └── dialogs/                     # 对话框
├── utils/                     # 工具类
│   ├── config_manager.py            # 配置管理
│   ├── logger_helper.py             # 日志工具
│   └── license_manager.py           # 授权管理
└── config/                    # 配置文件
    └── app_config.json              # 应用配置
```

## 🔧 **开发环境**

### **系统要求**
- **操作系统**: Windows 10/11 (64位)
- **Python版本**: 3.10+
- **内存**: 4GB以上
- **硬盘**: 2GB可用空间

### **开发工具**
- **IDE**: VS Code / PyCharm
- **版本控制**: Git
- **包管理**: pip
- **构建工具**: Nuitka

### **依赖安装**
```bash
# 克隆项目
git clone https://github.com/JACKCHENPANG/JCY5001A.git
cd JCY5001A

# 安装依赖
pip install -r requirements.txt

# 运行程序
python main.py
```

## 📊 **数据库设计**

### **主要数据表**
```sql
-- 测试结果表
CREATE TABLE test_results (
    id INTEGER PRIMARY KEY,
    channel_number INTEGER,
    battery_code TEXT,
    test_start_time TEXT,
    voltage REAL,
    rs_value REAL,
    rct_value REAL,
    rsei_value REAL,
    w_impedance REAL,
    is_pass BOOLEAN,
    fail_reason TEXT,
    frequency_data TEXT,
    batch_id INTEGER
);

-- 基准数据表
CREATE TABLE baseline_data (
    id INTEGER PRIMARY KEY,
    channel_number INTEGER,
    frequency REAL,
    impedance_real REAL,
    impedance_imag REAL,
    created_at TEXT
);
```

## 🔌 **设备通信**

### **通信协议**
- **协议**: Modbus RTU
- **接口**: RS485/串口
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: None

### **主要指令**
```python
# 设置频率
"4100H {frequency}"

# 启动测试
"4300H"

# 读取阻抗实部
"4400H"

# 读取阻抗虚部
"4500H"

# 读取设备状态
"4600H"
```

## 🧪 **测试模式**

### **研究模式**
- **频率范围**: 0.01Hz - 7.8kHz
- **频点数量**: 20个对数分布
- **测试时间**: 约15-20分钟
- **应用场景**: 科研分析、详细测试

### **生产模式**
- **频率范围**: 1Hz - 1kHz
- **频点数量**: 5-10个
- **测试时间**: 约2-5分钟
- **应用场景**: 生产线质检

## 📈 **算法说明**

### **EIS分析算法**
```python
# Rs计算：虚部过零点实部值
Rs = real_part_at_zero_crossing

# Rct计算：低频实部 - Rs
Rct = low_freq_real - Rs

# W阻抗：45度线斜率
W = slope_of_45_degree_line
```

### **数据质量检查**
- **电压范围**: 2.0V - 5.0V
- **阻抗范围**: 0.1mΩ - 100mΩ
- **数据连续性**: 检查异常跳变
- **重复性**: 变异系数<20%

## 🚀 **部署指南**

### **构建步骤**
```bash
# 1. 清理代码
python cleanup_codebase.py --execute

# 2. 运行测试
python -m pytest tests/

# 3. 构建可执行文件
python build_nuitka_v082.py

# 4. 打包分发
# 输出目录: dist/JCY5001A_V0.80.06/
```

### **配置文件**
```json
{
  "device": {
    "port": "COM3",
    "baudrate": 115200,
    "timeout": 5.0
  },
  "test_params": {
    "test_mode": "simultaneous",
    "gain": "auto",
    "average_times": 1
  },
  "frequency": {
    "mode": "multi",
    "list": [1007.083, 625.612, ...]
  }
}
```

## 🔒 **授权管理**

### **授权机制**
- **硬件绑定**: CPU序列号 + 主板序列号
- **时间限制**: 试用期30天
- **功能限制**: 通道数量、测试次数
- **管理员密码**: JCY5001-ADMIN (Ctrl+U)

### **授权文件**
```
data/license.dat - 授权数据文件
utils/license_manager.py - 授权管理模块
```

## 📝 **维护指南**

### **日常维护**
1. **数据库清理**: 定期清理历史数据
2. **日志管理**: 控制日志文件大小
3. **配置备份**: 定期备份配置文件
4. **性能监控**: 监控内存和CPU使用

### **故障排除**
```python
# 常见问题
1. 设备连接失败 -> 检查串口配置
2. 测试超时 -> 调整超时参数
3. 数据异常 -> 检查设备状态
4. 界面卡死 -> 重启应用程序
```

### **升级流程**
1. **备份数据**: 备份数据库和配置
2. **停止服务**: 关闭应用程序
3. **更新代码**: 替换程序文件
4. **迁移数据**: 运行数据迁移脚本
5. **验证功能**: 测试核心功能

## 📞 **技术支持**

### **联系方式**
- **开发者**: Jack Chen (张海)
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/JACKCHENPANG/JCY5001A.git

### **文档资源**
- **用户手册**: docs/USER_GUIDE.md
- **API文档**: docs/API_REFERENCE.md
- **更新日志**: docs/CHANGELOG.md
- **故障排除**: docs/TROUBLESHOOTING.md

### **技术交流**
- **问题反馈**: GitHub Issues
- **功能建议**: GitHub Discussions
- **代码贡献**: Pull Request

## ✅ **移交检查清单**

### **代码交付**
- [ ] 源代码完整性检查
- [ ] 依赖文件完整性检查
- [ ] 配置文件模板检查
- [ ] 构建脚本验证
- [ ] 测试用例执行

### **文档交付**
- [ ] 项目说明文档
- [ ] 技术架构文档
- [ ] API接口文档
- [ ] 部署运维文档
- [ ] 用户操作手册

### **环境交付**
- [ ] 开发环境搭建指南
- [ ] 测试环境配置
- [ ] 生产环境部署
- [ ] 数据库初始化
- [ ] 授权配置说明

### **知识转移**
- [ ] 系统架构讲解
- [ ] 核心算法说明
- [ ] 关键模块演示
- [ ] 故障处理培训
- [ ] 维护操作培训

---

**移交完成日期**: ___________  
**移交人签字**: ___________  
**接收人签字**: ___________
