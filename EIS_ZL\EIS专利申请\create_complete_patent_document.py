#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的专利申请文档（含所有附图）
将专利申请技术文档与所有附图整合

Author: AI Assistant
Date: 2025-08-09
"""

import os
from docx import Document
from docx.shared import Inches, Cm
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

class CompletePatentDocumentCreator:
    """完整专利文档创建器"""
    
    def __init__(self, source_docx, figures_dir):
        """
        初始化创建器
        
        Args:
            source_docx: 源DOCX文件路径
            figures_dir: 图片文件夹路径
        """
        self.source_docx = source_docx
        self.figures_dir = figures_dir
        self.doc = Document(source_docx)
        
        # 图片文件映射（按顺序）
        self.figure_files = [
            ("图1", "图1_系统整体架构图.png", "基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图"),
            ("图2", "图2_DNB1101BB芯片连接电路图.png", "DNB1101BB芯片引脚连接电路图及外围器件配置图"),
            ("图3", "图3_外部电流源电路详细图.png", "外部电流源电路详细图（含MOSFET驱动电路）"),
            ("图4", "图4_EIS测试完整流程图.png", "电化学阻抗谱(EIS)测试完整流程图"),
            ("图5", "图5_典型电池奈奎斯特曲线示例图.png", "典型电池奈奎斯特曲线示例图（含参数标注）"),
            ("图6", "图6_多维参数提取算法流程图.png", "多维参数提取算法流程图"),
            ("图7", "图7_九档智能分组决策流程图.png", "基于Rs和Rp的九档智能分组决策流程图"),
            ("图8", "图8_Modbus RTU通信协议时序图.png", "Modbus RTU通信协议时序图"),
            ("图9", "图9_频率扫描序列设置图.png", "频率扫描序列设置图"),
            ("图10", "图10_增益自适应调节流程图.png", "增益自适应调节流程图")
        ]
    
    def create_complete_document(self):
        """创建完整的专利文档"""
        print("开始创建完整的专利申请文档...")
        
        # 在说明书附图说明部分插入所有图片
        self._insert_all_figures()
        
        # 保存文档
        output_path = "DNB1101BB发明专利申请完整文档_含全部附图.docx"
        self.doc.save(output_path)
        print(f"完整文档已保存为：{output_path}")
        
        return output_path
    
    def _insert_all_figures(self):
        """插入所有图片"""
        print("正在插入所有附图...")
        
        # 找到说明书附图说明部分
        attachment_section_found = False
        insert_position = len(self.doc.paragraphs)  # 默认在文档末尾
        
        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()
            if "说明书附图说明" in text or "附图说明" in text:
                attachment_section_found = True
                insert_position = i + 1
                print(f"找到附图说明部分，位置：{i}")
                break
        
        if not attachment_section_found:
            # 如果没找到，在文档末尾添加附图说明部分
            print("未找到附图说明部分，在文档末尾添加...")
            self._add_figure_section_header()
        
        # 插入所有图片
        for fig_key, fig_file, fig_caption in self.figure_files:
            self._insert_single_figure(fig_key, fig_file, fig_caption)
    
    def _add_figure_section_header(self):
        """添加附图说明部分标题"""
        # 添加分页符
        self.doc.add_page_break()
        
        # 添加标题
        header = self.doc.add_heading("说明书附图说明", level=2)
        header.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 添加说明文字
        intro_text = """本发明专利申请包含以下10个附图，详细展示了基于DNB1101BB芯片的电池电化学阻抗谱测试设备的技术方案："""
        intro_para = self.doc.add_paragraph(intro_text)
        intro_para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
        # 添加空行
        self.doc.add_paragraph()
    
    def _insert_single_figure(self, fig_key, fig_file, fig_caption):
        """插入单个图片"""
        try:
            # 图片文件路径
            figure_path = os.path.join(self.figures_dir, fig_file)
            
            if not os.path.exists(figure_path):
                print(f"警告：图片文件不存在 - {figure_path}")
                return
            
            # 添加图片标题
            title_para = self.doc.add_paragraph()
            title_run = title_para.add_run(f"{fig_key}：{fig_caption}")
            title_run.bold = True
            title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            
            # 添加空行
            self.doc.add_paragraph()
            
            # 插入图片
            img_para = self.doc.add_paragraph()
            img_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            
            # 根据图片类型设置合适的宽度
            width = self._get_figure_width(fig_key)
            img_run = img_para.add_run()
            img_run.add_picture(figure_path, width=width)
            
            # 添加图片说明
            caption_para = self.doc.add_paragraph()
            caption_run = caption_para.add_run(f"（{fig_key}：{fig_caption}）")
            caption_run.italic = True
            caption_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            
            # 添加分隔空行
            self.doc.add_paragraph()
            self.doc.add_paragraph()
            
            print(f"成功插入{fig_key}：{fig_caption}")
            
        except Exception as e:
            print(f"插入{fig_key}时出错：{e}")
    
    def _get_figure_width(self, fig_key):
        """根据图片类型返回合适的宽度"""
        width_settings = {
            "图1": Inches(6.5),   # 系统架构图 - 最大
            "图2": Inches(6.5),   # 芯片电路图 - 最大
            "图3": Inches(5.5),   # 电流源电路 - 较大
            "图4": Inches(4.5),   # 流程图 - 中等
            "图5": Inches(5.5),   # 奈奎斯特曲线 - 较大
            "图6": Inches(6),     # 算法流程 - 较大
            "图7": Inches(6.5),   # 分组算法 - 最大
            "图8": Inches(5.5),   # 通信时序 - 较大
            "图9": Inches(5.5),   # 频率扫描 - 较大
            "图10": Inches(5),    # 增益控制 - 中等
        }
        
        return width_settings.get(fig_key, Inches(5.5))  # 默认5.5英寸
    
    def add_figure_list(self):
        """添加附图清单"""
        # 在附图说明前添加附图清单
        list_para = self.doc.add_paragraph()
        list_run = list_para.add_run("附图清单：")
        list_run.bold = True
        
        for fig_key, _, fig_caption in self.figure_files:
            item_para = self.doc.add_paragraph()
            item_para.style = 'List Bullet'
            item_para.add_run(f"{fig_key}：{fig_caption}")
        
        # 添加空行
        self.doc.add_paragraph()

def main():
    """主函数"""
    print("完整专利申请文档创建工具")
    print("=" * 60)
    
    # 文件路径
    source_docx = "DNB1101BB发明专利申请完整技术文档.docx"
    figures_dir = "patent_figures"
    
    # 检查文件是否存在
    if not os.path.exists(source_docx):
        print(f"错误：找不到源DOCX文件 - {source_docx}")
        return
    
    if not os.path.exists(figures_dir):
        print(f"错误：找不到图片文件夹 - {figures_dir}")
        return
    
    # 检查图片文件
    figure_files = [
        "图1_系统整体架构图.png",
        "图2_DNB1101BB芯片连接电路图.png", 
        "图3_外部电流源电路详细图.png",
        "图4_EIS测试完整流程图.png",
        "图5_典型电池奈奎斯特曲线示例图.png",
        "图6_多维参数提取算法流程图.png",
        "图7_九档智能分组决策流程图.png",
        "图8_Modbus RTU通信协议时序图.png",
        "图9_频率扫描序列设置图.png",
        "图10_增益自适应调节流程图.png"
    ]
    
    missing_files = []
    for fig_file in figure_files:
        if not os.path.exists(os.path.join(figures_dir, fig_file)):
            missing_files.append(fig_file)
    
    if missing_files:
        print("警告：以下图片文件缺失：")
        for file in missing_files:
            print(f"  - {file}")
        print()
    
    # 创建完整文档
    creator = CompletePatentDocumentCreator(source_docx, figures_dir)
    
    # 添加附图清单
    creator.add_figure_list()
    
    # 创建完整文档
    output_file = creator.create_complete_document()
    
    print("\n" + "=" * 60)
    print("完整专利申请文档创建完成！")
    print(f"输出文件：{output_file}")
    print("\n文档包含：")
    print("✓ 权利要求书")
    print("✓ 说明书")
    print("✓ 说明书摘要") 
    print("✓ 说明书附图说明")
    print("✓ 核心软件算法源代码")
    print("✓ 10张高质量专利附图")
    print("\n该文档可直接用于专利申请提交！")

if __name__ == "__main__":
    main()
