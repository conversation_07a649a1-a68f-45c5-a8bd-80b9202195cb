{"template_id": "user_20250621_121218", "name": "我的我的我的标准模板", "description": "基于 基于 基于 50x30mm标准标签模板，包含完整的测试信息", "size": "50x30mm", "elements": [{"element_id": "title", "element_type": "text", "x": 12, "y": 8, "width": 200, "height": 24, "content": "JCY5001AS 电池测试", "font_family": "微软雅黑", "font_size": 20, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "channel_voltage", "element_type": "text", "x": 0, "y": 67, "width": 200, "height": 22, "content": "通道: CH{channel_number}    电压:{voltage}V", "font_family": "微软雅黑", "font_size": 16, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "rs_value", "element_type": "text", "x": 6, "y": 99, "width": 200, "height": 22, "content": "Rs:{rs_value}mΩ", "font_family": "微软雅黑", "font_size": 16, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "rct_value", "element_type": "text", "x": 11, "y": 127, "width": 200, "height": 22, "content": "Rct:{rct_value}mΩ", "font_family": "微软雅黑", "font_size": 16, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "test_status", "element_type": "text", "x": 14, "y": 163, "width": 150, "height": 24, "content": "状态: {is_pass}", "font_family": "微软雅黑", "font_size": 20, "font_style": "normal", "text_color": "#000000", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "qr_code", "element_type": "qr_code", "x": 261, "y": 100, "width": 100, "height": 100, "content": "{battery_code}", "font_family": "微软雅黑", "font_size": 14, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "dropped_timestamp_114642786246", "element_type": "text", "x": 20, "y": 208, "width": 100, "height": 20, "content": "{timestamp}", "font_family": "微软雅黑", "font_size": 14, "font_style": "normal", "text_color": "#000000", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}], "version": "1.0", "created_time": "2025-06-21T12:12:18.239766", "modified_time": "2025-06-21T12:12:18.239766", "author": "用户"}