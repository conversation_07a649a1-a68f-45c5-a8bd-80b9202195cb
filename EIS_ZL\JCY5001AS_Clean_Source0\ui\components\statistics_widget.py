# -*- coding: utf-8 -*-
"""
统计区域组件
显示总测试数、合格数、不合格数、良率和Rs-Rct档位分布图

Author: Jack
Date: 2025-01-27
"""

import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QGroupBox, QGridLayout, QFrame, QTableWidget,
    QTableWidgetItem, QHeaderView, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from typing import Optional
import logging

logger = logging.getLogger(__name__)

from utils.config_manager import ConfigManager
from utils.statistics_counter_manager import StatisticsCounterManager


class StatisticsWidget(QWidget):
    """统计区域组件"""

    # 信号定义
    statistics_updated = pyqtSignal(dict)  # 统计数据更新信号

    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化统计组件

        Args:
            config_manager: 配置管理器
            parent: 父窗口
        """
        super().__init__(parent)

        self.config_manager = config_manager

        # 🔧 新增：使用独立的统计计数器管理器
        self.counter_manager = StatisticsCounterManager(config_manager)

        # 统计数据
        self.total_count = 0
        self.pass_count = 0
        self.fail_count = 0
        self.grade_distribution = {}  # Rs-Rct档位分布

        # 初始化界面
        self._init_ui()
        self._init_grade_table()

        # 🔧 修复：启动时加载统计计数器数据
        self._load_counter_statistics()

        logger.debug("统计区域组件初始化完成")

    def _init_ui(self):
        """初始化用户界面"""
        # 创建主布局 - 利用增加的显示空间
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)  # 增加边距以利用更多空间
        main_layout.setSpacing(6)  # 增加间距以改善视觉效果

        # 创建分组框 - 补全"测试统计"字体显示
        group_box = QGroupBox("测试统计")
        group_box.setObjectName("statisticsGroup")
        # 🔧 确保标题字体完整显示，不被截断
        group_box.setMinimumWidth(800)  # 增加最小宽度，确保标题完整显示
        group_box.setMaximumHeight(350)  # 控制高度，避免过度拉伸
        main_layout.addWidget(group_box)

        # 创建内容布局 - 内容向左移动，填充空白区域
        content_layout = QHBoxLayout(group_box)
        content_layout.setContentsMargins(5, 5, 5, 10)   # 🔧 减少左边距，让内容向左移动
        content_layout.setSpacing(8)  # 🔧 减少间距，让内容更紧凑向左
        content_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)  # 🔧 内容左对齐并顶部对齐

        # 左侧：统计数据 - 紧凑显示
        stats_widget = self._create_statistics_section()

        # 🔧 强制显示统计区域，确保4行都可见
        stats_widget.setVisible(True)
        stats_widget.show()

        content_layout.addWidget(stats_widget, 1)  # 🔧 统计数据占1份空间，更紧凑

        # 右侧：档位分布图 - 向左移动，填充空白区域
        grade_widget = self._create_grade_distribution_section()
        content_layout.addWidget(grade_widget, 2)  # 🔧 档位分布图占2份空间，向左移动

        # 🔧 添加右侧弹性空间，将内容推向左侧
        content_layout.addStretch(1)  # 右侧弹性空间，将所有内容推向左侧

        # 设置组件样式
        self._apply_styles()

    def _create_statistics_section(self):
        """创建统计数据区域"""
        stats_widget = QWidget()

        # 🔧 让统计区域内容紧贴顶部，自然高度
        stats_widget.setMinimumHeight(150)  # 🔧 减少最小高度，让内容紧凑
        # 移除固定高度，让内容自然排列到顶部

        stats_layout = QGridLayout(stats_widget)
        stats_layout.setSpacing(4)  # 减少间距，让统计框更靠近标签
        stats_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 🔧 统计框对齐到顶部
        stats_layout.setContentsMargins(0, 0, 0, 0)  # 🔧 移除所有边距，紧贴顶部

        # 总测试数
        stats_layout.addWidget(QLabel("总测试数:"), 0, 0)
        self.total_count_label = QLabel("0")
        self.total_count_label.setObjectName("valueLabel")
        self.total_count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 显示优化：居中对齐
        stats_layout.addWidget(self.total_count_label, 0, 1)

        # 合格数
        stats_layout.addWidget(QLabel("合格数:"), 1, 0)
        self.pass_count_label = QLabel("0")
        self.pass_count_label.setObjectName("passLabel")
        self.pass_count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 显示优化：居中对齐
        stats_layout.addWidget(self.pass_count_label, 1, 1)

        # 不合格数
        stats_layout.addWidget(QLabel("不合格数:"), 2, 0)
        self.fail_count_label = QLabel("0")
        self.fail_count_label.setObjectName("failLabel")
        self.fail_count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 显示优化：居中对齐

        # 🔧 强制显示不合格数框
        self.fail_count_label.setVisible(True)
        self.fail_count_label.show()
        print(f"🔧 不合格数框创建: {self.fail_count_label.text()}")

        stats_layout.addWidget(self.fail_count_label, 2, 1)

        # 良率
        stats_layout.addWidget(QLabel("良率:"), 3, 0)
        self.yield_rate_label = QLabel("0.0%")
        self.yield_rate_label.setObjectName("yieldLabel")
        self.yield_rate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 显示优化：居中对齐

        # 🔧 强制显示良率框
        self.yield_rate_label.setVisible(True)
        self.yield_rate_label.show()
        print(f"🔧 良率框创建: {self.yield_rate_label.text()}")

        stats_layout.addWidget(self.yield_rate_label, 3, 1)

        # 设置列拉伸和间距 - 让统计框紧贴标签，不拉伸
        stats_layout.setColumnStretch(0, 0)  # 标签列固定宽度
        stats_layout.setColumnStretch(1, 0)  # 统计框列固定宽度，不拉伸
        stats_layout.setColumnStretch(2, 1)  # 添加第三列用于占据剩余空间
        stats_layout.setHorizontalSpacing(5)  # 标签和数值框之间的小间距
        stats_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距

        # 在每行添加一个空的拉伸列，让数值框紧跟标签
        for row in range(4):
            spacer = QWidget()
            stats_layout.addWidget(spacer, row, 2)  # 第三列添加空白占位符

        return stats_widget

    def _create_grade_distribution_section(self):
        """创建档位分布图区域"""
        grade_widget = QWidget()
        grade_layout = QVBoxLayout(grade_widget)
        grade_layout.setSpacing(6)  # 增加间距以利用更多空间
        grade_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 🔧 档位分布内容对齐到顶部
        grade_layout.setContentsMargins(0, 0, 0, 0)  # 🔧 移除所有边距，紧贴顶部

        # 档位范围信息区域（紧凑显示，为表格留出更多空间）
        range_container = self._create_grade_range_display()
        grade_layout.addWidget(range_container, 0)  # 固定高度，不占用额外空间

        # 档位分布表格（充分利用红色框区域的空间，确保Rs3行显示）
        self.grade_table = QTableWidget()
        self.grade_table.setObjectName("gradeTable")

        # 🔧 强制显示表格，确保可见性
        self.grade_table.setVisible(True)
        self.grade_table.show()
        self.grade_table.raise_()  # 提升到最前面

        # 🔧 调试信息
        print(f"表格创建完成: 行数={self.grade_table.rowCount()}, 列数={self.grade_table.columnCount()}")
        print(f"表格大小: {self.grade_table.size()}")
        print(f"表格可见性: {self.grade_table.isVisible()}")

        grade_layout.addWidget(self.grade_table, 1)  # 正常权重，显示3x3完整表格

        return grade_widget

    def _create_grade_range_display(self):
        """创建档位范围显示区域（扩展到右边空白位置，补全字体显示）"""
        container = QWidget()
        container_layout = QGridLayout(container)  # 🔧 改回网格布局，更好控制扩展
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(8)  # 🔧 适当间距
        container_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)

        # Rs档位范围显示（扩展到右边空白位置）
        rs_title_label = QLabel("Rs档位范围:")
        rs_title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 🔧 标题左对齐
        rs_title_label.setMinimumWidth(90)  # 🔧 增加标题宽度，确保字体完整显示
        rs_title_label.setMaximumWidth(90)
        # 🔧 设置字体，确保文字完整显示
        rs_title_label.setStyleSheet("""
            QLabel {
                font-size: 11pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        container_layout.addWidget(rs_title_label, 0, 0)

        self.rs_range_label = QLabel()
        self.rs_range_label.setObjectName("rangeValueLabel")
        self.rs_range_label.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 🔧 内容左对齐
        # 🔧 扩展到右边空白位置，确保内容完整显示
        self.rs_range_label.setMinimumWidth(500)  # 🔧 大幅增加宽度，扩展到右边空白位置
        self.rs_range_label.setMaximumWidth(800)  # 🔧 允许更大宽度
        container_layout.addWidget(self.rs_range_label, 0, 1)

        # Rct档位范围显示（扩展到右边空白位置）
        rct_title_label = QLabel("Rct档位范围:")
        rct_title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 🔧 标题左对齐
        rct_title_label.setMinimumWidth(90)  # 🔧 增加标题宽度，确保字体完整显示
        rct_title_label.setMaximumWidth(90)
        # 🔧 设置字体，确保文字完整显示
        rct_title_label.setStyleSheet("""
            QLabel {
                font-size: 11pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        container_layout.addWidget(rct_title_label, 1, 0)

        self.rct_range_label = QLabel()
        self.rct_range_label.setObjectName("rangeValueLabel")
        self.rct_range_label.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 🔧 内容左对齐
        # 🔧 扩展到右边空白位置，确保内容完整显示
        self.rct_range_label.setMinimumWidth(500)  # 🔧 大幅增加宽度，扩展到右边空白位置
        self.rct_range_label.setMaximumWidth(800)  # 🔧 允许更大宽度
        container_layout.addWidget(self.rct_range_label, 1, 1)

        # 🔧 设置列权重，让内容扩展到右边空白位置
        container_layout.setColumnStretch(0, 0)  # 标题列固定宽度
        container_layout.setColumnStretch(1, 1)  # 🔧 内容列拉伸，占据右边空白位置

        # 压缩档位范围显示高度
        container.setMaximumHeight(70)

        # 初始化档位范围显示
        self._update_grade_range_display()

        return container

    def _init_grade_table(self):
        """初始化档位分布表格"""
        try:
            # 获取档位设置 - 强制确保Rs也是3档，显示完整9宫格
            rs_grades = 3  # 强制设置Rs为3档，确保显示Rs1-Rs3完整行
            rct_grades = 3  # Rct固定3档

            # 设置表格大小
            self.grade_table.setRowCount(rs_grades)
            self.grade_table.setColumnCount(rct_grades)

            # 设置表头
            rs_headers = [f"Rs{i+1}" for i in range(rs_grades)]
            rct_headers = [f"Rct{i+1}" for i in range(rct_grades)]

            self.grade_table.setVerticalHeaderLabels(rs_headers)
            self.grade_table.setHorizontalHeaderLabels(rct_headers)

            # 初始化表格内容 - 添加测试数据验证3行3列显示
            test_data = [
                [114, 112, 90],   # Rs1行
                [96, 106, 101],   # Rs2行
                [88, 82, 81]      # Rs3行
            ]

            for row in range(rs_grades):
                for col in range(rct_grades):
                    # 使用测试数据而不是0，验证所有行都能显示
                    test_value = test_data[row][col] if row < len(test_data) and col < len(test_data[row]) else 0
                    item = QTableWidgetItem(str(test_value))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                    # 设置背景颜色
                    if row == 0:  # Rs1 - 绿色
                        item.setBackground(QColor("#d5f4e6"))
                    elif row == 1:  # Rs2 - 黄色
                        item.setBackground(QColor("#fef9e7"))
                    else:  # Rs3+ - 红色
                        item.setBackground(QColor("#fadbd8"))

                    self.grade_table.setItem(row, col, item)
                    print(f"🔧 设置表格项 [{row},{col}] = {test_value}")  # 调试信息

            print(f"🔧 表格初始化完成: {rs_grades}行 x {rct_grades}列")
            print(f"🔧 表格实际大小: {self.grade_table.rowCount()}行 x {self.grade_table.columnCount()}列")
            logger.info(f"✅ 档位表格初始化完成: {rs_grades}行 x {rct_grades}列，测试数据已填充")

            # 设置表格属性
            self.grade_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            self.grade_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            self.grade_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
            self.grade_table.setSelectionMode(QTableWidget.SelectionMode.NoSelection)

            # 🔧 设置表格行高，确保3x3表格完整显示
            self.grade_table.verticalHeader().setDefaultSectionSize(30)  # 🔧 增加行高到30px
            self.grade_table.horizontalHeader().setDefaultSectionSize(80)  # 🔧 增加列宽到80px
            self.grade_table.verticalHeader().setMinimumSectionSize(25)    # 🔧 最小行高25px
            self.grade_table.horizontalHeader().setMinimumSectionSize(25)  # 🔧 表头最小高度25px

            # 🔧 设置表格尺寸策略，确保表格完整显示
            self.grade_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            self.grade_table.setMinimumHeight(120)  # 🔧 最小高度：表头30px + 3行×30px = 120px
            self.grade_table.setMaximumHeight(150)  # 🔧 最大高度150px，确保有足够空间

            # 🔧 确保表格边框完整显示
            self.grade_table.setShowGrid(True)  # 🔧 显示网格线
            self.grade_table.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Plain)  # 🔧 设置边框样式

        except Exception as e:
            logger.error(f"初始化档位分布表格失败: {e}")

    def _get_grade_ranges(self):
        """获取当前档位范围配置（直接同步判定设置页面的计算结果）"""
        try:
            # 获取Rs档位配置
            rs_ranges = self._get_rs_grade_ranges_from_settings()

            # 获取Rct档位配置
            rct_ranges = self._get_rct_grade_ranges_from_settings()

            return rs_ranges, rct_ranges

        except Exception as e:
            logger.error(f"获取档位范围失败: {e}")
            return ["配置错误"], ["配置错误"]

    def _get_rs_grade_ranges_from_settings(self):
        """获取Rs档位范围配置（使用与判定设置页面完全相同的逻辑）"""
        try:
            # 获取Rs档位数量
            rs_grade_count = self.config_manager.get('grade_settings.rs_grade_count', 3)

            # 获取自动计算选项
            rs_auto_calc = self.config_manager.get('grade_settings.rs_auto_calc', True)

            # 移除1档特殊处理，统一显示具体数值范围

            if rs_auto_calc:
                # 自动计算模式：使用与判定设置页面相同的计算逻辑
                min_value = self.config_manager.get('grade_settings.rs_min', 0.5)
                max_value = self.config_manager.get('grade_settings.rs_max', 50.0)

                ranges = self._calculate_auto_ranges(min_value, max_value, rs_grade_count)
                display_text = self._format_ranges_text("Rs", ranges)
            else:
                # 手动模式：使用与判定设置页面相同的逻辑
                ranges = []

                if rs_grade_count >= 1:
                    grade1_max = self.config_manager.get('grade_settings.rs1_max', 17.0)
                    ranges.append((0.0, grade1_max))

                if rs_grade_count >= 2:
                    grade2_max = self.config_manager.get('grade_settings.rs2_max', 33.5)
                    ranges.append((grade1_max, grade2_max))

                if rs_grade_count >= 3:
                    grade3_max = self.config_manager.get('grade_settings.rs3_max', 50.0)
                    ranges.append((grade2_max, grade3_max))

                display_text = self._format_ranges_text("Rs", ranges)

            # 将多行文本转换为单行显示格式
            return self._convert_to_single_line_format(display_text)

        except Exception as e:
            logger.error(f"获取Rs档位范围失败: {e}")
            return ["Rs配置错误"]

    def _get_rct_grade_ranges_from_settings(self):
        """获取Rct档位范围配置（使用与判定设置页面完全相同的逻辑，固定3档）"""
        try:
            # 获取自动计算选项
            rct_auto_calc = self.config_manager.get('grade_settings.rct_auto_calc', True)

            if rct_auto_calc:
                # 自动计算模式：使用与判定设置页面相同的计算逻辑
                min_value = self.config_manager.get('grade_settings.rct_min', 5.0)
                max_value = self.config_manager.get('grade_settings.rct_max', 100.0)

                ranges = self._calculate_auto_ranges(min_value, max_value, 3)
                display_text = self._format_ranges_text("Rct", ranges)
            else:
                # 手动模式：使用与判定设置页面相同的逻辑
                grade1_max = self.config_manager.get('grade_settings.rct1_max', 35.0)
                grade2_max = self.config_manager.get('grade_settings.rct2_max', 70.0)
                grade3_max = self.config_manager.get('grade_settings.rct3_max', 100.0)

                ranges = [
                    (0.0, grade1_max),
                    (grade1_max, grade2_max),
                    (grade2_max, grade3_max)
                ]
                display_text = self._format_ranges_text("Rct", ranges)

            # 将多行文本转换为单行显示格式
            return self._convert_to_single_line_format(display_text)

        except Exception as e:
            logger.error(f"获取Rct档位范围失败: {e}")
            return ["Rct配置错误"]

    def _calculate_auto_ranges(self, min_value: float, max_value: float, grade_count: int) -> list:
        """自动计算档位范围（与判定设置页面完全相同的算法）"""
        try:
            ranges = []
            if grade_count <= 0:
                return ranges

            step = (max_value - min_value) / grade_count

            for i in range(grade_count):
                range_min = min_value + i * step
                range_max = min_value + (i + 1) * step
                ranges.append((range_min, range_max))

            return ranges

        except Exception as e:
            logger.error(f"自动计算档位范围失败: {e}")
            return []

    def _format_ranges_text(self, prefix: str, ranges: list) -> str:
        """格式化档位范围文本（与判定设置页面完全相同的格式）"""
        try:
            if not ranges:
                return f"{prefix}档位范围计算失败"

            lines = []
            for i, (min_val, max_val) in enumerate(ranges, 1):
                lines.append(f"{prefix}{i}档: {min_val:.3f} - {max_val:.3f} mΩ")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"格式化档位范围文本失败: {e}")
            return f"{prefix}档位范围显示错误"

    def _convert_to_single_line_format(self, multi_line_text: str) -> list:
        """将多行文本转换为单行显示格式（显示完整范围信息）"""
        try:
            lines = multi_line_text.strip().split('\n')
            single_line_ranges = []

            for line in lines:
                if ':' in line:
                    # 提取档位信息，转换格式
                    parts = line.split(':')
                    if len(parts) >= 2:
                        grade_part = parts[0].strip()  # 如 "Rs1档"
                        range_part = parts[1].strip()  # 如 "0.500 - 17.167 mΩ"

                        # 解析范围值
                        if ' - ' in range_part:
                            range_values = range_part.replace(' mΩ', '').split(' - ')
                            if len(range_values) == 2:
                                min_val = float(range_values[0])
                                max_val = float(range_values[1])

                                # 提取档位数字
                                grade_num = grade_part[-2]  # 提取档位数字

                                # 转换为主界面完整范围显示格式（显示上下限，保留3位小数）
                                single_line_ranges.append(f"{grade_num}档({min_val:.3f}-{max_val:.3f}mΩ)")

            return single_line_ranges if single_line_ranges else [multi_line_text]

        except Exception as e:
            logger.error(f"转换显示格式失败: {e}")
            return [multi_line_text]

    def _update_grade_range_display(self):
        """更新档位范围显示"""
        try:
            rs_ranges, rct_ranges = self._get_grade_ranges()

            # 更新Rs档位范围显示（简洁格式）
            rs_text = " | ".join(rs_ranges)
            self.rs_range_label.setText(rs_text)

            # 更新Rct档位范围显示（简洁格式）
            rct_text = " | ".join(rct_ranges)
            self.rct_range_label.setText(rct_text)

        except Exception as e:
            logger.error(f"更新档位范围显示失败: {e}")

    def _load_counter_statistics(self):
        """🔧 修复：加载统计计数器数据"""
        try:
            logger.info("📊 加载统计计数器数据...")

            # 从计数器管理器获取统计数据
            stats = self.counter_manager.get_statistics()

            # 更新统计数据
            self.total_count = stats['total_count']
            self.pass_count = stats['pass_count']
            self.fail_count = stats['fail_count']
            self.grade_distribution = stats['grade_distribution']

            # 更新档位分布表格
            self._update_grade_table_from_distribution()

            # 更新UI显示
            self._update_statistics_display()

            logger.info(f"✅ 统计计数器数据加载完成: 总数={self.total_count}, 合格={self.pass_count}, 不合格={self.fail_count}")

        except Exception as e:
            logger.error(f"❌ 加载统计计数器数据失败: {e}")
            # 如果加载失败，保持默认值0
            self.total_count = 0
            self.pass_count = 0
            self.fail_count = 0
            self.grade_distribution = {}
            self._update_statistics_display()

    def _update_grade_table_from_distribution(self):
        """从档位分布数据更新表格"""
        try:
            # 重置表格
            for row in range(self.grade_table.rowCount()):
                for col in range(self.grade_table.columnCount()):
                    item = self.grade_table.item(row, col)
                    if item:
                        item.setText("0")

            # 更新档位分布表格
            for grade_key, count in self.grade_distribution.items():
                if grade_key.startswith('Rs') and '-Rct' in grade_key:
                    # 解析档位信息，如 "Rs1-Rct2"
                    parts = grade_key.split('-')
                    if len(parts) == 2:
                        rs_part = parts[0].replace('Rs', '')
                        rct_part = parts[1].replace('Rct', '')

                        try:
                            rs_grade = int(rs_part)
                            rct_grade = int(rct_part)

                            if 1 <= rs_grade <= 3 and 1 <= rct_grade <= 3:
                                row_index = rs_grade - 1
                                col_index = rct_grade - 1

                                if (row_index < self.grade_table.rowCount() and
                                    col_index < self.grade_table.columnCount()):
                                    item = self.grade_table.item(row_index, col_index)
                                    if item:
                                        item.setText(str(count))
                        except ValueError:
                            continue

            logger.debug(f"档位分布表格更新完成: {len(self.grade_distribution)} 个档位组合")

        except Exception as e:
            logger.error(f"更新档位分布表格失败: {e}")

    def _update_statistics_display(self):
        """更新统计数据显示"""
        try:
            # 更新统计标签
            self.total_count_label.setText(str(self.total_count))
            self.pass_count_label.setText(str(self.pass_count))
            self.fail_count_label.setText(str(self.fail_count))

            # 计算良率
            if self.total_count > 0:
                yield_rate = (self.pass_count / self.total_count) * 100
                self.yield_rate_label.setText(f"{yield_rate:.1f}%")
            else:
                self.yield_rate_label.setText("0.0%")

            logger.debug(f"统计显示更新完成: 总数={self.total_count}, 良率={self.yield_rate_label.text()}")

        except Exception as e:
            logger.error(f"更新统计显示失败: {e}")

    def refresh_statistics(self):
        """🔧 公共方法：刷新统计数据（供外部调用）"""
        try:
            logger.debug("🔄 刷新统计数据...")
            self._load_historical_statistics()

        except Exception as e:
            logger.error(f"刷新统计数据失败: {e}")

    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QGroupBox#statisticsGroup {
                font-weight: bold;
                border: 4px solid #27ae60 !important;  /* 🔧 进一步增加边框宽度到4px，确保绿色线框完整 */
                border-radius: 5px;
                margin: 2px;        /* 🔧 添加外边距，确保边框完整显示 */
                padding: 8px;       /* 🔧 添加内边距，确保内容不贴边框 */
                background-color: white;
                min-height: 300px;  /* 🔧 减少最小高度，让内容更紧凑 */
                min-width: 800px;   /* 🔧 确保宽度足够，标题完整显示 */
                /* 🔧 确保边框完整显示 */
                border-style: solid !important;
                border-color: #27ae60 !important;
                /* 🔧 确保边框不被裁剪 */
                box-sizing: border-box;
                outline: none;
            }

            QGroupBox#statisticsGroup::title {
                subcontrol-origin: margin;
                left: 10px;         /* 🔧 调整左边距，确保标题完整显示 */
                padding: 2px 8px 2px 8px;  /* 🔧 增加内边距，确保"测试统计"文字完整显示 */
                color: #27ae60;
                font-size: 14pt;    /* 🔧 增大字体到14pt，确保"测试统计"清晰可见 */
                font-weight: bold;
                /* 🔧 确保标题完整显示，不被截断 */
                background-color: white;
                min-width: 80px;    /* 🔧 确保标题宽度足够显示"测试统计" */
                max-width: 120px;   /* 🔧 限制最大宽度，避免过度拉伸 */
                /* 🔧 确保文字不被截断 */
                white-space: nowrap;
                overflow: visible;
                text-overflow: clip;
            }

            QLabel {
                font-size: 14pt;    /* 字体优化：标签文字进一步增加到14pt */
                font-weight: bold;
                color: #2c3e50;
            }

            QLabel#valueLabel {
                font-size: 16pt;    /* 字体优化：进一步增大到16pt */
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px 6px !important;   /* 🔧 减少内边距，紧凑显示 */
                min-width: 50px !important;    /* 🔧 统一宽度：32px → 50px，与良率框一致 */
                max-width: 50px !important;    /* 🔧 固定宽度，去除多余空间 */
                min-height: 32px !important;   /* 🔧 统一高度32px */
                max-height: 32px !important;   /* 🔧 强制统一高度 */
                text-align: center;
                margin: 0px !important;  /* 移除外边距 */
            }

            QLabel#passLabel {
                font-size: 16pt;    /* 字体优化：进一步增大到16pt */
                font-weight: bold;
                color: #27ae60;
                background-color: #d5f4e6;
                border: 1px solid #27ae60;
                border-radius: 4px;
                padding: 4px 6px !important;   /* 🔧 减少内边距，紧凑显示 */
                min-width: 50px !important;    /* 🔧 统一宽度：32px → 50px，与良率框一致 */
                max-width: 50px !important;    /* 🔧 固定宽度，去除多余空间 */
                min-height: 32px !important;   /* 🔧 统一高度32px */
                max-height: 32px !important;   /* 🔧 强制统一高度 */
                text-align: center;
                margin: 0px !important;  /* 移除外边距 */
            }

            QLabel#failLabel {
                font-size: 14pt;    /* 字体优化：增大字体从12pt到14pt（+2pt） */
                font-weight: bold;  /* 显示优化：加粗显示 */
                color: #ffffff !important;      /* 🔧 白色文字，确保可见 */
                background-color: #ff0000 !important;  /* 🔧 强制红色背景，确保可见 */
                border: 3px solid #000000 !important;  /* 🔧 黑色边框，确保可见 */
                border-radius: 2px;  /* 进一步压缩优化：从3px减少到2px */
                padding: 4px 6px !important;   /* 🔧 减少内边距，紧凑显示 */
                min-width: 50px !important;    /* 🔧 统一宽度：32px → 50px，与良率框一致 */
                max-width: 50px !important;    /* 🔧 固定宽度，去除多余空间 */
                min-height: 32px !important;   /* 🔧 统一高度32px */
                max-height: 32px !important;   /* 🔧 强制统一高度 */
                text-align: center; /* 显示优化：居中对齐显示 */
                margin: 0px !important;  /* 移除外边距 */
            }

            QLabel#yieldLabel {
                font-size: 14pt;    /* 字体优化：增大字体从12pt到14pt（+2pt） */
                font-weight: bold;  /* 显示优化：加粗显示 */
                color: #ffffff !important;      /* 🔧 白色文字，确保可见 */
                background-color: #0000ff !important;  /* 🔧 强制蓝色背景，确保可见 */
                border: 3px solid #000000 !important;  /* 🔧 黑色边框，确保可见 */
                border-radius: 2px;  /* 进一步压缩优化：从3px减少到2px */
                padding: 4px 6px !important;   /* 🔧 减少内边距，紧凑显示 */
                min-width: 50px !important;    /* 🔧 适当加长：32px → 50px，确保"1.0%"完整显示 */
                max-width: 50px !important;    /* 🔧 固定宽度，去除多余空间 */
                min-height: 32px !important;   /* 🔧 统一高度32px */
                max-height: 32px !important;   /* 🔧 强制统一高度 */
                text-align: center; /* 显示优化：居中对齐显示 */
                margin: 0px !important;  /* 移除外边距 */
            }

            QLabel#subtitleLabel {
                font-size: 10pt;
                font-weight: bold;
                color: #34495e;
                margin-bottom: 2px;  /* 压缩优化：从5px减少到2px */
            }

            QTableWidget#gradeTable {
                gridline-color: #34495e !important;  /* 🔧 深色网格线，确保可见 */
                background-color: white;  /* 🔧 白色背景，更清晰 */
                border: 2px solid #34495e !important;  /* 🔧 完整边框，确保四周都有边框 */
                border-radius: 4px;
                font-size: 14pt;     /* 表格文字大小 */
                font-weight: bold;
                min-height: 120px !important;   /* 🔧 增加表格高度，确保3x3表格完整显示 */
                max-height: 150px !important;   /* 🔧 适当增加最大高度 */
                selection-background-color: #3498db;  /* 选中背景色 */
            }

            QTableWidget#gradeTable::item {
                padding: 6px;        /* 内边距 */
                border: 1px solid #34495e !important;  /* 🔧 强制单元格边框，确保网格完整 */
                font-size: 14pt;     /* 表格项文字大小 */
                font-weight: bold;
                text-align: center;
                min-height: 25px !important;    /* 🔧 增加行高，确保内容完整显示 */
                max-height: 35px !important;    /* 🔧 适当的最大行高 */
            }

            QHeaderView::section {
                background-color: #34495e !important;  /* 🔧 表头背景色 */
                color: white !important;  /* 🔧 白色文字 */
                padding: 6px;        /* 表头内边距 */
                border: 1px solid #2c3e50 !important;  /* 🔧 表头边框，确保完整显示 */
                font-weight: bold;
                font-size: 12pt;     /* 表头文字大小 */
                min-height: 25px !important;    /* 🔧 表头高度 */
                max-height: 30px !important;    /* 🔧 表头最大高度 */
            }

            QLabel#rangeValueLabel {
                font-size: 10pt;     /* 🔧 增大字体，确保文字清晰完整显示 */
                font-weight: bold;
                color: #2c3e50;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 4px 8px;    /* 🔧 增加内边距，确保文字完整显示 */
                min-width: 500px;    /* 🔧 大幅增加最小宽度，扩展到右边空白位置 */
                max-width: 800px;    /* 🔧 大幅增加最大宽度，充分利用右边空白区域 */
                min-height: 26px;    /* 🔧 增加最小高度，确保字体完整显示 */
                max-height: 30px;    /* 🔧 增加最大高度，避免文字被截断 */
                text-align: left;    /* 🔧 左对齐 */
                word-wrap: break-word;  /* 允许长文本换行 */
                /* 🔧 确保内容完整显示 */
                margin-left: 0px;
                margin-right: 0px;
                /* 🔧 确保文字不被截断 */
                white-space: nowrap;  /* 防止文字换行被截断 */
                overflow: visible;    /* 确保内容可见 */
                text-overflow: clip;  /* 不使用省略号，显示完整内容 */
            }
        """)



    def _update_display(self):
        """更新显示"""
        try:
            # 更新统计数据
            self.total_count_label.setText(str(self.total_count))
            self.pass_count_label.setText(str(self.pass_count))
            self.fail_count_label.setText(str(self.fail_count))

            # 计算良率
            if self.total_count > 0:
                yield_rate = (self.pass_count / self.total_count) * 100
                self.yield_rate_label.setText(f"{yield_rate:.1f}%")
            else:
                self.yield_rate_label.setText("0.0%")

        except Exception as e:
            logger.error(f"更新显示失败: {e}")

    def clear_statistics(self):
        """清理统计数据"""
        try:
            # 🔧 修复：使用计数器管理器清除统计数据
            self.counter_manager.clear_statistics()

            # 重置本地数据
            self.total_count = 0
            self.pass_count = 0
            self.fail_count = 0
            self.grade_distribution.clear()

            # 重置表格
            for row in range(self.grade_table.rowCount()):
                for col in range(self.grade_table.columnCount()):
                    item = self.grade_table.item(row, col)
                    if item:
                        item.setText("0")

            # 更新显示
            self._update_display()

            # 发送信号
            self.statistics_updated.emit(self.get_statistics())

            logger.info("统计数据已清理")

        except Exception as e:
            logger.error(f"清理统计数据失败: {e}")

    def add_test_result(self, is_pass: bool, rs_grade: Optional[str] = None, rct_grade: Optional[str] = None):
        """
        添加测试结果到统计

        Args:
            is_pass: 是否通过测试
            rs_grade: Rs档位
            rct_grade: Rct档位
        """
        try:
            # 🔧 修复：添加调试日志，跟踪统计更新
            logger.info(f"📊 [统计组件] 添加测试结果: 通过={is_pass}, Rs档位={rs_grade}, Rct档位={rct_grade}")
            
            # 🔧 修复：只使用计数器管理器添加测试结果，避免重复计算
            self.counter_manager.add_test_result(is_pass, rs_grade, rct_grade)

            # 🔧 修复：从计数器管理器重新加载统计数据，确保数据一致性
            stats = self.counter_manager.get_statistics()
            old_total = self.total_count
            self.total_count = stats['total_count']
            self.pass_count = stats['pass_count']
            self.fail_count = stats['fail_count']
            self.grade_distribution = stats['grade_distribution'].copy()

            # 🔧 修复：添加统计变化日志
            logger.info(f"📊 [统计组件] 统计更新: {old_total} -> {self.total_count} (增加{self.total_count - old_total})")

            # 更新档位分布表格显示
            self._update_grade_table_from_distribution()

            # 更新UI显示
            self._update_statistics_display()

            # 发送信号
            self.statistics_updated.emit(self.get_statistics())

            logger.info(f"📊 [统计组件] 测试结果添加完成: 总数={self.total_count}, 合格={self.pass_count}, 不合格={self.fail_count}")

        except Exception as e:
            logger.error(f"添加测试结果失败: {e}")

    def get_statistics(self) -> dict:
        """
        获取统计数据

        Returns:
            统计数据字典
        """
        yield_rate = (self.pass_count / self.total_count * 100) if self.total_count > 0 else 0.0

        return {
            'total_count': self.total_count,
            'pass_count': self.pass_count,
            'fail_count': self.fail_count,
            'yield_rate': yield_rate,
            'grade_distribution': self.grade_distribution.copy()
        }

    def load_settings(self):
        """重新加载设置"""
        # 重新初始化档位表格
        self._init_grade_table()
        # 更新档位范围显示
        self._update_grade_range_display()

    def update_grade_settings(self):
        """更新档位设置（外部调用接口）"""
        try:
            # 重新初始化档位表格
            self._init_grade_table()
            # 更新档位范围显示
            self._update_grade_range_display()
            logger.debug("档位设置已更新")
        except Exception as e:
            logger.error(f"更新档位设置失败: {e}")
