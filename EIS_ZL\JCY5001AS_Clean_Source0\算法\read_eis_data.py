#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取EIS测试数据文件夹内容
"""

import os
import pandas as pd
import glob
from pathlib import Path

def list_eis_files():
    """列出EIS测试数据文件夹中的所有文件"""
    base_path = r"C:\Users\<USER>\Desktop\EIS测试数据_20250625"
    
    if not os.path.exists(base_path):
        print(f"文件夹不存在: {base_path}")
        return
    
    print(f"EIS测试数据文件夹内容: {base_path}")
    print("=" * 60)
    
    # 递归列出所有文件
    for root, dirs, files in os.walk(base_path):
        level = root.replace(base_path, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            print(f"{subindent}{file} ({file_size} bytes)")
            
            # 如果是Excel文件，尝试读取工作表名称
            if file.endswith(('.xlsx', '.xls')):
                try:
                    xl_file = pd.ExcelFile(file_path)
                    print(f"{subindent}  工作表: {xl_file.sheet_names}")
                except Exception as e:
                    print(f"{subindent}  读取失败: {str(e)}")

def find_battery_data():
    """查找314d和7430mAh电芯数据"""
    base_path = r"C:\Users\<USER>\Desktop\EIS测试数据_20250625"
    
    if not os.path.exists(base_path):
        print(f"文件夹不存在: {base_path}")
        return
    
    print("查找314d和7430mAh电芯数据...")
    print("=" * 40)
    
    # 搜索包含关键词的文件
    keywords = ['314d', '314D', '7430', 'mAh', 'MAH']
    
    for root, dirs, files in os.walk(base_path):
        for file in files:
            file_lower = file.lower()
            for keyword in keywords:
                if keyword.lower() in file_lower:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    print(f"找到相关文件: {file}")
                    print(f"  路径: {file_path}")
                    print(f"  大小: {file_size} bytes")
                    
                    # 如果是Excel文件，尝试读取内容概览
                    if file.endswith(('.xlsx', '.xls')):
                        try:
                            xl_file = pd.ExcelFile(file_path)
                            print(f"  工作表: {xl_file.sheet_names}")
                            
                            # 读取第一个工作表的前几行
                            df = pd.read_excel(file_path, sheet_name=0, nrows=5)
                            print(f"  列名: {list(df.columns)}")
                            print(f"  数据行数: {len(pd.read_excel(file_path, sheet_name=0))}")
                        except Exception as e:
                            print(f"  读取失败: {str(e)}")
                    print("-" * 40)
                    break

if __name__ == "__main__":
    print("开始扫描EIS测试数据...")
    list_eis_files()
    print("\n")
    find_battery_data()