#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JCY5001AS界面响应性改进效果
验证在不同屏幕分辨率下的显示效果

Author: Assistant
Date: 2025-07-05
"""

import sys
import os
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QFrame, QLineEdit, QGroupBox,
                             QScrollArea, QTabWidget, QPushButton)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class InterfaceResponsivenessTestWindow(QMainWindow):
    """界面响应性测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 界面响应性测试")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        self._add_description(main_layout)
        
        # 创建分辨率测试标签页
        self._create_resolution_tests(main_layout)
        
        # 添加控制按钮
        self._add_control_buttons(main_layout)
        
        logger.info("界面响应性测试窗口已启动")
        logger.info("主要改进：")
        logger.info("• 电池码输入框：最小150px，最大250px，防止过度拉伸")
        logger.info("• Rs/Rct标题：最小70px，最大90px，确保完整显示")
        logger.info("• Rs/Rct数值：最小100px，最大150px，优化显示效果")
        logger.info("• 增强响应性：在不同屏幕分辨率下都能正常显示")
    
    def _add_description(self, layout):
        """添加说明文字"""
        desc_label = QLabel("""
        <h2>JCY5001AS 界面响应性测试</h2>
        <p><b>测试目的：</b>验证界面组件在不同屏幕分辨率下的显示效果</p>
        <p><b>主要改进：</b></p>
        <ul>
        <li>电池码输入框：设置最小宽度150px，最大宽度250px，防止过度拉伸</li>
        <li>Rs/Rct标题标签：最小宽度70px，最大宽度90px，确保完整显示</li>
        <li>Rs/Rct数值标签：最小宽度100px，最大宽度150px，优化显示效果</li>
        <li>增强响应性：在不同屏幕分辨率下都能保持良好的显示效果</li>
        </ul>
        """)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 11pt;
            }
        """)
        layout.addWidget(desc_label)
    
    def _create_resolution_tests(self, layout):
        """创建分辨率测试标签页"""
        tab_widget = QTabWidget()
        
        # 不同分辨率的测试配置
        resolutions = [
            ("1200x800 (最小)", 1200, 800),
            ("1366x768 (常见)", 1366, 768),
            ("1920x1080 (标准)", 1920, 1080),
            ("2560x1440 (高分)", 2560, 1440)
        ]
        
        for name, width, height in resolutions:
            tab_content = self._create_resolution_test_tab(width, height)
            tab_widget.addTab(tab_content, name)
        
        layout.addWidget(tab_widget)
    
    def _create_resolution_test_tab(self, width, height):
        """创建分辨率测试标签页内容"""
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 分辨率信息
        info_label = QLabel(f"模拟分辨率: {width}x{height}")
        info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        scroll_layout.addWidget(info_label)
        
        # 创建通道显示演示
        for i in range(4):  # 显示4个通道作为演示
            channel_demo = self._create_channel_demo(i + 1, width)
            scroll_layout.addWidget(channel_demo)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        return scroll_area
    
    def _create_channel_demo(self, channel_num, screen_width):
        """创建通道显示演示"""
        # 根据屏幕宽度调整通道宽度
        if screen_width <= 1200:
            channel_width = 280
        elif screen_width <= 1366:
            channel_width = 320
        elif screen_width <= 1920:
            channel_width = 360
        else:
            channel_width = 400
        
        group_box = QGroupBox(f"通道 {channel_num}")
        group_box.setFixedWidth(channel_width)
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        
        layout = QVBoxLayout(group_box)
        layout.setContentsMargins(6, 8, 6, 6)
        layout.setSpacing(3)
        
        # 主内容区域
        main_content = QFrame()
        main_layout = QHBoxLayout(main_content)
        main_layout.setSpacing(8)
        main_layout.setContentsMargins(4, 4, 4, 4)
        
        # 左列
        left_column = self._create_left_column_demo(screen_width)
        main_layout.addLayout(left_column, 2)
        
        # 右列
        right_column = self._create_right_column_demo(screen_width)
        main_layout.addLayout(right_column, 3)
        
        layout.addWidget(main_content)
        
        return group_box
    
    def _create_left_column_demo(self, screen_width):
        """创建左列演示"""
        left_layout = QVBoxLayout()
        left_layout.setSpacing(2)
        
        # 电池码输入区域
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(8)
        
        battery_label = QLabel("电池码:")
        battery_label.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        battery_label.setMinimumWidth(50)
        battery_layout.addWidget(battery_label)
        
        battery_edit = QLineEdit("JCY-20250705-6274")
        battery_edit.setStyleSheet("""
            QLineEdit {
                font-size: 10pt;
                padding: 5px 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
                min-width: 150px;
                max-width: 250px;
                min-height: 26px;
            }
        """)
        battery_layout.addWidget(battery_edit, 2)
        
        left_layout.addLayout(battery_layout)
        
        # 电压显示
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(6)
        
        voltage_label = QLabel("电压(V):")
        voltage_label.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        voltage_label.setMinimumWidth(50)
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel("3.247")
        voltage_value.setStyleSheet("font-size: 11pt; font-weight: bold; color: #2c3e50;")
        voltage_layout.addWidget(voltage_value)
        voltage_layout.addStretch()
        
        left_layout.addLayout(voltage_layout)
        left_layout.addStretch()
        
        return left_layout
    
    def _create_right_column_demo(self, screen_width):
        """创建右列演示"""
        right_layout = QVBoxLayout()
        right_layout.setSpacing(4)
        
        # Rs显示
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(8)
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet("""
            font-size: 11pt; 
            color: #7f8c8d; 
            font-weight: bold;
            min-width: 70px;
            max-width: 90px;
        """)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("17.807")
        rs_value.setStyleSheet("""
            QLabel {
                font-size: 13pt;
                font-weight: bold;
                color: #2c3e50;
                background-color: #f8f9fa;
                padding: 4px 8px;
                border: 1px solid #ecf0f1;
                border-radius: 5px;
                min-width: 100px;
                max-width: 150px;
                text-align: left;
            }
        """)
        rs_layout.addWidget(rs_value)
        rs_layout.addStretch(2)
        
        right_layout.addLayout(rs_layout)
        
        # Rct显示
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(8)
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet("""
            font-size: 11pt; 
            color: #7f8c8d; 
            font-weight: bold;
            min-width: 70px;
            max-width: 90px;
        """)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("2.224")
        rct_value.setStyleSheet("""
            QLabel {
                font-size: 13pt;
                font-weight: bold;
                color: #2c3e50;
                background-color: #f8f9fa;
                padding: 4px 8px;
                border: 1px solid #ecf0f1;
                border-radius: 5px;
                min-width: 100px;
                max-width: 150px;
                text-align: left;
            }
        """)
        rct_layout.addWidget(rct_value)
        rct_layout.addStretch(2)
        
        right_layout.addLayout(rct_layout)
        right_layout.addStretch()
        
        return right_layout
    
    def _add_control_buttons(self, layout):
        """添加控制按钮"""
        button_layout = QHBoxLayout()
        
        # 测试不同窗口大小按钮
        sizes = [
            ("最小尺寸 (1200x800)", 1200, 800),
            ("常见尺寸 (1366x768)", 1366, 768),
            ("标准尺寸 (1920x1080)", 1920, 1080),
            ("高分尺寸 (2560x1440)", 2560, 1440)
        ]
        
        for name, width, height in sizes:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, w=width, h=height: self.resize(w, h))
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px 16px;
                    font-size: 10pt;
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            button_layout.addWidget(btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = InterfaceResponsivenessTestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
