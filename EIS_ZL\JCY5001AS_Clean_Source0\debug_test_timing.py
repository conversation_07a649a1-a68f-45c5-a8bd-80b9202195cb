#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间调试工具
检查影响测试时间的所有配置参数
"""

import json
import sys
import os

def load_config():
    """加载配置文件"""
    config_path = "config/app_config.json"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return None

def analyze_test_timing_config(config):
    """分析测试时间相关配置"""
    print("🚀 测试时间配置分析")
    print("=" * 80)
    
    # 1. 频率配置
    frequency_config = config.get('frequency', {})
    preset_mode = frequency_config.get('preset_mode', 'unknown')
    frequency_list = frequency_config.get('list', [])
    
    print(f"📊 频率配置:")
    print(f"   • 预设模式: {preset_mode}")
    print(f"   • 频点数量: {len(frequency_list)}个")
    print(f"   • 频率范围: {frequency_list[0]:.3f}Hz ~ {frequency_list[-1]:.3f}Hz")
    
    # 2. 测试参数配置
    test_params = config.get('test_params', {})
    average_times = test_params.get('average_times', 1)
    test_mode = test_params.get('test_mode', 'unknown')
    gain = test_params.get('gain', 'unknown')
    
    print(f"\n⚙️  测试参数:")
    print(f"   • 平均次数: {average_times}")
    print(f"   • 测试模式: {test_mode}")
    print(f"   • 增益设置: {gain}")
    
    # 3. 测试配置
    test_config = config.get('test', {})
    timeout = test_config.get('timeout', 60)
    interval = test_config.get('interval', 3)
    use_parallel_staggered = test_config.get('use_parallel_staggered_mode', False)
    critical_frequency = test_config.get('critical_frequency', 15.0)
    
    print(f"\n🔧 测试配置:")
    print(f"   • 测试超时: {timeout}秒")
    print(f"   • 测试间隔: {interval}秒")
    print(f"   • 并行错频模式: {'启用' if use_parallel_staggered else '禁用'}")
    print(f"   • 临界频率: {critical_frequency}Hz")
    
    # 4. 性能优化配置
    try:
        with open("config/performance_optimization.json", 'r', encoding='utf-8') as f:
            perf_config = json.load(f)
            
        print(f"\n⚡ 性能优化配置:")
        
        # 通信配置
        comm_config = perf_config.get('communication', {})
        print(f"   • 通信超时: {comm_config.get('timeout', 0.3)}秒")
        print(f"   • 重试次数: {comm_config.get('retry_count', 1)}")
        print(f"   • 命令延时: {comm_config.get('command_delay', 10)}ms")
        
        # 错频延时配置
        staggered_delay = perf_config.get('staggered_delay', {})
        if staggered_delay.get('enable', False):
            print(f"   • 错频延时启用: 是")
            print(f"     - 高频延时: {staggered_delay.get('high_frequency_delay', 10)}ms")
            print(f"     - 中频延时: {staggered_delay.get('medium_frequency_delay', 15)}ms")
            print(f"     - 低频延时: {staggered_delay.get('low_frequency_delay', 25)}ms")
        else:
            print(f"   • 错频延时启用: 否")
            
        # 测量优化配置
        measurement = perf_config.get('measurement', {})
        print(f"   • 智能超时: {'启用' if measurement.get('enable_smart_timeout', True) else '禁用'}")
        print(f"   • 早期完成: {'启用' if measurement.get('enable_early_completion', False) else '禁用'}")
        print(f"   • 自适应超时: {'启用' if measurement.get('adaptive_timeout', True) else '禁用'}")
        
    except Exception as e:
        print(f"\n⚠️  无法读取性能优化配置: {e}")
    
    return True

def estimate_test_time(config):
    """估算测试时间"""
    print(f"\n⏱️  测试时间估算:")
    print("-" * 60)
    
    # 获取配置参数
    frequency_list = config.get('frequency', {}).get('list', [])
    average_times = config.get('test_params', {}).get('average_times', 1)
    timeout = config.get('test', {}).get('timeout', 60)
    interval = config.get('test', {}).get('interval', 3)
    use_parallel_staggered = config.get('test', {}).get('use_parallel_staggered_mode', False)
    
    num_frequencies = len(frequency_list)
    
    if use_parallel_staggered:
        # 并行错频模式：高频点错频测试，低频点同时测试
        critical_frequency = config.get('test', {}).get('critical_frequency', 15.0)
        high_freq_count = sum(1 for f in frequency_list if f > critical_frequency)
        low_freq_count = num_frequencies - high_freq_count
        
        # 高频点：每个频点单独测试
        high_freq_time = high_freq_count * (timeout + interval) * average_times
        # 低频点：同时测试
        low_freq_time = (timeout + interval) * average_times if low_freq_count > 0 else 0
        
        estimated_time = high_freq_time + low_freq_time
        
        print(f"📊 并行错频模式估算:")
        print(f"   • 高频点数量: {high_freq_count}个 (>{critical_frequency}Hz)")
        print(f"   • 低频点数量: {low_freq_count}个 (≤{critical_frequency}Hz)")
        print(f"   • 高频点测试时间: {high_freq_time:.1f}秒")
        print(f"   • 低频点测试时间: {low_freq_time:.1f}秒")
        
    else:
        # 传统模式：所有频点依次测试
        estimated_time = num_frequencies * (timeout + interval) * average_times
        
        print(f"📊 传统模式估算:")
        print(f"   • 频点数量: {num_frequencies}个")
        print(f"   • 单频点时间: {timeout + interval}秒")
        print(f"   • 平均次数: {average_times}")
    
    print(f"\n🎯 预估总测试时间: {estimated_time:.1f}秒 ({estimated_time/60:.1f}分钟)")
    
    # 与实际时间对比
    if estimated_time > 70:
        print("⚠️  预估时间较长，可能的优化建议:")
        print("   • 减少平均次数")
        print("   • 降低测试超时时间")
        print("   • 启用并行错频模式")
        print("   • 使用生产模式（更少频点）")
    elif estimated_time < 60:
        print("✅ 预估时间合理")
    
    return estimated_time

def main():
    """主函数"""
    print("🔍 JCY5001AS 测试时间调试工具")
    print("=" * 80)
    
    # 加载配置
    config = load_config()
    if not config:
        return 1
    
    # 分析配置
    analyze_test_timing_config(config)
    
    # 估算测试时间
    estimate_test_time(config)
    
    print("\n" + "=" * 80)
    print("✅ 分析完成")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
