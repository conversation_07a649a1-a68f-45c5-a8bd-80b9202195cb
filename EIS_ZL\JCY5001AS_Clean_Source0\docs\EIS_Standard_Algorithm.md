# EIS标准分析算法（双模型版本）

## 算法概述

电池阻抗谱(EIS)分析的双模型标准算法，根据电池新旧状态自动选择相应的等效电路模型：

### 新电池模型（单半圆）
**总阻抗 = Rs + Rct + W**

### 旧电池模型（双半圆）
**总阻抗 = Rs + Rsei + Rct + W**

## 各组分定义

- **Rs**：溶液阻抗（高频截距）
- **Rsei**：SEI膜阻抗（第一个半圆）
- **Rct**：电荷转移阻抗（第二个半圆）
- **W**：Warburg阻抗（45°尾部扩散阻抗）

## 双模型计算流程

### 1. Rs计算（溶液阻抗）
**方法：虚部过零点法**
- 寻找虚部从负值变为正值的过零点
- 使用线性插值计算过零点对应的实部值
- 如果没有过零点，使用最接近零的虚部点对应的实部值

```python
# 伪代码
for i in range(len(imag_parts) - 1):
    if imag_parts[i] * imag_parts[i + 1] < 0:  # 符号变化
        # 线性插值找到过零点
        Rs = x1 - y1 * (x2 - x1) / (y2 - y1)
        break
```

### 2. 电池模型检测
**方法：基于谷点数量和SEI阻抗大小的智能判断**

#### 判断逻辑：
1. **谷点数量分析**：
   - ≥2个谷点 → 检查第二个谷点的Rsei值
   - 1个谷点 → 检查总阻抗大小
   - 0个谷点 → 默认新电池

2. **SEI阻抗阈值**：
   - Rsei > 0.5mΩ → 旧电池（明显SEI膜）
   - Rsei ≤ 0.5mΩ → 新电池（SEI膜很薄）

3. **总阻抗阈值**：
   - 总阻抗 > 5mΩ → 可能是旧电池
   - 总阻抗 ≤ 5mΩ → 新电池

```python
# 模型检测伪代码
if valley_count >= 2:
    potential_rsei = second_valley_real - Rs
    if potential_rsei > 0.5:
        return "旧电池"
    else:
        return "新电池"
elif valley_count == 1:
    total_resistance = valley_real - Rs
    if total_resistance > 5.0:
        return "旧电池"
    else:
        return "新电池"
else:
    return "新电池"
```

### 3. 新电池模型分析（Rs + Rct + W）
**适用于：SEI膜很薄的新电池**

#### 步骤：
1. 寻找第一个谷点计算Rct
2. 如无谷点，使用最低频点估算（Rct占30%）
3. 计算W阻抗：W = 最低频点 - Rs - Rct

### 4. 旧电池模型分析（Rs + Rsei + Rct + W）
**适用于：SEI膜明显的旧电池**

#### 步骤：
1. 双谷点方法：
   - 第一个谷点（低频）：Rct + Rsei
   - 第二个谷点（高频）：Rsei
   - 计算：Rct = (Rct + Rsei) - Rsei
2. 单谷点估算：Rsei占70%，Rct占30%
3. 计算W阻抗：W = 最低频点 - Rs - Rsei - Rct

```python
# 旧电池双谷点计算
Rct_plus_Rsei = first_valley_real - Rs
Rsei = second_valley_real - Rs
Rct = Rct_plus_Rsei - Rsei
```

### 5. W阻抗计算（最低频点法）
**方法：使用最低频点总阻抗减去各组分**

```python
# 获取最低频点阻抗
min_freq_real = real_parts[min_freq_index]
min_freq_imag = imag_parts[min_freq_index]

# 计算W阻抗
W_real = min_freq_real - Rs - Rsei - Rct
W_imag = min_freq_imag
W_magnitude = sqrt(W_real² + W_imag²)
W_phase = arctan(W_imag / W_real)
```

## 算法验证

### 验证公式：
- **实部验证**：Rs + Rsei + Rct + W_real = 最低频点实部
- **W阻抗特征**：相位角应接近±45°（典型Warburg阻抗）

### 合理性检查：
- Rs > 0（溶液阻抗为正）
- Rsei ≥ 0（SEI膜阻抗非负）
- Rct > 0（电荷转移阻抗为正）
- W_real > 0（扩散阻抗实部为正）
- |W_phase| ≈ 45°（Warburg阻抗特征）

## 示例计算结果

### 新电池模型示例
**第一组数据**：
- **电池模型**: 新电池
- **Rs** = 2.098 mΩ
- **Rct** = 0.548 mΩ
- **W** = 1.732 + 1.382j mΩ (幅值2.216mΩ, 相位38.6°)
- **验证**: 2.098 + 0.548 + 1.732 = 4.378 mΩ ✓

### 旧电池模型示例
**第二组数据**：
- **电池模型**: 旧电池
- **Rs** = 14.968 mΩ
- **Rsei** = 7.632 mΩ
- **Rct** = 0.430 mΩ
- **W** = 1.319 + 1.352j mΩ (幅值1.889mΩ, 相位45.7°)
- **验证**: 14.968 + 7.632 + 0.430 + 1.319 = 24.349 mΩ ✓

## 实现注意事项

1. **数据预处理**：确保频率和阻抗数据的对应关系正确
2. **异常处理**：处理无谷点、单谷点等特殊情况
3. **数值稳定性**：避免除零错误，设置合理的阈值
4. **物理合理性**：检查计算结果是否符合物理意义

## 更新日志

- **2024-12-28 v2.0**：升级为双模型EIS分析算法
- 新增电池模型自动检测功能
- 区分新电池（单半圆）和旧电池（双半圆）模型
- 针对不同模型优化计算精度
- 提高算法的适应性和准确性

- **2024-12-28 v1.0**：建立标准EIS分析算法
- 基于谷点分析法优化Rct计算精度
- 采用最低频点法计算W阻抗
- 验证算法的物理合理性和数值稳定性
