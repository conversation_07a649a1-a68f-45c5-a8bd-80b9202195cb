{"app": {"name": "JCY5001A鲸测云8路EIS阻抗筛选仪", "version": "V0.80.10", "trial_days": 30, "start_date": "2025-05-26T07:28:23.393457", "show_startup_license_dialog": false}, "ui": {"theme": "light", "language": "zh_CN", "window_size": [1920, 991], "window_position": [7, -9], "auto_save_layout": true, "window": {"maximized": true, "fullscreen": false, "width": 1918, "height": 1061, "x": 7, "y": 0}}, "batch_info": {"batch_number": "BATCH-20250624-001", "operator": "1111111", "cell_type": "磷酸铁锂", "cell_spec": "555555555", "standard_voltage": 3.2, "standard_capacity": 3000, "standard_capacity_unit": "mAh", "new_resistance": 6.0, "eol_resistance": 12.0}, "test_params": {"test_mode": "staggered", "gain": "1", "average_times": 1, "battery_range": "10mΩ以下", "resistance_range": "10R", "voltage_range": {"min": 3.6999999999999997, "max": 4.1, "standard": 3.21, "tolerance_percent": 10.0}, "delay": 100, "voltage_tolerance": 0.5, "critical_frequency": 15.0}, "frequency": {"mode": "multi", "single_freq": 1007.0827, "multi_freq": {"preset": "磷酸铁锂", "custom_list": [1007.083, 625.612, 389.1, 242.234, 150.681, 93.46, 57.221, 36.24, 22.888, 13.351, 11.444, 9.537, 7.629, 5.722, 3.815, 1.907, 0.954, 0.477, 0.238, 0.119], "min_freq": 0.01, "max_freq": 7800, "points": 20, "distribution": "log"}, "single_mode": false, "preset_mode": "研究模式", "list": [1007.083, 625.612, 389.1, 242.234, 150.681, 93.46, 57.221, 36.24, 22.888, 13.351, 11.444, 9.537, 7.629, 5.722, 3.815, 1.907, 0.954, 0.477, 0.238, 0.119], "single_frequency": 1007.08, "frequency_order": "high_to_low", "custom_list": [1007.083, 625.612, 389.1, 242.234, 150.681, 93.46, 57.221, 36.24, 22.888, 13.351, 11.444, 9.537, 7.629, 5.722, 3.815, 1.907, 0.954, 0.477, 0.238, 0.119]}, "test": {"continuous_mode": false, "continuous_mode_delay": 3, "auto_detect": false, "data_optimization": true, "optimization_delay": 0.5, "enabled_channels": [1, 2, 3, 4, 5, 6, 7, 8], "battery_coding": {"mode": "scan", "length_check": true, "expected_length": 12, "auto_rule": "BATCH_{batch}_{index:04d}"}, "label_print": {"enabled": false, "code_type": "qr"}, "timeout": 8, "retry_count": 1, "interval": 3, "functionality": "test_value_123", "count_limit_enabled": false, "max_count": 100, "use_parallel_staggered_mode": true, "critical_frequency": 15.0, "timeout_seconds": 150, "max_retries": 5, "error_recovery": true}, "battery_detection": {"enabled": true, "voltage_threshold_remove": 5.0, "voltage_threshold_min": 2.0, "voltage_threshold_max": 5.0, "detection_interval": 0.5, "stable_count_required": 2, "auto_restart_delay": 1.0, "impedance_response_check_enabled": false, "optimization_note": "已禁用阻抗响应检测以节省12秒启动时间"}, "device": {"connection": {"port": "COM48", "baudrate": 115200, "timeout": 1.0, "type": "modbus_rtu", "device_address": 1, "last_port": "COM48", "retry_count": 2, "retry_delay": 0.03, "health_check_interval": 30.0, "max_consecutive_failures": 15}, "channels": 8, "printer": {"name": "", "connected": false}, "barcode_scanner": {"enabled": false, "serial_length_min": 8, "serial_length_max": 30, "format_validation": false, "allowed_chars": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", "uniqueness_check": true, "auto_generation": {"enabled": true, "prefix": "JCY", "date_format": "YYYYMMDD", "sequence_digits": 4, "separator": "-"}}, "address": "", "port": 8080, "timeout": 10, "auto_reconnect": true, "reconnect_interval": 30, "auto_connect": true, "com_port": "COM5"}, "grade_settings": {"rs_grades": 3, "rct_grades": 3, "rs_boundaries": [5.0, 10.0], "rct_boundaries": [8.0, 15.0], "battery_type": 1, "auto_calc_range": true, "tolerance_mode": 1, "voltage_diff": 0.2, "rs_grade_count": 3, "rs_min": 16.0, "rs_max": 19.0, "rs_auto_calc": true, "rs1_max": 12.667, "rs2_max": 15.333, "rs3_max": 18.0, "rct_min": 1.0, "rct_max": 3.0, "rct_auto_calc": true, "rct1_max": 6.667, "rct2_max": 8.333, "rct3_max": 10.0, "standard_voltage": 3.9, "min_voltage": 3.7, "max_voltage": 4.1}, "database": {"path": "data/test_results.db", "backup_enabled": true, "backup_interval": 24}, "printer": {"type": "热敏打印机", "name": "Gprinter GP-2270T", "connection": "USB", "quality": "高质量", "density": "high", "contrast": "high"}, "label_print": {"auto_print": true, "print_pass_only": false, "copies": 1, "quality": "高质量", "font_enhancement": true, "bold_enhancement": true}, "logging": {"enable_system_log": true, "level": "INFO", "debug_mode": true}, "communication": {"enable_logging": false, "protocol": "Modbus RTU", "baudrate": "115200", "timeout": 0.8, "data_bits": "8", "stop_bits": "1", "parity": "无", "command_delay": 15, "response_timeout": 0.8}, "storage_management": {"database_size_limit_mb": 500, "log_size_limit_mb": 20, "warning_threshold_percent": 80, "auto_cleanup_enabled": true, "cleanup_frequency_days": 2, "log_retention_days": 7, "data_archive_days": 90, "temp_file_cleanup_enabled": true, "last_cleanup_time": "2025-07-07T10:30:56.941851", "storage_monitoring_enabled": true, "monitoring_interval_minutes": 60}, "debug": {"enable_license_debug": false, "description": "调试功能配置 - 生产环境请保持为false"}, "test_config": {"auto_detect": true, "data_optimization": true, "optimization_delay": 0.5, "battery_coding": {"mode": "scan", "length_check": true, "expected_length": 12, "auto_rule": "BATCH_{batch}_{index:04d}"}, "label_print": {"enabled": false, "code_type": "qr"}}, "product": {"batch_number": "BATCH-20250624-001", "operator": "1111111", "production_date": "周日 7月 6 2025", "remarks": "", "battery_type": "磷酸铁锂", "battery_spec": "555555555", "standard_voltage": 3.2, "capacity": 3000, "capacity_unit": "mAh"}, "impedance": {"rs_min": 16.0, "rs_grade1_max": 17.0, "rs_grade2_max": 18.0, "rs_grade3_max": 19.0, "rct_min": 1.0, "rct_grade1_max": 1.6666666666666665, "rct_grade2_max": 2.333333333333333, "rct_grade3_max": 3.0, "rs_grade_count": 3, "rct_grade_count": 3}, "data": {"optimization": true, "outlier_filter": true, "smoothing": true, "smoothing_strength": 5, "auto_save": true, "save_raw": false}, "label": {"auto_print": false, "print_pass_only": false, "template": "简化模板", "copies": 1, "coding_rule": "自定义规则", "coding_prefix": "BAT", "serial_start": 1, "template_content": "电池标签\n电池码: {battery_code}\n测试时间: {test_time}\nRs值: {rs_value}mΩ\nRct值: {rct_value}mΩ\n档位: {grade}", "width": 400, "height": 240}, "staggered_delay": {"enable": true, "high_frequency_threshold": 1007.0827, "medium_frequency_threshold": 101.0897, "low_frequency_threshold": 11.4441, "high_frequency_delay": 15, "medium_frequency_delay": 20, "low_frequency_delay": 35, "ultra_low_frequency_delay": 0, "description": "错频启动延时配置(ms): 高频≥1007Hz, 中频101-1006Hz, 低频11-100Hz, 超低频<11Hz"}, "staggered_startup": {"mode": "parallel_staggered", "description": "错频启动模式: sequential(错频启动), simultaneous(同时启动)"}, "test_count": {"channel_1": 1010, "channel_2": 1010, "channel_3": 1010, "channel_4": 1010, "channel_5": 1010, "channel_6": 1010, "channel_7": 1010, "channel_8": 1010}, "probe_pin": {"warning_threshold": 1000, "max_lifetime": 10000}, "serial_numbers": {"used_list": ["JCY-20250620-3157", "JCY-20250620-3187", "JCY-20250620-3200", "JCY-20250620-3179", "JCY-20250620-3193", "JCY-20250620-3215", "JCY-20250620-3174", "JCY-20250620-3196", "JCY-20250620-3219", "JCY-20250620-3198", "JCY-20250620-3170", "JCY-20250620-3177", "JCY-20250620-3211", "JCY-20250620-3173", "JCY-20250620-3205", "JCY-20250620-3209", "JCY-20250620-3188", "JCY-20250620-3182", "JCY-20250620-3208", "JCY-20250620-3175", "JCY-20250620-3162", "JCY-20250620-3154", "JCY-20250620-3202", "JCY-20250620-3195", "JCY-20250620-3148", "JCY-20250620-3191", "JCY-20250620-3183", "JCY-20250620-3204", "JCY-20250620-3156", "JCY-20250620-3166", "JCY-20250620-3167", "JCY-20250620-3199", "JCY-20250620-3161", "JCY-20250620-3153", "JCY-20250620-3207", "JCY-20250620-3189", "JCY-20250620-3218", "JCY-20250620-3184", "JCY-20250620-3165", "JCY-20250620-3212", "JCY-20250620-3192", "JCY-20250620-3163", "JCY-20250620-3203", "JCY-20250620-3168", "JCY-20250620-3169", "JCY-20250620-3213", "JCY-20250620-3152", "JCY-20250620-3206", "JCY-20250620-3190", "JCY-20250620-3186", "JCY-20250620-3185", "JCY-20250620-3214", "JCY-20250620-3164", "JCY-20250620-3172", "JCY-20250620-3210", "JCY-20250620-3181", "JCY-20250620-3150", "JCY-20250620-3176", "JCY-20250620-3151", "JCY-20250620-3201", "JCY-20250620-3180", "JCY-20250620-3171", "JCY-20250620-3149", "JCY-20250620-3216", "JCY-20250620-3217", "JCY-20250620-3160", "JCY-20250620-3194", "JCY-20250620-3158", "JCY-20250620-3197", "JCY-20250620-3159", "JCY-20250620-3178", "JCY-20250620-3155", "JCY-20250619-3144", "JCY-20250619-3134", "JCY-20250619-3147", "JCY-20250619-3124", "JCY-20250619-3119", "JCY-20250619-3131", "JCY-20250619-3132", "JCY-20250619-3130", "JCY-20250619-3117", "JCY-20250619-3118", "JCY-20250619-3120", "JCY-20250619-3116", "JCY-20250619-3126", "JCY-20250619-3122", "JCY-20250619-3127", "JCY-20250619-3139", "JCY-20250619-3140", "JCY-20250619-3135", "JCY-20250619-3128", "JCY-20250619-3136", "JCY-20250619-3133", "JCY-20250619-3121", "JCY-20250619-3146", "JCY-20250619-3123", "JCY-20250619-3145", "JCY-20250619-3138", "JCY-20250619-3141", "JCY-20250619-3129", "JCY-20250619-3125", "JCY-20250619-3143", "JCY-20250619-3142", "JCY-20250619-3137", "JCY-20250617-3101", "JCY-20250617-3100", "JCY-20250617-3102", "JCY-20250617-3103", "JCY-20250617-3105", "JCY-20250617-3104", "JCY-20250617-3111", "JCY-20250617-3113", "JCY-20250617-3108", "JCY-20250617-3115", "JCY-20250617-3112", "JCY-20250617-3107", "JCY-20250617-3114", "JCY-20250617-3110", "JCY-20250617-3109", "JCY-20250617-3106", "JCY-20250614-3022", "JCY-20250614-3060", "JCY-20250614-2989", "JCY-20250614-3079", "JCY-20250614-2991", "JCY-20250614-3040", "JCY-20250614-3028", "JCY-20250614-3053", "JCY-20250614-3091", "JCY-20250614-2994", "JCY-20250614-3085", "JCY-20250614-3023", "JCY-20250614-3046", "JCY-20250614-3064", "JCY-20250614-3065", "JCY-20250614-3029", "JCY-20250614-3048", "JCY-20250614-3004", "JCY-20250614-3054", "JCY-20250614-2998", "JCY-20250614-3027", "JCY-20250614-3003", "JCY-20250614-3024", "JCY-20250614-3092", "JCY-20250614-3045", "JCY-20250614-3017", "JCY-20250614-3012", "JCY-20250614-3011", "JCY-20250614-3062", "JCY-20250614-3031", "JCY-20250614-3015", "JCY-20250614-3002", "JCY-20250614-3076", "JCY-20250614-3013", "JCY-20250614-3021", "JCY-20250614-2999", "JCY-20250614-3070", "JCY-20250614-3088", "JCY-20250614-3074", "JCY-20250614-3055", "JCY-20250614-3099", "JCY-20250614-3044", "JCY-20250614-3005", "JCY-20250614-3035", "JCY-20250614-3026", "JCY-20250614-3059", "JCY-20250614-3008", "JCY-20250614-3052", "JCY-20250614-3042", "JCY-20250614-3038", "JCY-20250614-3081", "JCY-20250614-3093", "JCY-20250614-3039", "JCY-20250614-2990", "JCY-20250614-3047", "JCY-20250614-3082", "JCY-20250614-3018", "JCY-20250614-3009", "JCY-20250614-3067", "JCY-20250614-3071", "JCY-20250614-3050", "JCY-20250614-2995", "JCY-20250614-3030", "JCY-20250614-3051", "JCY-20250614-3073", "JCY-20250614-3078", "JCY-20250614-3033", "JCY-20250614-3006", "JCY-20250614-3019", "JCY-20250614-3084", "JCY-20250614-3090", "JCY-20250614-3077", "JCY-20250614-3072", "JCY-20250614-3057", "JCY-20250614-3089", "JCY-20250614-2997", "JCY-20250614-3037", "JCY-20250614-3025", "JCY-20250614-3010", "JCY-20250614-3087", "JCY-20250614-3049", "JCY-20250614-3066", "JCY-20250614-3061", "JCY-20250614-2992", "JCY-20250614-2988", "JCY-20250614-3034", "JCY-20250614-3043", "JCY-20250614-3020", "JCY-20250614-3080", "JCY-20250614-3032", "JCY-20250614-3058", "JCY-20250614-3098", "JCY-20250614-3016", "JCY-20250614-3014", "JCY-20250614-3041", "JCY-20250614-3063", "JCY-20250614-2996", "JCY-20250614-3068", "JCY-20250614-3069", "JCY-20250614-3056", "JCY-20250614-3094", "JCY-20250614-3095", "JCY-20250614-3083", "JCY-20250614-3036", "JCY-20250614-3007", "JCY-20250614-2993", "JCY-20250614-3075", "JCY-20250614-3086", "JCY-20250614-3097", "JCY-20250614-3096", "JCY-20250614-3000", "JCY-20250614-3001", "JCY-20250611-2967", "JCY-20250611-2980", "JCY-20250611-2968", "JCY-20250611-2966", "JCY-20250611-2983", "JCY-20250611-2981", "JCY-20250611-2976", "JCY-20250611-2965", "JCY-20250611-2987", "JCY-20250611-2985", "JCY-20250611-2975", "JCY-20250611-2971", "JCY-20250611-2974", "JCY-20250611-2964", "JCY-20250611-2986", "JCY-20250611-2984", "JCY-20250611-2969", "JCY-20250611-2972", "JCY-20250611-2973", "JCY-20250611-2979", "JCY-20250611-2977", "JCY-20250611-2970", "JCY-20250611-2978", "JCY-20250611-2982", "JCY-20250610-2923", "JCY-20250610-2854", "JCY-20250610-2901", "JCY-20250610-2948", "JCY-20250610-2903", "JCY-20250610-2927", "JCY-20250610-2871", "JCY-20250610-2856", "JCY-20250610-2928", "JCY-20250610-2868", "JCY-20250610-2866", "JCY-20250610-2847", "JCY-20250610-2853", "JCY-20250610-2936", "JCY-20250610-2896", "JCY-20250610-2873", "JCY-20250610-2913", "JCY-20250610-2899", "JCY-20250610-2920", "JCY-20250610-2894", "JCY-20250610-2902", "JCY-20250610-2958", "JCY-20250610-2864", "JCY-20250610-2926", "JCY-20250610-2897", "JCY-20250610-2952", "JCY-20250610-2841", "JCY-20250610-2916", "JCY-20250610-2942", "JCY-20250610-2883", "JCY-20250610-2917", "JCY-20250610-2893", "JCY-20250610-2862", "JCY-20250610-2930", "JCY-20250610-2850", "JCY-20250610-2858", "JCY-20250610-2931", "JCY-20250610-2885", "JCY-20250610-2955", "JCY-20250610-2943", "JCY-20250610-2877", "JCY-20250610-2860", "JCY-20250610-2905", "JCY-20250610-2852", "JCY-20250610-2863", "JCY-20250610-2874", "JCY-20250610-2940", "JCY-20250610-2962", "JCY-20250610-2859", "JCY-20250610-2915", "JCY-20250610-2924", "JCY-20250610-2957", "JCY-20250610-2912", "JCY-20250610-2918", "JCY-20250610-2947", "JCY-20250610-2925", "JCY-20250610-2869", "JCY-20250610-2941", "JCY-20250610-2910", "JCY-20250610-2933", "JCY-20250610-2909", "JCY-20250610-2886", "JCY-20250610-2837", "JCY-20250610-2939", "JCY-20250610-2914", "JCY-20250610-2951", "JCY-20250610-2932", "JCY-20250610-2836", "JCY-20250610-2950", "JCY-20250610-2888", "JCY-20250610-2882", "JCY-20250610-2906", "JCY-20250610-2861", "JCY-20250610-2904", "JCY-20250610-2929", "JCY-20250610-2956", "JCY-20250610-2937", "JCY-20250610-2891", "JCY-20250610-2889", "JCY-20250610-2872", "JCY-20250610-2959", "JCY-20250610-2890", "JCY-20250610-2900", "JCY-20250610-2849", "JCY-20250610-2840", "JCY-20250610-2844", "JCY-20250610-2911", "JCY-20250610-2881", "JCY-20250610-2954", "JCY-20250610-2875", "JCY-20250610-2876", "JCY-20250610-2880", "JCY-20250610-2867", "JCY-20250610-2878", "JCY-20250610-2892", "JCY-20250610-2870", "JCY-20250610-2842", "JCY-20250610-2895", "JCY-20250610-2946", "JCY-20250610-2898", "JCY-20250610-2907", "JCY-20250610-2848", "JCY-20250610-2846", "JCY-20250610-2963", "JCY-20250610-2938", "JCY-20250610-2935", "JCY-20250610-2949", "JCY-20250610-2922", "JCY-20250610-2865", "JCY-20250610-2921", "JCY-20250610-2838", "JCY-20250610-2884", "JCY-20250610-2961", "JCY-20250610-2919", "JCY-20250610-2879", "JCY-20250610-2960", "JCY-20250610-2839", "JCY-20250610-2934", "JCY-20250610-2857", "JCY-20250610-2845", "JCY-20250610-2855", "JCY-20250610-2944", "JCY-20250610-2908", "JCY-20250610-2843", "JCY-20250610-2851", "JCY-20250610-2945", "JCY-20250610-2953", "JCY-20250610-2887", "JCY-20250609-2817", "JCY-20250609-2804", "JCY-20250609-2820", "JCY-20250609-2827", "JCY-20250609-2822", "JCY-20250609-2825", "JCY-20250609-2813", "JCY-20250609-2810", "JCY-20250609-2812", "JCY-20250609-2819", "JCY-20250609-2808", "JCY-20250609-2831", "JCY-20250609-2818", "JCY-20250609-2828", "JCY-20250609-2814", "JCY-20250609-2834", "JCY-20250609-2829", "JCY-20250609-2823", "JCY-20250609-2811", "JCY-20250609-2821", "JCY-20250609-2835", "JCY-20250609-2833", "JCY-20250609-2806", "JCY-20250609-2807", "JCY-20250609-2826", "JCY-20250609-2809", "JCY-20250609-2816", "JCY-20250609-2815", "JCY-20250609-2824", "JCY-20250609-2830", "JCY-20250609-2805", "JCY-20250609-2832", "JCY-20250607-2751", "JCY-20250607-2543", "JCY-20250607-2656", "JCY-20250607-2743", "JCY-20250607-2703", "JCY-20250607-2681", "JCY-20250607-2759", "JCY-20250607-2780", "JCY-20250607-2778", "JCY-20250607-2661", "JCY-20250607-2542", "JCY-20250607-2587", "JCY-20250607-2544", "JCY-20250607-2801", "JCY-20250607-2789", "JCY-20250607-2586", "JCY-20250607-2596", "JCY-20250607-2679", "JCY-20250607-2634", "JCY-20250607-2731", "JCY-20250607-2564", "JCY-20250607-2651", "JCY-20250607-2657", "JCY-20250607-2718", "JCY-20250607-2633", "JCY-20250607-2706", "JCY-20250607-2755", "JCY-20250607-2715", "JCY-20250607-2717", "JCY-20250607-2550", "JCY-20250607-2603", "JCY-20250607-2593", "JCY-20250607-2788", "JCY-20250607-2613", "JCY-20250607-2797", "JCY-20250607-2645", "JCY-20250607-2803", "JCY-20250607-2604", "JCY-20250607-2637", "JCY-20250607-2688", "JCY-20250607-2638", "JCY-20250607-2705", "JCY-20250607-2635", "JCY-20250607-2551", "JCY-20250607-2796", "JCY-20250607-2674", "JCY-20250607-2762", "JCY-20250607-2727", "JCY-20250607-2648", "JCY-20250607-2592", "JCY-20250607-2784", "JCY-20250607-2654", "JCY-20250607-2666", "JCY-20250607-2665", "JCY-20250607-2702", "JCY-20250607-2726", "JCY-20250607-2610", "JCY-20250607-2763", "JCY-20250607-2573", "JCY-20250607-2753", "JCY-20250607-2608", "JCY-20250607-2629", "JCY-20250607-2580", "JCY-20250607-2594", "JCY-20250607-2578", "JCY-20250607-2618", "JCY-20250607-2708", "JCY-20250607-2642", "JCY-20250607-2584", "JCY-20250607-2640", "JCY-20250607-2598", "JCY-20250607-2790", "JCY-20250607-2562", "JCY-20250607-2646", "JCY-20250607-2605", "JCY-20250607-2622", "JCY-20250607-2737", "JCY-20250607-2722", "JCY-20250607-2582", "JCY-20250607-2684", "JCY-20250607-2738", "JCY-20250607-2710", "JCY-20250607-2767", "JCY-20250607-2667", "JCY-20250607-2696", "JCY-20250607-2729", "JCY-20250607-2791", "JCY-20250607-2785", "JCY-20250607-2776", "JCY-20250607-2700", "JCY-20250607-2739", "JCY-20250607-2748", "JCY-20250607-2602", "JCY-20250607-2571", "JCY-20250607-2671", "JCY-20250607-2764", "JCY-20250607-2747", "JCY-20250607-2583", "JCY-20250607-2649", "JCY-20250607-2631", "JCY-20250607-2620", "JCY-20250607-2775", "JCY-20250607-2553", "JCY-20250607-2745", "JCY-20250607-2742", "JCY-20250607-2574", "JCY-20250607-2617", "JCY-20250607-2601", "JCY-20250607-2664", "JCY-20250607-2662", "JCY-20250607-2736", "JCY-20250607-2676", "JCY-20250607-2546", "JCY-20250607-2628", "JCY-20250607-2735", "JCY-20250607-2585", "JCY-20250607-2566", "JCY-20250607-2612", "JCY-20250607-2588", "JCY-20250607-2691", "JCY-20250607-2615", "JCY-20250607-2589", "JCY-20250607-2677", "JCY-20250607-2770", "JCY-20250607-2779", "JCY-20250607-2547", "JCY-20250607-2611", "JCY-20250607-2771", "JCY-20250607-2568", "JCY-20250607-2644", "JCY-20250607-2599", "JCY-20250607-2579", "JCY-20250607-2595", "JCY-20250607-2669", "JCY-20250607-2802", "JCY-20250607-2686", "JCY-20250607-2685", "JCY-20250607-2621", "JCY-20250607-2641", "JCY-20250607-2600", "JCY-20250607-2773", "JCY-20250607-2670", "JCY-20250607-2690", "JCY-20250607-2716", "JCY-20250607-2540", "JCY-20250607-2687", "JCY-20250607-2549", "JCY-20250607-2675", "JCY-20250607-2792", "JCY-20250607-2659", "JCY-20250607-2709", "JCY-20250607-2712", "JCY-20250607-2682", "JCY-20250607-2570", "JCY-20250607-2626", "JCY-20250607-2786", "JCY-20250607-2697", "JCY-20250607-2783", "JCY-20250607-2572", "JCY-20250607-2800", "JCY-20250607-2576", "JCY-20250607-2623", "JCY-20250607-2782", "JCY-20250607-2548", "JCY-20250607-2647", "JCY-20250607-2774", "JCY-20250607-2558", "JCY-20250607-2713", "JCY-20250607-2581", "JCY-20250607-2636", "JCY-20250607-2799", "JCY-20250607-2678", "JCY-20250607-2707", "JCY-20250607-2680", "JCY-20250607-2699", "JCY-20250607-2695", "JCY-20250607-2732", "JCY-20250607-2725", "JCY-20250607-2777", "JCY-20250607-2720", "JCY-20250607-2556", "JCY-20250607-2609", "JCY-20250607-2672", "JCY-20250607-2793", "JCY-20250607-2632", "JCY-20250607-2728", "JCY-20250607-2719", "JCY-20250607-2630", "JCY-20250607-2795", "JCY-20250607-2734", "JCY-20250607-2569", "JCY-20250607-2643", "JCY-20250607-2694", "JCY-20250607-2555", "JCY-20250607-2765", "JCY-20250607-2625", "JCY-20250607-2650", "JCY-20250607-2591", "JCY-20250607-2597", "JCY-20250607-2761", "JCY-20250607-2721", "JCY-20250607-2607", "JCY-20250607-2758", "JCY-20250607-2683", "JCY-20250607-2619", "JCY-20250607-2655", "JCY-20250607-2730", "JCY-20250607-2689", "JCY-20250607-2577", "JCY-20250607-2575", "JCY-20250607-2744", "JCY-20250607-2741", "JCY-20250607-2606", "JCY-20250607-2733", "JCY-20250607-2627", "JCY-20250607-2750", "JCY-20250607-2658", "JCY-20250607-2704", "JCY-20250607-2798", "JCY-20250607-2714", "JCY-20250607-2724", "JCY-20250607-2541", "JCY-20250607-2614", "JCY-20250607-2752", "JCY-20250607-2692", "JCY-20250607-2673", "JCY-20250607-2723", "JCY-20250607-2652", "JCY-20250607-2639", "JCY-20250607-2711", "JCY-20250607-2740", "JCY-20250607-2560", "JCY-20250607-2668", "JCY-20250607-2772", "JCY-20250607-2787", "JCY-20250607-2756", "JCY-20250607-2746", "JCY-20250607-2794", "JCY-20250607-2653", "JCY-20250607-2749", "JCY-20250607-2624", "JCY-20250607-2559", "JCY-20250607-2766", "JCY-20250607-2660", "JCY-20250607-2557", "JCY-20250607-2768", "JCY-20250607-2663", "JCY-20250607-2616", "JCY-20250607-2590", "JCY-20250607-2563", "JCY-20250607-2567", "JCY-20250607-2769", "JCY-20250607-2754", "JCY-20250607-2552", "JCY-20250607-2698", "JCY-20250607-2701", "JCY-20250607-2545", "JCY-20250607-2561", "JCY-20250607-2757", "JCY-20250607-2565", "JCY-20250607-2554", "JCY-20250607-2781", "JCY-20250607-2760", "JCY-20250607-2693", "JCY-20250606-2489", "JCY-20250606-2410", "JCY-20250606-2479", "JCY-20250606-2422", "JCY-20250606-2491", "JCY-20250606-2521", "JCY-20250606-2448", "JCY-20250606-2461", "JCY-20250606-2452", "JCY-20250606-2432", "JCY-20250606-2451", "JCY-20250606-2408", "JCY-20250606-2484", "JCY-20250606-2401", "JCY-20250606-2480", "JCY-20250606-2405", "JCY-20250606-2513", "JCY-20250606-2397", "JCY-20250606-2436", "JCY-20250606-2384", "JCY-20250606-2467", "JCY-20250606-2400", "JCY-20250606-2508", "JCY-20250606-2531", "JCY-20250606-2496", "JCY-20250606-2440", "JCY-20250606-2501", "JCY-20250606-2486", "JCY-20250606-2498", "JCY-20250606-2392", "JCY-20250606-2485", "JCY-20250606-2456", "JCY-20250606-2427", "JCY-20250606-2403", "JCY-20250606-2413", "JCY-20250606-2460", "JCY-20250606-2488", "JCY-20250606-2502", "JCY-20250606-2487", "JCY-20250606-2398", "JCY-20250606-2523", "JCY-20250606-2535", "JCY-20250606-2505", "JCY-20250606-2386", "JCY-20250606-2390", "JCY-20250606-2430", "JCY-20250606-2528", "JCY-20250606-2385", "JCY-20250606-2518", "JCY-20250606-2437", "JCY-20250606-2380", "JCY-20250606-2465", "JCY-20250606-2383", "JCY-20250606-2532", "JCY-20250606-2514", "JCY-20250606-2449", "JCY-20250606-2474", "JCY-20250606-2490", "JCY-20250606-2472", "JCY-20250606-2473", "JCY-20250606-2426", "JCY-20250606-2409", "JCY-20250606-2519", "JCY-20250606-2533", "JCY-20250606-2443", "JCY-20250606-2414", "JCY-20250606-2441", "JCY-20250606-2520", "JCY-20250606-2387", "JCY-20250606-2493", "JCY-20250606-2435", "JCY-20250606-2525", "JCY-20250606-2511", "JCY-20250606-2470", "JCY-20250606-2537", "JCY-20250606-2439", "JCY-20250606-2527", "JCY-20250606-2418", "JCY-20250606-2506", "JCY-20250606-2524", "JCY-20250606-2495", "JCY-20250606-2477", "JCY-20250606-2494", "JCY-20250606-2389", "JCY-20250606-2512", "JCY-20250606-2399", "JCY-20250606-2382", "JCY-20250606-2406", "JCY-20250606-2499", "JCY-20250606-2492", "JCY-20250606-2469", "JCY-20250606-2530", "JCY-20250606-2417", "JCY-20250606-2455", "JCY-20250606-2454", "JCY-20250606-2516", "JCY-20250606-2457", "JCY-20250606-2419", "JCY-20250606-2404", "JCY-20250606-2468", "JCY-20250606-2476", "JCY-20250606-2539", "JCY-20250606-2393", "JCY-20250606-2458", "JCY-20250606-2481", "JCY-20250606-2396", "JCY-20250606-2425", "JCY-20250606-2464", "JCY-20250606-2510", "JCY-20250606-2429", "JCY-20250606-2433", "JCY-20250606-2504", "JCY-20250606-2447", "JCY-20250606-2450", "JCY-20250606-2497", "JCY-20250606-2466", "JCY-20250606-2453", "JCY-20250606-2482", "JCY-20250606-2500", "JCY-20250606-2459", "JCY-20250606-2471", "JCY-20250606-2423", "JCY-20250606-2394", "JCY-20250606-2463", "JCY-20250606-2442", "JCY-20250606-2534", "JCY-20250606-2475", "JCY-20250606-2407", "JCY-20250606-2445", "JCY-20250606-2388", "JCY-20250606-2462", "JCY-20250606-2478", "JCY-20250606-2434", "JCY-20250606-2428", "JCY-20250606-2402", "JCY-20250606-2415", "JCY-20250606-2431", "JCY-20250606-2438", "JCY-20250606-2526", "JCY-20250606-2509", "JCY-20250606-2420", "JCY-20250606-2483", "JCY-20250606-2517", "JCY-20250606-2412", "JCY-20250606-2411", "JCY-20250606-2538", "JCY-20250606-2446", "JCY-20250606-2416", "JCY-20250606-2529", "JCY-20250606-2536", "JCY-20250606-2507", "JCY-20250606-2503", "JCY-20250606-2515", "JCY-20250606-2395", "JCY-20250606-2522", "JCY-20250606-2444", "JCY-20250606-2424", "JCY-20250606-2381", "JCY-20250606-2391", "JCY-20250606-2421", "JCY-20250605-2304", "JCY-20250605-2326", "JCY-20250605-2360", "JCY-20250605-2068", "JCY-20250605-2230", "JCY-20250605-2104", "JCY-20250605-2370", "JCY-20250605-2169", "JCY-20250605-2260", "JCY-20250605-2365", "JCY-20250605-2126", "JCY-20250605-2372", "JCY-20250605-2368", "JCY-20250605-2340", "JCY-20250605-2163", "JCY-20250605-2123", "JCY-20250605-2278", "JCY-20250605-2323", "JCY-20250605-2195", "JCY-20250605-2261", "JCY-20250605-2196", "JCY-20250605-2124", "JCY-20250605-2233", "JCY-20250605-2325", "JCY-20250605-2180", "JCY-20250605-2267", "JCY-20250605-2101", "JCY-20250605-2244", "JCY-20250605-2268", "JCY-20250605-2254", "JCY-20250605-2315", "JCY-20250605-2137", "JCY-20250605-2263", "JCY-20250605-2338", "JCY-20250605-2153", "JCY-20250605-2070", "JCY-20250605-2294", "JCY-20250605-2217", "JCY-20250605-2330", "JCY-20250605-2081", "JCY-20250605-2352", "JCY-20250605-2103", "JCY-20250605-2356", "JCY-20250605-2147", "JCY-20250605-2321", "JCY-20250605-2116", "JCY-20250605-2319", "JCY-20250605-2345", "JCY-20250605-2194", "JCY-20250605-2237", "JCY-20250605-2119", "JCY-20250605-2158", "JCY-20250605-2242", "JCY-20250605-2093", "JCY-20250605-2224", "JCY-20250605-2291", "JCY-20250605-2146", "JCY-20250605-2358", "JCY-20250605-2184", "JCY-20250605-2112", "JCY-20250605-2303", "JCY-20250605-2132", "JCY-20250605-2063", "JCY-20250605-2110", "JCY-20250605-2219", "JCY-20250605-2295", "JCY-20250605-2171", "JCY-20250605-2280", "JCY-20250605-2284", "JCY-20250605-2308", "JCY-20250605-2149", "JCY-20250605-2181", "JCY-20250605-2199", "JCY-20250605-2188", "JCY-20250605-2271", "JCY-20250605-2290", "JCY-20250605-2156", "JCY-20250605-2152", "JCY-20250605-2098", "JCY-20250605-2202", "JCY-20250605-2157", "JCY-20250605-2139", "JCY-20250605-2142", "JCY-20250605-2346", "JCY-20250605-2246", "JCY-20250605-2193", "JCY-20250605-2283", "JCY-20250605-2133", "JCY-20250605-2097", "JCY-20250605-2167", "JCY-20250605-2086", "JCY-20250605-2311", "JCY-20250605-2240", "JCY-20250605-2300", "CH6-748822007", "CH6-748788159", "CH7-748820813", "CH-748821996", "CH5-748822412", "CH6-748822025", "CH8-748820813", "CH-1748822007", "CH5-748822007", "CH5-749016945", "CH-749094366", "98709780978", "CH7-748822007", "CH4-749094366", "CH3-748786064", "CH7-748822412", "CH-749095905", "CH5-748786064", "CH4-748787807", "CH6-748822412", "CH6-749094366", "97689870978", "4321431256", "CH6-748821996", "CH-1748786064", "CH3-748822007", "CH-1748820813", "CH4-748821444", "CH7-749094366", "CH3-748818322", "CH8-748818322", "CH8-748821996", "CH6-748788433", "CH5-748821444", "CH5-748821996", "CH7-749016945", "CH-1749095905", "CH4-748821996", "CH4-748818322", "CH6-748786064", "CH-1748821996", "CH8-749094366", "CH4-748822007", "CH5-748820813", "CH8-748822412", "CH-1748821444", "CH-748786064", "CH-748822007", "CH5-749094366", "CH3-748821996", "CH7-748821444", "CH7-748821996", "CH6-749016945", "CH-1748820829", "CH3-749094366", "CH-748821444", "CH4-748822412", "CH-748820813", "CH-1748818322", "CH6-748818322", "CH3-748820813", "CH4-748820813", "CH7-748818322", "CH3-748822025", "CH-748818322", "CH3-748822412"], "current_sequence": 3220}, "label_template": {"current_template_id": "compact_30x20", "current_name": "11111", "current_size": "30x20mm"}, "voltage_standard_voltage": 3.9, "voltage_min_voltage": 3.6999999999999997, "voltage_max_voltage": 4.1, "voltage_tolerance_percent": 5.0, "voltage_auto_calc_range": true, "voltage_battery_type": 1, "outlier_is_enabled": false, "outlier_active_baseline_id": null, "outlier_channel_mode": "average_all", "outlier_deviation_threshold": 5.0, "voltage_tolerance_mode": 1, "voltage_voltage_diff": 0.2, "data_upload": {"enabled": false, "server_url": "http://localhost:5002", "endpoint": "/api/test-results", "timeout": 30, "retry_count": 3, "retry_delay": 1.0, "batch_size": 10, "device_id": "JCY5001A_001", "auto_upload": true, "upload_on_test_complete": true, "upload_batch_results": false, "auth_token": "", "auth_type": "bearer", "auto_auth": true, "username": "admin", "password": "Admin123!", "token_refresh_threshold": 300, "description": "数据上传配置 - 测试完成后自动上传结果到远程服务器，支持自动认证"}}