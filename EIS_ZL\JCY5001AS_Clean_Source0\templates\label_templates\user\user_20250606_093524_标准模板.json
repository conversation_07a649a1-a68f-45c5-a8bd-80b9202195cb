{"template_id": "user_20250606_093524", "name": "标准模板", "description": "用户自定义模板 - 标准模板", "size": "50x30mm", "elements": [{"element_id": "title", "element_type": "text", "x": 15, "y": -11, "width": 200, "height": 24, "content": "JCY5001AS 电池测试", "font_family": "微软雅黑", "font_size": 20, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "battery_code", "element_type": "text", "x": 28, "y": 35, "width": 200, "height": 20, "content": "电池码: {battery_code}", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "channel_voltage", "element_type": "text", "x": 13, "y": 63, "width": 200, "height": 22, "content": "通道: CH{channel_number}    电压: {voltage:.2f}V", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "rs_value", "element_type": "text", "x": 7, "y": 92, "width": 200, "height": 22, "content": "Rs: {rs_value:.3f}mΩ    档位: G{rs_grade}", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "rct_value", "element_type": "text", "x": 8, "y": 125, "width": 200, "height": 22, "content": "Rct: {rct_value:.3f}mΩ   档位: G{rct_grade}", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "test_status", "element_type": "text", "x": 14, "y": 177, "width": 150, "height": 24, "content": "状态: {is_pass}", "font_family": "微软雅黑", "font_size": 20, "font_style": "bold", "text_color": "green", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "timestamp", "element_type": "text", "x": 8, "y": 224, "width": 200, "height": 16, "content": "时间: {timestamp}", "font_family": "微软雅黑", "font_size": 14, "font_style": "bold", "text_color": "gray", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "qr_code", "element_type": "qr_code", "x": 315, "y": 75, "width": 85, "height": 85, "content": "{battery_code}", "font_family": "微软雅黑", "font_size": 14, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}]}