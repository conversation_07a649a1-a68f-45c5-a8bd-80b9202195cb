# JCY5001A 电池阻抗测试系统

## 📋 项目简介

JCY5001A是一个专业的电池交流阻抗测试系统上位机软件，用于锂电池和磷酸铁锂电池的阻抗特性测试与分析。

## 🏗️ 项目结构

```
JCY5001AS_Clean_Source/
├── main.py                 # 主程序入口
├── backend/                # 后端业务逻辑
│   ├── communication_manager.py      # 通信管理
│   ├── test_engine.py               # 测试引擎
│   ├── data_processor.py            # 数据处理
│   ├── eis_analyzer.py              # 阻抗分析
│   └── ...
├── ui/                     # 用户界面
│   ├── main_window.py              # 主窗口
│   ├── components/                 # UI组件
│   ├── dialogs/                    # 对话框
│   └── widgets/                    # 自定义控件
├── utils/                  # 工具模块
│   ├── config_manager.py           # 配置管理
│   ├── logger_helper.py            # 日志工具
│   ├── scan_gun_manager.py         # 扫码枪管理
│   └── docs/                       # 技术文档
├── data/                   # 数据管理
│   ├── database_manager.py         # 数据库管理
│   ├── backup/                     # 数据备份
│   └── samples/                    # 样本数据
├── config/                 # 配置文件
│   ├── app_config.json             # 应用配置
│   └── app_config.example.json     # 配置示例
└── resources/              # 资源文件
    └── styles/                     # 样式文件
```

## 🔧 核心功能

### 测试功能
- **双模式测试**: 研究模式(0.01Hz-7.8kHz)和生产模式
- **8通道并行**: 支持8通道同时测试，错频启动防干扰
- **实时监控**: 电池连接检测、电压监控、测试进度显示
- **智能判断**: Rs/Rct参数分级、电压范围检测、异常值剔除

### 数据管理
- **数据存储**: SQLite数据库，支持测试结果和详细数据
- **数据分析**: Nyquist图显示、多通道对比、历史数据查询
- **数据导出**: Excel格式导出，支持结果数据和详细数据

### 用户界面
- **生产界面**: 8通道2x4网格布局，实时状态显示
- **设备设置**: 串口配置、通道使能、测试参数设置
- **学习功能**: 基准样本管理、异常值检测、中位值分析

### 扩展功能
- **扫码支持**: USB扫码枪集成，电池码自动识别
- **标签打印**: NIIMBOT打印机支持，多模板设计
- **权限管理**: 用户登录、试用期管理、功能授权

## 🚀 快速开始

### 环境要求
- Python 3.10+
- PyQt5
- 其他依赖见requirements.txt

### 安装步骤
1. 克隆项目到本地
2. 安装Python依赖：`pip install -r requirements.txt`
3. 配置应用设置：复制`config/app_config.example.json`为`config/app_config.json`
4. 运行程序：`python main.py`

### 硬件连接
- 阻抗测试设备：COM16端口，115200波特率，RS422通信
- 扫码枪：USB HID设备，自动识别
- 标签打印机：NIIMBOT K3_W，USB连接

## 📊 技术特点

### 通信协议
- **Modbus RTU**: 标准工业通信协议
- **命令集**: 05H单通道测试、0F00H全通道启动、状态轮询
- **错频机制**: 高频测试500ms间隔启动，防止通道间干扰

### 数据处理
- **阻抗计算**: Rs零虚部交点法、Rct峰值点分析
- **单位转换**: 微欧(μΩ)原始数据，界面显示毫欧(mΩ)
- **异常检测**: Z值偏差分析，可配置阈值

### 界面设计
- **响应式布局**: 自适应窗口大小，支持全屏显示
- **实时更新**: 测试状态、进度显示、电压监控
- **用户体验**: 快捷键支持、自动确认、状态同步

## 🔍 代码质量

### 架构设计
- **模块化**: 清晰的模块划分，单一职责原则
- **解耦合**: 业务逻辑与界面分离，便于维护
- **可扩展**: 插件化设计，支持功能扩展

### 代码规范
- **命名规范**: 统一的变量和函数命名
- **注释完整**: 关键逻辑详细注释
- **异常处理**: 完善的错误处理机制

### 性能优化
- **多线程**: 测试任务异步执行，界面响应流畅
- **内存管理**: 及时释放资源，避免内存泄漏
- **数据库优化**: 索引优化，批量操作

## 📈 版本历史

- **v1.0**: 基础测试功能，单通道测试
- **v2.0**: 8通道并行，错频启动
- **v3.0**: 扫码功能，标签打印
- **v4.0**: 学习功能，异常检测
- **v5.0**: 界面优化，用户体验提升

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request

## 📄 许可证

本项目采用专有许可证，仅供授权用户使用。

## 📞 技术支持

如有技术问题，请联系开发团队。

---

**注意**: 这是一个干净的源代码版本，已移除所有构建产物、测试文件和调试脚本。