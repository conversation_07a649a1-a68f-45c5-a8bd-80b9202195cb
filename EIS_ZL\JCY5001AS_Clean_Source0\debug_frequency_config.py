#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试频率配置脚本
检查程序中所有可能影响频率数量的配置和逻辑
"""

import json
import os
import sys

def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'app_config.json')
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return None

def check_frequency_configs(config):
    """检查所有频率相关配置"""
    print("🔍 检查频率配置...")
    print("=" * 80)
    
    # 1. 基本频率配置
    frequency_config = config.get('frequency', {})
    print(f"📋 预设模式: {frequency_config.get('preset_mode', 'N/A')}")
    print(f"📋 频率模式: {frequency_config.get('mode', 'N/A')}")
    
    # 2. 频率列表
    frequency_list = frequency_config.get('list', [])
    print(f"📊 frequency.list: {len(frequency_list)}个频点")
    if frequency_list:
        print(f"   前5个: {frequency_list[:5]}")
        print(f"   后5个: {frequency_list[-5:]}")
    
    # 3. 自定义频率列表
    custom_list = frequency_config.get('multi_freq', {}).get('custom_list', [])
    print(f"📊 frequency.multi_freq.custom_list: {len(custom_list)}个频点")
    if custom_list:
        print(f"   前5个: {custom_list[:5]}")
        print(f"   后5个: {custom_list[-5:]}")
    
    # 4. 测试配置
    test_config = config.get('test', {})
    print(f"📋 并行错频模式: {test_config.get('use_parallel_staggered_mode', 'N/A')}")
    print(f"📋 临界频率: {test_config.get('critical_frequency', 'N/A')}Hz")
    print(f"📋 最大测试次数: {test_config.get('max_count', 'N/A')}")
    print(f"📋 计数限制启用: {test_config.get('count_limit_enabled', 'N/A')}")
    
    # 5. 检查配置一致性
    print("\n🔧 配置一致性检查:")
    if frequency_list == custom_list:
        print("✅ frequency.list 和 custom_list 配置一致")
    else:
        print("❌ frequency.list 和 custom_list 配置不一致!")
        print(f"   frequency.list: {len(frequency_list)}个频点")
        print(f"   custom_list: {len(custom_list)}个频点")
    
    return frequency_list, custom_list, test_config

def simulate_test_config_manager(config):
    """模拟测试配置管理器的频率读取"""
    print("\n🔧 模拟 test_config_manager.py 频率读取:")
    print("-" * 60)
    
    # 模拟 test_config_manager.py 的逻辑
    frequency_config = config.get('frequency', {})
    frequency_mode = frequency_config.get('mode', 'multi')
    
    if frequency_mode == 'single':
        single_freq = frequency_config.get('single_freq', 1007.0827)
        frequencies = [single_freq]
        print(f"📊 单频模式: {frequencies}")
    else:
        # 从 frequency.list 读取
        frequencies = frequency_config.get('list', [])
        print(f"📊 多频模式 (从frequency.list): {len(frequencies)}个频点")
        if frequencies:
            print(f"   完整列表: {frequencies}")
    
    return frequencies

def simulate_test_control_widget(config):
    """模拟测试控制组件的频率读取"""
    print("\n🔧 模拟 test_control_widget.py 频率读取:")
    print("-" * 60)
    
    # 模拟修复后的 test_control_widget.py 逻辑
    frequency_config = config.get('frequency', {})
    frequency_mode = frequency_config.get('mode', 'multi')
    
    if frequency_mode == 'single':
        single_freq = frequency_config.get('single_freq', 1007.0827)
        frequencies = [single_freq]
        print(f"📊 单频模式: {frequencies}")
    else:
        # 优先从 frequency.list 读取
        frequencies = frequency_config.get('list', [])
        if not frequencies:
            # 备用：从custom_list读取
            frequencies = frequency_config.get('multi_freq', {}).get('custom_list', [])
            print(f"📊 多频模式 (从custom_list备用): {len(frequencies)}个频点")
        else:
            print(f"📊 多频模式 (从frequency.list): {len(frequencies)}个频点")
        
        if frequencies:
            print(f"   完整列表: {frequencies}")
    
    return frequencies

def check_parallel_staggered_impact(config, frequencies):
    """检查并行错频模式的影响"""
    print("\n🔧 检查并行错频模式影响:")
    print("-" * 60)
    
    test_config = config.get('test', {})
    use_parallel = test_config.get('use_parallel_staggered_mode', False)
    critical_freq = test_config.get('critical_frequency', 15.0)
    
    print(f"📋 并行错频模式: {'启用' if use_parallel else '禁用'}")
    print(f"📋 临界频率: {critical_freq}Hz")
    
    if use_parallel and frequencies:
        # 分析频点分组
        high_freq = [f for f in frequencies if f >= critical_freq]
        low_freq = [f for f in frequencies if f < critical_freq]
        
        print(f"📊 高频组 (>={critical_freq}Hz): {len(high_freq)}个频点")
        if high_freq:
            print(f"   {high_freq}")
        
        print(f"📊 低频组 (<{critical_freq}Hz): {len(low_freq)}个频点")
        if low_freq:
            print(f"   {low_freq}")
        
        print(f"⚠️  并行错频模式可能导致只测试其中一组频点！")
    else:
        print(f"✅ 并行错频模式已禁用，应该测试所有{len(frequencies)}个频点")

def main():
    """主函数"""
    print("🚀 频率配置调试工具")
    print("=" * 80)
    
    # 加载配置
    config = load_config()
    if not config:
        sys.exit(1)
    
    # 检查频率配置
    frequency_list, custom_list, test_config = check_frequency_configs(config)
    
    # 模拟不同组件的频率读取
    tcm_frequencies = simulate_test_config_manager(config)
    tcw_frequencies = simulate_test_control_widget(config)
    
    # 检查并行错频模式影响
    check_parallel_staggered_impact(config, tcm_frequencies)
    
    # 总结
    print("\n🎯 总结:")
    print("=" * 80)
    print(f"📊 配置文件中的频点数量:")
    print(f"   frequency.list: {len(frequency_list)}个")
    print(f"   custom_list: {len(custom_list)}个")
    print(f"📊 组件读取的频点数量:")
    print(f"   test_config_manager: {len(tcm_frequencies)}个")
    print(f"   test_control_widget: {len(tcw_frequencies)}个")
    print(f"📋 并行错频模式: {'启用' if test_config.get('use_parallel_staggered_mode') else '禁用'}")
    
    if len(tcm_frequencies) == len(tcw_frequencies) == 20:
        if not test_config.get('use_parallel_staggered_mode'):
            print("✅ 配置正确，应该能测试20个频点！")
        else:
            print("⚠️  并行错频模式启用，可能影响测试频点数量")
    else:
        print("❌ 配置不一致，需要进一步检查")

if __name__ == "__main__":
    main()
