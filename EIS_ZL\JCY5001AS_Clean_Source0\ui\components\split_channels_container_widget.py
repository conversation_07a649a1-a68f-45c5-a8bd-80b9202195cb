# -*- coding: utf-8 -*-
"""
分行8通道容器组件
管理8个通道的显示，分为两行显示（第一行4个通道，第二行4个通道）

Author: Jack
Date: 2025-06-03
"""

from PyQt5.QtWidgets import (
    QWidget, QGridLayout
)
from PyQt5.QtCore import pyqtSignal
from typing import Optional
import logging

logger = logging.getLogger(__name__)

from utils.config_manager import ConfigManager
from ui.components.channel_display_widget import ChannelDisplayWidget

# 常量定义
CHANNEL_COUNT = 8  # 通道总数
GRID_ROWS = 2      # 网格行数
GRID_COLS = 4      # 网格列数
LAYOUT_MARGIN = 5  # 布局边距
LAYOUT_SPACING = 5 # 布局间距


class SplitChannelsContainerWidget(QWidget):
    """分行8通道容器组件"""

    # 信号定义
    channel_test_completed = pyqtSignal(int, dict)  # 通道测试完成信号
    channel_battery_code_changed = pyqtSignal(int, str)  # 通道电池码变更信号
    all_channels_ready = pyqtSignal()  # 所有通道准备就绪信号

    def __init__(self, config_manager: ConfigManager, row1_container, row2_container, parent=None):
        """
        初始化分行8通道容器

        Args:
            config_manager: 配置管理器
            row1_container: 第一行容器
            row2_container: 第二行容器
            parent: 父窗口
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.row1_container = row1_container
        self.row2_container = row2_container
        self.channels = []  # 通道组件列表

        # 初始化界面
        self._init_ui()
        self._connect_signals()

        logger.debug("分行8通道容器组件初始化完成")

    def _is_valid_channel(self, channel_number: int) -> bool:
        """
        验证通道号是否有效

        Args:
            channel_number: 通道号

        Returns:
            是否有效
        """
        return 1 <= channel_number <= len(self.channels)

    def _init_ui(self):
        """初始化用户界面"""
        # 创建第一行布局（通道1-4）
        row1_layout = QGridLayout(self.row1_container)
        row1_layout.setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN)
        row1_layout.setSpacing(LAYOUT_SPACING)

        # 创建第二行布局（通道5-8）
        row2_layout = QGridLayout(self.row2_container)
        row2_layout.setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN)
        row2_layout.setSpacing(LAYOUT_SPACING)

        # 创建通道组件
        for i in range(CHANNEL_COUNT):
            channel_number = i + 1
            channel = ChannelDisplayWidget(channel_number, self.config_manager)
            self.channels.append(channel)

            # 计算位置
            if i < 4:  # 第一行（通道1-4）
                col = i
                row1_layout.addWidget(channel, 0, col)
                # 设置列拉伸
                row1_layout.setColumnStretch(col, 1)
            else:  # 第二行（通道5-8）
                col = i - 4
                row2_layout.addWidget(channel, 0, col)
                # 设置列拉伸
                row2_layout.setColumnStretch(col, 1)

        # 设置行拉伸
        row1_layout.setRowStretch(0, 1)
        row2_layout.setRowStretch(0, 1)

    def _connect_signals(self):
        """连接信号"""
        try:
            for channel in self.channels:
                # 连接通道信号
                channel.test_completed.connect(self._on_channel_test_completed)
                channel.battery_code_changed.connect(self._on_channel_battery_code_changed)

        except Exception as e:
            logger.error(f"连接通道信号失败: {e}")

    def _on_channel_test_completed(self, channel_num: int, result_data: dict):
        """通道测试完成处理"""
        try:
            logger.debug(f"通道{channel_num}测试完成")
            
            # 转发信号
            self.channel_test_completed.emit(channel_num, result_data)
            
            # 检查是否所有通道都完成测试
            self._check_all_channels_ready()
            
        except Exception as e:
            logger.error(f"处理通道{channel_num}测试完成失败: {e}")

    def _on_channel_battery_code_changed(self, channel_num: int, battery_code: str):
        """通道电池码变更处理"""
        try:
            # 通道电池码变更 - 运行时不输出日志
            pass
            
            # 转发信号
            self.channel_battery_code_changed.emit(channel_num, battery_code)
            
        except Exception as e:
            logger.error(f"处理通道{channel_num}电池码变更失败: {e}")

    def _check_all_channels_ready(self):
        """检查所有通道是否准备就绪"""
        try:
            # 检查所有通道是否都完成测试
            all_ready = True
            for channel in self.channels:
                if hasattr(channel, 'is_test_completed') and not channel.is_test_completed():
                    all_ready = False
                    break
            
            if all_ready:
                logger.info("所有通道测试完成")
                self.all_channels_ready.emit()
                
        except Exception as e:
            logger.error(f"检查所有通道状态失败: {e}")

    def get_channel(self, channel_number: int) -> Optional[ChannelDisplayWidget]:
        """
        获取指定通道组件

        Args:
            channel_number: 通道号

        Returns:
            通道组件或None
        """
        if self._is_valid_channel(channel_number):
            return self.channels[channel_number - 1]
        return None

    def update_channel_progress(self, channel_num: int, progress_data: dict):
        """
        更新通道测试进度

        Args:
            channel_num: 通道号
            progress_data: 进度数据
        """
        try:
            channel = self.get_channel(channel_num)
            if channel:
                # 🔧 修复：添加频点信息处理逻辑
                # 提取频点信息
                frequency = progress_data.get('frequency', 0)
                frequency_index = progress_data.get('frequency_index', 0)
                total_frequencies = progress_data.get('total_frequencies', 0)
                state = progress_data.get('state', 'unknown')

                # 更新通道测试进度
                channel.update_test_progress(progress_data)

                # 🔧 修复：如果有频点信息，更新频点显示
                if frequency > 0 and hasattr(channel, 'update_frequency_info'):
                    # 检查是否为错频模式
                    mode = progress_data.get('mode', 'unknown')

                    if mode == 'staggered':
                        # 错频模式：记录通道的专用频率，防止被统一更新覆盖
                        if not hasattr(self, '_staggered_channel_frequencies'):
                            self._staggered_channel_frequencies = {}

                        self._staggered_channel_frequencies[channel_num] = frequency
                        logger.debug(f"🔧 错频模式: 通道{channel_num}专用频率{frequency}Hz")

                    # 调用频率更新
                    channel.update_frequency_info(frequency, frequency_index, total_frequencies, state)
            else:
                logger.warning(f"通道{channel_num}不存在")

        except Exception as e:
            logger.error(f"更新通道{channel_num}进度失败: {e}")

    def update_channel_outlier_rate_result(self, channel_num: int, outlier_result: dict, 
                                         baseline_filename: str, frequency_deviations: dict, is_final: bool):
        """
        更新通道离群率结果

        Args:
            channel_num: 通道号
            outlier_result: 离群率结果
            baseline_filename: 基线文件名
            frequency_deviations: 频率偏差
            is_final: 是否为最终结果
        """
        try:
            channel = self.get_channel(channel_num)
            if channel and hasattr(channel, 'update_outlier_rate_result'):
                channel.update_outlier_rate_result(outlier_result, baseline_filename, frequency_deviations, is_final)
            else:
                logger.warning(f"通道{channel_num}不存在或不支持离群率更新")

        except Exception as e:
            logger.error(f"更新通道{channel_num}离群率结果失败: {e}")

    def set_channel_enabled(self, channel_num: int, enabled: bool):
        """
        设置通道启用状态

        Args:
            channel_num: 通道号
            enabled: 是否启用
        """
        try:
            channel = self.get_channel(channel_num)
            if channel:
                channel.set_enabled(enabled)
            else:
                logger.warning(f"通道{channel_num}不存在")

        except Exception as e:
            logger.error(f"设置通道{channel_num}启用状态失败: {e}")

    def update_all_outlier_detection_status(self, enabled: bool):
        """
        更新所有通道的离群检测状态

        Args:
            enabled: 是否启用离群检测
        """
        try:
            for channel in self.channels:
                if hasattr(channel, 'set_outlier_detection_enabled'):
                    channel.set_outlier_detection_enabled(enabled)

        except Exception as e:
            logger.error(f"更新所有通道离群检测状态失败: {e}")

    def reset_all_channels(self):
        """重置所有通道状态"""
        try:
            for channel in self.channels:
                if hasattr(channel, 'reset_channel'):
                    channel.reset_channel()

        except Exception as e:
            logger.error(f"重置所有通道失败: {e}")

    def get_all_channel_data(self) -> dict:
        """
        获取所有通道数据

        Returns:
            所有通道数据字典
        """
        try:
            data = {}
            for i, channel in enumerate(self.channels):
                channel_num = i + 1
                if hasattr(channel, 'get_channel_data'):
                    data[channel_num] = channel.get_channel_data()
                else:
                    data[channel_num] = {}

            return data

        except Exception as e:
            logger.error(f"获取所有通道数据失败: {e}")
            return {}

    def set_channel_battery_code(self, channel_number: int, battery_code: str):
        """
        设置指定通道的电池码

        Args:
            channel_number: 通道号 (1-8)
            battery_code: 电池码
        """
        try:
            channel_widget = self.get_channel(channel_number)
            if channel_widget and hasattr(channel_widget, 'battery_code_edit'):
                channel_widget.battery_code_edit.setText(battery_code)
                # 通道电池码已设置 - 运行时不输出日志
                pass
            else:
                logger.warning(f"❌ 无效的通道号或通道组件: {channel_number}")

        except Exception as e:
            logger.error(f"❌ 设置通道{channel_number}电池码失败: {e}")

    def get_channel_battery_codes(self) -> list:
        """
        获取所有通道的电池码

        Returns:
            电池码列表 (8个元素，对应通道1-8)
        """
        battery_codes = [""] * CHANNEL_COUNT

        try:
            for i, channel in enumerate(self.channels):
                if hasattr(channel, 'battery_code_edit'):
                    battery_codes[i] = channel.battery_code_edit.text().strip()

        except Exception as e:
            logger.error(f"获取通道电池码失败: {e}")

        return battery_codes

    def get_enabled_channels(self) -> list:
        """
        获取启用的通道列表

        Returns:
            启用的通道号列表
        """
        enabled_channels = []

        try:
            for i, channel in enumerate(self.channels):
                channel_num = i + 1
                if hasattr(channel, 'is_enabled') and channel.is_enabled:
                    enabled_channels.append(channel_num)
                elif not hasattr(channel, 'is_enabled'):
                    # 如果没有is_enabled属性，默认认为启用
                    enabled_channels.append(channel_num)

        except Exception as e:
            logger.error(f"获取启用通道列表失败: {e}")
            # 返回默认的所有通道
            enabled_channels = list(range(1, CHANNEL_COUNT + 1))

        return enabled_channels

    def update_channel_test_count(self, channel_num: int, count: int):
        """
        更新指定通道的测试计数显示
        
        Args:
            channel_num: 通道号
            count: 测试计数
        """
        try:
            channel = self.get_channel(channel_num)
            if channel:
                # 检查通道是否有测试计数更新方法
                if hasattr(channel, 'update_test_count'):
                    channel.update_test_count(count)
                    logger.debug(f"✅ 通道{channel_num}测试计数已更新: {count}")
                elif hasattr(channel, 'set_test_count'):
                    channel.set_test_count(count)
                    logger.debug(f"✅ 通道{channel_num}测试计数已设置: {count}")
                else:
                    logger.debug(f"⚠️ 通道{channel_num}不支持测试计数更新")
            else:
                logger.warning(f"❌ 通道{channel_num}不存在")
                
        except Exception as e:
            logger.error(f"❌ 更新通道{channel_num}测试计数失败: {e}")

    def refresh_all_test_counts(self):
        """刷新所有通道的测试计数显示"""
        try:
            for i in range(CHANNEL_COUNT):
                channel_num = i + 1
                # 从配置获取测试计数
                count = self.config_manager.get(f'test_count.channel_{channel_num}', 0)
                self.update_channel_test_count(channel_num, count)
            
            logger.info("✅ 所有通道测试计数已刷新")
            
        except Exception as e:
            logger.error(f"❌ 刷新所有通道测试计数失败: {e}")
