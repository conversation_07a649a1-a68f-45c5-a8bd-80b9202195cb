#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成可编辑的专利附图文件
支持SVG、ODG等可编辑格式，避免字体乱码问题

Author: Augment Agent
Date: 2025-01-09
"""

import os
import xml.etree.ElementTree as ET
from xml.dom import minidom

class EditableFigureGenerator:
    """可编辑图形生成器"""
    
    def __init__(self, output_dir="editable_figures"):
        """初始化生成器"""
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def create_svg_figure1(self):
        """创建图1的SVG版本"""
        # 创建SVG根元素
        svg = ET.Element('svg')
        svg.set('width', '1400')
        svg.set('height', '1000')
        svg.set('viewBox', '0 0 1400 1000')
        svg.set('xmlns', 'http://www.w3.org/2000/svg')
        
        # 添加样式定义
        style = ET.SubElement(svg, 'style')
        style.text = """
        .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
        .component-text { font-family: Arial, sans-serif; font-size: 16px; text-anchor: middle; }
        .label-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
        .param-text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; font-style: italic; }
        .flow-text { font-family: Arial, sans-serif; font-size: 12px; }
        """
        
        # 标题
        title = ET.SubElement(svg, 'text')
        title.set('x', '700')
        title.set('y', '50')
        title.set('class', 'title')
        title.text = '图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图'
        
        # 组件定义
        components = [
            # (x, y, width, height, color, text, text_lines)
            (100, 150, 200, 120, '#ADD8E6', '1-被测电池', ['1-被测电池', '1.9V-5.5V']),
            (100, 350, 200, 120, '#F5DEB3', '7-测试夹具', ['7-测试夹具', '四线制连接', '精确测量']),
            (450, 300, 300, 200, '#90EE90', '2-DNB1101BB', ['2-DNB1101BB', 'EIS测试芯片', '0.0075Hz-7800Hz']),
            (100, 600, 200, 150, '#F08080', '4-外部电流源', ['4-外部电流源', 'PMV28UNEA', '20Ω/10Ω/6.67Ω/5Ω']),
            (850, 300, 250, 200, '#FFFFE0', '3-STM32F103RCT6', ['3-STM32F103RCT6', '主控制器', '72MHz ARM']),
            (850, 600, 250, 150, '#D3D3D3', '5-串口显示屏', ['5-串口显示屏', '实时显示', '测试结果']),
            (850, 100, 250, 120, '#B0C4DE', '6-PC上位机', ['6-PC上位机', 'Modbus RTU', '数据分析'])
        ]
        
        # 绘制组件
        for x, y, w, h, color, id_text, text_lines in components:
            # 绘制矩形
            rect = ET.SubElement(svg, 'rect')
            rect.set('x', str(x))
            rect.set('y', str(y))
            rect.set('width', str(w))
            rect.set('height', str(h))
            rect.set('fill', color)
            rect.set('stroke', 'black')
            rect.set('stroke-width', '2')
            rect.set('rx', '10')
            
            # 添加文本
            for i, line in enumerate(text_lines):
                text = ET.SubElement(svg, 'text')
                text.set('x', str(x + w/2))
                text.set('y', str(y + h/2 - (len(text_lines)-1)*10 + i*20))
                text.set('class', 'component-text')
                if i == 0:
                    text.set('font-weight', 'bold')
                text.text = line
        
        # 添加箭头连接
        arrows = [
            # 电池 ↔ 测试夹具
            {'x1': 200, 'y1': 270, 'x2': 200, 'y2': 350, 'color': 'black', 'type': 'double', 'label': '电气连接'},
            # 测试夹具 → DNB1101BB
            {'x1': 300, 'y1': 410, 'x2': 450, 'y2': 400, 'color': 'blue', 'type': 'single', 'label': '电压/电流测量信号'},
            # DNB1101BB → STM32
            {'x1': 750, 'y1': 400, 'x2': 850, 'y2': 400, 'color': 'purple', 'type': 'single', 'label': 'SPI 1Mbps'},
            # STM32 → PC
            {'x1': 975, 'y1': 300, 'x2': 975, 'y2': 220, 'color': 'red', 'type': 'single', 'label': 'USB/UART'},
            # STM32 → 显示屏
            {'x1': 975, 'y1': 500, 'x2': 975, 'y2': 600, 'color': 'green', 'type': 'single', 'label': 'UART 115200bps'},
            # DNB1101BB → 电流源
            {'x1': 500, 'y1': 500, 'x2': 300, 'y2': 650, 'color': 'orange', 'type': 'single', 'label': 'VSW/VDR控制信号'},
            # 电流源 → 测试夹具
            {'x1': 200, 'y1': 600, 'x2': 200, 'y2': 470, 'color': 'red', 'type': 'single', 'label': '激励电流'}
        ]
        
        # 绘制箭头
        for arrow in arrows:
            self._draw_arrow_svg(svg, arrow)
        
        # 添加信号流向说明
        flow_box = ET.SubElement(svg, 'rect')
        flow_box.set('x', '400')
        flow_box.set('y', '800')
        flow_box.set('width', '600')
        flow_box.set('height', '120')
        flow_box.set('fill', '#FFFACD')
        flow_box.set('stroke', 'gray')
        flow_box.set('stroke-width', '1')
        flow_box.set('rx', '5')
        
        flow_texts = [
            '信号流向说明：',
            '1. 电池通过测试夹具连接到系统',
            '2. DNB1101BB芯片测量电池的电压和电流',
            '3. 外部电流源提供EIS测试所需的激励信号',
            '4. STM32控制器处理测试数据和系统控制',
            '5. 测试结果同时显示在本地屏幕和上位机'
        ]
        
        for i, text in enumerate(flow_texts):
            flow_text = ET.SubElement(svg, 'text')
            flow_text.set('x', '420')
            flow_text.set('y', str(820 + i*15))
            flow_text.set('class', 'flow-text')
            if i == 0:
                flow_text.set('font-weight', 'bold')
            flow_text.text = text
        
        # 技术参数
        param_text = ET.SubElement(svg, 'text')
        param_text.set('x', '700')
        param_text.set('y', '970')
        param_text.set('class', 'param-text')
        param_text.text = '系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C'
        
        # 保存SVG文件
        self._save_svg(svg, 'figure1_system_architecture.svg')
        print("图1 SVG版本生成完成")
    
    def _draw_arrow_svg(self, svg, arrow):
        """绘制SVG箭头"""
        x1, y1, x2, y2 = arrow['x1'], arrow['y1'], arrow['x2'], arrow['y2']
        color = arrow['color']
        
        # 绘制线条
        line = ET.SubElement(svg, 'line')
        line.set('x1', str(x1))
        line.set('y1', str(y1))
        line.set('x2', str(x2))
        line.set('y2', str(y2))
        line.set('stroke', color)
        line.set('stroke-width', '2')
        
        # 计算箭头方向
        import math
        dx = x2 - x1
        dy = y2 - y1
        length = math.sqrt(dx*dx + dy*dy)
        if length > 0:
            dx /= length
            dy /= length
        
        # 绘制箭头头部
        arrow_length = 10
        arrow_width = 5
        
        # 箭头点
        points = [
            (x2, y2),
            (x2 - arrow_length*dx + arrow_width*dy, y2 - arrow_length*dy - arrow_width*dx),
            (x2 - arrow_length*dx - arrow_width*dy, y2 - arrow_length*dy + arrow_width*dx)
        ]
        
        arrow_head = ET.SubElement(svg, 'polygon')
        arrow_head.set('points', ' '.join([f"{x},{y}" for x, y in points]))
        arrow_head.set('fill', color)
        
        # 如果是双向箭头，在起点也画箭头
        if arrow.get('type') == 'double':
            points2 = [
                (x1, y1),
                (x1 + arrow_length*dx + arrow_width*dy, y1 + arrow_length*dy - arrow_width*dx),
                (x1 + arrow_length*dx - arrow_width*dy, y1 + arrow_length*dy + arrow_width*dx)
            ]
            arrow_head2 = ET.SubElement(svg, 'polygon')
            arrow_head2.set('points', ' '.join([f"{x},{y}" for x, y in points2]))
            arrow_head2.set('fill', color)
        
        # 添加标签
        if 'label' in arrow:
            label_x = (x1 + x2) / 2
            label_y = (y1 + y2) / 2 - 10
            label = ET.SubElement(svg, 'text')
            label.set('x', str(label_x))
            label.set('y', str(label_y))
            label.set('class', 'label-text')
            label.set('fill', color)
            label.text = arrow['label']
    
    def _save_svg(self, svg_element, filename):
        """保存SVG文件"""
        # 转换为字符串并格式化
        rough_string = ET.tostring(svg_element, 'unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_string = reparsed.toprettyxml(indent="  ")
        
        # 移除空行
        lines = [line for line in pretty_string.split('\n') if line.strip()]
        pretty_string = '\n'.join(lines)
        
        # 保存文件
        filepath = os.path.join(self.output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(pretty_string)
        
        print(f"SVG文件已保存: {filepath}")
    
    def create_drawio_xml(self):
        """创建Draw.io格式的XML文件"""
        # Draw.io XML格式
        mxfile = ET.Element('mxfile')
        mxfile.set('host', 'app.diagrams.net')
        mxfile.set('modified', '2025-01-09T00:00:00.000Z')
        mxfile.set('agent', 'Patent Figure Generator')

        diagram = ET.SubElement(mxfile, 'diagram')
        diagram.set('id', 'figure1')
        diagram.set('name', '图1-系统架构图')

        # 创建图形数据
        mxGraphModel = ET.SubElement(diagram, 'mxGraphModel')
        mxGraphModel.set('dx', '1426')
        mxGraphModel.set('dy', '827')
        mxGraphModel.set('grid', '1')
        mxGraphModel.set('gridSize', '10')

        root = ET.SubElement(mxGraphModel, 'root')

        # 添加默认层
        layer0 = ET.SubElement(root, 'mxCell')
        layer0.set('id', '0')

        layer1 = ET.SubElement(root, 'mxCell')
        layer1.set('id', '1')
        layer1.set('parent', '0')

        # 添加组件
        components_drawio = [
            {'id': '2', 'value': '1-被测电池\n1.9V-5.5V', 'x': '100', 'y': '150', 'width': '200', 'height': '120', 'fillColor': '#ADD8E6'},
            {'id': '3', 'value': '7-测试夹具\n四线制连接\n精确测量', 'x': '100', 'y': '350', 'width': '200', 'height': '120', 'fillColor': '#F5DEB3'},
            {'id': '4', 'value': '2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz', 'x': '450', 'y': '300', 'width': '300', 'height': '200', 'fillColor': '#90EE90'},
            {'id': '5', 'value': '4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω', 'x': '100', 'y': '600', 'width': '200', 'height': '150', 'fillColor': '#F08080'},
            {'id': '6', 'value': '3-STM32F103RCT6\n主控制器\n72MHz ARM', 'x': '850', 'y': '300', 'width': '250', 'height': '200', 'fillColor': '#FFFFE0'},
            {'id': '7', 'value': '5-串口显示屏\n实时显示\n测试结果', 'x': '850', 'y': '600', 'width': '250', 'height': '150', 'fillColor': '#D3D3D3'},
            {'id': '8', 'value': '6-PC上位机\nModbus RTU\n数据分析', 'x': '850', 'y': '100', 'width': '250', 'height': '120', 'fillColor': '#B0C4DE'}
        ]

        for comp in components_drawio:
            cell = ET.SubElement(root, 'mxCell')
            cell.set('id', comp['id'])
            cell.set('value', comp['value'])
            cell.set('style', f"rounded=1;whiteSpace=wrap;html=1;fillColor={comp['fillColor']};strokeColor=#000000;strokeWidth=2;")
            cell.set('vertex', '1')
            cell.set('parent', '1')

            geometry = ET.SubElement(cell, 'mxGeometry')
            geometry.set('x', comp['x'])
            geometry.set('y', comp['y'])
            geometry.set('width', comp['width'])
            geometry.set('height', comp['height'])
            geometry.set('as', 'geometry')

        # 保存Draw.io文件
        self._save_xml(mxfile, 'figure1_system_architecture.drawio')
        print("Draw.io XML文件生成完成")

    def _save_xml(self, xml_element, filename):
        """保存XML文件"""
        rough_string = ET.tostring(xml_element, 'unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_string = reparsed.toprettyxml(indent="  ")

        filepath = os.path.join(self.output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(pretty_string)

        print(f"XML文件已保存: {filepath}")

    def create_html_editable(self):
        """创建HTML可编辑版本"""
        html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { width: 1400px; height: 1000px; position: relative; border: 1px solid #ccc; }
        .component { position: absolute; border: 2px solid black; border-radius: 10px;
                    display: flex; align-items: center; justify-content: center;
                    text-align: center; font-weight: bold; cursor: move; }
        .title { text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 20px; }
        .battery { background-color: #ADD8E6; left: 100px; top: 150px; width: 200px; height: 120px; }
        .fixture { background-color: #F5DEB3; left: 100px; top: 350px; width: 200px; height: 120px; }
        .chip { background-color: #90EE90; left: 450px; top: 300px; width: 300px; height: 200px; }
        .current-source { background-color: #F08080; left: 100px; top: 600px; width: 200px; height: 150px; }
        .mcu { background-color: #FFFFE0; left: 850px; top: 300px; width: 250px; height: 200px; }
        .display { background-color: #D3D3D3; left: 850px; top: 600px; width: 250px; height: 150px; }
        .pc { background-color: #B0C4DE; left: 850px; top: 100px; width: 250px; height: 120px; }
        .arrow { position: absolute; }
        .flow-description { position: absolute; left: 400px; top: 800px; width: 600px;
                           background-color: #FFFACD; border: 1px solid gray; padding: 10px;
                           border-radius: 5px; }
        .tech-params { position: absolute; left: 400px; top: 970px; width: 600px;
                      text-align: center; font-style: italic; font-weight: bold; }
    </style>
</head>
<body>
    <div class="title">图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图</div>

    <div class="container">
        <div class="component battery">1-被测电池<br>1.9V-5.5V</div>
        <div class="component fixture">7-测试夹具<br>四线制连接<br>精确测量</div>
        <div class="component chip">2-DNB1101BB<br>EIS测试芯片<br>0.0075Hz-7800Hz</div>
        <div class="component current-source">4-外部电流源<br>PMV28UNEA<br>20Ω/10Ω/6.67Ω/5Ω</div>
        <div class="component mcu">3-STM32F103RCT6<br>主控制器<br>72MHz ARM</div>
        <div class="component display">5-串口显示屏<br>实时显示<br>测试结果</div>
        <div class="component pc">6-PC上位机<br>Modbus RTU<br>数据分析</div>

        <!-- 这里可以添加SVG箭头 -->
        <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0; pointer-events: none;">
            <!-- 电池 ↔ 测试夹具 -->
            <line x1="200" y1="270" x2="200" y2="350" stroke="black" stroke-width="2" marker-end="url(#arrowhead)" marker-start="url(#arrowhead)"/>
            <text x="220" y="310" fill="black" font-size="12">电气连接</text>

            <!-- 测试夹具 → DNB1101BB -->
            <line x1="300" y1="410" x2="450" y2="400" stroke="blue" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
            <text x="375" y="395" fill="blue" font-size="12">电压/电流测量信号</text>

            <!-- DNB1101BB → STM32 -->
            <line x1="750" y1="400" x2="850" y2="400" stroke="purple" stroke-width="2" marker-end="url(#arrowhead-purple)"/>
            <text x="800" y="390" fill="purple" font-size="12">SPI 1Mbps</text>

            <!-- STM32 → PC -->
            <line x1="975" y1="300" x2="975" y2="220" stroke="red" stroke-width="2" marker-end="url(#arrowhead-red)"/>
            <text x="985" y="260" fill="red" font-size="12">USB/UART</text>

            <!-- STM32 → 显示屏 -->
            <line x1="975" y1="500" x2="975" y2="600" stroke="green" stroke-width="2" marker-end="url(#arrowhead-green)"/>
            <text x="985" y="550" fill="green" font-size="12">UART 115200bps</text>

            <!-- DNB1101BB → 电流源 -->
            <line x1="500" y1="500" x2="300" y2="650" stroke="orange" stroke-width="2" marker-end="url(#arrowhead-orange)"/>
            <text x="400" y="575" fill="orange" font-size="12">VSW/VDR控制信号</text>

            <!-- 电流源 → 测试夹具 -->
            <line x1="200" y1="600" x2="200" y2="470" stroke="red" stroke-width="2" marker-end="url(#arrowhead-red)"/>
            <text x="220" y="535" fill="red" font-size="12">激励电流</text>

            <!-- 定义箭头标记 -->
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
                </marker>
                <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="blue"/>
                </marker>
                <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="purple"/>
                </marker>
                <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="red"/>
                </marker>
                <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="green"/>
                </marker>
                <marker id="arrowhead-orange" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="orange"/>
                </marker>
            </defs>
        </svg>

        <div class="flow-description">
            <strong>信号流向说明：</strong><br>
            1. 电池通过测试夹具连接到系统<br>
            2. DNB1101BB芯片测量电池的电压和电流<br>
            3. 外部电流源提供EIS测试所需的激励信号<br>
            4. STM32控制器处理测试数据和系统控制<br>
            5. 测试结果同时显示在本地屏幕和上位机
        </div>

        <div class="tech-params">
            系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C
        </div>
    </div>

    <script>
        // 使组件可拖拽
        document.querySelectorAll('.component').forEach(component => {
            component.addEventListener('mousedown', function(e) {
                let isDragging = true;
                let startX = e.clientX - component.offsetLeft;
                let startY = e.clientY - component.offsetTop;

                function onMouseMove(e) {
                    if (isDragging) {
                        component.style.left = (e.clientX - startX) + 'px';
                        component.style.top = (e.clientY - startY) + 'px';
                    }
                }

                function onMouseUp() {
                    isDragging = false;
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                }

                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            });
        });
    </script>
</body>
</html>"""

        filepath = os.path.join(self.output_dir, 'figure1_editable.html')
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"HTML可编辑文件已保存: {filepath}")

    def generate_all_editable_figures(self):
        """生成所有可编辑格式的附图"""
        print("开始生成可编辑格式的专利附图...")
        self.create_svg_figure1()
        self.create_drawio_xml()
        self.create_html_editable()
        print("所有可编辑格式附图生成完成！")
        print(f"文件保存在: {self.output_dir}")
        print("支持的编辑软件:")
        print("- SVG: Inkscape, Adobe Illustrator, LibreOffice Draw")
        print("- Draw.io: https://app.diagrams.net/")
        print("- HTML: 任何文本编辑器或网页浏览器")

if __name__ == "__main__":
    generator = EditableFigureGenerator()
    generator.generate_all_editable_figures()
