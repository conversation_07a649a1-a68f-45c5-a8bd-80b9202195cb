#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI显示修复验证脚本
用于验证测试数据显示问题的修复效果

版权所有：鲸测云
作者：weiwei
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_ui_fix.log', encoding='utf-8')
        ]
    )

def test_ui_display_fix():
    """测试UI显示修复"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🔍 开始测试UI显示修复...")
        
        # 1. 测试配置管理器
        logger.info("1. 测试配置管理器...")
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        logger.info("✅ 配置管理器初始化成功")
        
        # 2. 测试通信管理器（模拟未连接状态）
        logger.info("2. 测试通信管理器...")
        from backend.communication_manager import CommunicationManager
        comm_manager = CommunicationManager(config_manager)
        logger.info(f"通信管理器连接状态: {comm_manager.is_connected}")
        
        # 3. 测试UI组件
        logger.info("3. 测试UI组件...")
        from PyQt5.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        # 4. 测试通道显示组件
        logger.info("4. 测试通道显示组件...")
        from ui.components.channel_display_widget import ChannelDisplayWidget
        
        # 创建测试通道
        test_channel = ChannelDisplayWidget(1, config_manager)
        logger.info("✅ 通道显示组件创建成功")
        
        # 5. 测试数据更新
        logger.info("5. 测试数据更新...")
        test_voltage = 3.75
        test_rs = 1.234
        test_rct = 2.567
        test_progress = 100
        
        test_channel.update_test_data(test_voltage, test_rs, test_rct, test_progress)
        logger.info(f"✅ 数据更新成功: V={test_voltage}V, Rs={test_rs}mΩ, Rct={test_rct}mΩ")
        
        # 6. 测试结果设置
        logger.info("6. 测试结果设置...")
        test_channel.set_test_completed(
            is_pass=False,
            rs_grade=2,
            rct_grade=3,
            fail_items=["Rs", "Rct"]
        )
        logger.info("✅ 测试结果设置成功")
        
        # 7. 验证UI更新器
        logger.info("7. 验证UI更新器...")
        if hasattr(test_channel, 'ui_updater') and test_channel.ui_updater:
            logger.info("✅ UI更新器存在")
            
            # 测试各个显示更新
            success = True
            success &= test_channel.ui_updater.update_voltage_display(test_voltage)
            success &= test_channel.ui_updater.update_impedance_display(test_rs, test_rct)
            success &= test_channel.ui_updater.update_progress_display(test_progress)
            
            if success:
                logger.info("✅ UI更新器功能正常")
            else:
                logger.warning("⚠️ UI更新器部分功能异常")
        else:
            logger.warning("⚠️ UI更新器不存在")
        
        # 8. 测试模拟模式
        logger.info("8. 测试模拟模式...")
        from ui.test_flow_managers.test_flow_manager_adapter import TestFlowManagerAdapter
        from ui.device_connection_manager import DeviceConnectionManager

        # 创建设备连接管理器
        device_conn_manager = DeviceConnectionManager(None, config_manager, comm_manager)

        # 创建测试流程管理器适配器
        test_adapter = TestFlowManagerAdapter(None, config_manager, comm_manager, device_conn_manager)
        
        # 测试模拟模式启动
        battery_codes = ["TEST-001", "TEST-002", "TEST-003", "TEST-004"]
        result = test_adapter._start_simulation_mode(battery_codes)
        
        if result:
            logger.info("✅ 模拟模式启动成功")
        else:
            logger.warning("⚠️ 模拟模式启动失败")
        
        logger.info("🎉 UI显示修复测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 启动UI显示修复验证测试")
    
    try:
        success = test_ui_display_fix()
        
        if success:
            logger.info("✅ 所有测试通过，UI显示修复验证成功")
            return 0
        else:
            logger.error("❌ 测试失败，需要进一步检查")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
