#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的档位设置主组件
作为各个管理器的协调器，保持较小的体积

Author: Jack
Date: 2025-06-04
"""

import logging
from typing import Dict, Any
from PyQt5.QtWidgets import QWidget, QMessageBox, QDialog
from PyQt5.QtCore import QTimer

# 导入管理器
from .outlier_detection_ui_manager import OutlierDetectionUIManager
from .grade_range_manager import GradeRangeManager
from .voltage_range_manager import VoltageRangeManager
from .grade_settings_ui_manager import GradeSettingsUIManager

# 导入安全数值输入框
from .safe_double_spinbox import SafeDoubleSpinBox

logger = logging.getLogger(__name__)


class GradeSettingsWidget(QWidget):
    """
    档位设置主组件（重构版）
    
    职责：
    - 协调各个管理器
    - 处理管理器间的通信
    - 管理整体状态
    """
    
    def __init__(self, config_manager, parent=None):
        """
        初始化档位设置组件
        
        Args:
            config_manager: 配置管理器
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.config_manager = config_manager
        
        # 初始化管理器
        self._init_managers()
        
        # 创建界面
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 初始化数据
        self._init_data()
        
        logger.info("档位设置组件初始化完成")
    
    def _init_managers(self):
        """初始化各个管理器"""
        try:
            # 创建管理器实例
            self.outlier_ui_manager = OutlierDetectionUIManager(self)
            self.grade_range_manager = GradeRangeManager(self.config_manager, self)
            self.voltage_range_manager = VoltageRangeManager(self.config_manager, self)
            self.ui_manager = GradeSettingsUIManager(self)
            
            logger.debug("所有管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化管理器失败: {e}")
            QMessageBox.critical(self, "错误", f"初始化失败: {e}")
    
    def _init_ui(self):
        """初始化界面"""
        try:
            # 创建主布局
            main_layout = self.ui_manager.create_main_layout()

            # 创建离群检测组
            outlier_group = self.outlier_ui_manager.create_outlier_detection_group()

            # 尝试添加到第一行布局
            try:
                first_row_item = main_layout.itemAt(0)
                if first_row_item:
                    first_row_layout = first_row_item.layout()
                    if first_row_layout:
                        # 使用addWidget添加到第一行的开头
                        from PyQt5.QtWidgets import QHBoxLayout
                        if isinstance(first_row_layout, QHBoxLayout):
                            # 在电压组之前插入离群检测组
                            first_row_layout.insertWidget(0, outlier_group)
                            logger.debug("离群检测组已添加到第一行")
                        else:
                            # 如果不是水平布局，直接添加
                            first_row_layout.addWidget(outlier_group)
                            logger.debug("离群检测组已添加到第一行布局")
                    else:
                        raise Exception("无法获取第一行布局")
                else:
                    raise Exception("无法获取第一行项目")
            except Exception as layout_error:
                logger.warning(f"添加到第一行失败: {layout_error}，尝试添加到主布局顶部")
                # 备用方案：添加到主布局的顶部
                main_layout.insertWidget(0, outlier_group)
                logger.info("离群检测组已添加到主布局顶部")

            logger.debug("界面初始化完成")

        except Exception as e:
            logger.error(f"初始化界面失败: {e}")
            # 最后的备用方案：创建简单布局
            try:
                from PyQt5.QtWidgets import QVBoxLayout
                if not self.layout():
                    simple_layout = QVBoxLayout(self)
                    outlier_group = self.outlier_ui_manager.create_outlier_detection_group()
                    simple_layout.addWidget(outlier_group)
                    logger.info("使用简单布局添加离群检测组")
            except Exception as final_error:
                logger.error(f"最终备用方案也失败: {final_error}")
    
    def _connect_signals(self):
        """连接管理器间的信号"""
        try:
            # UI管理器信号连接
            self.ui_manager.settings_changed.connect(self._on_settings_changed)
            
            # 离群检测UI管理器信号连接
            self.outlier_ui_manager.settings_changed.connect(self._on_outlier_settings_changed)
            self.outlier_ui_manager.baseline_refresh_requested.connect(self._on_baseline_refresh_requested)
            self.outlier_ui_manager.baseline_manage_requested.connect(self._on_baseline_manage_requested)
            
            # 档位范围管理器信号连接
            self.grade_range_manager.ranges_updated.connect(self._on_ranges_updated)
            
            # 电压范围管理器信号连接
            self.voltage_range_manager.voltage_config_changed.connect(self._on_voltage_config_changed)
            
            logger.debug("信号连接完成")
            
        except Exception as e:
            logger.error(f"连接信号失败: {e}")
    
    def _init_data(self):
        """初始化数据"""
        try:
            # 延迟加载数据以提升启动速度
            QTimer.singleShot(100, self._load_initial_data)
            
        except Exception as e:
            logger.error(f"初始化数据失败: {e}")
    
    def _load_initial_data(self):
        """加载初始数据"""
        try:
            # 加载配置
            self.grade_range_manager.load_config()
            self.voltage_range_manager.load_config()

            # 更新显示
            self._update_all_displays()

            # 先加载基准列表
            self._load_baseline_list()

            # 然后加载离群检测配置（确保基准列表已加载）
            self._load_outlier_config()

            logger.info("初始数据加载完成")

        except Exception as e:
            logger.error(f"加载初始数据失败: {e}")

    def _load_outlier_config(self):
        """加载离群检测配置"""
        try:
            # 加载离群检测配置（优先从数据库加载）
            try:
                from backend.outlier_detection_manager import OutlierDetectionManager
                outlier_manager = OutlierDetectionManager()
                outlier_config = outlier_manager.get_detection_config()

                if outlier_config:
                    self.outlier_ui_manager.load_outlier_config(outlier_config)
                    logger.debug("从数据库加载离群检测配置成功")
                else:
                    logger.debug("数据库中无离群检测配置，使用默认配置")

            except Exception as e:
                logger.error(f"从数据库加载离群检测配置失败: {e}")
                # 回退到配置文件加载
                if self.config_manager:
                    outlier_config = {}
                    for key in ['is_enabled', 'active_baseline_id', 'channel_mode', 'deviation_threshold']:
                        value = self.config_manager.get(f"outlier_{key}")
                        if value is not None:
                            outlier_config[key] = value

                    if outlier_config:
                        self.outlier_ui_manager.load_outlier_config(outlier_config)
                        logger.debug("从配置文件加载离群检测配置成功")

        except Exception as e:
            logger.error(f"加载离群检测配置失败: {e}")
    
    def _on_settings_changed(self):
        """处理设置变更"""
        try:
            # 获取配置
            voltage_config = self.ui_manager.get_voltage_config()
            rs_config = self.ui_manager.get_rs_config()
            rct_config = self.ui_manager.get_rct_config()
            
            # 更新管理器
            self.voltage_range_manager.update_voltage_config(**voltage_config)
            self.grade_range_manager.update_rs_config(**rs_config)
            self.grade_range_manager.update_rct_config(**rct_config)
            
            # 更新显示
            self._update_all_displays()
            
        except Exception as e:
            logger.error(f"处理设置变更失败: {e}")
    
    def _on_outlier_settings_changed(self):
        """处理离群检测设置变更"""
        try:
            # 获取离群检测配置
            outlier_config = self.outlier_ui_manager.get_outlier_config()

            # 先保存配置（确保用户选择被保存）
            if self.config_manager:
                for key, value in outlier_config.items():
                    self.config_manager.set(f"outlier_{key}", value)

            # 然后验证选中的基准（验证失败不影响保存）
            if outlier_config.get('is_enabled') and outlier_config.get('active_baseline_id'):
                validation_result = self.outlier_ui_manager.validate_selected_baseline()
                if not validation_result.get('valid'):
                    logger.warning(f"基准验证失败: {validation_result.get('error')}")
                    # 注意：不再阻止保存，只是提醒用户
                    QMessageBox.warning(
                        self, "基准验证失败",
                        f"选中的基准存在问题：\n\n{validation_result.get('error')}\n\n配置已保存，但建议重新选择基准或检查基准数据。"
                    )
                elif validation_result.get('warning'):
                    logger.warning(f"基准验证警告: {validation_result.get('warning')}")
                    # 显示警告但不阻止保存
                    QMessageBox.information(
                        self, "基准验证警告",
                        f"基准验证警告：\n\n{validation_result.get('warning')}\n\n配置已保存，建议检查频率配置是否正确。"
                    )

            logger.debug("离群检测设置已更新")

        except Exception as e:
            logger.error(f"处理离群检测设置变更失败: {e}")
    
    def _on_baseline_refresh_requested(self):
        """处理基准刷新请求"""
        try:
            self._load_baseline_list()
            QMessageBox.information(self, "成功", "基准列表已刷新")
            
        except Exception as e:
            logger.error(f"处理基准刷新请求失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新基准列表失败: {e}")
    
    def _on_baseline_manage_requested(self):
        """处理基准管理请求"""
        try:
            # 打开基准管理对话框
            from ui.dialogs.baseline_manager_dialog import BaselineManagerDialog

            dialog = BaselineManagerDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # 刷新基准列表
                self._load_baseline_list()
                logger.info("基准管理完成，已刷新基准列表")

        except Exception as e:
            logger.error(f"处理基准管理请求失败: {e}")
            QMessageBox.critical(self, "错误", f"打开基准管理失败: {e}")
    
    def _on_ranges_updated(self, range_type: str, ranges: list):
        """处理档位范围更新"""
        try:
            if range_type == 'rs':
                text = self.grade_range_manager.get_rs_ranges_text()
                self.ui_manager.update_rs_display(text)
            elif range_type == 'rct':
                text = self.grade_range_manager.get_rct_ranges_text()
                self.ui_manager.update_rct_display(text)
            
        except Exception as e:
            logger.error(f"处理档位范围更新失败: {e}")
    
    def _on_voltage_config_changed(self):
        """处理电压配置变更"""
        try:
            text = self.voltage_range_manager.get_voltage_range_text()
            self.ui_manager.update_voltage_display(text)
            
        except Exception as e:
            logger.error(f"处理电压配置变更失败: {e}")
    
    def _update_all_displays(self):
        """更新所有显示"""
        try:
            # 更新电压范围显示
            voltage_text = self.voltage_range_manager.get_voltage_range_text()
            self.ui_manager.update_voltage_display(voltage_text)
            
            # 更新Rs档位显示
            rs_text = self.grade_range_manager.get_rs_ranges_text()
            self.ui_manager.update_rs_display(rs_text)
            
            # 更新Rct档位显示
            rct_text = self.grade_range_manager.get_rct_ranges_text()
            self.ui_manager.update_rct_display(rct_text)
            
        except Exception as e:
            logger.error(f"更新所有显示失败: {e}")
    
    def _load_baseline_list(self):
        """加载基准列表"""
        try:
            # 从学习功能模块的数据库获取基准列表
            from backend.outlier_detection_manager import OutlierDetectionManager

            outlier_manager = OutlierDetectionManager()
            baselines = outlier_manager.get_all_baselines()

            logger.debug(f"从数据库加载到{len(baselines)}个基准")

            # 加载到UI组件
            self.outlier_ui_manager.load_baseline_list(baselines)

        except Exception as e:
            logger.error(f"加载基准列表失败: {e}")
            # 如果加载失败，使用空列表
            self.outlier_ui_manager.load_baseline_list([])
    
    def get_voltage_config(self) -> Dict[str, Any]:
        """获取电压配置（向后兼容）"""
        return self.voltage_range_manager.get_voltage_config()
    
    def get_rs_config(self) -> Dict[str, Any]:
        """获取Rs配置（向后兼容）"""
        return self.grade_range_manager.get_rs_config()
    
    def get_rct_config(self) -> Dict[str, Any]:
        """获取Rct配置（向后兼容）"""
        return self.grade_range_manager.get_rct_config()
    
    def get_outlier_config(self) -> Dict[str, Any]:
        """获取离群检测配置（向后兼容）"""
        return self.outlier_ui_manager.get_outlier_config()
    
    def validate_voltage(self, voltage: float) -> bool:
        """验证电压（向后兼容）"""
        return self.voltage_range_manager.validate_voltage(voltage)
    
    def get_grade_by_value(self, value: float, grade_type: str) -> str:
        """根据值获取档位（向后兼容）"""
        return self.grade_range_manager.get_grade_by_value(value, grade_type)
    
    def apply_settings(self):
        """应用设置（设置对话框标准接口）"""
        try:
            # 获取当前UI配置
            voltage_config = self.ui_manager.get_voltage_config()
            rs_config = self.ui_manager.get_rs_config()
            rct_config = self.ui_manager.get_rct_config()

            # 更新管理器配置
            self.voltage_range_manager.update_voltage_config(**voltage_config)
            self.grade_range_manager.update_rs_config(**rs_config)
            self.grade_range_manager.update_rct_config(**rct_config)

            # 保存所有配置
            self.save_all_config()

            logger.info("档位设置应用成功")

        except Exception as e:
            logger.error(f"应用档位设置失败: {e}")
            raise

    def validate_settings(self) -> bool:
        """验证设置（设置对话框标准接口）"""
        try:
            # 验证电压配置
            if not self.voltage_range_manager.validate_config():
                return False

            # 验证档位配置
            validation_result = self.grade_range_manager.validate_ranges()
            if not validation_result.get('rs', True) or not validation_result.get('rct', True):
                return False

            return True

        except Exception as e:
            logger.error(f"验证档位设置失败: {e}")
            return False

    def load_settings(self):
        """加载设置（设置对话框标准接口）"""
        try:
            self.load_all_config()
            logger.debug("档位设置加载完成")

        except Exception as e:
            logger.error(f"加载档位设置失败: {e}")

    def save_all_config(self):
        """保存所有配置"""
        try:
            self.grade_range_manager.save_config()
            self.voltage_range_manager.save_config()

            # 保存UI管理器配置
            if self.config_manager:
                # 保存Rs配置
                rs_config = self.ui_manager.get_rs_config()
                for key, value in rs_config.items():
                    if key == 'grade_count':
                        self.config_manager.set('grade_settings.rs_grade_count', value)
                    elif key == 'min_value':
                        self.config_manager.set('grade_settings.rs_min', value)
                    elif key == 'max_value':
                        self.config_manager.set('grade_settings.rs_max', value)
                    elif key == 'auto_calc':
                        self.config_manager.set('grade_settings.rs_auto_calc', value)
                    elif key == 'grade1_max':
                        self.config_manager.set('grade_settings.rs1_max', value)
                    elif key == 'grade2_max':
                        self.config_manager.set('grade_settings.rs2_max', value)
                    elif key == 'grade3_max':
                        self.config_manager.set('grade_settings.rs3_max', value)

                # 保存Rct配置
                rct_config = self.ui_manager.get_rct_config()
                for key, value in rct_config.items():
                    if key == 'min_value':
                        self.config_manager.set('grade_settings.rct_min', value)
                    elif key == 'max_value':
                        self.config_manager.set('grade_settings.rct_max', value)
                    elif key == 'auto_calc':
                        self.config_manager.set('grade_settings.rct_auto_calc', value)
                    elif key == 'grade1_max':
                        self.config_manager.set('grade_settings.rct1_max', value)
                    elif key == 'grade2_max':
                        self.config_manager.set('grade_settings.rct2_max', value)
                    elif key == 'grade3_max':
                        self.config_manager.set('grade_settings.rct3_max', value)

                # 🔧 简化：保存电压配置（仅电压差模式）
                voltage_config = self.ui_manager.get_voltage_config()
                for key, value in voltage_config.items():
                    if key == 'standard_voltage':
                        self.config_manager.set('grade_settings.standard_voltage', value)
                    elif key == 'min_voltage':
                        self.config_manager.set('grade_settings.min_voltage', value)
                    elif key == 'max_voltage':
                        self.config_manager.set('grade_settings.max_voltage', value)
                    elif key == 'auto_calc_range':
                        self.config_manager.set('grade_settings.auto_calc_range', value)
                    elif key == 'battery_type':
                        self.config_manager.set('grade_settings.battery_type', value)
                    elif key == 'voltage_diff':
                        self.config_manager.set('grade_settings.voltage_diff', value)

            # 保存离群检测配置到数据库
            outlier_config = self.outlier_ui_manager.get_outlier_config()
            if outlier_config:
                try:
                    from backend.outlier_detection_manager import OutlierDetectionManager
                    outlier_manager = OutlierDetectionManager()
                    outlier_manager.update_detection_config(outlier_config)
                    logger.debug("离群检测配置已保存到数据库")
                except Exception as e:
                    logger.error(f"保存离群检测配置到数据库失败: {e}")

                # 同时保存到配置文件（向后兼容）
                if self.config_manager:
                    for key, value in outlier_config.items():
                        self.config_manager.set(f"outlier_{key}", value)

            logger.info("所有配置保存完成")

        except Exception as e:
            logger.error(f"保存所有配置失败: {e}")
    
    def load_all_config(self):
        """加载所有配置"""
        try:
            self.grade_range_manager.load_config()
            self.voltage_range_manager.load_config()

            # 加载UI管理器配置
            if self.config_manager:
                # 加载Rs配置
                rs_config = {}
                rs_config['grade_count'] = self.config_manager.get('grade_settings.rs_grade_count', 3)
                rs_config['min_value'] = self.config_manager.get('grade_settings.rs_min', 0.5)
                rs_config['max_value'] = self.config_manager.get('grade_settings.rs_max', 50.0)
                rs_config['auto_calc'] = self.config_manager.get('grade_settings.rs_auto_calc', True)
                rs_config['grade1_max'] = self.config_manager.get('grade_settings.rs1_max', 17.0)
                rs_config['grade2_max'] = self.config_manager.get('grade_settings.rs2_max', 33.5)
                rs_config['grade3_max'] = self.config_manager.get('grade_settings.rs3_max', 50.0)
                self.ui_manager.load_rs_config(rs_config)

                # 加载Rct配置
                rct_config = {}
                rct_config['min_value'] = self.config_manager.get('grade_settings.rct_min', 5.0)
                rct_config['max_value'] = self.config_manager.get('grade_settings.rct_max', 100.0)
                rct_config['auto_calc'] = self.config_manager.get('grade_settings.rct_auto_calc', True)
                rct_config['grade1_max'] = self.config_manager.get('grade_settings.rct1_max', 35.0)
                rct_config['grade2_max'] = self.config_manager.get('grade_settings.rct2_max', 70.0)
                rct_config['grade3_max'] = self.config_manager.get('grade_settings.rct3_max', 100.0)
                self.ui_manager.load_rct_config(rct_config)

                # 🔧 简化：加载电压配置（仅电压差模式）
                voltage_config = {}
                voltage_config['standard_voltage'] = self.config_manager.get('grade_settings.standard_voltage', 3.21)
                voltage_config['min_voltage'] = self.config_manager.get('grade_settings.min_voltage', 3.05)
                voltage_config['max_voltage'] = self.config_manager.get('grade_settings.max_voltage', 3.37)
                voltage_config['auto_calc_range'] = self.config_manager.get('grade_settings.auto_calc_range', True)
                voltage_config['battery_type'] = self.config_manager.get('grade_settings.battery_type', 0)
                voltage_config['voltage_diff'] = self.config_manager.get('grade_settings.voltage_diff', 0.16)
                self.ui_manager.load_voltage_config(voltage_config)

            # 加载离群检测配置（使用专门的方法）
            self._load_outlier_config()

            # 更新显示
            self._update_all_displays()

            logger.info("所有配置加载完成")

        except Exception as e:
            logger.error(f"加载所有配置失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 保存配置
            self.save_all_config()
            
            # 清理各个管理器
            if hasattr(self, 'outlier_ui_manager'):
                self.outlier_ui_manager.cleanup()
            
            if hasattr(self, 'grade_range_manager'):
                self.grade_range_manager.cleanup()
            
            if hasattr(self, 'voltage_range_manager'):
                self.voltage_range_manager.cleanup()
            
            if hasattr(self, 'ui_manager'):
                self.ui_manager.cleanup()
            
            logger.info("档位设置组件资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.cleanup()
        super().closeEvent(event)
