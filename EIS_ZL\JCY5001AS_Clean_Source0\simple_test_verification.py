#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试验证脚本
验证配置是否正确并模拟测试过程
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_and_verify_config():
    """加载并验证配置"""
    print("🔍 简单测试验证工具")
    print("=" * 60)
    
    try:
        # 直接读取配置文件
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("📋 当前配置状态:")
        
        # 检查测试配置
        test_config = config.get('test', {})
        count_limit_enabled = test_config.get('count_limit_enabled', True)
        max_count = test_config.get('max_count', 5)
        timeout = test_config.get('timeout', 52)
        use_parallel_staggered = test_config.get('use_parallel_staggered_mode', False)
        
        print(f"   • 计数限制: {'禁用' if not count_limit_enabled else '启用'}")
        print(f"   • 最大测试次数: {max_count}")
        print(f"   • 超时时间: {timeout}秒")
        print(f"   • 并行错频模式: {'启用' if use_parallel_staggered else '禁用'}")
        
        # 检查频率配置
        frequency_config = config.get('frequency', {})
        frequency_list = frequency_config.get('list', [])
        preset_mode = frequency_config.get('preset_mode', 'unknown')
        
        print(f"   • 预设模式: {preset_mode}")
        print(f"   • 频点数量: {len(frequency_list)}个")
        if frequency_list:
            print(f"   • 频率范围: {frequency_list[0]:.3f}Hz ~ {frequency_list[-1]:.3f}Hz")
        
        # 检查测试次数配置
        test_count_config = config.get('test_count', {})
        print(f"   • 通道测试次数限制:")
        for channel, count in test_count_config.items():
            print(f"     - {channel}: {count}")
        
        # 验证配置是否正确
        config_issues = []
        
        if count_limit_enabled:
            config_issues.append("计数限制仍然启用")
        
        if max_count < 100:
            config_issues.append(f"最大测试次数过小: {max_count}")
        
        if timeout > 15:
            config_issues.append(f"超时时间过长: {timeout}秒")
        
        if not use_parallel_staggered:
            config_issues.append("并行错频模式未启用")
        
        if len(frequency_list) != 20:
            config_issues.append(f"频点数量不正确: {len(frequency_list)}个")
        
        print(f"\n🔧 配置验证结果:")
        if config_issues:
            print("❌ 发现配置问题:")
            for issue in config_issues:
                print(f"   • {issue}")
            return False
        else:
            print("✅ 所有配置都正确!")
            return True
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def simulate_test_timing():
    """模拟测试时间计算"""
    print(f"\n⏱️ 测试时间模拟:")
    print("-" * 40)
    
    try:
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取配置参数
        test_config = config.get('test', {})
        frequency_config = config.get('frequency', {})
        
        timeout = test_config.get('timeout', 8)
        interval = test_config.get('interval', 3)
        use_parallel_staggered = test_config.get('use_parallel_staggered_mode', True)
        critical_frequency = test_config.get('critical_frequency', 15.0)
        
        frequency_list = frequency_config.get('list', [])
        
        print(f"📊 测试参数:")
        print(f"   • 频点数量: {len(frequency_list)}")
        print(f"   • 单频点超时: {timeout}秒")
        print(f"   • 频点间隔: {interval}秒")
        print(f"   • 并行错频模式: {'启用' if use_parallel_staggered else '禁用'}")
        print(f"   • 临界频率: {critical_frequency}Hz")
        
        if use_parallel_staggered:
            # 并行错频模式计算
            high_freq_count = sum(1 for f in frequency_list if f > critical_frequency)
            low_freq_count = len(frequency_list) - high_freq_count
            
            # 高频点串行测试，低频点并行测试
            high_freq_time = high_freq_count * (timeout + interval)
            low_freq_time = (timeout + interval) if low_freq_count > 0 else 0
            
            total_time = high_freq_time + low_freq_time
            
            print(f"\n📈 并行错频模式时间估算:")
            print(f"   • 高频点 (>{critical_frequency}Hz): {high_freq_count}个")
            print(f"   • 低频点 (≤{critical_frequency}Hz): {low_freq_count}个")
            print(f"   • 高频点测试时间: {high_freq_time}秒")
            print(f"   • 低频点测试时间: {low_freq_time}秒")
            
        else:
            # 传统模式计算
            total_time = len(frequency_list) * (timeout + interval)
            
            print(f"\n📈 传统模式时间估算:")
            print(f"   • 总频点数: {len(frequency_list)}")
            print(f"   • 单频点时间: {timeout + interval}秒")
        
        print(f"\n🎯 预估总测试时间: {total_time}秒 ({total_time/60:.1f}分钟)")
        
        # 评估时间合理性
        if 60 <= total_time <= 180:  # 1-3分钟
            print("✅ 预估时间合理 (1-3分钟范围)")
        elif total_time < 60:
            print("⚠️ 预估时间较短 (可能配置过于激进)")
        else:
            print("⚠️ 预估时间较长 (可能需要进一步优化)")
        
        return total_time
        
    except Exception as e:
        print(f"❌ 时间模拟失败: {e}")
        return None

def check_device_connection():
    """检查设备连接状态"""
    print(f"\n🔌 设备连接检查:")
    print("-" * 40)
    
    try:
        # 尝试导入通信管理器
        from backend.communication_manager import CommunicationManager
        
        comm_manager = CommunicationManager()
        
        # 尝试连接
        print("正在尝试连接设备...")
        if comm_manager.connect():
            print("✅ 设备连接成功")
            
            # 尝试获取设备信息
            try:
                device_info = comm_manager.get_device_info()
                if device_info:
                    print(f"📱 设备信息: {device_info}")
                else:
                    print("📱 设备信息: 无法获取")
            except:
                print("📱 设备信息: 获取失败")
            
            # 断开连接
            comm_manager.disconnect()
            print("🔌 设备连接已断开")
            return True
        else:
            print("❌ 设备连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 设备连接检查异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始验证...")
    
    # 1. 验证配置
    config_ok = load_and_verify_config()
    
    # 2. 模拟测试时间
    estimated_time = simulate_test_timing()
    
    # 3. 检查设备连接
    device_ok = check_device_connection()
    
    # 4. 总结
    print(f"\n🎯 验证总结:")
    print("=" * 60)
    
    print(f"📋 配置状态: {'✅ 正确' if config_ok else '❌ 有问题'}")
    print(f"⏱️ 预估测试时间: {estimated_time/60:.1f}分钟" if estimated_time else "❌ 无法计算")
    print(f"🔌 设备连接: {'✅ 正常' if device_ok else '❌ 异常'}")
    
    if config_ok and estimated_time and device_ok:
        print(f"\n🎉 验证通过！系统已准备好进行完整的20频点测试！")
        print(f"📊 预期效果:")
        print(f"   • 测试所有20个频点")
        print(f"   • 测试时间约{estimated_time/60:.1f}分钟")
        print(f"   • 使用并行错频模式提高效率")
    else:
        print(f"\n⚠️ 验证发现问题，请检查配置或设备连接")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
