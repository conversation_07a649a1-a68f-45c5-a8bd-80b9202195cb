# -*- coding: utf-8 -*-
"""
设置加载管理器
从MainWindow中提取的设置加载相关功能

职责：
- 启动时设置加载
- 通道使能状态设置
- 产品信息设置
- 测试参数设置
- 界面显示设置

Author: Jack
Date: 2025-01-30
"""

import logging
from typing import Dict, Any
from PyQt5.QtCore import QObject

logger = logging.getLogger(__name__)


class SettingsLoader(QObject):
    """
    设置加载管理器
    
    职责：
    - 软件启动时的各种设置加载
    - 通道配置加载
    - 产品信息配置加载
    - 测试参数配置加载
    - UI显示配置加载
    """
    
    def __init__(self, main_window, config_manager):
        """
        初始化设置加载管理器
        
        Args:
            main_window: 主窗口实例
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.main_window = main_window
        self.config_manager = config_manager
        
        logger.info("设置加载管理器初始化完成")
    
    def load_startup_settings(self):
        """
        软件启动时加载所有设置
        """
        try:
            logger.info("开始加载启动设置...")

            # 1. 加载通道使能状态
            self.load_channel_enable_settings()

            # 2. 加载产品信息设置
            self.load_product_info_settings()

            # 3. 加载测试参数设置
            self.load_test_parameter_settings()

            # 4. 加载界面显示设置
            self.load_ui_display_settings()

            # 5. 加载离群检测设置
            self.load_outlier_detection_settings()

            # 6. 初始化打印机状态显示
            self.initialize_printer_status()

            logger.info("启动设置加载完成")

        except Exception as e:
            logger.error(f"加载启动设置失败: {e}")
    
    def load_channel_enable_settings(self):
        """加载通道使能状态设置"""
        try:
            # 获取启用的通道列表
            enabled_channels = self.config_manager.get('test.enabled_channels', list(range(1, 9)))

            # 更新通道容器的使能状态
            channels_container = self.main_window.ui_component_manager.get_component('channels_container')
            if channels_container:
                for channel_num in range(1, 9):
                    is_enabled = channel_num in enabled_channels
                    if hasattr(channels_container, 'set_channel_enabled'):
                        channels_container.set_channel_enabled(channel_num, is_enabled)

                logger.info(f"通道使能状态已加载: {enabled_channels}")
            else:
                logger.warning("通道容器组件未找到，无法设置通道使能状态")

        except Exception as e:
            logger.error(f"加载通道使能设置失败: {e}")
    
    def load_product_info_settings(self):
        """加载产品信息设置"""
        try:
            # 更新批次信息组件
            batch_info = self.main_window.ui_component_manager.get_component('batch_info')
            if batch_info and hasattr(batch_info, 'load_settings'):
                batch_info.load_settings()
                logger.info("产品信息设置已加载")
            else:
                logger.warning("批次信息组件未找到或不支持设置加载")

        except Exception as e:
            logger.error(f"加载产品信息设置失败: {e}")
    
    def load_test_parameter_settings(self):
        """加载测试参数设置"""
        try:
            # 加载频率设置
            self._load_frequency_settings()

            # 加载阻抗范围设置
            self._load_impedance_range_settings()

            # 加载档位配置
            self._load_gain_settings()

            # 🔧 修复：刷新测试控制组件
            self._refresh_test_control_component()

            logger.debug("测试参数设置加载完成")

        except Exception as e:
            logger.error(f"加载测试参数设置失败: {e}")

    def _refresh_test_control_component(self):
        """刷新测试控制组件"""
        try:
            # 更新测试控制组件
            test_control = self.main_window.ui_component_manager.get_component('test_control')
            if test_control and hasattr(test_control, 'load_settings'):
                test_control.load_settings()
                logger.info("✅ 测试控制组件已刷新")
            else:
                logger.warning("⚠️ 测试控制组件未找到或不支持设置加载")

        except Exception as e:
            logger.error(f"❌ 刷新测试控制组件失败: {e}")
    
    def _load_frequency_settings(self):
        """加载频率设置"""
        try:
            # 获取频率配置
            frequency_config = self.config_manager.get('test.frequency', {})
            
            # 这里可以添加频率设置的具体加载逻辑
            # 例如：设置默认频率、频率范围等
            
            logger.debug(f"频率设置已加载: {frequency_config}")
            
        except Exception as e:
            logger.error(f"加载频率设置失败: {e}")
    
    def _load_impedance_range_settings(self):
        """加载阻抗范围设置"""
        try:
            # 获取阻抗范围配置
            impedance_config = self.config_manager.get('test.impedance_range', {})
            
            # 这里可以添加阻抗范围设置的具体加载逻辑
            
            logger.debug(f"阻抗范围设置已加载: {impedance_config}")
            
        except Exception as e:
            logger.error(f"加载阻抗范围设置失败: {e}")
    
    def _load_gain_settings(self):
        """加载档位设置"""
        try:
            # 获取档位配置
            gain_config = self.config_manager.get('test.gain', {})
            
            # 这里可以添加档位设置的具体加载逻辑
            
            logger.debug(f"档位设置已加载: {gain_config}")
            
        except Exception as e:
            logger.error(f"加载档位设置失败: {e}")
    
    def load_ui_display_settings(self):
        """加载界面显示设置"""
        try:
            # 加载主题设置
            self._load_theme_settings()
            
            # 加载字体设置
            self._load_font_settings()
            
            # 加载布局设置
            self._load_layout_settings()
            
            logger.debug("界面显示设置加载完成")

        except Exception as e:
            logger.error(f"加载界面显示设置失败: {e}")
    
    def _load_theme_settings(self):
        """加载主题设置"""
        try:
            theme = self.config_manager.get('ui.style.theme', 'default')
            
            # 应用主题设置
            if hasattr(self.main_window, 'window_layout_manager'):
                # 主题设置在样式应用时处理
                pass
            
            logger.debug(f"主题设置已加载: {theme}")
            
        except Exception as e:
            logger.error(f"加载主题设置失败: {e}")
    
    def _load_font_settings(self):
        """加载字体设置"""
        try:
            font_config = self.config_manager.get('ui.font', {})
            
            # 这里可以添加字体设置的具体加载逻辑
            
            logger.debug(f"字体设置已加载: {font_config}")
            
        except Exception as e:
            logger.error(f"加载字体设置失败: {e}")
    
    def _load_layout_settings(self):
        """加载布局设置"""
        try:
            layout_config = self.config_manager.get('ui.layout', {})
            
            # 这里可以添加布局设置的具体加载逻辑
            
            logger.debug(f"布局设置已加载: {layout_config}")
            
        except Exception as e:
            logger.error(f"加载布局设置失败: {e}")

    def load_outlier_detection_settings(self):
        """加载离群检测设置"""
        try:
            # 获取离群检测配置
            try:
                from backend.outlier_detection_manager import OutlierDetectionManager
                outlier_manager = OutlierDetectionManager()
                config = outlier_manager.get_detection_config()

                enabled = config.get('is_enabled', False)

                # 更新所有通道的离群检测状态
                channels_container = self.main_window.ui_component_manager.get_component('channels_container')
                if channels_container and hasattr(channels_container, 'update_all_outlier_detection_status'):
                    channels_container.update_all_outlier_detection_status(enabled)
                    logger.info(f"离群检测设置已加载: {'启用' if enabled else '禁用'}")
                else:
                    logger.warning("通道容器组件未找到，无法设置离群检测状态")

            except Exception as e:
                logger.warning(f"获取离群检测配置失败: {e}，使用默认禁用状态")
                # 默认禁用状态
                channels_container = self.main_window.ui_component_manager.get_component('channels_container')
                if channels_container and hasattr(channels_container, 'update_all_outlier_detection_status'):
                    channels_container.update_all_outlier_detection_status(False)

        except Exception as e:
            logger.error(f"加载离群检测设置失败: {e}")

    def initialize_printer_status(self):
        """初始化打印机状态显示"""
        try:
            # 手动触发一次打印机状态检查和UI更新
            if hasattr(self.main_window, 'printer_manager'):
                # 获取当前打印机状态
                current_status = self.main_window.printer_manager.get_current_status()
                printer_info = self.main_window.printer_manager.get_printer_status()

                # 更新UI显示
                self.main_window.ui_component_manager.update_printer_status(current_status, printer_info)

                logger.info(f"打印机状态初始化完成: {'已连接' if current_status else '未连接'}")
            else:
                logger.warning("打印机管理器未找到，无法初始化打印机状态")

        except Exception as e:
            logger.error(f"初始化打印机状态失败: {e}")

    def get_settings_status(self) -> Dict[str, Any]:
        """
        获取设置加载状态

        Returns:
            设置状态字典
        """
        try:
            return {
                'channel_enable_loaded': True,  # 简化状态检查
                'product_info_loaded': True,
                'test_parameter_loaded': True,
                'ui_display_loaded': True,
                'outlier_detection_loaded': True,
                'printer_status_initialized': True,
                'all_settings_loaded': True
            }

        except Exception as e:
            logger.error(f"获取设置状态失败: {e}")
            return {'error': str(e)}