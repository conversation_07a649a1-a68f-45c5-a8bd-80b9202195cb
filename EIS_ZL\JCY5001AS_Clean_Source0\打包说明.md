# JCY5001A鲸测云8路EIS阻抗筛选仪 V0.80.10 打包说明

## 📋 项目概述

**JCY5001A鲸测云8路EIS阻抗筛选仪** 是一个专业的电池交流阻抗测试系统上位机软件。

### 基本信息
- **产品名称**: JCY5001A鲸测云8路EIS阻抗筛选仪
- **产品型号**: JCY5001A
- **版本号**: V0.80.10
- **公司**: 鲸测云 (JingCeYun)
- **开发者**: <PERSON> (张海)
- **联系方式**: <EMAIL>
- **开发语言**: Python 3.10 + PyQt5
- **项目类型**: 电池管理系统(BMS)上位机软件

### 核心功能
- 8通道并行测试
- 支持锂电池和磷酸铁锂电池
- 实时阻抗数据监控
- Modbus RTU通信协议
- Excel数据导出
- 标签打印功能

## 🚀 使用Nuitka打包

### 前提条件
1. 确保已安装Python 3.10+
2. 安装Nuitka: `pip install nuitka`
3. 安装项目依赖: `pip install -r requirements.txt`

### 打包步骤

#### 方法1: 使用专用打包脚本（推荐）
```bash
# 在项目根目录运行
python build_nuitka_v08010.py
```

#### 方法2: 手动命令行打包
```bash
python -m nuitka \
    --standalone \
    --enable-plugin=pyqt5 \
    --include-data-dir=config=config \
    --include-data-dir=resources=resources \
    --include-data-dir=templates=templates \
    --include-data-dir=data=data \
    --include-package=PyQt5 \
    --include-package=serial \
    --include-package=numpy \
    --include-package=scipy \
    --include-package=pandas \
    --include-package=matplotlib \
    --include-package=openpyxl \
    --include-package=xlsxwriter \
    --include-package=PIL \
    --include-package=SQLAlchemy \
    --include-package=psutil \
    --windows-console-mode=disable \
    --windows-icon-from-ico=resources/icons/app_icon.ico \
    --output-dir=dist \
    --output-filename=鲸测云高压内阻测试仪_V0.80.10.exe \
    --company-name=JingCeYun \
    --product-name="JCY5001A Battery Impedance Tester" \
    --file-version=********* \
    --product-version=********* \
    --file-description="JingCeYun High Voltage Internal Resistance Tester" \
    --copyright="Copyright (C) 2025 JingCeYun. Author: weiwei" \
    --assume-yes-for-downloads \
    --show-progress \
    --show-memory \
    --jobs=4 \
    --low-memory \
    main.py
```

### 打包特点
- **独立模式**: 使用`--standalone`，不是单文件模式
- **完整目录**: 生成包含所有依赖的目录结构
- **中文支持**: 支持中文文件名和界面
- **资源包含**: 自动包含配置、资源、模板等目录

## 📦 输出结构

打包完成后会生成：
```
dist/
├── 鲸测云高压内阻测试仪_V0.80.10_YYYYMMDD_HHMMSS/
│   ├── 鲸测云高压内阻测试仪_V0.80.10.exe  # 主程序
│   ├── config/                              # 配置文件
│   ├── resources/                           # 资源文件
│   ├── templates/                           # 模板文件
│   ├── data/                               # 数据文件
│   ├── 安装说明.txt                         # 安装说明
│   ├── README.md                           # 项目说明
│   ├── requirements.txt                    # 依赖列表
│   └── [其他运行库文件...]
└── 鲸测云高压内阻测试仪_V0.80.10_YYYYMMDD_HHMMSS.zip  # 压缩包
```

## 💡 使用说明

### 部署步骤
1. 将整个目录复制到目标机器
2. 确保目标机器满足系统要求
3. 运行主程序exe文件
4. 首次运行会自动创建配置文件

### 系统要求
- Windows 10/11 (64位)
- 至少4GB内存
- 至少2GB磁盘空间
- 支持的串口设备

### 硬件连接
- **阻抗测试设备**: COM端口，115200波特率，RS422通信
- **扫码枪**: USB HID设备，自动识别
- **标签打印机**: NIIMBOT系列，USB连接

## ⚠️ 注意事项

1. **完整目录**: 整个目录都是必需的，请勿删除任何文件
2. **独立运行**: 目标机器无需安装Python环境
3. **权限要求**: 可能需要管理员权限访问串口
4. **防火墙**: 确保防火墙不阻止程序运行
5. **版本兼容**: 建议在相同或更新的Windows版本上运行

## 🔧 故障排除

### 常见问题
1. **缺少DLL**: 确保目标机器安装了Visual C++ Redistributable
2. **串口访问**: 检查COM端口权限和设备连接
3. **启动失败**: 查看日志文件排查问题
4. **界面异常**: 确保显示设置兼容

### 技术支持
- 公司: 鲸测云
- 作者: weiwei
- 版本: V0.80.10

## 📝 更新日志

### V0.80.10 更新内容
- 版本号更新至V0.80.10
- 优化系统稳定性和性能
- 完善8通道并行测试功能
- 增强数据管理和导出功能
- 修复已知问题和bug
- 优化用户界面体验

---

**重要提醒**: 本软件为专业测试设备配套软件，请确保正确连接硬件设备后使用。
