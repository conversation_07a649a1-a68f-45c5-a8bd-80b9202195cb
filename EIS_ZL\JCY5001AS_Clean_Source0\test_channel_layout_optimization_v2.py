#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 通道布局优化测试脚本 v2
测试电池扫码值框扩展和RS/RCT显示区域优化效果

优化内容：
1. 显示数值框宽度压缩为60-80像素
2. RS和RCT标签向右移动，为扫码值框腾出空间
3. 扩展电池扫码值输入框长度
4. 压缩左侧数值显示区域，为右侧扫码值提供更多空间
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QLineEdit, QGroupBox, QFrame, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.components.channel_display_widget import ChannelDisplayWidget
    from utils.config_manager import ConfigManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("将使用简化的演示版本")

class ChannelLayoutOptimizationTest(QMainWindow):
    """通道布局优化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 通道布局优化测试 v2")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 通道布局优化测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
        优化内容：
        • 显示数值框宽度压缩为60-80像素（原120-200像素）
        • RS和RCT标签向右移动，为扫码值框腾出空间
        • 扩展电池扫码值输入框长度（180-300像素）
        • 压缩左侧数值显示区域，优化整体布局
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("color: #7f8c8d; background-color: #f8f9fa; padding: 10px; border-radius: 5px;")
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        self._create_test_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
    
    def _create_test_area(self, layout):
        """创建测试区域"""
        test_frame = QFrame()
        test_frame.setStyleSheet("background-color: #f5f5f5; border-radius: 10px; padding: 10px;")
        test_layout = QVBoxLayout(test_frame)
        
        # 尝试使用真实的通道组件
        try:
            config_manager = ConfigManager()
            
            # 创建通道组件网格
            channels_layout = QHBoxLayout()
            
            # 创建两个通道进行对比测试
            for i in range(2):
                channel_number = i + 1
                channel = ChannelDisplayWidget(channel_number, config_manager)
                
                # 设置测试数据
                self._set_test_data(channel, channel_number)
                
                channels_layout.addWidget(channel)
            
            test_layout.addLayout(channels_layout)
            
        except Exception as e:
            print(f"无法创建真实通道组件: {e}")
            # 创建演示版本
            demo_layout = self._create_demo_channels()
            test_layout.addLayout(demo_layout)
        
        layout.addWidget(test_frame)
    
    def _set_test_data(self, channel, channel_number):
        """设置测试数据"""
        try:
            # 设置长电池码进行测试
            long_battery_codes = [
                "BAT123456789ABCDEF0123456789",  # 28字符长码
                "CELL987654321ZYXWVU9876543210"  # 30字符长码
            ]
            
            battery_code = long_battery_codes[channel_number - 1]
            if hasattr(channel, 'battery_code_edit'):
                channel.battery_code_edit.setText(battery_code)
            
            # 设置阻抗值
            rs_values = ["12.345", "98.765"]
            rct_values = ["234.56", "876.54"]
            
            if hasattr(channel, 'rs_label'):
                channel.rs_label.setText(rs_values[channel_number - 1])
            if hasattr(channel, 'rct_label'):
                channel.rct_label.setText(rct_values[channel_number - 1])
            
            # 设置其他测试数据
            if hasattr(channel, 'test_count_label'):
                channel.test_count_label.setText(str(channel_number * 10))
            if hasattr(channel, 'test_time_label'):
                channel.test_time_label.setText(f"00:0{channel_number}:30")
            if hasattr(channel, 'voltage_label'):
                channel.voltage_label.setText(f"{3.2 + channel_number * 0.1:.3f}")
                
        except Exception as e:
            print(f"设置测试数据失败: {e}")
    
    def _create_demo_channels(self):
        """创建演示通道（当无法加载真实组件时）"""
        demo_layout = QHBoxLayout()
        
        for i in range(2):
            channel_number = i + 1
            demo_channel = self._create_demo_channel(channel_number)
            demo_layout.addWidget(demo_channel)
        
        return demo_layout
    
    def _create_demo_channel(self, channel_number):
        """创建演示通道"""
        # 创建通道组框
        channel_group = QGroupBox(f"通道 {channel_number}")
        channel_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d1d5db;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 12px 0 12px;
                color: #1f2937;
                font-size: 15pt;
                font-weight: 600;
                background-color: #ffffff;
            }
        """)
        
        # 创建内容布局
        content_layout = QVBoxLayout(channel_group)
        content_layout.setContentsMargins(6, 8, 6, 6)
        content_layout.setSpacing(3)
        
        # 创建主内容区域
        main_container = QFrame()
        main_layout = QHBoxLayout(main_container)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(8, 8, 8, 8)
        
        # 左列：基本信息（压缩）
        left_column = self._create_demo_left_column(channel_number)
        main_layout.addLayout(left_column, 15)  # 1.5份权重
        
        # 右列：阻抗值显示（扩展）
        right_column = self._create_demo_right_column(channel_number)
        main_layout.addLayout(right_column, 35)  # 3.5份权重
        
        content_layout.addWidget(main_container)
        
        return channel_group
    
    def _create_demo_left_column(self, channel_number):
        """创建演示左列"""
        left_column = QVBoxLayout()
        left_column.setSpacing(4)
        
        # 测试计数和时间（压缩版本）
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(4)
        
        # 计数
        count_label = QLabel("计数:")
        count_label.setStyleSheet("font-size: 11pt; color: #7f8c8d; font-weight: bold;")
        count_label.setMaximumWidth(45)
        count_time_layout.addWidget(count_label)
        
        count_value = QLabel(str(channel_number * 10))
        count_value.setStyleSheet("font-size: 12pt; color: #27ae60; font-weight: bold;")
        count_value.setMaximumWidth(50)
        count_time_layout.addWidget(count_value)
        
        # 分隔符
        separator = QLabel("|")
        separator.setStyleSheet("font-size: 10pt; color: #bdc3c7;")
        separator.setMaximumWidth(10)
        count_time_layout.addWidget(separator)
        
        # 时间
        time_label = QLabel("用时:")
        time_label.setStyleSheet("font-size: 11pt; color: #7f8c8d; font-weight: bold;")
        time_label.setMaximumWidth(45)
        count_time_layout.addWidget(time_label)
        
        time_value = QLabel(f"00:0{channel_number}:30")
        time_value.setStyleSheet("""
            font-size: 11pt; font-weight: bold; color: #3498db;
            background-color: #ebf3fd; border: 1px solid #3498db;
            border-radius: 2px; padding: 1px 3px;
            max-height: 16px; min-width: 60px; max-width: 80px;
        """)
        count_time_layout.addWidget(time_value)
        
        count_time_layout.addStretch()
        left_column.addLayout(count_time_layout)
        
        # 电池码输入（扩展版本）
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(4)
        
        battery_label = QLabel("电池:")
        battery_label.setStyleSheet("font-size: 12pt; color: #7f8c8d; font-weight: bold;")
        battery_label.setMaximumWidth(45)
        battery_layout.addWidget(battery_label)
        
        # 长电池码测试
        long_codes = [
            "BAT123456789ABCDEF0123456789",
            "CELL987654321ZYXWVU9876543210"
        ]
        
        battery_edit = QLineEdit(long_codes[channel_number - 1])
        battery_edit.setStyleSheet("""
            border: 1px solid #bdc3c7; border-radius: 3px;
            padding: 2px 6px; background-color: white;
            font-size: 12pt; max-height: 30px; color: #2c3e50;
            min-height: 28px; min-width: 180px; max-width: 300px;
        """)
        battery_layout.addWidget(battery_edit, 1)
        
        left_column.addLayout(battery_layout)
        
        # 电压显示（压缩版本）
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(4)
        
        voltage_label = QLabel("电压:")
        voltage_label.setStyleSheet("font-size: 11pt; color: #7f8c8d; font-weight: bold;")
        voltage_label.setMaximumWidth(45)
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel(f"{3.2 + channel_number * 0.1:.3f}")
        voltage_value.setMaximumWidth(70)
        voltage_layout.addWidget(voltage_value)
        
        voltage_layout.addStretch()
        left_column.addLayout(voltage_layout)
        
        left_column.addStretch()
        return left_column
    
    def _create_demo_right_column(self, channel_number):
        """创建演示右列"""
        right_column = QVBoxLayout()
        right_column.setSpacing(4)
        
        # RS显示（优化版本）
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(6)
        rs_layout.addStretch(1)  # 左侧弹性空间，将标签向右移动
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet("font-size: 11pt; color: #7f8c8d; font-weight: bold;")
        rs_title.setMinimumWidth(65)
        rs_title.setMaximumWidth(75)
        rs_title.setAlignment(Qt.AlignRight)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("12.345" if channel_number == 1 else "98.765")
        rs_value.setStyleSheet("font-size: 13pt; font-weight: bold; color: #2c3e50;")
        rs_value.setMinimumWidth(70)
        rs_value.setMaximumWidth(85)
        rs_value.setAlignment(Qt.AlignLeft)
        rs_layout.addWidget(rs_value)
        
        rs_layout.addStretch(0)  # 减少右侧弹性空间
        right_column.addLayout(rs_layout)
        
        # RCT显示（优化版本）
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(6)
        rct_layout.addStretch(1)  # 左侧弹性空间，将标签向右移动
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet("font-size: 11pt; color: #7f8c8d; font-weight: bold;")
        rct_title.setMinimumWidth(65)
        rct_title.setMaximumWidth(75)
        rct_title.setAlignment(Qt.AlignRight)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("234.56" if channel_number == 1 else "876.54")
        rct_value.setStyleSheet("font-size: 13pt; font-weight: bold; color: #2c3e50;")
        rct_value.setMinimumWidth(70)
        rct_value.setMaximumWidth(85)
        rct_value.setAlignment(Qt.AlignLeft)
        rct_layout.addWidget(rct_value)
        
        rct_layout.addStretch(0)  # 减少右侧弹性空间
        right_column.addLayout(rct_layout)
        
        right_column.addStretch()
        return right_column
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 测试长电池码按钮
        test_long_code_btn = QPushButton("测试超长电池码")
        test_long_code_btn.clicked.connect(self._test_long_battery_code)
        test_long_code_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(test_long_code_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置测试")
        reset_btn.clicked.connect(self._reset_test)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _test_long_battery_code(self):
        """测试超长电池码"""
        print("测试超长电池码显示效果...")
        # 这里可以添加更多测试逻辑
    
    def _reset_test(self):
        """重置测试"""
        print("重置测试数据...")
        # 这里可以添加重置逻辑

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = ChannelLayoutOptimizationTest()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
