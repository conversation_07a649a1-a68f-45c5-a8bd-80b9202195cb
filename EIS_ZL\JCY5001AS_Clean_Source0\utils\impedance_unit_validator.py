#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单位验证工具模块

提供阻抗数据单位验证和转换功能，确保产线和研发系统数据一致性。

Author: Augment Agent
Date: 2025-01-28
"""

import logging
from typing import Union, Tuple

logger = logging.getLogger(__name__)


class ImpedanceUnitValidator:
    """阻抗单位验证器"""
    
    # 锂电池阻抗合理范围定义
    VALID_RANGES = {
        'mΩ': {
            'rs_min': 0.01, 'rs_max': 50.0,      # Rs: 0.01-50 mΩ
            'rct_min': 0.01, 'rct_max': 50.0,    # Rct: 0.01-50 mΩ
            'real_min': 0.01, 'real_max': 50.0,  # 实部: 0.01-50 mΩ
            'imag_min': -50.0, 'imag_max': 50.0  # 虚部: -50 to 50 mΩ
        },
        'μΩ': {
            'rs_min': 10.0, 'rs_max': 50000.0,      # Rs: 10-50000 μΩ
            'rct_min': 10.0, 'rct_max': 50000.0,    # Rct: 10-50000 μΩ
            'real_min': 10.0, 'real_max': 50000.0,  # 实部: 10-50000 μΩ
            'imag_min': -50000.0, 'imag_max': 50000.0  # 虚部: -50000 to 50000 μΩ
        }
    }
    
    @classmethod
    def validate_rs_value(cls, value: float, unit: str = 'mΩ') -> bool:
        """验证Rs值"""
        if unit not in cls.VALID_RANGES:
            logger.warning(f"不支持的单位: {unit}")
            return False
        
        range_def = cls.VALID_RANGES[unit]
        is_valid = range_def['rs_min'] <= value <= range_def['rs_max']
        
        if not is_valid:
            logger.warning(f"Rs值超出合理范围: {value} {unit} (期望: {range_def['rs_min']}-{range_def['rs_max']} {unit})")
        
        return is_valid
    
    @classmethod
    def validate_rct_value(cls, value: float, unit: str = 'mΩ') -> bool:
        """验证Rct值"""
        if unit not in cls.VALID_RANGES:
            logger.warning(f"不支持的单位: {unit}")
            return False
        
        range_def = cls.VALID_RANGES[unit]
        is_valid = range_def['rct_min'] <= value <= range_def['rct_max']
        
        if not is_valid:
            logger.warning(f"Rct值超出合理范围: {value} {unit} (期望: {range_def['rct_min']}-{range_def['rct_max']} {unit})")
        
        return is_valid
    
    @classmethod
    def validate_impedance_data(cls, real: float, imag: float, unit: str = 'mΩ') -> Tuple[bool, bool]:
        """验证阻抗数据"""
        if unit not in cls.VALID_RANGES:
            logger.warning(f"不支持的单位: {unit}")
            return False, False
        
        range_def = cls.VALID_RANGES[unit]
        
        real_valid = range_def['real_min'] <= real <= range_def['real_max']
        imag_valid = range_def['imag_min'] <= imag <= range_def['imag_max']
        
        if not real_valid:
            logger.warning(f"实部阻抗超出合理范围: {real} {unit}")
        if not imag_valid:
            logger.warning(f"虚部阻抗超出合理范围: {imag} {unit}")
        
        return real_valid, imag_valid
    
    @classmethod
    def convert_uohm_to_mohm(cls, value_uohm: float) -> float:
        """μΩ转换为mΩ"""
        return value_uohm / 1000.0
    
    @classmethod
    def convert_mohm_to_uohm(cls, value_mohm: float) -> float:
        """mΩ转换为μΩ"""
        return value_mohm * 1000.0
    
    @classmethod
    def detect_unit_from_value(cls, rs_value: float, rct_value: float) -> str:
        """根据数值大小推测单位"""
        # 如果Rs和Rct都在mΩ合理范围内
        if (cls.VALID_RANGES['mΩ']['rs_min'] <= rs_value <= cls.VALID_RANGES['mΩ']['rs_max'] and
            cls.VALID_RANGES['mΩ']['rct_min'] <= rct_value <= cls.VALID_RANGES['mΩ']['rct_max']):
            return 'mΩ'
        
        # 如果Rs和Rct都在μΩ合理范围内
        if (cls.VALID_RANGES['μΩ']['rs_min'] <= rs_value <= cls.VALID_RANGES['μΩ']['rs_max'] and
            cls.VALID_RANGES['μΩ']['rct_min'] <= rct_value <= cls.VALID_RANGES['μΩ']['rct_max']):
            return 'μΩ'
        
        # 无法确定，返回默认单位
        logger.warning(f"无法确定单位，Rs={rs_value}, Rct={rct_value}")
        return 'mΩ'
