# 电池检测优化配置
# 
# 此配置用于控制电池检测的行为和性能
# 
# 优化说明：
# - 禁用阻抗响应检测可以节省约12秒的检测时间
# - 快速模式只进行电压检测，适用于大多数测试场景
# - 如果需要检测接触不良，可以启用完整模式

[battery_detection]
# 是否启用快速检测模式（推荐）
fast_detection_mode = true

# 是否启用阻抗响应检测（会增加12秒检测时间）
impedance_response_check_enabled = false

# 电压检测阈值
voltage_min_threshold = 2.0
voltage_max_threshold = 5.0

# 检测超时设置
voltage_check_timeout = 1.0
impedance_response_timeout = 3.0

[performance]
# 检测结果缓存时间（秒）
cache_duration = 5

# 状态检查间隔（秒）
status_check_interval = 0.2

# 最大状态检查次数
max_status_check_attempts = 10

[logging]
# 是否显示详细的检测日志
verbose_logging = true

# 是否显示时间节省信息
show_time_savings = true
