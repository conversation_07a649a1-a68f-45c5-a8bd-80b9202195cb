#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 解锁码生成工具（厂家专用）
独立的解锁码生成应用程序，不包含在客户端软件中

Author: Jack
Date: 2025-01-30
"""

import sys
import os
import logging
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QGroupBox, QTextEdit, QMessageBox, QComboBox,
    QSpinBox, QSplashScreen
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unlock_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class UnlockCodeGeneratorApp(QDialog):
    """解锁码生成工具应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        super().__init__()
        
        self.license_manager = None
        self._init_license_manager()
        self._init_ui()
        
        logger.info("解锁码生成工具初始化完成")
    
    def _init_license_manager(self):
        """初始化授权管理器"""
        try:
            from utils.license_manager import LicenseManager
            self.license_manager = LicenseManager()
        except Exception as e:
            logger.error(f"初始化授权管理器失败: {e}")
            QMessageBox.critical(self, "初始化错误", f"授权管理器初始化失败：\n\n{e}")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS - 解锁码生成工具（厂家专用）")
        self.setFixedSize(800, 700)
        
        # 设置应用图标
        try:
            icon_path = os.path.join("resources", "icons", "unlock.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        # 应用工业设计风格
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                color: #2c3e50;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 16px;
                font-size: 14pt;
                background-color: white;
                min-height: 35px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("JCY5001AS 解锁码生成工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(20)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #e74c3c; margin-bottom: 15px;")
        main_layout.addWidget(title_label)
        
        # 警告标签
        warning_label = QLabel("⚠️ 厂家专用工具 - 请勿分发给客户")
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setStyleSheet("""
            color: #e74c3c;
            font-weight: bold;
            font-size: 11pt;
            background-color: #fdf2f2;
            border: 2px solid #e74c3c;
            border-radius: 5px;
            padding: 8px;
            margin-bottom: 10px;
        """)
        main_layout.addWidget(warning_label)
        
        # 身份验证区域
        self._create_auth_section(main_layout)
        
        # 解锁码生成区域
        self._create_generation_section(main_layout)
        
        # 生成的解锁码显示区域
        self._create_result_section(main_layout)
        
        # 底部按钮
        self._create_buttons(main_layout)
    
    def _create_auth_section(self, main_layout):
        """创建身份验证区域"""
        auth_group = QGroupBox("身份验证")
        auth_layout = QVBoxLayout(auth_group)
        
        # 管理员密码
        password_layout = QHBoxLayout()
        password_label = QLabel("管理员密码:")
        password_label.setMinimumWidth(100)
        
        self.admin_password_input = QLineEdit()
        self.admin_password_input.setEchoMode(QLineEdit.Password)
        self.admin_password_input.setPlaceholderText("请输入管理员密码")
        
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.admin_password_input)
        auth_layout.addLayout(password_layout)
        
        main_layout.addWidget(auth_group)
    
    def _create_generation_section(self, main_layout):
        """创建解锁码生成区域"""
        gen_group = QGroupBox("解锁码生成")
        gen_layout = QVBoxLayout(gen_group)
        
        # 客户硬件指纹
        fingerprint_layout = QVBoxLayout()
        fingerprint_label = QLabel("客户硬件指纹:")
        
        self.customer_fingerprint_input = QTextEdit()
        self.customer_fingerprint_input.setMinimumHeight(140)
        self.customer_fingerprint_input.setMaximumHeight(160)
        self.customer_fingerprint_input.setPlaceholderText("请粘贴客户提供的硬件指纹...")
        self.customer_fingerprint_input.setStyleSheet("""
            QTextEdit {
                font-size: 13pt;
                line-height: 1.5;
                padding: 16px;
            }
        """)
        
        fingerprint_layout.addWidget(fingerprint_label)
        fingerprint_layout.addWidget(self.customer_fingerprint_input)
        gen_layout.addLayout(fingerprint_layout)
        
        # 解锁类型和天数设置
        type_layout = QHBoxLayout()
        
        type_label = QLabel("解锁类型:")
        type_label.setMinimumWidth(80)
        
        self.unlock_type_combo = QComboBox()
        self.unlock_type_combo.addItems(["完整解锁", "临时解锁"])
        self.unlock_type_combo.currentTextChanged.connect(self._on_unlock_type_changed)
        
        self.days_label = QLabel("天数:")
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 365)
        self.days_spinbox.setValue(30)
        self.days_spinbox.setSuffix(" 天")
        
        # 初始隐藏天数设置
        self.days_label.setVisible(False)
        self.days_spinbox.setVisible(False)
        
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.unlock_type_combo)
        type_layout.addWidget(self.days_label)
        type_layout.addWidget(self.days_spinbox)
        type_layout.addStretch()
        
        gen_layout.addLayout(type_layout)
        
        # 生成按钮
        self.generate_button = QPushButton("🔑 生成解锁码")
        self.generate_button.clicked.connect(self._generate_unlock_code)
        self.generate_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                font-size: 12pt;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        gen_layout.addWidget(self.generate_button)
        
        main_layout.addWidget(gen_group)
    
    def _create_result_section(self, main_layout):
        """创建结果显示区域"""
        result_group = QGroupBox("生成的解锁码")
        result_layout = QVBoxLayout(result_group)
        
        self.result_display = QTextEdit()
        self.result_display.setMinimumHeight(140)
        self.result_display.setMaximumHeight(160)
        self.result_display.setReadOnly(True)
        self.result_display.setPlaceholderText("生成的解锁码将显示在这里...")
        self.result_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                font-family: 'Courier New', monospace;
                font-size: 15pt;
                font-weight: bold;
                line-height: 1.5;
                padding: 16px;
            }
        """)
        
        # 复制按钮
        copy_button = QPushButton("📋 复制解锁码")
        copy_button.clicked.connect(self._copy_unlock_code)
        copy_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        result_layout.addWidget(self.result_display)
        result_layout.addWidget(copy_button)
        
        main_layout.addWidget(result_group)
    
    def _create_buttons(self, main_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        
        # 关于按钮
        about_button = QPushButton("关于")
        about_button.clicked.connect(self._show_about)
        about_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        # 退出按钮
        exit_button = QPushButton("退出")
        exit_button.clicked.connect(self.accept)
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        button_layout.addWidget(about_button)
        button_layout.addStretch()
        button_layout.addWidget(exit_button)
        
        main_layout.addLayout(button_layout)
    
    def _on_unlock_type_changed(self, unlock_type):
        """解锁类型变更处理"""
        is_temp = (unlock_type == "临时解锁")
        self.days_label.setVisible(is_temp)
        self.days_spinbox.setVisible(is_temp)
    
    def _generate_unlock_code(self):
        """生成解锁码"""
        try:
            # 验证管理员密码
            admin_password = self.admin_password_input.text().strip()
            if not admin_password:
                QMessageBox.warning(self, "输入错误", "请输入管理员密码")
                return
            
            if admin_password != "JCY5001-ADMIN":
                QMessageBox.warning(self, "验证失败", "管理员密码错误")
                return
            
            # 获取客户硬件指纹
            customer_fingerprint = self.customer_fingerprint_input.toPlainText().strip()
            if not customer_fingerprint:
                QMessageBox.warning(self, "输入错误", "请输入客户硬件指纹")
                return
            
            # 清理硬件指纹（移除空格、换行等）
            customer_fingerprint = ''.join(customer_fingerprint.split())
            
            # 获取解锁类型和天数
            unlock_type_text = self.unlock_type_combo.currentText()
            if unlock_type_text == "完整解锁":
                unlock_type = "full"
                extend_days = None
            else:
                unlock_type = "temp"
                extend_days = self.days_spinbox.value()
            
            # 生成解锁码
            if not self.license_manager:
                QMessageBox.critical(self, "错误", "授权管理器未初始化")
                return
            
            result = self.license_manager.generate_unlock_code(
                customer_fingerprint=customer_fingerprint,
                customer_id="CUSTOMER_001",
                unlock_type=unlock_type,
                extend_days=extend_days
            )
            
            if result.get('success', False):
                unlock_code = result.get('unlock_code', '')
                self.result_display.setText(unlock_code)
                
                QMessageBox.information(
                    self,
                    "生成成功",
                    f"解锁码生成成功！\n\n类型: {result.get('unlock_type', '')}\n"
                    f"{'天数: ' + str(extend_days) + ' 天' if extend_days else ''}\n\n"
                    f"请将解锁码发送给客户。"
                )
            else:
                error_msg = result.get('message', '生成失败')
                QMessageBox.warning(self, "生成失败", error_msg)
                
        except Exception as e:
            logger.error(f"生成解锁码失败: {e}")
            QMessageBox.critical(self, "错误", f"生成解锁码时发生错误：\n\n{e}")
    
    def _copy_unlock_code(self):
        """复制解锁码"""
        try:
            unlock_code = self.result_display.toPlainText().strip()
            if unlock_code:
                clipboard = QApplication.clipboard()
                clipboard.setText(unlock_code)
                QMessageBox.information(self, "复制成功", "解锁码已复制到剪贴板")
            else:
                QMessageBox.warning(self, "复制失败", "没有可复制的解锁码")
                
        except Exception as e:
            logger.error(f"复制解锁码失败: {e}")
            QMessageBox.warning(self, "复制失败", f"复制失败：{e}")
    
    def _show_about(self):
        """显示关于信息"""
        about_text = """
JCY5001AS 解锁码生成工具

版本: 1.0.0
作者: Jack
日期: 2025-01-30

这是厂家专用的解锁码生成工具，用于为客户生成软件解锁码。

⚠️ 重要提醒：
• 此工具仅供厂家内部使用
• 请勿将此工具分发给客户
• 管理员密码请妥善保管
• 生成的解锁码请通过安全渠道发送给客户

技术支持：请联系开发团队
        """
        QMessageBox.about(self, "关于", about_text.strip())


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app.setApplicationName("JCY5001AS解锁码生成工具")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("鲸测云")
        
        # 显示启动画面
        splash_text = "JCY5001AS\n解锁码生成工具\n\n厂家专用"
        splash = QSplashScreen()
        splash.showMessage(splash_text, Qt.AlignCenter, Qt.white)
        splash.show()
        
        # 延迟显示主窗口
        def show_main_window():
            splash.close()
            generator = UnlockCodeGeneratorApp()
            generator.show()
            generator.raise_()
            generator.activateWindow()
        
        QTimer.singleShot(2000, show_main_window)
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        try:
            QMessageBox.critical(None, "启动错误", f"应用程序启动失败：\n\n{e}")
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    main()
