# 设备状态码处理功能说明

## 📋 概述

本文档详细说明了JCY5001A阻抗测试仪的设备状态码处理功能，包括状态码0003H（电池电压低或电池未安装）等异常状态的检测、处理和UI显示机制。

## 🎯 功能特性

### 1. 完整的状态码支持
- ✅ **0000H**: 空闲状态
- ✅ **0001H**: ZM测量中
- ✅ **0002H**: 平衡功能运行中
- ✅ **0003H**: 电池电压低或电池未安装 ⚠️
- ✅ **0004H**: 设置错误 ⚠️
- ✅ **0005H**: 硬件错误/ADC错误 ❌
- ✅ **0006H**: 测量完成

### 2. 智能异常处理
- 🔍 **自动检测**: 测试前自动检查所有通道状态
- 🚫 **异常跳过**: 自动跳过异常通道，继续测试正常通道
- 📊 **状态分类**: 按严重程度分类（正常/警告/错误/严重）
- 🔄 **实时监控**: 测试过程中持续监控状态变化

### 3. 直观的UI显示
- 🎨 **状态颜色**: 不同异常状态使用不同颜色标识
- 📝 **详细描述**: 显示具体的异常原因和建议
- 🔔 **实时更新**: 状态变化时立即更新UI显示
- 📋 **状态摘要**: 提供整体状态统计信息

## 🏗️ 架构设计

### 核心组件

#### 1. DeviceStatusManager (设备状态码管理器)
```python
# 位置: backend/device_status_manager.py
# 职责: 状态码解析、分析和处理策略
```

**主要功能:**
- 状态码枚举定义和解析
- 通道状态信息生成
- 可用通道筛选
- 异常通道识别
- 状态统计摘要

#### 2. ChannelStatusException (通道状态异常类)
```python
# 位置: backend/exceptions.py
# 职责: 专门的通道状态异常处理
```

**新增错误码:**
- `CHANNEL_BATTERY_ERROR` (1110): 通道电池异常
- `CHANNEL_HARDWARE_ERROR` (1111): 通道硬件异常
- `CHANNEL_SETTING_ERROR` (1112): 通道设置异常
- `CHANNEL_STATUS_UNKNOWN` (1113): 通道状态未知
- `CHANNEL_BALANCING_ERROR` (1114): 通道平衡功能异常

#### 3. UI状态显示扩展
```python
# 位置: ui/components/channel_display_widget.py
# 职责: 通道异常状态的可视化显示
```

**新增状态类型:**
- `channel_error`: 通道异常
- `battery_error`: 电池异常
- `hardware_error`: 硬件异常
- `skipped`: 跳过测试

## 🔧 使用方法

### 1. 基本状态检查

```python
from backend.communication_manager import CommunicationManager

# 获取通信管理器实例
comm_manager = CommunicationManager(config)

# 检查启用通道的状态
enabled_channels = [1, 2, 3, 4, 5, 6, 7, 8]
status_result = comm_manager.check_channels_status(enabled_channels)

# 获取检查结果
available_channels = status_result['available_channels']
error_channels = status_result['error_channels']
can_start_test = status_result['can_start_test']

print(f"可用通道: {available_channels}")
print(f"异常通道: {error_channels}")
print(f"可以开始测试: {can_start_test}")
```

### 2. 单个通道状态查询

```python
# 查询单个通道状态
channel_index = 2  # 通道3 (索引从0开始)
status_info = comm_manager.get_channel_status_info(channel_index)

if status_info:
    print(f"通道{status_info['channel_number']}:")
    print(f"  状态码: {status_info['status_code_hex']}")
    print(f"  描述: {status_info['description']}")
    print(f"  严重程度: {status_info['severity']}")
    print(f"  是否跳过: {status_info['should_skip']}")
    print(f"  可以测试: {status_info['can_test']}")
```

### 3. UI状态更新

```python
# 在通道显示组件中设置异常状态
channel_widget.set_channel_status_error(
    status_code=0x0003,
    description="电池电压低或未安装",
    severity="error"
)

# 清除异常状态
channel_widget.clear_channel_error()
```

## 🎨 UI样式说明

### 状态颜色方案

| 状态类型 | 颜色 | 背景色 | 说明 |
|---------|------|--------|------|
| 通道异常 | #e67e22 (橙色) | #fdf2e9 | 一般性通道异常 |
| 电池异常 | #8e44ad (紫色) | #f4ecf7 | 电池电压低或未安装 |
| 硬件异常 | #c0392b (深红) | #fadbd8 | 硬件错误/ADC错误 |
| 跳过测试 | #7f8c8d (灰色) | #ecf0f1 | 平衡运行中等 |

### CSS样式定义

```css
/* 电池异常状态 */
QLabel#resultBatteryError {
    color: #8e44ad;
    font-weight: bold;
    font-size: 14px;
    background-color: #f4ecf7;
    border: 1px solid #8e44ad;
    border-radius: 3px;
    padding: 2px 4px;
}

/* 硬件异常状态 */
QLabel#resultHardwareError {
    color: #c0392b;
    font-weight: bold;
    font-size: 14px;
    background-color: #fadbd8;
    border: 1px solid #c0392b;
    border-radius: 3px;
    padding: 2px 4px;
}
```

## 🔄 工作流程

### 测试启动流程

```mermaid
graph TD
    A[启动测试] --> B[检查通道状态码]
    B --> C{有异常通道?}
    C -->|是| D[标记异常通道]
    C -->|否| E[所有通道正常]
    D --> F[更新UI显示异常]
    D --> G[筛选可用通道]
    G --> H{有可用通道?}
    H -->|是| I[使用可用通道测试]
    H -->|否| J[停止测试]
    E --> I
    I --> K[开始测试]
    F --> G
```

### 状态码处理策略

| 状态码 | 严重程度 | 处理策略 | UI显示 |
|-------|---------|---------|--------|
| 0x0000 | 正常 | 继续测试 | 等待测试 |
| 0x0001 | 正常 | 等待完成 | 测量中 |
| 0x0002 | 警告 | 跳过通道 | 跳过测试 |
| 0x0003 | 错误 | 跳过通道 | 电池异常 |
| 0x0004 | 错误 | 跳过通道 | 设置异常 |
| 0x0005 | 严重 | 跳过通道 | 硬件异常 |
| 0x0006 | 正常 | 继续测试 | 测量完成 |

## 📊 状态统计信息

系统会自动生成状态统计摘要：

```python
{
    "total_channels": 8,           # 总通道数
    "available_channels": 5,       # 可用通道数
    "error_channels": 3,           # 异常通道数
    "battery_errors": 1,           # 电池异常数
    "hardware_errors": 1,          # 硬件异常数
    "setting_errors": 1            # 设置异常数
}
```

## 🚨 注意事项

### 1. 状态码检查时机
- ✅ **测试启动前**: 必须检查所有启用通道状态
- ✅ **测试过程中**: 定期检查状态变化
- ✅ **异常发生时**: 立即检查相关通道状态

### 2. 异常处理原则
- 🔄 **自动恢复**: 优先尝试自动恢复异常状态
- 🚫 **安全跳过**: 无法恢复时安全跳过异常通道
- 📝 **详细记录**: 记录所有异常状态和处理过程
- 👤 **用户通知**: 及时通知用户异常情况

### 3. 性能考虑
- ⚡ **批量检查**: 使用批量读取提高检查效率
- 🔄 **缓存机制**: 合理缓存状态信息避免重复查询
- ⏱️ **超时处理**: 设置合理的超时时间避免阻塞

## 🔧 配置选项

可以通过配置文件调整状态码处理行为：

```json
{
    "device_status": {
        "check_interval": 1000,        // 状态检查间隔(ms)
        "auto_skip_errors": true,      // 自动跳过异常通道
        "strict_mode": false,          // 严格模式(任何异常都停止测试)
        "retry_count": 3,              // 状态读取重试次数
        "timeout": 5000                // 状态读取超时(ms)
    }
}
```

## 📈 扩展性

### 1. 新增状态码支持
只需在`DeviceStatusCode`枚举中添加新的状态码定义，系统会自动支持。

### 2. 自定义处理策略
可以通过继承`DeviceStatusManager`类来实现自定义的状态处理策略。

### 3. UI样式定制
通过修改CSS样式文件可以自定义异常状态的显示效果。

---

**作者**: Jack  
**日期**: 2025-01-30  
**版本**: 1.0.0
