#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
序列号模式配置工具

用于配置序列号管理器的工作模式：
- 轻量级模式：只在内存中维护序列号，不持久化存储，大幅减少系统开销
- 完整模式：完整的序列号管理功能，包括历史记录和重复检查

Author: Jack
Date: 2025-06-20
"""

import json
import os
import sys
from pathlib import Path

def load_config():
    """加载配置文件"""
    config_path = Path("config/app_config.json")
    
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return {}
    else:
        print("⚠️ 配置文件不存在，将创建新配置")
        return {}

def save_config(config):
    """保存配置文件"""
    config_path = Path("config/app_config.json")
    
    # 确保目录存在
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存到: {config_path}")
        return True
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")
        return False

def get_current_mode(config):
    """获取当前序列号模式"""
    lightweight_mode = config.get('serial_numbers', {}).get('lightweight_mode', True)
    return "轻量级模式" if lightweight_mode else "完整模式"

def set_lightweight_mode(config, enabled=True):
    """设置轻量级模式"""
    if 'serial_numbers' not in config:
        config['serial_numbers'] = {}
    
    config['serial_numbers']['lightweight_mode'] = enabled
    
    # 如果启用轻量级模式，清理历史数据配置
    if enabled:
        if 'used_list' in config['serial_numbers']:
            del config['serial_numbers']['used_list']
        if 'current_sequence' in config['serial_numbers']:
            del config['serial_numbers']['current_sequence']
        print("🧹 已清理历史序列号数据配置")

def show_mode_comparison():
    """显示模式对比"""
    print("\n" + "="*60)
    print("📊 序列号管理模式对比")
    print("="*60)
    
    print("\n🚀 轻量级模式 (推荐)")
    print("  ✅ 优点:")
    print("    • 启动速度快，无需加载历史记录")
    print("    • 内存占用少，不持久化存储")
    print("    • 日志输出极少，运行流畅")
    print("    • 适合大多数测试场景")
    print("  ⚠️ 限制:")
    print("    • 重启后不记住历史序列号")
    print("    • 无法防止跨会话的重复序列号")
    
    print("\n🔧 完整模式")
    print("  ✅ 优点:")
    print("    • 完整的序列号历史记录")
    print("    • 严格的重复检查")
    print("    • 持久化存储，重启后保持")
    print("  ❌ 缺点:")
    print("    • 启动速度慢（需加载历史记录）")
    print("    • 内存占用高，磁盘IO频繁")
    print("    • 大量日志输出，影响性能")

def main():
    """主函数"""
    print("🔧 序列号模式配置工具")
    print("="*40)
    
    # 加载当前配置
    config = load_config()
    current_mode = get_current_mode(config)
    
    print(f"\n📋 当前模式: {current_mode}")
    
    while True:
        print("\n🎯 请选择操作:")
        print("1. 启用轻量级模式 (推荐)")
        print("2. 启用完整模式")
        print("3. 查看模式对比")
        print("4. 查看当前状态")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == "0":
            print("👋 再见!")
            break
        elif choice == "1":
            set_lightweight_mode(config, True)
            if save_config(config):
                print("✅ 轻量级模式已启用!")
                print("💡 重启软件后生效，将大幅提升性能并减少日志输出")
        elif choice == "2":
            set_lightweight_mode(config, False)
            if save_config(config):
                print("✅ 完整模式已启用!")
                print("💡 重启软件后生效，将提供完整的序列号管理功能")
        elif choice == "3":
            show_mode_comparison()
        elif choice == "4":
            current_mode = get_current_mode(config)
            print(f"\n📋 当前模式: {current_mode}")
            
            # 显示相关配置
            serial_config = config.get('serial_numbers', {})
            print(f"📊 配置详情:")
            print(f"  • 轻量级模式: {serial_config.get('lightweight_mode', True)}")
            if 'used_list' in serial_config:
                print(f"  • 历史序列号数量: {len(serial_config['used_list'])}")
            if 'current_sequence' in serial_config:
                print(f"  • 当前序列号计数: {serial_config['current_sequence']}")
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        sys.exit(1)
