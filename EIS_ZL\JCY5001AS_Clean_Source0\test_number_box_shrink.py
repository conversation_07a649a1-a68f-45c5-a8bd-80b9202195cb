#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 数值框缩短测试
测试显示"0.000"数值的框缩短至刚好能显示4个字符
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.components.statistics_widget import StatisticsWidget
from ui.components.channel_display_widget import ChannelDisplayWidget

class TestNumberBoxShrinkWindow(QMainWindow):
    """数值框缩短测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS 数值框缩短测试 - 刚好显示4个字符")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 数值框缩短测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
🎯 测试目标：缩短数值显示框至刚好能显示4个字符

📊 修改内容：
1. ✅ 电压显示框 - "0.000"
   - 最小宽度：20px → 32px（刚好显示4个字符）
   - 最大宽度：30px → 32px（固定宽度，去除多余空间）

2. ✅ RS/RCT阻抗值显示框 - "0.000"
   - 最小宽度：35px → 32px（刚好显示4个字符）
   - 最大宽度：40px → 32px（固定宽度，去除多余空间）

3. ✅ 统计数值框 - 各种数字
   - 总测试数框：60px → 32px（刚好显示4个字符）
   - 合格数框：60px → 32px（刚好显示4个字符）
   - 不合格数框：60px → 32px（刚好显示4个字符）
   - 良率框：60px → 32px（刚好显示4个字符）

🔧 技术实现：
• 统一设置宽度为32px，刚好容纳"0.000"这4个字符
• 减少内边距从12px到6px，紧凑显示
• 固定最小和最大宽度相同，去除多余空间
• 保持字体大小和颜色不变

✅ 预期效果：
• 所有数值显示框明显变窄，刚好适配内容
• 去除框右侧的多余空白区域
• 数字内容仍能正常显示，不被截断
• 为其他组件释放更多空间
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        test_layout = QHBoxLayout()
        
        # 左侧：统计显示组件
        self.statistics_widget = StatisticsWidget()
        test_layout.addWidget(self.statistics_widget, 1)
        
        # 右侧：通道显示组件
        self.channel_widget = ChannelDisplayWidget(channel_id=1)
        test_layout.addWidget(self.channel_widget, 1)
        
        main_layout.addLayout(test_layout)
        
        # 添加测试数据
        self.load_test_data()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 更新统计数据
            self.statistics_widget.update_statistics(
                total_count=1530,
                qualified_count=870,
                unqualified_count=660,
                yield_rate=56.9
            )
            
            # 更新档位分布数据
            grade_data = {
                (0, 0): 114, (0, 1): 112, (0, 2): 90,   # Rs1行
                (1, 0): 96,  (1, 1): 106, (1, 2): 101,  # Rs2行  
                (2, 0): 88,  (2, 1): 82,  (2, 2): 81    # Rs3行
            }
            
            self.statistics_widget.update_grade_distribution(grade_data)
            
            # 更新通道数据
            self.channel_widget.update_test_data({
                'voltage': 3.456,
                'rs_value': 12.345,
                'rct_value': 67.890,
                'battery_code': 'TEST123456789ABCDEF',
                'test_count': 25,
                'test_time': '15:30:45'
            })
            
            print("✅ 测试数据加载完成")
            print("📊 统计数据: 总数=1530, 合格=870, 不合格=660, 良率=56.9%")
            print("🔋 通道数据: 电压=3.456V, Rs=12.345mΩ, Rct=67.890mΩ")
            print("🎯 重点观察:")
            print("   1. 所有显示'0.000'或数字的框是否明显变窄")
            print("   2. 框的宽度是否刚好适配显示的内容")
            print("   3. 是否去除了右侧多余的空白区域")
            print("   4. 数字内容是否仍能正常显示")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("JCY5001AS数值框缩短测试")
    app.setApplicationVersion("1.0")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = TestNumberBoxShrinkWindow()
    window.show()
    
    print("🚀 JCY5001AS 数值框缩短测试启动")
    print("📝 测试重点:")
    print("   1. 数值显示框是否缩短至刚好显示4个字符")
    print("   2. 是否去除了多余的空白区域")
    print("   3. 数字内容是否仍能正常显示")
    print("   4. 整体布局是否更加紧凑")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
