# -*- coding: utf-8 -*-
"""
测试结果管理器
负责测试结果的计算、保存、批次管理等功能

从TestFlowController中提取的测试结果管理功能，遵循单一职责原则

Author: Jack
Date: 2025-01-30
"""

import logging
import time
import sys
import os
import json
import math
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class TestResultManager:
    """
    测试结果管理器
    
    职责：
    - Rs/Rct计算和分析
    - 测试结果保存
    - 批次管理
    - 档位计算和合格性判断
    - 测试时间记录
    """
    
    def __init__(self, config_manager, impedance_data_manager):
        """
        初始化测试结果管理器

        Args:
            config_manager: 配置管理器
            impedance_data_manager: 阻抗数据管理器
        """
        self.config_manager = config_manager
        self.impedance_data_manager = impedance_data_manager

        # 测试时间记录
        self.test_start_times = {}  # 记录每个通道的测试开始时间
        self.test_end_times = {}    # 记录每个通道的测试结束时间

        # 批次信息
        self.current_batch_id = None
        self.batch_info = {}

        # 电池码信息
        self.battery_codes = []

        # 数据库管理器（延迟初始化）
        self._db_manager = None

        # EIS分析器（延迟初始化）
        self._eis_analyzer = None

        # 🔧 新增：初始化新的管理器
        self._failure_reason_manager = None
        self._test_mode_manager = None
        self._product_info_manager = None
        self._test_parameter_calculator = None

        logger.info("测试结果管理器初始化完成")

    @property
    def failure_reason_manager(self):
        """失败原因管理器（延迟初始化）"""
        if self._failure_reason_manager is None:
            from backend.failure_reason_manager import FailureReasonManager
            self._failure_reason_manager = FailureReasonManager(self.config_manager)
        return self._failure_reason_manager

    @property
    def test_mode_manager(self):
        """测试模式管理器（延迟初始化）"""
        if self._test_mode_manager is None:
            from backend.test_mode_manager import TestModeManager
            self._test_mode_manager = TestModeManager(self.config_manager)
        return self._test_mode_manager

    @property
    def product_info_manager(self):
        """产品信息管理器（延迟初始化）"""
        if self._product_info_manager is None:
            from backend.product_info_manager import ProductInfoManager
            self._product_info_manager = ProductInfoManager(self.config_manager)
        return self._product_info_manager

    @property
    def test_parameter_calculator(self):
        """测试参数计算器（延迟初始化）"""
        if self._test_parameter_calculator is None:
            from backend.test_parameter_calculator import TestParameterCalculator
            self._test_parameter_calculator = TestParameterCalculator(self.config_manager)
        return self._test_parameter_calculator
    
    @property
    def db_manager(self):
        """延迟初始化数据库管理器"""
        if self._db_manager is None:
            try:
                # 🔧 修复：使用全局数据库管理器实例
                from data.database_manager import _database_manager
                if _database_manager is not None:
                    self._db_manager = _database_manager
                    logger.info("✅ 使用全局数据库管理器实例")
                else:
                    # 如果全局实例不存在，创建新实例
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    from data.database_manager import DatabaseManager
                    self._db_manager = DatabaseManager()
                    logger.info("✅ 创建新的数据库管理器实例")

                # 测试数据库连接
                if self._db_manager:
                    db_info = self._db_manager.get_database_info()
                    logger.info(f"数据库管理器初始化成功: {db_info['db_path']}")

            except Exception as e:
                logger.error(f"❌ 初始化数据库管理器失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                self._db_manager = None
        return self._db_manager
    
    @property
    def eis_analyzer(self):
        """延迟初始化EIS分析器"""
        if self._eis_analyzer is None:
            try:
                from backend.eis_analyzer import EISAnalyzer
                self._eis_analyzer = EISAnalyzer()
            except Exception as e:
                logger.error(f"初始化EIS分析器失败: {e}")
                self._eis_analyzer = None
        return self._eis_analyzer
    
    def setup_new_batch(self, batch_info: Dict, battery_codes: List[str]) -> int:
        """
        设置新的批次信息，确保批次ID的独立性
        
        Args:
            batch_info: 批次信息
            battery_codes: 电池码列表
            
        Returns:
            新创建的批次ID
        """
        try:
            logger.info("📋 设置新批次信息...")
            
            # 1. 创建新的批次记录（确保独立性）
            new_batch_id = self._create_new_batch(batch_info)
            
            # 2. 设置当前批次信息
            self.current_batch_id = new_batch_id
            self.batch_info = batch_info.copy()  # 使用副本避免引用问题
            self.batch_info['batch_id'] = new_batch_id  # 确保批次ID一致
            
            # 3. 记录电池码信息
            self.battery_codes = battery_codes.copy() if battery_codes else []
            
            logger.info(f"✅ 新批次设置完成: ID={new_batch_id}, 批次号={batch_info.get('batch_number', 'Unknown')}")
            logger.info(f"📦 电池码数量: {len(self.battery_codes)}")
            
            return new_batch_id
            
        except Exception as e:
            logger.error(f"设置新批次失败: {e}")
            # 使用默认批次ID作为备用
            self.current_batch_id = self._ensure_batch_exists()
            self.batch_info = batch_info.copy()
            return self.current_batch_id
    
    def record_test_start(self, channel_nums: List[int]):
        """
        记录测试开始时间
        
        Args:
            channel_nums: 通道号列表
        """
        test_start_time = time.time()
        for channel_num in channel_nums:
            self.test_start_times[channel_num] = test_start_time
        
        logger.info(f"记录测试开始时间: 通道{channel_nums}")
    
    def record_test_end(self, channel_nums: List[int]):
        """
        记录测试结束时间
        
        Args:
            channel_nums: 通道号列表
        """
        test_end_time = time.time()
        for channel_num in channel_nums:
            self.test_end_times[channel_num] = test_end_time
        
        logger.info(f"记录测试结束时间: 通道{channel_nums}")
    
    def calculate_rs_rct_for_channel(self, channel_num: int) -> Tuple[float, float]:
        """
        为指定通道计算Rs和Rct值（使用真实阻抗数据）
        
        Args:
            channel_num: 通道号（1-8）
            
        Returns:
            (rs_value, rct_value) 元组
        """
        try:
            logger.info(f"开始为通道{channel_num}计算Rs和Rct值（使用真实数据）")
            
            # 从阻抗数据管理器获取该通道的所有频点数据
            channel_data = self.impedance_data_manager.get_channel_impedance_data(channel_num)

            # 添加调试信息
            logger.info(f"通道{channel_num}阻抗数据获取结果: {len(channel_data) if channel_data else 0}个频点")
            if channel_data:
                frequencies = list(channel_data.keys())
                logger.info(f"通道{channel_num}频点列表: {sorted(frequencies)}")
                # 显示前几个数据点的详细信息
                for i, (freq, data) in enumerate(list(channel_data.items())[:3]):
                    logger.info(f"通道{channel_num}频点{freq}Hz: Re={data.get('real', 0):.3f}μΩ, Im={data.get('imag', 0):.3f}μΩ")

            if not channel_data:
                logger.warning(f"通道{channel_num}没有阻抗数据，使用默认值")
                return 5.0, 10.0
            
            # 提取频率和阻抗数据
            frequencies = []
            real_parts = []
            imag_parts = []
            
            for freq, impedance_info in channel_data.items():
                frequencies.append(freq)
                real_parts.append(impedance_info['real'] / 1000.0)  # 转换为mΩ
                imag_parts.append(impedance_info['imag'] / 1000.0)  # 转换为mΩ
            
            if len(frequencies) == 0:
                logger.warning(f"通道{channel_num}没有有效的阻抗数据点")
                return 5.0, 10.0
            
            logger.info(f"通道{channel_num}阻抗数据: {len(frequencies)}个频点，"
                       f"频率范围{min(frequencies):.3f}-{max(frequencies):.3f}Hz")
            
            # 使用EIS分析器计算Rs和Rct
            if not self.eis_analyzer:
                logger.error("EIS分析器未初始化")
                return 5.0, 10.0
            
            rs_value, rct_value = self.eis_analyzer.calculate_rs_rct_standard(
                frequencies, real_parts, imag_parts
            )
            
            logger.info(f"通道{channel_num}EIS分析结果: Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ")
            
            return rs_value, rct_value
            
        except Exception as e:
            logger.error(f"计算通道{channel_num}的Rs/Rct失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return 5.0, 10.0
    
    def calculate_grades(self, rs_value: float, rct_value: float) -> Tuple[int, int]:
        """
        计算Rs和Rct档位
        
        Args:
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            
        Returns:
            (rs_grade, rct_grade) 元组
        """
        try:
            # 获取Rs档位配置
            rs_grade_count = self.config_manager.get('impedance.rs_grade_count', 3)
            
            # 计算Rs档位
            if rs_grade_count == 1:
                rs_grade = 1  # 1档模式下始终为1档
            elif rs_grade_count == 2:
                rs_grade1_max = self.config_manager.get('impedance.rs_grade1_max', 25.0)
                rs_grade2_max = self.config_manager.get('impedance.rs_grade2_max', 50.0)
                if rs_value <= rs_grade1_max:
                    rs_grade = 1
                elif rs_value <= rs_grade2_max:
                    rs_grade = 2
                else:
                    rs_grade = 2  # 超出范围仍为2档（最高档位）
            else:  # 3档
                rs_grade1_max = self.config_manager.get('impedance.rs_grade1_max', 17.0)
                rs_grade2_max = self.config_manager.get('impedance.rs_grade2_max', 33.5)
                rs_grade3_max = self.config_manager.get('impedance.rs_grade3_max', 50.0)
                if rs_value <= rs_grade1_max:
                    rs_grade = 1
                elif rs_value <= rs_grade2_max:
                    rs_grade = 2
                elif rs_value <= rs_grade3_max:
                    rs_grade = 3
                else:
                    rs_grade = 3  # 超出范围仍为3档（最高档位）
            
            # 计算Rct档位（固定3档）
            rct_grade1_max = self.config_manager.get('impedance.rct_grade1_max', 35.0)
            rct_grade2_max = self.config_manager.get('impedance.rct_grade2_max', 70.0)
            rct_grade3_max = self.config_manager.get('impedance.rct_grade3_max', 100.0)
            
            if rct_value <= rct_grade1_max:
                rct_grade = 1
            elif rct_value <= rct_grade2_max:
                rct_grade = 2
            elif rct_value <= rct_grade3_max:
                rct_grade = 3
            else:
                rct_grade = 3  # 超出范围仍为3档（最高档位）
            
            return rs_grade, rct_grade
            
        except Exception as e:
            logger.error(f"计算档位失败: {e}")
            return 1, 1
    
    def judge_test_result(self, voltage: float, rs_value: float, rct_value: float, outlier_result: Optional[str] = None, channel_num: Optional[int] = None) -> Tuple[bool, List[str]]:
        """
        判断测试结果（按优先级判断）

        优先级顺序：
        1. 电压检测（电压范围不合格）
        2. 离群率检测（偏差超过阈值）
        3. Rs档位判断
        4. Rct档位判断

        Args:
            voltage: 电压值 (V)
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)
            outlier_result: 离群率检测结果（"PASS"或偏差百分比）
            channel_num: 通道号（用于调试日志）

        Returns:
            (is_pass, fail_items) 元组
        """
        try:
            # 获取合格标准配置
            voltage_min = self.config_manager.get('test_params.voltage_range.min', 2.889)
            voltage_max = self.config_manager.get('test_params.voltage_range.max', 3.531)
            rs_min = self.config_manager.get('impedance.rs_min', 0.5)
            rs_max = self.config_manager.get('impedance.rs_grade3_max', 50.0)
            rct_min = self.config_manager.get('impedance.rct_min', 0.5)
            rct_max = self.config_manager.get('impedance.rct_grade3_max', 100.0)

            fail_items = []

            # 第一优先级：电压检测
            if voltage < voltage_min or voltage > voltage_max:
                fail_items.append("电压")
                logger.debug(f"通道{channel_num}电压不合格: {voltage:.3f}V (范围: {voltage_min:.3f}-{voltage_max:.3f}V)")
                return False, fail_items  # 电压不合格，直接返回，不进行Rs/Rct档位统计

            # 第二优先级：离群率检测
            if outlier_result and outlier_result != "PASS" and outlier_result != "--":
                # 检查是否启用离群检测
                try:
                    from backend.outlier_detection_manager import OutlierDetectionManager
                    outlier_manager = OutlierDetectionManager()
                    config = outlier_manager.get_detection_config()

                    if config.get('is_enabled', False):
                        fail_items.append("离群")
                        logger.debug(f"通道{channel_num}离群率不合格: {outlier_result}")
                        return False, fail_items  # 离群率不合格，直接返回，不进行Rs/Rct档位统计
                except Exception as e:
                    logger.debug(f"检查离群检测配置失败: {e}")

            # 第三优先级：Rs档位判断
            if rs_value > rs_max or rs_value < rs_min:
                fail_items.append("Rs")
                logger.debug(f"通道{channel_num}Rs不合格: {rs_value:.3f}mΩ (范围: {rs_min:.3f}-{rs_max:.3f}mΩ)")

            # 第四优先级：Rct档位判断
            if rct_value > rct_max or rct_value < rct_min:
                fail_items.append("Rct")
                logger.debug(f"通道{channel_num}Rct不合格: {rct_value:.3f}mΩ (范围: {rct_min:.3f}-{rct_max:.3f}mΩ)")

            is_pass = len(fail_items) == 0

            if is_pass:
                logger.debug(f"通道{channel_num}测试合格: 电压={voltage:.3f}V, Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ, 离群率={outlier_result}")
            else:
                logger.debug(f"通道{channel_num}测试不合格: 失败项目={fail_items}")

            return is_pass, fail_items

        except Exception as e:
            logger.error(f"判断测试结果失败: {e}")
            return False, ["系统错误"]

    def get_latest_test_results(self, enabled_channels: List[int]) -> List[Dict[str, Any]]:
        """
        获取最新的测试结果数据

        Args:
            enabled_channels: 启用的通道列表

        Returns:
            测试结果列表
        """
        try:
            results = []

            for channel_num in enabled_channels:
                # 计算Rs和Rct值（使用真实阻抗数据）
                rs_value, rct_value = self.calculate_rs_rct_for_channel(channel_num)

                # 获取真实电压（从最近的测试结果中获取）
                voltage = 3.7  # 默认电压

                # 尝试从最近保存的测试结果中获取电压
                try:
                    if self.db_manager:
                        recent_results = self.db_manager.get_recent_test_results(
                            channel_number=channel_num,
                            limit=1
                        )
                        if recent_results:
                            voltage = recent_results[0].get('voltage', 3.7)
                            logger.debug(f"从数据库获取通道{channel_num}最近电压: {voltage:.3f}V")
                except Exception as e:
                    logger.debug(f"从数据库获取电压失败: {e}")
                    voltage = 3.7

                # 计算档位
                rs_grade, rct_grade = self.calculate_grades(rs_value, rct_value)

                # 判断合格性
                is_pass, fail_items = self.judge_test_result(voltage, rs_value, rct_value)

                # 🔧 获取频点数据（用于EIS专业分析）
                frequency_data = []
                try:
                    if self.impedance_data_manager:
                        # 获取该通道的阻抗数据
                        channel_impedance_data = self.impedance_data_manager.get_channel_impedance_data(channel_num)

                        if channel_impedance_data:
                            # 转换为EIS分析器期望的格式
                            for freq, impedance_info in channel_impedance_data.items():
                                if isinstance(impedance_info, dict):
                                    # 🔧 修复：使用正确的字段名
                                    real_imp = impedance_info.get('real', 0.0) / 1000.0  # μΩ转换为mΩ
                                    imag_imp = impedance_info.get('imag', 0.0) / 1000.0  # μΩ转换为mΩ

                                    # 计算阻抗模值
                                    magnitude = math.sqrt(real_imp**2 + imag_imp**2)

                                    # 🔧 修复：相位角计算 - 不再对虚部取反，因为设备数据已经是正确符号
                                    phase = math.degrees(math.atan2(imag_imp, real_imp)) if real_imp != 0 else 0.0

                                    frequency_data.append({
                                        'frequency': freq,
                                        'impedance_real': real_imp,
                                        'impedance_imag': imag_imp,
                                        'impedance_magnitude': magnitude,
                                        'impedance_phase': phase
                                    })

                            logger.debug(f"通道{channel_num}获取到{len(frequency_data)}个频点的阻抗数据")
                        else:
                            logger.debug(f"通道{channel_num}没有阻抗数据")
                except Exception as e:
                    logger.debug(f"获取通道{channel_num}频点数据失败: {e}")

                # 构建结果数据（包含EIS频点数据）
                result = {
                    'channel': channel_num,
                    'voltage': round(voltage, 3),
                    'rs_value': round(rs_value, 3),
                    'rct_value': round(rct_value, 3),
                    'rs_grade': rs_grade,
                    'rct_grade': rct_grade,
                    'is_pass': is_pass,
                    'fail_items': fail_items,
                    'frequency_data': frequency_data,  # 🔧 新增：EIS频点数据
                    'timestamp': datetime.now().isoformat()  # 添加时间戳
                }

                results.append(result)
                logger.debug(f"获取通道{channel_num}最新测试结果: Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ")

            logger.info(f"获取最新测试结果完成: {len(results)} 个通道")
            return results

        except Exception as e:
            logger.error(f"获取最新测试结果失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def save_test_result(self, channel_num: int, result_data: Dict[str, Any], test_config: Dict[str, Any]):
        """
        保存测试结果到数据库

        Args:
            channel_num: 通道号（1-8）
            result_data: 测试结果数据
            test_config: 测试配置
        """
        try:
            # 🔧 修复：增强数据库管理器检查和调试信息
            logger.info(f"开始保存通道{channel_num}测试结果到数据库...")

            if not self.db_manager:
                logger.error("数据库管理器未初始化，尝试重新初始化...")
                # 尝试重新初始化数据库管理器
                try:
                    from data.database_manager import DatabaseManager
                    self._db_manager = DatabaseManager()
                    logger.info("数据库管理器重新初始化成功")
                except Exception as e:
                    logger.error(f"重新初始化数据库管理器失败: {e}")
                    return

            if not self.db_manager:
                logger.error("数据库管理器仍然未初始化，无法保存测试结果")
                return

            logger.info(f"数据库管理器状态正常，开始保存通道{channel_num}数据...")

            # 获取当前批次ID
            current_batch_id = self.current_batch_id
            if current_batch_id is None:
                current_batch_id = self._ensure_batch_exists()
                self.current_batch_id = current_batch_id

            # 计算测试时间
            start_time = self.test_start_times.get(channel_num, time.time())
            end_time = self.test_end_times.get(channel_num, time.time())
            test_duration = end_time - start_time

            # 转换为ISO格式时间字符串
            start_time_iso = datetime.fromtimestamp(start_time).isoformat()
            end_time_iso = datetime.fromtimestamp(end_time).isoformat()

            # 获取实际的电池码（修复电池码保存逻辑）
            battery_code = self._get_actual_battery_code(channel_num)

            # 🔧 新增：使用新管理器生成详细信息
            # 生成详细的失败原因
            detailed_fail_reason = self.failure_reason_manager.generate_detailed_failure_reason(
                result_data['voltage'],
                result_data['rs_value'],
                result_data['rct_value'],
                result_data.get('outlier_result')
            )

            # 获取测试模式信息
            test_mode_description = self.test_mode_manager.get_mode_description_for_database()

            # 获取产品信息
            product_info = self.product_info_manager.get_complete_product_info()

            # 计算Rct变异系数（连续测试模式下基于历史数据）
            rct_coefficient_of_variation = 0.0

            # 检查是否为连续测试模式
            test_mode = test_config.get('test_mode', '')
            continuous_mode = test_config.get('continuous_mode', False)

            if continuous_mode:
                # 连续测试模式：获取同一电池的历史Rct值
                battery_code = self._get_current_battery_code(channel_num)
                historical_rct_values = self._get_historical_rct_values(battery_code, channel_num)

                # 添加当前Rct值
                current_rct = result_data['rct_value']
                if current_rct > 0:
                    historical_rct_values.append(current_rct)

                # 计算变异系数（需要至少2个值）
                if len(historical_rct_values) >= 2:
                    rct_coefficient_of_variation = self.test_parameter_calculator.calculate_rct_coefficient_of_variation(historical_rct_values)
                    logger.info(f"通道{channel_num}连续测试Rct变异系数: {rct_coefficient_of_variation:.2f}% (基于{len(historical_rct_values)}个值)")
                else:
                    logger.debug(f"通道{channel_num}Rct值数量不足({len(historical_rct_values)})，无法计算变异系数")
            else:
                # 单次测试模式：从频率数据中提取多个Rct相关值
                frequency_data = result_data.get('frequency_data', [])
                if frequency_data:
                    # 从不同频率点提取阻抗值作为Rct变化的参考
                    rct_values = [result_data['rct_value']]  # 基础值
                    # 可以根据需要添加更多频率相关的阻抗值
                    rct_coefficient_of_variation = self.test_parameter_calculator.calculate_rct_coefficient_of_variation(rct_values)

            # 计算容量预测
            capacity_prediction = self.test_parameter_calculator.calculate_capacity_prediction(
                result_data['voltage'],
                result_data['rs_value'],
                result_data['rct_value']
            )

            # 准备测试结果记录
            test_result = {
                'batch_id': current_batch_id,
                'channel_number': channel_num,
                'battery_code': battery_code,  # 使用实际的电池码
                'test_start_time': start_time_iso,
                'test_end_time': end_time_iso,
                'test_duration': test_duration,  # 测试持续时间（秒）
                'voltage': result_data['voltage'],
                'rs_value': result_data['rs_value'],
                'rct_value': result_data['rct_value'],
                'w_impedance': None,  # W阻抗（如果有的话）
                'rs_grade': result_data['rs_grade'],
                'rct_grade': result_data['rct_grade'],
                'is_pass': result_data['is_pass'],
                'fail_reason': detailed_fail_reason,  # 🔧 使用详细的失败原因
                'test_mode': test_mode_description,  # 🔧 使用详细的测试模式描述
                'frequency_list': test_config.get('frequencies', []),  # 当前测试的频率列表
                'raw_data': {
                    'impedance_data': self.impedance_data_manager.get_channel_impedance_data(channel_num),
                    'test_config': test_config
                },
                # 🔧 新增字段
                'operator': product_info['operator'],  # 操作员信息
                'battery_type': product_info['battery_type'],  # 电池类型
                'battery_spec': product_info['battery_spec'],  # 电池规格
                'rct_coefficient_of_variation': rct_coefficient_of_variation,  # Rct变异系数
                'capacity_prediction': capacity_prediction,  # 容量预测
                # 离群率相关字段（如果有）
                'outlier_result': result_data.get('outlier_result'),
                'baseline_filename': result_data.get('baseline_filename'),
                'baseline_id': result_data.get('baseline_id'),
                'max_deviation_percent': result_data.get('max_deviation_percent'),
                'frequency_deviations': result_data.get('frequency_deviations', {}),
                # 频率数据
                'frequency_data': frequency_data
            }

            logger.info(f"通道{channel_num}测试时长: {test_duration:.1f}秒 ({start_time_iso} - {end_time_iso})")

            # 🔧 记录详细的保存信息
            logger.info(f"通道{channel_num}详细信息: 测试模式={test_mode_description}, "
                       f"操作员={product_info['operator']}, 电池类型={product_info['battery_type']}, "
                       f"电池规格={product_info['battery_spec']}")

            if detailed_fail_reason:
                logger.info(f"通道{channel_num}失败原因: {detailed_fail_reason}")

            logger.info(f"通道{channel_num}计算参数: Rct变异系数={rct_coefficient_of_variation:.2f}%, "
                       f"容量预测={capacity_prediction:.3f}AH")

            # 🔧 修复：增强数据库保存调试信息
            logger.info(f"准备保存通道{channel_num}测试结果到数据库...")
            logger.debug(f"数据库管理器状态: {self.db_manager is not None}")
            logger.debug(f"测试结果数据: {test_result}")

            # 保存到数据库
            result_id = self.db_manager.save_test_result(test_result)

            if result_id:
                logger.info(f"✅ 通道{channel_num}测试结果已保存到数据库: ID={result_id}")
            else:
                logger.error(f"❌ 通道{channel_num}测试结果保存失败: 数据库返回空结果ID")
                logger.error(f"测试结果数据: {test_result}")
                # 尝试检查数据库连接状态
                try:
                    db_info = self.db_manager.get_database_info()
                    logger.error(f"数据库信息: {db_info}")
                except Exception as db_e:
                    logger.error(f"获取数据库信息失败: {db_e}")

            # 在连续测试模式下保存容量预测数据
            if continuous_mode and rct_coefficient_of_variation > 0:
                self._save_capacity_prediction_data_if_enabled(
                    channel_num, test_result, rct_coefficient_of_variation
                )

        except Exception as e:
            logger.error(f"保存通道{channel_num}测试结果失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def _get_actual_battery_code(self, channel_num: int) -> str:
        """
        获取实际的电池码（修复电池码保存逻辑）

        Args:
            channel_num: 通道号（1-8）

        Returns:
            实际的电池码
        """
        try:
            # 方法1：从批次电池码列表获取（主要方法）
            if self.battery_codes and len(self.battery_codes) >= channel_num:
                battery_code = self.battery_codes[channel_num - 1]
                if battery_code and battery_code.strip():
                    logger.debug(f"从批次电池码列表获取通道{channel_num}电池码: {battery_code}")
                    return battery_code.strip()

            # 方法2：使用默认电池码（备用方案）
            default_code = f'BAT{channel_num:03d}'
            logger.warning(f"无法获取通道{channel_num}的实际电池码，使用默认值: {default_code}")
            return default_code

        except Exception as e:
            logger.error(f"获取通道{channel_num}电池码失败: {e}")
            return f'BAT{channel_num:03d}'

    def _create_new_batch(self, batch_info: Dict) -> int:
        """
        创建新的批次记录，确保每次测试都有独立的批次ID

        Args:
            batch_info: 批次信息

        Returns:
            新创建的批次ID
        """
        try:
            if not self.db_manager:
                logger.error("数据库管理器未初始化")
                return 1

            # 生成唯一的批次号（添加时间戳确保唯一性）
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S-%f")[:-3]  # 精确到毫秒
            original_batch_number = batch_info.get('batch_number', 'BATCH')
            unique_batch_number = f"{original_batch_number}-{timestamp}"

            # 准备批次数据
            new_batch_data = {
                'batch_number': unique_batch_number,
                'operator': batch_info.get('operator', 'system'),
                'cell_type': batch_info.get('cell_type', '磷酸铁锂'),
                'cell_spec': batch_info.get('cell_spec', '21700'),
                'standard_voltage': batch_info.get('standard_voltage', 3.2),
                'standard_capacity': batch_info.get('standard_capacity', 3000),
                'remarks': batch_info.get('remarks', f'测试开始时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
            }

            # 创建新批次
            batch_id = self.db_manager.create_batch(new_batch_data)

            logger.info(f"🆕 创建新批次成功: ID={batch_id}, 批次号={unique_batch_number}")
            return batch_id

        except Exception as e:
            logger.error(f"创建新批次失败: {e}")
            # 如果创建失败，使用默认批次
            return self._ensure_batch_exists()

    def _ensure_batch_exists(self) -> int:
        """
        确保批次记录存在，如果不存在则创建

        Returns:
            批次ID
        """
        try:
            if not self.db_manager:
                logger.error("数据库管理器未初始化")
                return 1

            # 获取最近的批次
            recent_batches = self.db_manager.get_recent_batches(limit=1)
            if recent_batches:
                batch_id = recent_batches[0]['id']
                logger.debug(f"使用现有批次: ID={batch_id}")
                return batch_id

            # 如果没有批次，创建默认批次
            batch_data = {
                'batch_number': f'DEFAULT-BATCH-{datetime.now().strftime("%Y%m%d-%H%M%S")}',
                'operator': 'system',
                'cell_type': '磷酸铁锂',
                'cell_spec': '21700',
                'standard_voltage': 3.2,
                'standard_capacity': 3000,
                'remarks': '系统自动创建的默认批次'
            }
            batch_id = self.db_manager.create_batch(batch_data)
            logger.info(f"创建新批次记录: ID={batch_id}, 批次号={batch_data['batch_number']}")
            return batch_id

        except Exception as e:
            logger.error(f"确保批次存在失败: {e}")
            return 1  # 返回默认批次ID

    def clear_test_data(self):
        """清理测试数据，防止数据覆盖"""
        try:
            logger.info("🧹 开始清理测试数据...")

            # 1. 清空测试时间记录
            self.test_start_times.clear()
            self.test_end_times.clear()
            logger.info("✅ 测试时间记录已清空")

            # 2. 重置批次相关信息
            self.current_batch_id = None
            self.batch_info.clear()
            logger.info("✅ 批次信息已重置")

            # 3. 清空电池码信息
            self.battery_codes.clear()
            logger.info("✅ 电池码信息已清空")

            # 4. 清理新管理器资源
            if self._failure_reason_manager:
                self._failure_reason_manager.cleanup()
                self._failure_reason_manager = None

            if self._test_mode_manager:
                self._test_mode_manager.cleanup()
                self._test_mode_manager = None

            if self._product_info_manager:
                self._product_info_manager.cleanup()
                self._product_info_manager = None

            if self._test_parameter_calculator:
                self._test_parameter_calculator.cleanup()
                self._test_parameter_calculator = None

            logger.info("✅ 新管理器资源已清理")

            logger.info("🎯 测试数据清理完成")

        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")

    def get_batch_info(self) -> Dict[str, Any]:
        """获取当前批次信息"""
        return {
            'current_batch_id': self.current_batch_id,
            'batch_info': self.batch_info.copy(),
            'battery_codes': self.battery_codes.copy()
        }

    def get_test_duration(self, channel_num: int) -> float:
        """
        获取指定通道的测试持续时间

        Args:
            channel_num: 通道号（1-8）

        Returns:
            测试持续时间（秒）
        """
        start_time = self.test_start_times.get(channel_num, 0)
        end_time = self.test_end_times.get(channel_num, 0)
        return end_time - start_time if start_time and end_time else 0

    def _get_current_battery_code(self, channel_num: int) -> str:
        """
        获取当前通道的电池编码

        Args:
            channel_num: 通道号（1-8）

        Returns:
            电池编码
        """
        try:
            # 从电池码列表中获取（通道号从1开始，列表索引从0开始）
            if self.battery_codes and len(self.battery_codes) >= channel_num:
                battery_code = self.battery_codes[channel_num - 1]
                if battery_code and battery_code.strip():
                    return battery_code.strip()

            # 如果没有记录，生成默认编码
            default_code = f"CH{channel_num:02d}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            logger.debug(f"通道{channel_num}未找到电池编码，使用默认编码: {default_code}")
            return default_code

        except Exception as e:
            logger.error(f"获取通道{channel_num}电池编码失败: {e}")
            return f"CH{channel_num:02d}-UNKNOWN"

    def _get_historical_rct_values(self, battery_code: str, channel_num: int, limit: int = 10) -> List[float]:
        """
        获取指定电池的历史Rct值

        Args:
            battery_code: 电池编码
            channel_num: 通道号
            limit: 获取的历史记录数量限制

        Returns:
            历史Rct值列表
        """
        try:
            if not self.db_manager or not battery_code:
                return []

            # 查询同一电池的历史测试结果
            historical_results = self.db_manager.get_test_results(
                battery_code=battery_code,
                channel_number=channel_num,
                limit=limit,
                offset=0,
                include_json=False
            )

            # 提取Rct值
            rct_values = []
            for result in historical_results:
                rct_value = result.get('rct_value', 0)
                if rct_value > 0:  # 只包含有效的Rct值
                    rct_values.append(rct_value)

            logger.debug(f"获取电池{battery_code}通道{channel_num}的历史Rct值: {len(rct_values)}个")
            return rct_values

        except Exception as e:
            logger.error(f"获取历史Rct值失败: {e}")
            return []

    def _save_capacity_prediction_data_if_enabled(self, channel_num: int, test_result: Dict[str, Any], rct_cv: float):
        """
        如果启用容量预测功能，保存容量预测数据

        Args:
            channel_num: 通道号
            test_result: 测试结果数据
            rct_cv: Rct变异系数
        """
        try:
            # 检查是否启用容量预测功能
            if not self.config_manager.get('test.capacity_prediction_enabled', False):
                logger.debug(f"通道{channel_num}容量预测功能未启用，跳过保存")
                return

            # 构建容量预测数据
            prediction_data = {
                'battery_code': test_result['battery_code'],
                'batch_id': test_result['batch_id'],
                'channel_number': channel_num,
                'test_date': datetime.now().date(),
                'voltage': test_result['voltage'],
                'rs_value': test_result['rs_value'],
                'rct_value': test_result['rct_value'],
                'rct_coefficient_of_variation': rct_cv,
                'voltage_range_min': self.config_manager.get('capacity_prediction.voltage_min', 3.0),
                'voltage_range_max': self.config_manager.get('capacity_prediction.voltage_max', 3.4),
                'rs_range_min': self.config_manager.get('capacity_prediction.rs_min', 0.1),
                'rs_range_max': self.config_manager.get('capacity_prediction.rs_max', 5.0),
                'notes': f'连续测试模式自动保存 - 通道{channel_num}'
            }

            # 保存到数据库
            prediction_id = self.db_manager.save_capacity_prediction_data(prediction_data)

            if prediction_id:
                logger.info(f"通道{channel_num}容量预测数据保存成功: ID={prediction_id}, Rct变异系数={rct_cv:.2f}%")
            else:
                logger.warning(f"通道{channel_num}容量预测数据保存失败")

        except Exception as e:
            logger.error(f"通道{channel_num}保存容量预测数据失败: {e}")
