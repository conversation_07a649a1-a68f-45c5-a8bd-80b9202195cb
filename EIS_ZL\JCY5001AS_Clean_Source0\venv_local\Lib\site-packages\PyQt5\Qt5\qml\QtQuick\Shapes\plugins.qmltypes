import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "qquickshape_p.h"
        name: "QQuickShape"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Shapes/Shape 1.0",
            "QtQuick.Shapes/Shape 1.1",
            "QtQuick.Shapes/Shape 1.11",
            "QtQuick.Shapes/Shape 1.4",
            "QtQuick.Shapes/Shape 1.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "RendererType"
            values: [
                "UnknownRenderer",
                "GeometryRenderer",
                "NvprRenderer",
                "SoftwareRenderer"
            ]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Processing"]
        }
        Enum {
            name: "ContainsMode"
            values: ["BoundingRectContains", "FillContains"]
        }
        Property { name: "rendererType"; type: "RendererType"; isReadonly: true }
        Property { name: "asynchronous"; type: "bool" }
        Property { name: "vendorExtensionsEnabled"; type: "bool" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "containsMode"; revision: 11; type: "ContainsMode" }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Signal { name: "rendererChanged" }
        Signal { name: "containsModeChanged"; revision: 11 }
        Method { name: "_q_shapePathChanged" }
    }
    Component {
        file: "qquickshape_p.h"
        name: "QQuickShapeConicalGradient"
        defaultProperty: "stops"
        prototype: "QQuickShapeGradient"
        exports: [
            "QtQuick.Shapes/ConicalGradient 1.0",
            "QtQuick.Shapes/ConicalGradient 1.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "centerX"; type: "double" }
        Property { name: "centerY"; type: "double" }
        Property { name: "angle"; type: "double" }
    }
    Component {
        file: "qquickshape_p.h"
        name: "QQuickShapeGradient"
        defaultProperty: "stops"
        prototype: "QQuickGradient"
        exports: [
            "QtQuick.Shapes/ShapeGradient 1.0",
            "QtQuick.Shapes/ShapeGradient 1.12"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "SpreadMode"
            values: ["PadSpread", "RepeatSpread", "ReflectSpread"]
        }
        Property { name: "spread"; type: "SpreadMode" }
    }
    Component {
        file: "qquickshape_p.h"
        name: "QQuickShapeLinearGradient"
        defaultProperty: "stops"
        prototype: "QQuickShapeGradient"
        exports: [
            "QtQuick.Shapes/LinearGradient 1.0",
            "QtQuick.Shapes/LinearGradient 1.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "x1"; type: "double" }
        Property { name: "y1"; type: "double" }
        Property { name: "x2"; type: "double" }
        Property { name: "y2"; type: "double" }
    }
    Component {
        file: "qquickshape_p.h"
        name: "QQuickShapePath"
        defaultProperty: "pathElements"
        prototype: "QQuickPath"
        exports: [
            "QtQuick.Shapes/ShapePath 1.0",
            "QtQuick.Shapes/ShapePath 1.14"
        ]
        exportMetaObjectRevisions: [0, 14]
        Enum {
            name: "FillRule"
            values: ["OddEvenFill", "WindingFill"]
        }
        Enum {
            name: "JoinStyle"
            values: ["MiterJoin", "BevelJoin", "RoundJoin"]
        }
        Enum {
            name: "CapStyle"
            values: ["FlatCap", "SquareCap", "RoundCap"]
        }
        Enum {
            name: "StrokeStyle"
            values: ["SolidLine", "DashLine"]
        }
        Property { name: "strokeColor"; type: "QColor" }
        Property { name: "strokeWidth"; type: "double" }
        Property { name: "fillColor"; type: "QColor" }
        Property { name: "fillRule"; type: "FillRule" }
        Property { name: "joinStyle"; type: "JoinStyle" }
        Property { name: "miterLimit"; type: "int" }
        Property { name: "capStyle"; type: "CapStyle" }
        Property { name: "strokeStyle"; type: "StrokeStyle" }
        Property { name: "dashOffset"; type: "double" }
        Property { name: "dashPattern"; type: "QVector<qreal>" }
        Property { name: "fillGradient"; type: "QQuickShapeGradient"; isPointer: true }
        Property { name: "scale"; revision: 14; type: "QSizeF" }
        Signal { name: "shapePathChanged" }
        Method { name: "_q_fillGradientChanged" }
    }
    Component {
        file: "qquickshape_p.h"
        name: "QQuickShapeRadialGradient"
        defaultProperty: "stops"
        prototype: "QQuickShapeGradient"
        exports: [
            "QtQuick.Shapes/RadialGradient 1.0",
            "QtQuick.Shapes/RadialGradient 1.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "centerX"; type: "double" }
        Property { name: "centerY"; type: "double" }
        Property { name: "centerRadius"; type: "double" }
        Property { name: "focalX"; type: "double" }
        Property { name: "focalY"; type: "double" }
        Property { name: "focalRadius"; type: "double" }
    }
}
