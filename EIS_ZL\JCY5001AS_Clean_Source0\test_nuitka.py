#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Nuitka测试脚本
用于测试基本的Nuitka打包功能
"""

import os
import sys
import subprocess

def test_nuitka_basic():
    """测试基本的Nuitka功能"""
    print("🔍 测试Nuitka基本功能...")
    
    # 检查Nuitka是否可用
    try:
        result = subprocess.run([sys.executable, "-m", "nuitka", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Nuitka版本: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Nuitka版本检查失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Nuitka不可用: {e}")
        return False

def build_simple():
    """简单的构建测试"""
    print("🚀 开始简单构建测试...")
    
    # 简化的构建命令
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",  # 独立模式
        "--enable-plugin=pyqt5",  # PyQt5插件
        "--output-dir=test_dist",  # 测试输出目录
        "--output-filename=test_app.exe",  # 测试文件名
        "--assume-yes-for-downloads",  # 自动确认
        "--show-progress",  # 显示进度
        "main.py"  # 主文件
    ]
    
    print("📋 构建命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行构建，设置较短的超时时间用于测试
        result = subprocess.run(cmd, timeout=300)  # 5分钟超时
        if result.returncode == 0:
            print("✅ 简单构建测试成功!")
            return True
        else:
            print(f"❌ 构建失败，返回码: {result.returncode}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ 构建超时（5分钟）")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Nuitka功能测试")
    print("=" * 40)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    # 测试Nuitka基本功能
    if not test_nuitka_basic():
        print("❌ Nuitka基本功能测试失败")
        return False
    
    print()
    
    # 询问是否进行构建测试
    response = input("是否进行简单构建测试？(y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        if build_simple():
            print("🎉 所有测试通过!")
        else:
            print("❌ 构建测试失败")
            return False
    else:
        print("⏭️ 跳过构建测试")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
