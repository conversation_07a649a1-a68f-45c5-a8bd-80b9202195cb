#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证内容向左移动和绿色边框补全的修改是否正确
"""

import sys
import os

def verify_content_left_move_changes():
    """验证内容向左移动和绿色边框补全的修改"""
    
    file_path = "ui/components/statistics_widget.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    checks = [
        # 1. 检查内容向左移动的修改
        ("内容左对齐设置", "Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop" in content),
        ("右侧弹性空间", "content_layout.addStretch(1)" in content),
        ("紧凑间距设置", "setSpacing(8)" in content),
        ("减少左边距", "setContentsMargins(5, 5, 5, 10)" in content),
        
        # 2. 检查档位范围向左移动
        ("档位范围水平布局", "QHBoxLayout(container)" in content),
        ("档位范围左对齐", "Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop" in content),
        ("档位范围弹性空间", "container_layout.addStretch(1)" in content),
        ("减少标题宽度", "setMinimumWidth(70)" in content),
        
        # 3. 检查绿色边框补全
        ("绿色边框增强", "border: 3px solid #27ae60 !important" in content),
        ("边框样式强制", "border-style: solid !important" in content),
        ("边框颜色强制", "border-color: #27ae60 !important" in content),
        ("标题背景设置", "background-color: white" in content),
        
        # 4. 检查档位范围标签优化
        ("档位范围宽度减少", "min-width: 280px" in content),
        ("档位范围最大宽度", "max-width: 400px" in content),
        ("档位范围左边距", "margin-left: 0px" in content),
        
        # 5. 检查布局权重调整
        ("统计数据权重", "addWidget(stats_widget, 1)" in content),
        ("档位分布权重", "addWidget(grade_widget, 2)" in content),
    ]
    
    print("🔍 验证内容向左移动和绿色边框补全修改:")
    print("=" * 60)
    
    all_passed = True
    for check_name, condition in checks:
        if condition:
            print(f"✅ {check_name}: 通过")
        else:
            print(f"❌ {check_name}: 失败")
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有修改验证通过！")
        print("\n📋 修改总结:")
        print("1. ✅ 内容向左移动优化")
        print("   - 设置左对齐和顶部对齐")
        print("   - 添加右侧弹性空间，将内容推向左侧")
        print("   - 减少间距和边距，让布局更紧凑")
        print("   - 调整布局权重，优化空间分配")
        
        print("\n2. ✅ 档位范围向左移动")
        print("   - 改为水平布局，更好控制左移")
        print("   - 减少标题宽度，让内容更紧凑")
        print("   - 添加弹性空间，将档位范围推向左侧")
        print("   - 优化列权重，避免过度拉伸")
        
        print("\n3. ✅ 绿色边框补全")
        print("   - 增加边框宽度到3px")
        print("   - 强制边框样式和颜色")
        print("   - 确保标题不影响边框显示")
        print("   - 优化边框显示效果")
        
        print("\n4. ✅ 档位范围标签优化")
        print("   - 减少最小宽度，让内容向左移动")
        print("   - 优化内边距和高度")
        print("   - 设置左边距为0，确保紧贴左侧")
        
    else:
        print("⚠️  部分修改可能未正确应用")
    
    return all_passed

def main():
    """主函数"""
    print("🚀 JCY5001AS 内容向左移动和绿色边框补全修改验证")
    print("📝 验证内容:")
    print("   1. 内容向左移动到空白处")
    print("   2. 绿色线框补全")
    print("   3. 布局优化和紧凑化")
    print("   4. 档位范围显示优化")
    print()
    
    success = verify_content_left_move_changes()
    
    if success:
        print("\n🎯 预期效果:")
        print("1. **右边内容向左移动** - Rs档位范围、Rct档位范围和表格都向左移动到空白处")
        print("2. **绿色线框完整** - 统计显示区域有完整的绿色边框")
        print("3. **布局更紧凑** - 减少不必要的空白，内容排列更紧密")
        print("4. **空间利用优化** - 通过弹性空间将内容推向左侧")
        
        print("\n🎯 建议测试步骤:")
        print("1. 运行主程序或测试程序")
        print("2. 观察统计显示区域的布局")
        print("3. 确认内容是否向左移动到空白处")
        print("4. 确认绿色边框是否完整显示")
    
    return success

if __name__ == "__main__":
    main()
