# -*- coding: utf-8 -*-
"""
数据库管理器
负责测试数据的存储、查询和管理

Author: Jack
Date: 2025-01-27
"""

import sqlite3
import os
import json
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple, Union
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器类"""

    def __init__(self, db_path: str = "data/test_results.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 初始化数据库
        self._init_database()

        # 执行数据库迁移
        self._migrate_database()

        logger.info(f"数据库管理器初始化完成: {db_path}")

    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 创建批次表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS batches (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        batch_number TEXT UNIQUE NOT NULL,
                        operator TEXT NOT NULL,
                        cell_type TEXT NOT NULL,
                        cell_spec TEXT NOT NULL,
                        standard_voltage REAL NOT NULL,
                        standard_capacity INTEGER NOT NULL,
                        start_time TIMESTAMP NOT NULL,
                        end_time TIMESTAMP,
                        total_count INTEGER DEFAULT 0,
                        pass_count INTEGER DEFAULT 0,
                        fail_count INTEGER DEFAULT 0,
                        yield_rate REAL DEFAULT 0.0,
                        remarks TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建测试结果表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS test_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        batch_id INTEGER NOT NULL,
                        channel_number INTEGER NOT NULL,
                        battery_code TEXT NOT NULL,
                        test_start_time TIMESTAMP NOT NULL,
                        test_end_time TIMESTAMP,
                        test_duration REAL,
                        voltage REAL,
                        rs_value REAL,
                        rct_value REAL,
                        w_impedance REAL,
                        rs_grade INTEGER,
                        rct_grade INTEGER,
                        is_pass BOOLEAN NOT NULL,
                        fail_reason TEXT,
                        test_mode TEXT NOT NULL,
                        frequency_list TEXT,
                        raw_data TEXT,
                        outlier_result TEXT,
                        baseline_filename TEXT,
                        baseline_id INTEGER,
                        max_deviation_percent REAL,
                        frequency_deviations TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (batch_id) REFERENCES batches (id)
                    )
                ''')

                # 创建频率数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS frequency_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_result_id INTEGER NOT NULL,
                        frequency REAL NOT NULL,
                        impedance_real REAL NOT NULL,
                        impedance_imag REAL NOT NULL,
                        impedance_magnitude REAL NOT NULL,
                        impedance_phase REAL NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (test_result_id) REFERENCES test_results (id)
                    )
                ''')

                # 创建阻抗测试明细数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS impedance_details (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        batch_id INTEGER NOT NULL,
                        channel_number INTEGER NOT NULL,
                        battery_code TEXT NOT NULL,
                        test_timestamp TEXT NOT NULL,
                        frequency REAL NOT NULL,
                        impedance_real REAL NOT NULL,
                        impedance_imag REAL NOT NULL,
                        voltage REAL,
                        test_sequence INTEGER,
                        z_value REAL,
                        baseline_z_value REAL,
                        deviation_percent REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (batch_id) REFERENCES batches (id)
                    )
                ''')

                # 创建系统日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        module TEXT,
                        function TEXT,
                        line_number INTEGER,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建容量预测数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS capacity_prediction_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        battery_code TEXT NOT NULL,
                        batch_id INTEGER,
                        channel_number INTEGER,
                        test_date DATE NOT NULL,
                        voltage REAL NOT NULL,
                        rs_value REAL NOT NULL,
                        rct_value REAL NOT NULL,
                        rct_coefficient_of_variation REAL NOT NULL,
                        actual_capacity REAL,
                        predicted_capacity REAL,
                        capacity_error REAL,
                        voltage_range_min REAL,
                        voltage_range_max REAL,
                        rs_range_min REAL,
                        rs_range_max REAL,
                        is_valid_for_training BOOLEAN DEFAULT 1,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (batch_id) REFERENCES batches (id)
                    )
                ''')

                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_batches_number ON batches (batch_number)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_results_batch ON test_results (batch_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_results_channel ON test_results (channel_number)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_results_time ON test_results (test_start_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_frequency_data_test ON frequency_data (test_result_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_impedance_details_batch ON impedance_details (batch_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_impedance_details_channel ON impedance_details (channel_number)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_impedance_details_timestamp ON impedance_details (test_timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_capacity_prediction_battery ON capacity_prediction_data (battery_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_capacity_prediction_date ON capacity_prediction_data (test_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_capacity_prediction_batch ON capacity_prediction_data (batch_id)')

                conn.commit()
                logger.info("数据库表结构初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def _migrate_database(self):
        """执行数据库迁移，添加新字段"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查test_results表是否需要添加离群率字段
                cursor.execute("PRAGMA table_info(test_results)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]

                # 需要添加的字段（包括离群率字段和新增字段）
                new_fields = [
                    ('outlier_result', 'TEXT'),
                    ('baseline_filename', 'TEXT'),
                    ('baseline_id', 'INTEGER'),
                    ('max_deviation_percent', 'REAL'),
                    ('frequency_deviations', 'TEXT'),
                    ('operator', 'TEXT'),  # 操作员
                    ('battery_type', 'TEXT'),  # 电池类型
                    ('battery_spec', 'TEXT'),  # 电池规格
                    ('batch_number', 'TEXT'),  # 🔧 新增：测试时的实际批次号
                    ('rct_coefficient_of_variation', 'REAL'),  # Rct变异系数
                    ('capacity_prediction', 'REAL'),  # 容量预测
                    ('voltage_range_min', 'REAL'),  # 电压范围最小值
                    ('voltage_range_max', 'REAL'),  # 电压范围最大值
                    ('rs_range_min', 'REAL'),  # Rs范围最小值
                    ('rs_range_max', 'REAL'),  # Rs范围最大值
                    ('rct_range_min', 'REAL'),  # Rct范围最小值
                    ('rct_range_max', 'REAL')  # Rct范围最大值
                ]

                for field_name, field_type in new_fields:
                    if field_name not in column_names:
                        try:
                            cursor.execute(f'ALTER TABLE test_results ADD COLUMN {field_name} {field_type}')
                            logger.info(f"添加test_results表字段: {field_name}")
                        except Exception as e:
                            logger.warning(f"添加test_results表字段{field_name}失败: {e}")

                # 检查impedance_details表是否需要添加离群率字段
                cursor.execute("PRAGMA table_info(impedance_details)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]

                # 需要添加的离群率字段
                detail_outlier_fields = [
                    ('z_value', 'REAL'),
                    ('baseline_z_value', 'REAL'),
                    ('deviation_percent', 'REAL')
                ]

                for field_name, field_type in detail_outlier_fields:
                    if field_name not in column_names:
                        try:
                            cursor.execute(f'ALTER TABLE impedance_details ADD COLUMN {field_name} {field_type}')
                            logger.info(f"添加impedance_details表字段: {field_name}")
                        except Exception as e:
                            logger.warning(f"添加impedance_details表字段{field_name}失败: {e}")

                conn.commit()
                logger.info("数据库迁移完成")

        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            # 迁移失败不应该阻止程序启动
            pass

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def create_batch(self, batch_info: Dict[str, Any]) -> int:
        """
        创建新批次

        Args:
            batch_info: 批次信息字典

        Returns:
            批次ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO batches (
                        batch_number, operator, cell_type, cell_spec,
                        standard_voltage, standard_capacity, start_time, remarks
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    batch_info['batch_number'],
                    batch_info['operator'],
                    batch_info['cell_type'],
                    batch_info['cell_spec'],
                    batch_info['standard_voltage'],
                    batch_info['standard_capacity'],
                    datetime.now(),
                    batch_info.get('remarks', '')
                ))

                batch_id = cursor.lastrowid
                conn.commit()

                logger.info(f"批次创建成功: {batch_info['batch_number']} (ID: {batch_id})")
                return batch_id

        except sqlite3.IntegrityError as e:
            # 检查是否是批次号重复错误
            if "batch_number" in str(e):
                # 批次号重复时，查找现有批次并返回其ID
                logger.info(f"批次号已存在，重用现有批次: {batch_info['batch_number']}")
                existing_batch = self.get_batch_by_number(batch_info['batch_number'])
                if existing_batch:
                    return existing_batch['id']
                else:
                    # 如果查找失败，生成新的批次号
                    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
                    new_batch_number = f"{batch_info['batch_number']}-{timestamp}"
                    logger.info(f"生成新批次号: {new_batch_number}")
                    batch_info['batch_number'] = new_batch_number
                    return self.create_batch(batch_info)  # 递归调用
            else:
                logger.error(f"数据库完整性错误: {e}")
                raise
        except Exception as e:
            logger.error(f"创建批次失败: {e}")
            raise

    def get_batch_by_number(self, batch_number: str) -> Optional[Dict[str, Any]]:
        """
        根据批次号获取批次信息

        Args:
            batch_number: 批次号

        Returns:
            批次信息字典，如果不存在返回None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('SELECT * FROM batches WHERE batch_number = ?', (batch_number,))
                row = cursor.fetchone()

                if row:
                    return dict(row)
                return None

        except Exception as e:
            logger.error(f"获取批次信息失败: {e}")
            raise

    def update_batch_statistics(self, batch_id: int):
        """
        更新批次统计信息

        Args:
            batch_id: 批次ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 计算统计数据
                cursor.execute('''
                    SELECT
                        COUNT(*) as total_count,
                        SUM(CASE WHEN is_pass = 1 THEN 1 ELSE 0 END) as pass_count,
                        SUM(CASE WHEN is_pass = 0 THEN 1 ELSE 0 END) as fail_count
                    FROM test_results
                    WHERE batch_id = ?
                ''', (batch_id,))

                stats = cursor.fetchone()
                total_count = stats['total_count']
                pass_count = stats['pass_count']
                fail_count = stats['fail_count']
                yield_rate = (pass_count / total_count * 100) if total_count > 0 else 0.0

                # 更新批次统计
                cursor.execute('''
                    UPDATE batches
                    SET total_count = ?, pass_count = ?, fail_count = ?,
                        yield_rate = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (total_count, pass_count, fail_count, yield_rate, batch_id))

                conn.commit()
                logger.debug(f"批次统计更新完成: ID={batch_id}, 总数={total_count}, 良率={yield_rate:.1f}%")

        except Exception as e:
            logger.error(f"更新批次统计失败: {e}")
            raise

    def save_test_result(self, test_data: Dict[str, Any]) -> int:
        """
        保存测试结果

        Args:
            test_data: 测试数据字典

        Returns:
            测试结果ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 插入测试结果
                cursor.execute('''
                    INSERT INTO test_results (
                        batch_id, channel_number, battery_code, test_start_time,
                        test_end_time, test_duration, voltage, rs_value, rct_value,
                        w_impedance, rs_grade, rct_grade, is_pass, fail_reason,
                        test_mode, frequency_list, raw_data, outlier_result,
                        baseline_filename, baseline_id, max_deviation_percent, frequency_deviations,
                        operator, battery_type, battery_spec, batch_number, rct_coefficient_of_variation, capacity_prediction,
                        voltage_range_min, voltage_range_max, rs_range_min, rs_range_max, rct_range_min, rct_range_max
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    test_data['batch_id'],
                    test_data['channel_number'],
                    test_data['battery_code'],
                    test_data['test_start_time'],
                    test_data['test_end_time'],
                    test_data['test_duration'],
                    test_data['voltage'],
                    test_data['rs_value'],
                    test_data['rct_value'],
                    test_data.get('w_impedance'),
                    test_data.get('rs_grade'),
                    test_data.get('rct_grade'),
                    test_data['is_pass'],
                    test_data.get('fail_reason'),
                    test_data['test_mode'],
                    json.dumps(test_data.get('frequency_list', [])),
                    json.dumps(test_data.get('raw_data', {})),
                    test_data.get('outlier_result'),
                    test_data.get('baseline_filename'),
                    test_data.get('baseline_id'),
                    test_data.get('max_deviation_percent'),
                    json.dumps(test_data.get('frequency_deviations', {})),
                    test_data.get('operator'),
                    test_data.get('battery_type'),
                    test_data.get('battery_spec'),
                    test_data.get('batch_number'),  # 🔧 新增：保存测试时的实际批次号
                    test_data.get('rct_coefficient_of_variation'),
                    test_data.get('capacity_prediction'),
                    test_data.get('voltage_range_min'),
                    test_data.get('voltage_range_max'),
                    test_data.get('rs_range_min'),
                    test_data.get('rs_range_max'),
                    test_data.get('rct_range_min'),
                    test_data.get('rct_range_max')
                ))

                test_result_id = cursor.lastrowid

                # 保存频率数据
                if 'frequency_data' in test_data:
                    for freq_data in test_data['frequency_data']:
                        cursor.execute('''
                            INSERT INTO frequency_data (
                                test_result_id, frequency, impedance_real, impedance_imag,
                                impedance_magnitude, impedance_phase
                            ) VALUES (?, ?, ?, ?, ?, ?)
                        ''', (
                            test_result_id,
                            freq_data['frequency'],
                            freq_data['impedance_real'],
                            freq_data['impedance_imag'],
                            freq_data['impedance_magnitude'],
                            freq_data['impedance_phase']
                        ))

                conn.commit()

                # 更新批次统计
                self.update_batch_statistics(test_data['batch_id'])

                logger.info(f"测试结果保存成功: {test_data['battery_code']} (ID: {test_result_id})")
                return test_result_id

        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")
            raise

    def save_impedance_detail(self, detail_data: Dict[str, Any]) -> int:
        """
        保存阻抗测试明细数据

        Args:
            detail_data: 明细数据字典，包含：
                - batch_id: 批次ID
                - channel_number: 通道号 (1-8)
                - battery_code: 电池码
                - test_timestamp: ISO格式时间戳
                - frequency: 测试频点 (Hz)
                - impedance_real: 阻抗实部 (mΩ)
                - impedance_imag: 阻抗虚部 (mΩ)
                - voltage: 电压 (V)
                - test_sequence: 测试序号

        Returns:
            明细记录ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO impedance_details (
                        batch_id, channel_number, battery_code, test_timestamp,
                        frequency, impedance_real, impedance_imag, voltage, test_sequence,
                        z_value, baseline_z_value, deviation_percent
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    detail_data['batch_id'],
                    detail_data['channel_number'],
                    detail_data['battery_code'],
                    detail_data['test_timestamp'],
                    detail_data['frequency'],
                    detail_data['impedance_real'],
                    detail_data['impedance_imag'],
                    detail_data.get('voltage'),
                    detail_data.get('test_sequence', 0),
                    detail_data.get('z_value'),
                    detail_data.get('baseline_z_value'),
                    detail_data.get('deviation_percent')
                ))

                detail_id = cursor.lastrowid
                conn.commit()

                logger.debug(f"阻抗明细数据保存成功: 通道{detail_data['channel_number']}, "
                           f"频率{detail_data['frequency']:.3f}Hz, "
                           f"实部{detail_data['impedance_real']:.3f}mΩ, "
                           f"虚部{detail_data['impedance_imag']:.3f}mΩ")

                return detail_id

        except Exception as e:
            logger.error(f"保存阻抗明细数据失败: {e}")
            raise

    def get_impedance_details(self, batch_id: Optional[int] = None,
                             channel_number: Optional[int] = None,
                             battery_code: Optional[str] = None,
                             limit: int = 1000) -> List[Dict[str, Any]]:
        """
        查询阻抗测试明细数据

        Args:
            batch_id: 批次ID
            channel_number: 通道号
            battery_code: 电池码
            limit: 查询限制数量

        Returns:
            明细数据列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if batch_id is not None:
                    conditions.append('batch_id = ?')
                    params.append(batch_id)

                if channel_number is not None:
                    conditions.append('channel_number = ?')
                    params.append(channel_number)

                if battery_code is not None:
                    conditions.append('battery_code = ?')
                    params.append(battery_code)

                where_clause = 'WHERE ' + ' AND '.join(conditions) if conditions else ''

                query = f'''
                    SELECT * FROM impedance_details
                    {where_clause}
                    ORDER BY test_timestamp ASC, test_sequence ASC
                    LIMIT ?
                '''

                params.append(limit)
                cursor.execute(query, params)

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"查询阻抗明细数据失败: {e}")
            raise

    def get_test_results(self, batch_id: Optional[int] = None,
                        start_date: Optional[date] = None,
                        end_date: Optional[date] = None,
                        channel_number: Optional[int] = None,
                        channel_numbers: Optional[List[int]] = None,
                        battery_code: Optional[str] = None,
                        battery_code_fuzzy: bool = False,
                        is_pass: Optional[bool] = None,
                        limit: int = 20,
                        offset: int = 0,
                        include_json: bool = False) -> List[Dict[str, Any]]:
        """
        查询测试结果（性能优化版本，支持分页）

        Args:
            batch_id: 批次ID
            start_date: 开始日期
            end_date: 结束日期
            channel_number: 单个通道号（与channel_numbers互斥）
            channel_numbers: 多个通道号列表（与channel_number互斥）
            battery_code: 电池码搜索文本
            battery_code_fuzzy: 是否模糊搜索电池码
            is_pass: 是否合格
            limit: 查询限制数量（默认20条）
            offset: 偏移量，用于分页
            include_json: 是否包含JSON字段（frequency_list, raw_data）

        Returns:
            测试结果列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if batch_id is not None:
                    conditions.append('tr.batch_id = ?')
                    params.append(batch_id)

                if start_date is not None:
                    conditions.append('DATE(tr.test_start_time) >= ?')
                    params.append(start_date.isoformat())

                if end_date is not None:
                    conditions.append('DATE(tr.test_start_time) <= ?')
                    params.append(end_date.isoformat())

                # 通道号筛选（支持单个或多个）
                if channel_numbers is not None and len(channel_numbers) > 0:
                    # 多通道筛选
                    placeholders = ','.join(['?'] * len(channel_numbers))
                    conditions.append(f'tr.channel_number IN ({placeholders})')
                    params.extend(channel_numbers)
                elif channel_number is not None:
                    # 单通道筛选（向后兼容）
                    conditions.append('tr.channel_number = ?')
                    params.append(channel_number)

                # 电池码筛选
                if battery_code is not None and battery_code.strip():
                    if battery_code_fuzzy:
                        # 模糊搜索（不区分大小写）
                        conditions.append('LOWER(tr.battery_code) LIKE LOWER(?)')
                        params.append(f'%{battery_code.strip()}%')
                    else:
                        # 精确匹配
                        conditions.append('tr.battery_code = ?')
                        params.append(battery_code.strip())

                if is_pass is not None:
                    conditions.append('tr.is_pass = ?')
                    params.append(is_pass)

                where_clause = 'WHERE ' + ' AND '.join(conditions) if conditions else ''

                # 性能优化：根据需要选择字段，避免不必要的JSON字段查询
                # 🔧 修复：优先使用测试结果表中的批次号、操作员、电池类型、规格信息
                if include_json:
                    select_fields = "tr.*, b.batch_number as batch_table_batch_number, b.operator as batch_operator, b.cell_type as batch_cell_type, b.cell_spec as batch_cell_spec"
                else:
                    select_fields = """tr.id, tr.batch_id, tr.channel_number, tr.battery_code,
                                     tr.test_start_time, tr.test_end_time, tr.test_duration,
                                     tr.voltage, tr.rs_value, tr.rct_value, tr.w_impedance, tr.rs_grade, tr.rct_grade,
                                     tr.is_pass, tr.fail_reason,
                                     tr.outlier_result, tr.baseline_filename, tr.baseline_id, tr.max_deviation_percent,
                                     tr.operator, tr.battery_type, tr.battery_spec, tr.batch_number,
                                     tr.voltage_range_min, tr.voltage_range_max, tr.rs_range_min, tr.rs_range_max, tr.rct_range_min, tr.rct_range_max,
                                     b.batch_number as batch_table_batch_number, b.operator as batch_operator, b.cell_type as batch_cell_type, b.cell_spec as batch_cell_spec"""

                query = f'''
                    SELECT {select_fields}
                    FROM test_results tr
                    LEFT JOIN batches b ON tr.batch_id = b.id
                    {where_clause}
                    ORDER BY tr.test_start_time DESC
                    LIMIT ? OFFSET ?
                '''

                params.extend([limit, offset])
                cursor.execute(query, params)

                results = []
                for row in cursor.fetchall():
                    result = dict(row)
                    # 性能优化：只在需要时解析JSON字段
                    if include_json:
                        if result.get('frequency_list'):
                            result['frequency_list'] = json.loads(result['frequency_list'])
                        if result.get('raw_data'):
                            result['raw_data'] = json.loads(result['raw_data'])
                    results.append(result)

                return results

        except Exception as e:
            logger.error(f"查询测试结果失败: {e}")
            raise

    def get_test_results_count(self, batch_id: Optional[int] = None,
                              start_date: Optional[date] = None,
                              end_date: Optional[date] = None,
                              channel_number: Optional[int] = None,
                              channel_numbers: Optional[List[int]] = None,
                              battery_code: Optional[str] = None,
                              battery_code_fuzzy: bool = False,
                              is_pass: Optional[bool] = None) -> int:
        """
        获取测试结果总数（用于分页）

        Args:
            batch_id: 批次ID
            start_date: 开始日期
            end_date: 结束日期
            channel_number: 单个通道号（与channel_numbers互斥）
            channel_numbers: 多个通道号列表（与channel_number互斥）
            battery_code: 电池码搜索文本
            battery_code_fuzzy: 是否模糊搜索电池码
            is_pass: 是否合格

        Returns:
            符合条件的测试结果总数
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件（与get_test_results保持一致）
                conditions = []
                params = []

                if batch_id is not None:
                    conditions.append('tr.batch_id = ?')
                    params.append(batch_id)

                if start_date is not None:
                    conditions.append('DATE(tr.test_start_time) >= ?')
                    params.append(start_date.isoformat())

                if end_date is not None:
                    conditions.append('DATE(tr.test_start_time) <= ?')
                    params.append(end_date.isoformat())

                # 通道号筛选（支持单个或多个）
                if channel_numbers is not None and len(channel_numbers) > 0:
                    # 多通道筛选
                    placeholders = ','.join(['?'] * len(channel_numbers))
                    conditions.append(f'tr.channel_number IN ({placeholders})')
                    params.extend(channel_numbers)
                elif channel_number is not None:
                    # 单通道筛选（向后兼容）
                    conditions.append('tr.channel_number = ?')
                    params.append(channel_number)

                # 电池码筛选
                if battery_code is not None and battery_code.strip():
                    if battery_code_fuzzy:
                        # 模糊搜索（不区分大小写）
                        conditions.append('LOWER(tr.battery_code) LIKE LOWER(?)')
                        params.append(f'%{battery_code.strip()}%')
                    else:
                        # 精确匹配
                        conditions.append('tr.battery_code = ?')
                        params.append(battery_code.strip())

                if is_pass is not None:
                    conditions.append('tr.is_pass = ?')
                    params.append(is_pass)

                where_clause = 'WHERE ' + ' AND '.join(conditions) if conditions else ''

                query = f'''
                    SELECT COUNT(*) as total
                    FROM test_results tr
                    LEFT JOIN batches b ON tr.batch_id = b.id
                    {where_clause}
                '''

                cursor.execute(query, params)
                result = cursor.fetchone()
                return result['total'] if result else 0

        except Exception as e:
            logger.error(f"查询测试结果总数失败: {e}")
            return 0

    def get_batch_statistics(self, batch_id: int) -> Dict[str, Any]:
        """
        获取批次统计信息

        Args:
            batch_id: 批次ID

        Returns:
            统计信息字典
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取基本统计（修复：只统计合格电池的Rs和Rct）
                cursor.execute('''
                    SELECT
                        COUNT(*) as total_count,
                        SUM(CASE WHEN is_pass = 1 THEN 1 ELSE 0 END) as pass_count,
                        SUM(CASE WHEN is_pass = 0 THEN 1 ELSE 0 END) as fail_count,
                        AVG(voltage) as avg_voltage,
                        AVG(CASE WHEN is_pass = 1 THEN rs_value ELSE NULL END) as avg_rs,
                        AVG(CASE WHEN is_pass = 1 THEN rct_value ELSE NULL END) as avg_rct,
                        MIN(test_start_time) as first_test,
                        MAX(test_end_time) as last_test
                    FROM test_results
                    WHERE batch_id = ?
                ''', (batch_id,))

                stats = dict(cursor.fetchone())

                # 计算良率
                if stats['total_count'] > 0:
                    stats['yield_rate'] = stats['pass_count'] / stats['total_count'] * 100
                else:
                    stats['yield_rate'] = 0.0

                # 获取Rs-Rct档位分布（修复：只统计合格电池的档位）
                cursor.execute('''
                    SELECT rs_grade, rct_grade, COUNT(*) as count
                    FROM test_results
                    WHERE batch_id = ? AND is_pass = 1 AND rs_grade IS NOT NULL AND rct_grade IS NOT NULL
                    GROUP BY rs_grade, rct_grade
                ''', (batch_id,))

                grade_distribution = {}
                for row in cursor.fetchall():
                    key = f"Rs{row['rs_grade']}-Rct{row['rct_grade']}"
                    grade_distribution[key] = row['count']

                stats['grade_distribution'] = grade_distribution

                return stats

        except Exception as e:
            logger.error(f"获取批次统计失败: {e}")
            raise

    def get_recent_batches(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的批次列表

        Args:
            limit: 查询限制数量

        Returns:
            批次列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM batches
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"获取最近批次失败: {e}")
            raise

    def delete_batch(self, batch_id: int) -> bool:
        """
        删除批次及其相关数据

        Args:
            batch_id: 批次ID

        Returns:
            是否删除成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 删除频率数据
                cursor.execute('''
                    DELETE FROM frequency_data
                    WHERE test_result_id IN (
                        SELECT id FROM test_results WHERE batch_id = ?
                    )
                ''', (batch_id,))

                # 删除测试结果
                cursor.execute('DELETE FROM test_results WHERE batch_id = ?', (batch_id,))

                # 删除批次
                cursor.execute('DELETE FROM batches WHERE id = ?', (batch_id,))

                conn.commit()

                logger.info(f"批次删除成功: ID={batch_id}")
                return True

        except Exception as e:
            logger.error(f"删除批次失败: {e}")
            return False

    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库

        Args:
            backup_path: 备份文件路径

        Returns:
            是否备份成功
        """
        try:
            import shutil

            # 确保备份目录存在
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)

            # 复制数据库文件
            shutil.copy2(self.db_path, backup_path)

            logger.info(f"数据库备份成功: {backup_path}")
            return True

        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False

    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息

        Returns:
            数据库信息字典
        """
        try:
            info = {
                'db_path': self.db_path,
                'db_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                'created_time': datetime.fromtimestamp(os.path.getctime(self.db_path)) if os.path.exists(self.db_path) else None,
                'modified_time': datetime.fromtimestamp(os.path.getmtime(self.db_path)) if os.path.exists(self.db_path) else None
            }

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取表统计
                tables = ['batches', 'test_results', 'frequency_data', 'system_logs']
                table_stats = {}

                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) as count FROM {table}')
                    table_stats[table] = cursor.fetchone()['count']

                info['table_stats'] = table_stats

            return info

        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {'error': str(e)}

    def log_system_event(self, level: str, message: str, module: Optional[str] = None,
                        function: Optional[str] = None, line_number: Optional[int] = None):
        """
        记录系统日志

        Args:
            level: 日志级别
            message: 日志消息
            module: 模块名
            function: 函数名
            line_number: 行号
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO system_logs (level, message, module, function, line_number)
                    VALUES (?, ?, ?, ?, ?)
                ''', (level, message, module, function, line_number))

                conn.commit()

        except Exception as e:
            # 避免日志记录失败影响主要功能
            logger.error(f"记录系统日志失败: {e}")

    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧日志

        Args:
            days: 保留天数
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    DELETE FROM system_logs
                    WHERE timestamp < datetime('now', '-{} days')
                '''.format(days))

                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"清理旧日志完成: 删除 {deleted_count} 条记录")

        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")

    def delete_test_result(self, test_result_id: int) -> bool:
        """
        删除单个测试结果及其关联的阻抗明细数据

        Args:
            test_result_id: 测试结果ID

        Returns:
            是否删除成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取测试结果信息用于日志
                cursor.execute('''
                    SELECT tr.*, b.batch_number
                    FROM test_results tr
                    LEFT JOIN batches b ON tr.batch_id = b.id
                    WHERE tr.id = ?
                ''', (test_result_id,))

                test_result = cursor.fetchone()
                if not test_result:
                    logger.warning(f"测试结果不存在: ID={test_result_id}")
                    return False

                # 删除阻抗明细数据
                cursor.execute('''
                    DELETE FROM impedance_details
                    WHERE batch_id = ? AND channel_number = ? AND battery_code = ?
                ''', (test_result['batch_id'], test_result['channel_number'], test_result['battery_code']))

                impedance_deleted = cursor.rowcount

                # 删除测试结果
                cursor.execute('DELETE FROM test_results WHERE id = ?', (test_result_id,))

                if cursor.rowcount == 0:
                    logger.warning(f"测试结果删除失败: ID={test_result_id}")
                    return False

                conn.commit()

                logger.info(f"测试结果删除成功: ID={test_result_id}, "
                           f"批次={test_result['batch_number']}, "
                           f"通道={test_result['channel_number']}, "
                           f"电池码={test_result['battery_code']}, "
                           f"删除阻抗明细数据={impedance_deleted}条")

                # 更新批次统计
                self.update_batch_statistics(test_result['batch_id'])

                return True

        except Exception as e:
            logger.error(f"删除测试结果失败: {e}")
            return False

    def delete_test_results(self, test_result_ids: List[int]) -> Dict[str, int]:
        """
        批量删除测试结果及其关联的阻抗明细数据

        Args:
            test_result_ids: 测试结果ID列表

        Returns:
            删除结果统计 {'success': 成功数量, 'failed': 失败数量, 'impedance_details': 明细数据删除数量}
        """
        try:
            success_count = 0
            failed_count = 0
            total_impedance_deleted = 0
            affected_batches = set()

            with self.get_connection() as conn:
                cursor = conn.cursor()

                for test_result_id in test_result_ids:
                    try:
                        # 获取测试结果信息
                        cursor.execute('''
                            SELECT tr.*, b.batch_number
                            FROM test_results tr
                            LEFT JOIN batches b ON tr.batch_id = b.id
                            WHERE tr.id = ?
                        ''', (test_result_id,))

                        test_result = cursor.fetchone()
                        if not test_result:
                            failed_count += 1
                            continue

                        # 删除阻抗明细数据
                        cursor.execute('''
                            DELETE FROM impedance_details
                            WHERE batch_id = ? AND channel_number = ? AND battery_code = ?
                        ''', (test_result['batch_id'], test_result['channel_number'], test_result['battery_code']))

                        impedance_deleted = cursor.rowcount
                        total_impedance_deleted += impedance_deleted

                        # 删除测试结果
                        cursor.execute('DELETE FROM test_results WHERE id = ?', (test_result_id,))

                        if cursor.rowcount > 0:
                            success_count += 1
                            affected_batches.add(test_result['batch_id'])
                            logger.debug(f"删除测试结果: ID={test_result_id}, 明细数据={impedance_deleted}条")
                        else:
                            failed_count += 1

                    except Exception as e:
                        logger.error(f"删除单个测试结果失败: ID={test_result_id}, 错误={e}")
                        failed_count += 1

                conn.commit()

                # 更新受影响批次的统计
                for batch_id in affected_batches:
                    self.update_batch_statistics(batch_id)

                logger.info(f"批量删除测试结果完成: 成功={success_count}, 失败={failed_count}, "
                           f"删除阻抗明细数据={total_impedance_deleted}条")

                return {
                    'success': success_count,
                    'failed': failed_count,
                    'impedance_details': total_impedance_deleted
                }

        except Exception as e:
            logger.error(f"批量删除测试结果失败: {e}")
            return {'success': 0, 'failed': len(test_result_ids), 'impedance_details': 0}

    def reset_database(self, keep_batches: bool = True) -> Dict[str, Any]:
        """
        重置数据库（清空测试数据，可选择保留批次信息）

        Args:
            keep_batches: 是否保留批次信息

        Returns:
            重置结果统计
        """
        try:
            # 创建备份
            backup_path = f"data/backup/database_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            backup_success = self.backup_database(backup_path)

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 统计重置前的数据
                cursor.execute('SELECT COUNT(*) as count FROM test_results')
                test_results_count = cursor.fetchone()['count']

                cursor.execute('SELECT COUNT(*) as count FROM impedance_details')
                impedance_details_count = cursor.fetchone()['count']

                cursor.execute('SELECT COUNT(*) as count FROM batches')
                batches_count = cursor.fetchone()['count']

                cursor.execute('SELECT COUNT(*) as count FROM system_logs')
                logs_count = cursor.fetchone()['count']

                # 删除测试数据
                cursor.execute('DELETE FROM impedance_details')
                cursor.execute('DELETE FROM test_results')

                if not keep_batches:
                    cursor.execute('DELETE FROM batches')
                else:
                    # 重置批次统计信息
                    cursor.execute('''
                        UPDATE batches SET
                        total_count = 0,
                        pass_count = 0,
                        fail_count = 0,
                        yield_rate = 0.0,
                        end_time = NULL
                    ''')

                # 清理旧日志（保留最近7天）
                cursor.execute('''
                    DELETE FROM system_logs
                    WHERE timestamp < datetime('now', '-7 days')
                ''')

                conn.commit()

                result = {
                    'backup_created': backup_success,
                    'backup_path': backup_path if backup_success else None,
                    'deleted_test_results': test_results_count,
                    'deleted_impedance_details': impedance_details_count,
                    'deleted_batches': batches_count if not keep_batches else 0,
                    'cleaned_logs': logs_count,
                    'batches_reset': batches_count if keep_batches else 0
                }

                logger.info(f"数据库重置完成: {result}")

                # 记录重置操作
                self.log_system_event('INFO', f'数据库重置完成: 删除测试结果{test_results_count}条, '
                                             f'删除阻抗明细{impedance_details_count}条, '
                                             f'保留批次={keep_batches}')

                return result

        except Exception as e:
            logger.error(f"数据库重置失败: {e}")
            return {'error': str(e)}

    def validate_data_integrity(self) -> Dict[str, Any]:
        """
        验证数据完整性

        Returns:
            验证结果报告
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查表结构
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row['name'] for row in cursor.fetchall()]

                required_tables = ['batches', 'test_results', 'impedance_details', 'system_logs']
                missing_tables = [table for table in required_tables if table not in tables]

                # 检查测试结果与批次的关联
                cursor.execute('''
                    SELECT COUNT(*) as count
                    FROM test_results tr
                    LEFT JOIN batches b ON tr.batch_id = b.id
                    WHERE b.id IS NULL
                ''')
                orphaned_test_results = cursor.fetchone()['count']

                # 检查阻抗明细数据与测试结果的关联
                cursor.execute('''
                    SELECT tr.id, tr.batch_id, tr.channel_number, tr.battery_code,
                           COUNT(id.id) as detail_count
                    FROM test_results tr
                    LEFT JOIN impedance_details id ON (
                        tr.batch_id = id.batch_id AND
                        tr.channel_number = id.channel_number AND
                        tr.battery_code = id.battery_code
                    )
                    GROUP BY tr.id, tr.batch_id, tr.channel_number, tr.battery_code
                ''')

                test_results_with_details = cursor.fetchall()
                missing_details = [row for row in test_results_with_details if row['detail_count'] == 0]

                # 检查孤立的阻抗明细数据
                cursor.execute('''
                    SELECT COUNT(*) as count
                    FROM impedance_details id
                    WHERE NOT EXISTS (
                        SELECT 1 FROM test_results tr
                        WHERE tr.batch_id = id.batch_id
                        AND tr.channel_number = id.channel_number
                        AND tr.battery_code = id.battery_code
                    )
                ''')
                orphaned_impedance_details = cursor.fetchone()['count']

                # 检查数据统计
                cursor.execute('SELECT COUNT(*) as count FROM batches')
                total_batches = cursor.fetchone()['count']

                cursor.execute('SELECT COUNT(*) as count FROM test_results')
                total_test_results = cursor.fetchone()['count']

                cursor.execute('SELECT COUNT(*) as count FROM impedance_details')
                total_impedance_details = cursor.fetchone()['count']

                # 检查批次统计准确性
                cursor.execute('''
                    SELECT b.id, b.batch_number, b.total_count, b.pass_count, b.fail_count,
                           COUNT(tr.id) as actual_total,
                           SUM(CASE WHEN tr.is_pass = 1 THEN 1 ELSE 0 END) as actual_pass,
                           SUM(CASE WHEN tr.is_pass = 0 THEN 1 ELSE 0 END) as actual_fail
                    FROM batches b
                    LEFT JOIN test_results tr ON b.id = tr.batch_id
                    GROUP BY b.id, b.batch_number, b.total_count, b.pass_count, b.fail_count
                ''')

                batch_stats_issues = []
                for row in cursor.fetchall():
                    if (row['total_count'] != row['actual_total'] or
                        row['pass_count'] != row['actual_pass'] or
                        row['fail_count'] != row['actual_fail']):
                        batch_stats_issues.append({
                            'batch_id': row['id'],
                            'batch_number': row['batch_number'],
                            'recorded': {
                                'total': row['total_count'],
                                'pass': row['pass_count'],
                                'fail': row['fail_count']
                            },
                            'actual': {
                                'total': row['actual_total'],
                                'pass': row['actual_pass'],
                                'fail': row['actual_fail']
                            }
                        })

                # 生成验证报告
                report = {
                    'timestamp': datetime.now().isoformat(),
                    'table_structure': {
                        'required_tables': required_tables,
                        'existing_tables': tables,
                        'missing_tables': missing_tables,
                        'structure_ok': len(missing_tables) == 0
                    },
                    'data_counts': {
                        'batches': total_batches,
                        'test_results': total_test_results,
                        'impedance_details': total_impedance_details
                    },
                    'data_integrity': {
                        'orphaned_test_results': orphaned_test_results,
                        'test_results_missing_details': len(missing_details),
                        'orphaned_impedance_details': orphaned_impedance_details,
                        'batch_stats_issues': len(batch_stats_issues)
                    },
                    'issues': {
                        'missing_details_list': missing_details[:10],  # 只显示前10个
                        'batch_stats_issues': batch_stats_issues[:5]   # 只显示前5个
                    },
                    'overall_status': 'HEALTHY' if (
                        len(missing_tables) == 0 and
                        orphaned_test_results == 0 and
                        len(missing_details) == 0 and
                        orphaned_impedance_details == 0 and
                        len(batch_stats_issues) == 0
                    ) else 'ISSUES_FOUND'
                }

                logger.info(f"数据完整性验证完成: 状态={report['overall_status']}")
                return report

        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            return {'error': str(e), 'overall_status': 'ERROR'}

    def check_data_integrity(self) -> Dict[str, Any]:
        """
        检查数据完整性（兼容性方法）

        Returns:
            验证结果报告
        """
        return self.validate_data_integrity()

    def save_capacity_prediction_data(self, prediction_data: Dict[str, Any]) -> int:
        """
        保存容量预测数据

        Args:
            prediction_data: 容量预测数据字典，包含：
                - battery_code: 电池编码
                - batch_id: 批次ID（可选）
                - channel_number: 通道号（可选）
                - test_date: 测试日期
                - voltage: 电压值
                - rs_value: Rs值
                - rct_value: Rct值
                - rct_coefficient_of_variation: Rct变异系数
                - actual_capacity: 实际容量（可选）
                - predicted_capacity: 预测容量（可选）
                - voltage_range_min/max: 电压范围
                - rs_range_min/max: Rs范围
                - notes: 备注（可选）

        Returns:
            容量预测数据ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 计算容量误差
                capacity_error = None
                if prediction_data.get('actual_capacity') and prediction_data.get('predicted_capacity'):
                    actual = prediction_data['actual_capacity']
                    predicted = prediction_data['predicted_capacity']
                    capacity_error = abs(actual - predicted) / actual * 100 if actual != 0 else None

                cursor.execute('''
                    INSERT INTO capacity_prediction_data (
                        battery_code, batch_id, channel_number, test_date,
                        voltage, rs_value, rct_value, rct_coefficient_of_variation,
                        actual_capacity, predicted_capacity, capacity_error,
                        voltage_range_min, voltage_range_max, rs_range_min, rs_range_max,
                        is_valid_for_training, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    prediction_data['battery_code'],
                    prediction_data.get('batch_id'),
                    prediction_data.get('channel_number'),
                    prediction_data['test_date'],
                    prediction_data['voltage'],
                    prediction_data['rs_value'],
                    prediction_data['rct_value'],
                    prediction_data['rct_coefficient_of_variation'],
                    prediction_data.get('actual_capacity'),
                    prediction_data.get('predicted_capacity'),
                    capacity_error,
                    prediction_data.get('voltage_range_min'),
                    prediction_data.get('voltage_range_max'),
                    prediction_data.get('rs_range_min'),
                    prediction_data.get('rs_range_max'),
                    prediction_data.get('is_valid_for_training', True),
                    prediction_data.get('notes', '')
                ))

                prediction_id = cursor.lastrowid
                conn.commit()

                logger.info(f"容量预测数据保存成功: {prediction_data['battery_code']} (ID: {prediction_id})")
                return prediction_id

        except Exception as e:
            logger.error(f"保存容量预测数据失败: {e}")
            raise

    def get_capacity_prediction_data(self, battery_code: Optional[str] = None,
                                   batch_id: Optional[int] = None,
                                   start_date: Optional[date] = None,
                                   end_date: Optional[date] = None,
                                   has_actual_capacity: Optional[bool] = None,
                                   limit: int = 100,
                                   offset: int = 0) -> List[Dict[str, Any]]:
        """
        查询容量预测数据

        Args:
            battery_code: 电池编码（支持模糊搜索）
            batch_id: 批次ID
            start_date: 开始日期
            end_date: 结束日期
            has_actual_capacity: 是否有实际容量数据
            limit: 查询限制数量
            offset: 偏移量

        Returns:
            容量预测数据列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if battery_code:
                    conditions.append('battery_code LIKE ?')
                    params.append(f'%{battery_code}%')

                if batch_id is not None:
                    conditions.append('batch_id = ?')
                    params.append(batch_id)

                if start_date:
                    conditions.append('test_date >= ?')
                    params.append(start_date.isoformat())

                if end_date:
                    conditions.append('test_date <= ?')
                    params.append(end_date.isoformat())

                if has_actual_capacity is not None:
                    if has_actual_capacity:
                        conditions.append('actual_capacity IS NOT NULL')
                    else:
                        conditions.append('actual_capacity IS NULL')

                where_clause = 'WHERE ' + ' AND '.join(conditions) if conditions else ''

                query = f'''
                    SELECT * FROM capacity_prediction_data
                    {where_clause}
                    ORDER BY test_date DESC, created_at DESC
                    LIMIT ? OFFSET ?
                '''

                params.extend([limit, offset])
                cursor.execute(query, params)

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"查询容量预测数据失败: {e}")
            raise

    def update_actual_capacity(self, prediction_id: int, actual_capacity: float, notes: str = '') -> bool:
        """
        更新实际容量值

        Args:
            prediction_id: 容量预测数据ID
            actual_capacity: 实际容量值
            notes: 备注

        Returns:
            是否更新成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取当前数据以计算误差
                cursor.execute('SELECT predicted_capacity FROM capacity_prediction_data WHERE id = ?', (prediction_id,))
                row = cursor.fetchone()

                if not row:
                    logger.warning(f"未找到ID为{prediction_id}的容量预测数据")
                    return False

                predicted_capacity = row['predicted_capacity']

                # 计算容量误差
                capacity_error = None
                if predicted_capacity and actual_capacity:
                    capacity_error = abs(actual_capacity - predicted_capacity) / actual_capacity * 100

                # 更新数据
                cursor.execute('''
                    UPDATE capacity_prediction_data
                    SET actual_capacity = ?, capacity_error = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (actual_capacity, capacity_error, notes, prediction_id))

                conn.commit()

                if cursor.rowcount > 0:
                    logger.info(f"实际容量更新成功: ID={prediction_id}, 容量={actual_capacity:.3f}AH")
                    return True
                else:
                    logger.warning(f"未找到ID为{prediction_id}的记录")
                    return False

        except Exception as e:
            logger.error(f"更新实际容量失败: {e}")
            return False

    def read_voltage(self, channel_number: int) -> Optional[float]:
        """
        读取通道电压（兼容性方法）
        
        注意：这个方法主要用于兼容性，实际电压读取应该通过通信管理器进行
        这里返回最近一次测试的电压值作为参考
        
        Args:
            channel_number: 通道号 (1-8)
            
        Returns:
            电压值，如果没有找到返回None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 查询该通道最近一次测试的电压
                cursor.execute('''
                    SELECT voltage FROM test_results 
                    WHERE channel_number = ? AND voltage IS NOT NULL
                    ORDER BY test_start_time DESC 
                    LIMIT 1
                ''', (channel_number,))
                
                row = cursor.fetchone()
                if row and row['voltage'] is not None:
                    voltage = float(row['voltage'])
                    logger.debug(f"从数据库读取通道{channel_number}电压: {voltage:.3f}V")
                    return voltage
                else:
                    logger.debug(f"未找到通道{channel_number}的电压记录")
                    return None
                    
        except Exception as e:
            logger.error(f"读取通道{channel_number}电压失败: {e}")
            return None


# ===== 全局数据库管理器实例管理 =====

# 全局数据库管理器实例
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> Optional[DatabaseManager]:
    """获取全局数据库管理器实例"""
    return _database_manager


def initialize_database_manager(db_path: str = "data/test_results.db") -> DatabaseManager:
    """
    初始化全局数据库管理器

    Args:
        db_path: 数据库文件路径

    Returns:
        数据库管理器实例
    """
    global _database_manager

    if _database_manager is None:
        _database_manager = DatabaseManager(db_path)

        logger.info("✅ 全局数据库管理器初始化完成")

    return _database_manager
