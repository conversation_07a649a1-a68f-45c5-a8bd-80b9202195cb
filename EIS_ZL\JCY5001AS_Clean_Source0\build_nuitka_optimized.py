#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A 优化版Nuitka打包脚本
基于最新依赖和最佳实践

版权所有：鲸测云
作者：weiwei
版本：v0.84 优化版
"""

import os
import sys
import subprocess
import shutil
import time
from datetime import datetime
from pathlib import Path

class NuitkaBuilder:
    """Nuitka构建器类"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.dist_dir = self.project_root / "dist"
        self.build_time = datetime.now()
        self.version = "0.84"
        
    def clean_build_dirs(self):
        """清理之前的构建目录"""
        print("🧹 清理构建目录...")
        
        dirs_to_clean = [
            'main.dist',
            'main.build', 
            'main.onefile-build',
            '__pycache__',
            'build',
            'dist/main.dist',
            'dist/main.build',
            'nuitka-crash-report.xml'
        ]
        
        for dir_name in dirs_to_clean:
            path = self.project_root / dir_name
            if path.exists():
                try:
                    if path.is_file():
                        path.unlink()
                    else:
                        shutil.rmtree(path)
                    print(f"  ✅ 已删除: {dir_name}")
                except Exception as e:
                    print(f"  ⚠️ 删除失败 {dir_name}: {e}")
    
    def check_dependencies(self):
        """检查必要的依赖"""
        print("🔍 检查依赖...")
        
        required_files = [
            "main.py",
            "requirements.txt",
            "config/app_config.json",
            "resources/icons/app_icon.ico"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ 缺少必要文件:")
            for file in missing_files:
                print(f"  - {file}")
            return False
        
        print("  ✅ 所有必要文件都存在")
        return True
    
    def build_with_nuitka(self):
        """使用Nuitka构建可执行文件"""
        print("🚀 开始使用Nuitka构建优化版...")
        
        # 构建命令 - 2025年最新优化版本
        cmd = [
            sys.executable, "-m", "nuitka",
            "--standalone",  # 独立模式
            "--enable-plugin=pyqt5",  # 启用PyQt5插件
            
            # 数据目录包含
            "--include-data-dir=config=config",
            "--include-data-dir=resources=resources", 
            "--include-data-dir=templates=templates",
            "--include-data-dir=data=data",
            
            # 核心包包含
            "--include-package=PyQt5",
            "--include-package=PyQt5.QtCore",
            "--include-package=PyQt5.QtGui", 
            "--include-package=PyQt5.QtWidgets",
            "--include-package=PyQt5.QtPrintSupport",
            
            # 通信和硬件
            "--include-package=serial",
            "--include-package=pywin32",
            "--include-package=win32print",
            "--include-package=win32api",
            
            # 科学计算
            "--include-package=numpy",
            "--include-package=scipy",
            "--include-package=pandas",
            "--include-package=matplotlib",
            
            # 文件处理
            "--include-package=openpyxl",
            "--include-package=xlsxwriter", 
            "--include-package=PIL",
            
            # 数据库和加密
            "--include-package=sqlalchemy",
            "--include-package=cryptography",
            "--include-package=qrcode",
            
            # 系统工具
            "--include-package=psutil",
            "--include-package=configparser",
            
            # Windows设置
            "--windows-console-mode=disable",
            "--windows-icon-from-ico=resources/icons/app_icon.ico",
            
            # 输出设置
            "--output-dir=dist",
            "--output-filename=JCY5001A_v0.84_Optimized.exe",
            
            # 版本信息
            "--company-name=JingCeYun",
            "--product-name=JCY5001A Battery Impedance Tester",
            "--file-version=*******",
            "--product-version=*******", 
            "--file-description=JCY5001A Battery Impedance Testing System Optimized",
            "--copyright=Copyright (C) 2025 JingCeYun",
            
            # 构建优化
            "--assume-yes-for-downloads",
            "--show-progress",
            "--show-memory",
            "--jobs=4",  # 并行作业数
            "--low-memory",  # 低内存模式
            "--remove-output",  # 移除之前的输出
            
            # 性能优化
            "--lto=yes",  # 链接时优化
            "--enable-plugin=anti-bloat",  # 反膨胀插件
            
            "main.py"  # 主文件
        ]
        
        print("📋 构建命令:")
        print(" ".join(cmd))
        print()
        
        start_time = time.time()
        
        try:
            # 执行构建
            result = subprocess.run(cmd, check=True, capture_output=False)
            build_time = time.time() - start_time
            print(f"✅ Nuitka构建完成! (耗时: {build_time:.1f}秒)")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Nuitka构建失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
    
    def copy_additional_files(self):
        """复制额外的文件到输出目录"""
        print("📁 复制额外文件...")
        
        dist_dir = self.project_root / "dist" / "main.dist"
        if not dist_dir.exists():
            print(f"❌ 输出目录不存在: {dist_dir}")
            return False
        
        # 需要复制的文件
        files_to_copy = [
            ("README.md", "README.md"),
            ("requirements.txt", "requirements.txt"),
            ("打包说明.md", "打包说明.md") if (self.project_root / "打包说明.md").exists() else None
        ]
        
        # 过滤掉不存在的文件
        files_to_copy = [f for f in files_to_copy if f is not None]
        
        for src, dst in files_to_copy:
            src_path = self.project_root / src
            if src_path.exists():
                dst_path = dist_dir / dst
                try:
                    if src_path.is_dir():
                        shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
                    else:
                        shutil.copy2(src_path, dst_path)
                    print(f"  ✅ 已复制: {src} -> {dst}")
                except Exception as e:
                    print(f"  ⚠️ 复制失败 {src}: {e}")
        
        return True
    
    def create_installer_info(self):
        """创建安装信息文件"""
        print("📝 创建安装信息...")
        
        dist_dir = self.project_root / "dist" / "main.dist"
        if not dist_dir.exists():
            return False
        
        info_content = f"""JCY5001A鲸测云8路EIS阻抗筛选仪 v{self.version} (优化版)
构建时间: {self.build_time.strftime('%Y-%m-%d %H:%M:%S')}
构建工具: Nuitka (优化版)

优化内容:
- 基于最新依赖重新构建
- 修复所有已知的启动和运行问题
- 优化性能和内存使用
- 完整包含所有必要依赖
- 支持最新的Windows系统

安装说明:
1. 解压所有文件到目标目录
2. 运行 JCY5001A_v{self.version}_Optimized.exe 启动程序
3. 首次运行会创建配置文件

系统要求:
- Windows 10/11 (64位)
- 至少4GB内存
- 至少1GB磁盘空间

技术支持:
- 公司: 鲸测云
- 版本: v{self.version} (优化版)
- 构建日期: {self.build_time.strftime('%Y-%m-%d')}

特性说明:
- 支持8路并行EIS阻抗测试
- 实时数据分析和结果显示
- 完整的打印和标签功能
- 数据库存储和历史记录
- 串口通信和设备控制
"""
        
        try:
            with open(dist_dir / "安装说明.txt", "w", encoding="utf-8") as f:
                f.write(info_content)
            print("  ✅ 安装说明已创建")
            return True
        except Exception as e:
            print(f"  ❌ 创建安装说明失败: {e}")
            return False
    
    def create_startup_script(self):
        """创建启动脚本"""
        print("📝 创建启动脚本...")
        
        dist_dir = self.project_root / "dist" / "main.dist"
        if not dist_dir.exists():
            return False
        
        bat_content = f"""@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   JCY5001A鲸测云8路EIS阻抗筛选仪
echo   版本: v{self.version} (优化版)
echo   构建时间: {self.build_time.strftime('%Y-%m-%d %H:%M:%S')}
echo ========================================
echo.
echo 正在启动程序...
echo.

REM 检查文件是否存在
if not exist "JCY5001A_v{self.version}_Optimized.exe" (
    echo ❌ 错误: 找不到可执行文件
    echo 请确保所有文件都已正确解压
    pause
    exit /b 1
)

REM 启动程序
start "" "JCY5001A_v{self.version}_Optimized.exe"

echo ✅ 程序已启动
echo.
echo 如果程序无法正常启动，请检查：
echo 1. 是否有杀毒软件阻止运行
echo 2. 是否有足够的系统权限
echo 3. 是否安装了必要的运行库
echo.
timeout /t 3 /nobreak >nul
"""
        
        try:
            with open(dist_dir / "启动程序.bat", "w", encoding="gbk") as f:
                f.write(bat_content)
            print("  ✅ 启动脚本已创建")
            return True
        except Exception as e:
            print(f"  ❌ 创建启动脚本失败: {e}")
            return False

    def create_package_archive(self):
        """创建打包压缩文件"""
        print("📦 创建打包压缩文件...")

        dist_dir = self.project_root / "dist" / "main.dist"
        if not dist_dir.exists():
            return False

        # 创建压缩文件名
        timestamp = self.build_time.strftime('%Y%m%d_%H%M%S')
        archive_name = f"JCY5001A_v{self.version}_Optimized_{timestamp}"
        archive_path = self.dist_dir / archive_name

        try:
            # 创建ZIP压缩文件
            shutil.make_archive(str(archive_path), 'zip', str(dist_dir))
            print(f"  ✅ 压缩文件已创建: {archive_name}.zip")
            return True
        except Exception as e:
            print(f"  ❌ 创建压缩文件失败: {e}")
            return False

    def run_build(self):
        """执行完整的构建流程"""
        print("🎯 JCY5001A v0.84 Nuitka优化版打包工具")
        print("=" * 60)
        print(f"构建时间: {self.build_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 步骤1: 检查依赖
        if not self.check_dependencies():
            print("❌ 依赖检查失败，停止打包")
            return False
        print()

        # 步骤2: 清理构建目录
        self.clean_build_dirs()
        print()

        # 步骤3: 使用Nuitka构建
        if not self.build_with_nuitka():
            print("❌ 构建失败，停止打包")
            return False
        print()

        # 步骤4: 复制额外文件
        if not self.copy_additional_files():
            print("⚠️ 复制额外文件失败，但构建已完成")
        print()

        # 步骤5: 创建安装信息
        if not self.create_installer_info():
            print("⚠️ 创建安装信息失败，但构建已完成")
        print()

        # 步骤6: 创建启动脚本
        if not self.create_startup_script():
            print("⚠️ 创建启动脚本失败，但构建已完成")
        print()

        # 步骤7: 创建压缩包
        if not self.create_package_archive():
            print("⚠️ 创建压缩包失败，但构建已完成")
        print()

        # 完成
        print("🎉 优化版打包完成!")
        print(f"📦 输出目录: dist/main.dist/")
        print(f"🚀 可执行文件: dist/main.dist/JCY5001A_v{self.version}_Optimized.exe")
        print(f"📋 启动脚本: dist/main.dist/启动程序.bat")
        print(f"📄 安装说明: dist/main.dist/安装说明.txt")
        print()
        print("💡 优化说明:")
        print("  - 基于最新依赖和最佳实践构建")
        print("  - 包含所有必要的Python包和DLL")
        print("  - 优化启动性能和运行稳定性")
        print("  - 支持完整的PyQt5功能和串口通信")
        print("  - 包含完整的科学计算和数据处理能力")
        print()
        print("🚀 部署建议:")
        print("  - 将整个 dist/main.dist/ 目录复制到目标机器")
        print("  - 使用启动脚本进行首次测试")
        print("  - 确保目标机器有足够的权限和资源")

        return True

def main():
    """主函数"""
    try:
        builder = NuitkaBuilder()
        success = builder.run_build()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        return 1
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
