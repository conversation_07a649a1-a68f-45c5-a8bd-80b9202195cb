#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Nuitka打包JCY5001A V0.80.10软件
生成独立的可执行文件（非单文件模式）
JCY5001A鲸测云8路EIS阻抗筛选仪专用打包脚本

基于项目移交文档的真实信息：
- 项目名称: JCY5001A鲸测云8路EIS阻抗筛选仪
- 开发者: <PERSON> (张海)
- 公司: 鲸测云 (JingCeYun)
- 版本: V0.80.10
- 项目类型: 电池交流阻抗测试系统上位机软件
"""

import os
import sys
import subprocess
import shutil
import zipfile
from datetime import datetime

def clean_build_dirs():
    """清理之前的构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [
        'main.dist',
        'main.build', 
        'main.onefile-build',
        '__pycache__',
        'build',
        'dist'
    ]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"  ✅ 已删除: {dir_name}")
            except Exception as e:
                print(f"  ⚠️ 删除失败 {dir_name}: {e}")

def check_dependencies():
    """检查必要的依赖"""
    print("🔍 检查依赖...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "config/app_config.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    # 检查图标文件
    icon_path = "resources/icons/app_icon.ico"
    if not os.path.exists(icon_path):
        print(f"⚠️ 图标文件不存在: {icon_path}")
        print("  将使用默认图标")
    
    print("✅ 依赖检查完成")
    return True

def build_with_nuitka():
    """使用Nuitka构建可执行文件"""
    print("🚀 开始使用Nuitka构建 JCY5001A鲸测云8路EIS阻抗筛选仪 V0.80.10...")

    # 构建命令 - 独立模式，不打成单文件
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",  # 独立模式（重要：不是onefile模式）
        "--enable-plugin=pyqt5",  # 启用PyQt5插件

        # 包含数据目录
        "--include-data-dir=config=config",  # 配置目录
        "--include-data-dir=resources=resources",  # 资源目录
        "--include-data-dir=templates=templates",  # 模板目录
        "--include-data-dir=data=data",  # 数据目录

        # 包含必要的Python包
        "--include-package=PyQt5",  # PyQt5 GUI框架
        "--include-package=serial",  # 串口通信
        "--include-package=numpy",  # 科学计算
        "--include-package=scipy",  # 科学计算
        "--include-package=pandas",  # 数据处理
        "--include-package=matplotlib",  # 图表绘制
        "--include-package=openpyxl",  # Excel处理
        "--include-package=xlsxwriter",  # Excel写入
        "--include-package=PIL",  # 图像处理（Pillow）
        "--include-package=sqlalchemy",  # 数据库ORM
        "--include-package=psutil",  # 系统工具
        "--include-package=configparser",  # 配置文件处理
        "--include-package=sqlite3",  # SQLite数据库
        "--include-package=json",  # JSON处理
        "--include-package=logging",  # 日志系统
        "--include-package=datetime",  # 日期时间处理
        "--include-package=threading",  # 多线程支持
        "--include-package=queue",  # 队列支持

        # Nuitka特定的模块包含（替代hidden-import）
        "--include-module=PyQt5.QtCore",
        "--include-module=PyQt5.QtGui",
        "--include-module=PyQt5.QtWidgets",
        "--include-module=PyQt5.QtPrintSupport",
        "--include-module=numpy.core._methods",
        "--include-module=numpy.lib.format",
        "--include-module=matplotlib.backends.backend_qt5agg",
        "--include-module=sqlalchemy.dialects.sqlite",
        "--include-module=serial.tools.list_ports",

        # Windows特定设置
        "--windows-console-mode=disable",  # 禁用控制台窗口
        "--windows-icon-from-ico=resources/icons/app_icon.ico" if os.path.exists("resources/icons/app_icon.ico") else "",  # 应用图标

        # 输出设置
        "--output-dir=dist",  # 输出目录
        "--output-filename=JCY5001A_V0.80.10.exe",  # 输出文件名（使用英文避免编码问题）

        # 版本信息（基于项目移交文档的真实信息）
        "--company-name=JingCeYun",  # 公司名称：鲸测云
        "--product-name=JCY5001A EIS Battery Impedance Tester",  # 产品名称
        "--file-version=*********",  # 文件版本
        "--product-version=*********",  # 产品版本
        "--file-description=JCY5001A 8-Channel EIS Battery Impedance Testing System",  # 文件描述
        "--copyright=Copyright (C) 2025 JingCeYun. Developer: Jack Chen",  # 版权信息（基于真实开发者信息）

        # 构建优化
        "--assume-yes-for-downloads",  # 自动确认下载
        "--show-progress",  # 显示进度
        "--show-memory",  # 显示内存使用
        "--jobs=4",  # 使用4个并行作业
        "--low-memory",  # 低内存模式

        # 主文件
        "main.py"
    ]
    
    # 过滤空字符串参数
    cmd = [arg for arg in cmd if arg]
    
    print("📋 构建命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行构建
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("✅ Nuitka构建完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka构建失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到输出目录"""
    print("📁 复制额外文件...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        print(f"❌ 输出目录不存在: {dist_dir}")
        return False
    
    # 需要复制的文件和目录
    files_to_copy = [
        ("README.md", "README.md"),
        ("requirements.txt", "requirements.txt"),
        ("LICENSE", "LICENSE") if os.path.exists("LICENSE") else None,
    ]
    
    # 过滤None值
    files_to_copy = [item for item in files_to_copy if item is not None]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(dist_dir, dst)
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
            try:
                if os.path.isdir(src):
                    shutil.copytree(src, dst_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(src, dst_path)
                print(f"  ✅ 已复制: {src} -> {dst}")
            except Exception as e:
                print(f"  ⚠️ 复制失败 {src}: {e}")
    
    return True

def create_installer_info():
    """创建安装信息文件"""
    print("📝 创建安装信息...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        return False
    
    info_content = f"""JCY5001A鲸测云8路EIS阻抗筛选仪 V0.80.10
电池交流阻抗测试系统上位机软件

构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
构建工具: Nuitka (独立模式)
版权所有: 鲸测云 (JingCeYun)
开发者: Jack Chen (张海)
联系方式: <EMAIL>

产品信息:
- 产品型号: JCY5001A
- 产品名称: 鲸测云8路EIS阻抗筛选仪
- 应用场景: 锂电池和磷酸铁锂电池阻抗特性测试
- 测试通道: 8通道并行测试
- 测试模式: 研究模式(0.01Hz-7.8kHz)和生产模式

安装说明:
1. 解压所有文件到目标目录
2. 运行 JCY5001A_V0.80.10.exe 启动程序
3. 首次运行会自动创建配置文件
4. 确保设备正确连接到指定COM端口

系统要求:
- Windows 10/11 (64位)
- 至少4GB内存
- 至少2GB磁盘空间
- 支持的串口设备

硬件连接:
- 阻抗测试设备：COM端口，115200波特率，Modbus RTU通信
- 扫码枪：USB HID设备，自动识别
- 标签打印机：NIIMBOT系列，USB连接

技术特点:
- 8通道并行测试，错频启动防干扰
- EIS阻抗分析：Rs、Rct、Rsei、W阻抗参数计算
- 实时数据监控和可视化
- SQLite数据库存储，Excel报表导出
- Modbus RTU工业通信协议

技术支持:
- 公司: 鲸测云 (JingCeYun)
- 版本: V0.80.10
- 构建日期: {datetime.now().strftime('%Y-%m-%d')}
- 开发者: Jack Chen (张海)
- 邮箱: <EMAIL>
- 域名: jingceyun.com

更新内容:
- 版本号更新至V0.80.10
- 优化8通道并行测试稳定性
- 完善EIS阻抗分析算法
- 增强数据管理和导出功能
- 修复设备通信相关问题
- 优化用户界面和操作体验

注意事项:
- 本软件为独立模式打包，包含所有必需的运行库
- 整个目录都是必需的，请勿删除任何文件
- 如遇技术问题请联系开发者或技术支持
"""
    
    try:
        with open(os.path.join(dist_dir, "安装说明.txt"), "w", encoding="utf-8") as f:
            f.write(info_content)
        print("  ✅ 安装说明已创建")
        return True
    except Exception as e:
        print(f"  ❌ 创建安装说明失败: {e}")
        return False

def create_distribution_package():
    """创建分发包"""
    print("📦 创建分发包...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        print("❌ 分发目录不存在")
        return False
    
    # 创建带时间戳的分发包名称
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    package_name = f"JCY5001A_V0.80.10_{timestamp}"
    
    # 重命名分发目录
    final_dist_dir = f"dist/{package_name}"
    try:
        if os.path.exists(final_dist_dir):
            shutil.rmtree(final_dist_dir)
        shutil.move(dist_dir, final_dist_dir)
        print(f"  ✅ 分发目录已重命名: {final_dist_dir}")
    except Exception as e:
        print(f"  ⚠️ 重命名分发目录失败: {e}")
        final_dist_dir = dist_dir
    
    # 创建ZIP压缩包
    zip_path = f"dist/{package_name}.zip"
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(final_dist_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, final_dist_dir)
                    zipf.write(file_path, arc_path)
        print(f"  ✅ 压缩包已创建: {zip_path}")
        return True
    except Exception as e:
        print(f"  ❌ 创建压缩包失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 JCY5001A鲸测云8路EIS阻抗筛选仪 V0.80.10 Nuitka打包工具")
    print("=" * 70)
    print("📋 项目信息:")
    print("  - 产品名称: JCY5001A鲸测云8路EIS阻抗筛选仪")
    print("  - 产品型号: JCY5001A")
    print("  - 版本号: V0.80.10")
    print("  - 公司: 鲸测云 (JingCeYun)")
    print("  - 开发者: Jack Chen (张海)")
    print("  - 项目类型: 电池交流阻抗测试系统上位机软件")
    print("  - 打包模式: 独立模式（非单文件）")
    print("=" * 70)

    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False

    # 步骤1: 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，停止打包")
        return False
    print()

    # 步骤2: 清理构建目录
    clean_build_dirs()
    print()

    # 步骤3: 使用Nuitka构建
    if not build_with_nuitka():
        print("❌ 构建失败，停止打包")
        return False
    print()

    # 步骤4: 复制额外文件
    if not copy_additional_files():
        print("⚠️ 复制额外文件失败，但构建已完成")
    print()

    # 步骤5: 创建安装信息
    if not create_installer_info():
        print("⚠️ 创建安装信息失败，但构建已完成")
    print()

    # 步骤6: 创建分发包
    if not create_distribution_package():
        print("⚠️ 创建分发包失败，但构建已完成")
    print()

    # 完成
    print("🎉 打包完成!")
    print("=" * 60)
    print("📦 输出信息:")

    # 查找最终的分发目录
    dist_base = "dist"
    if os.path.exists(dist_base):
        dist_items = [item for item in os.listdir(dist_base)
                     if item.startswith("JCY5001A_V0.80.10")]

        for item in dist_items:
            item_path = os.path.join(dist_base, item)
            if os.path.isdir(item_path):
                print(f"  📁 程序目录: {item_path}/")
                exe_path = os.path.join(item_path, "JCY5001A_V0.80.10.exe")
                if os.path.exists(exe_path):
                    print(f"  🚀 可执行文件: {exe_path}")
            elif item.endswith('.zip'):
                print(f"  📦 分发包: {item_path}")

    print()
    print("💡 使用说明:")
    print("  1. 独立模式打包 - 整个目录包含所有必需文件")
    print("  2. 可以将整个目录复制到目标机器上运行")
    print("  3. 首次运行会自动创建配置文件")
    print("  4. 确保目标机器满足系统要求")
    print("  5. 版本号V0.80.10将显示在主界面")
    print()
    print("🔧 技术特点:")
    print("  - 8通道并行EIS阻抗测试")
    print("  - 支持锂电池和磷酸铁锂电池")
    print("  - Modbus RTU工业通信协议")
    print("  - Rs、Rct、Rsei、W阻抗参数分析")
    print("  - 实时数据监控和可视化")
    print("  - SQLite数据库存储")
    print("  - Excel报表导出功能")

    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 打包成功完成!")
            print("🎯 JCY5001A鲸测云8路EIS阻抗筛选仪 V0.80.10 已准备就绪")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
