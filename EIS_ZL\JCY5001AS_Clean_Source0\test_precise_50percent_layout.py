#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精确的50%宽度缩短布局修改
验证所有数值框宽度缩短50%，电池扫码框大幅扩展的效果
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QLineEdit, QGroupBox, QFrame, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.components.channel_display_widget import ChannelDisplayWidget
    from utils.config_manager import ConfigManager
    REAL_COMPONENT_AVAILABLE = True
except ImportError as e:
    print(f"导入真实组件失败: {e}")
    REAL_COMPONENT_AVAILABLE = False

class Precise50PercentLayoutTest(QMainWindow):
    """精确50%宽度缩短布局测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 精确50%宽度缩短测试")
        self.setGeometry(100, 100, 1600, 700)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 精确50%宽度缩短测试")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 添加详细说明
        info_label = QLabel("""
        🎯 精确修改内容（所有数值框宽度缩短50%）：
        
        📊 左侧组件宽度变化：
        • 测试计数标签: 30px → 15px (-50%)    • 测试计数数值: 50px → 25px (-50%)
        • 测试时间标签: 30px → 15px (-50%)    • 测试时间数值: 60px → 30px (-50%)
        • 电池码标签: 30px → 15px (-50%)      • 电压标签: 30px → 15px (-50%)
        • 电压数值: 40px → 20px (-50%)
        
        ⚡ RS/RCT区域变化（向中心靠拢）：
        • RS/RCT标签: 60px → 30px (-50%)      • RS/RCT数值: 70px → 35px (-50%)
        • 仍能完整显示5位数字的阻抗值
        
        📱 电池扫码框大幅扩展：
        • 最小宽度: 200px → 300px (+50%)      • 最大宽度: 400px → 600px (+50%)
        • 支持显示20-30字符以上的长条码
        
        🔧 使用!important强制CSS覆盖，确保效果立即可见
        """)
        info_label.setFont(QFont("Microsoft YaHei", 9))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #e8f5e8; 
            padding: 15px; 
            border-radius: 8px; 
            border: 2px solid #27ae60;
            line-height: 1.4;
        """)
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        self._create_test_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
    
    def _create_test_area(self, layout):
        """创建测试区域"""
        test_frame = QFrame()
        test_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        test_layout = QVBoxLayout(test_frame)
        
        if REAL_COMPONENT_AVAILABLE:
            # 使用真实组件
            try:
                config_manager = ConfigManager()
                
                # 创建通道组件
                channels_layout = QHBoxLayout()
                
                # 创建两个通道进行对比测试
                for i in range(2):
                    channel_number = i + 1
                    channel = ChannelDisplayWidget(channel_number, config_manager)
                    
                    # 设置测试数据
                    self._set_test_data(channel, channel_number)
                    
                    channels_layout.addWidget(channel)
                
                test_layout.addLayout(channels_layout)
                
            except Exception as e:
                print(f"创建真实通道组件失败: {e}")
                # 回退到演示版本
                demo_layout = self._create_demo_channels()
                test_layout.addLayout(demo_layout)
        else:
            # 使用演示版本
            demo_layout = self._create_demo_channels()
            test_layout.addLayout(demo_layout)
        
        layout.addWidget(test_frame)
    
    def _set_test_data(self, channel, channel_number):
        """设置测试数据"""
        try:
            # 设置长电池码进行测试
            long_battery_codes = [
                "BATTERY_LONG_CODE_123456789ABCDEFGHIJKLMNOP_TEST_001",  # 48字符
                "CELL_ULTRA_LONG_BARCODE_987654321ZYXWVUTSRQPONMLKJI_002"  # 52字符
            ]
            
            battery_code = long_battery_codes[channel_number - 1]
            if hasattr(channel, 'battery_code_edit'):
                channel.battery_code_edit.setText(battery_code)
            
            # 设置5位数阻抗值测试
            rs_values = ["12345", "98765"]  # 5位数测试
            rct_values = ["23456", "87654"]  # 5位数测试
            
            if hasattr(channel, 'rs_label'):
                channel.rs_label.setText(rs_values[channel_number - 1])
            if hasattr(channel, 'rct_label'):
                channel.rct_label.setText(rct_values[channel_number - 1])
            
            # 设置其他测试数据
            if hasattr(channel, 'test_count_label'):
                channel.test_count_label.setText(str(channel_number * 123))  # 3位数测试
            if hasattr(channel, 'test_time_label'):
                channel.test_time_label.setText(f"02:3{channel_number}:15")
            if hasattr(channel, 'voltage_label'):
                channel.voltage_label.setText(f"{3.456 + channel_number * 0.123:.3f}")  # 5位数测试
                
        except Exception as e:
            print(f"设置测试数据失败: {e}")
    
    def _create_demo_channels(self):
        """创建演示通道（当无法加载真实组件时）"""
        demo_layout = QHBoxLayout()
        
        for i in range(2):
            channel_number = i + 1
            demo_channel = self._create_demo_channel(channel_number)
            demo_layout.addWidget(demo_channel)
        
        return demo_layout
    
    def _create_demo_channel(self, channel_number):
        """创建演示通道"""
        # 创建通道组框
        channel_group = QGroupBox(f"通道 {channel_number} - 50%宽度缩短测试")
        channel_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 12px 0 12px;
                color: #2c3e50;
                font-size: 14pt;
                font-weight: 600;
                background-color: #ffffff;
            }
        """)
        
        # 创建内容布局
        content_layout = QVBoxLayout(channel_group)
        content_layout.setContentsMargins(8, 10, 8, 8)
        content_layout.setSpacing(5)
        
        # 创建主内容区域
        main_container = QFrame()
        main_layout = QHBoxLayout(main_container)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 左列：基本信息（50%宽度缩短）
        left_column = self._create_demo_left_column(channel_number)
        main_layout.addLayout(left_column, 10)  # 1份权重
        
        # 右列：阻抗值显示（50%宽度缩短，向中心靠拢）
        right_column = self._create_demo_right_column(channel_number)
        main_layout.addLayout(right_column, 40)  # 4份权重
        
        content_layout.addWidget(main_container)
        
        return channel_group
    
    def _create_demo_left_column(self, channel_number):
        """创建演示左列（50%宽度缩短版本）"""
        left_column = QVBoxLayout()
        left_column.setSpacing(6)
        
        # 测试计数和时间（50%宽度缩短）
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(3)
        
        # 计数 - 50%宽度缩短
        count_label = QLabel("计数:")
        count_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        count_label.setMinimumWidth(15)  # 50%缩短: 30px → 15px
        count_label.setMaximumWidth(20)  # 50%缩短: 40px → 20px
        count_time_layout.addWidget(count_label)
        
        count_value = QLabel(str(channel_number * 123))
        count_value.setStyleSheet("font-size: 10pt; color: #27ae60; font-weight: bold;")
        count_value.setMinimumWidth(15)  # 50%缩短: 30px → 15px
        count_value.setMaximumWidth(25)  # 50%缩短: 50px → 25px
        count_time_layout.addWidget(count_value)
        
        # 分隔符
        separator = QLabel("|")
        separator.setStyleSheet("font-size: 8pt; color: #bdc3c7;")
        separator.setMaximumWidth(8)
        count_time_layout.addWidget(separator)
        
        # 时间 - 50%宽度缩短
        time_label = QLabel("用时:")
        time_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        time_label.setMinimumWidth(15)  # 50%缩短: 30px → 15px
        time_label.setMaximumWidth(20)  # 50%缩短: 40px → 20px
        count_time_layout.addWidget(time_label)
        
        time_value = QLabel(f"02:3{channel_number}:15")
        time_value.setStyleSheet("""
            font-size: 9pt; font-weight: bold; color: #3498db;
            background-color: #ebf3fd; border: 1px solid #3498db;
            border-radius: 2px; padding: 1px 2px;
            max-height: 14px;
        """)
        time_value.setMinimumWidth(30)  # 50%缩短: 60px → 30px
        time_value.setMaximumWidth(40)  # 50%缩短: 80px → 40px
        count_time_layout.addWidget(time_value)
        
        count_time_layout.addStretch()
        left_column.addLayout(count_time_layout)
        
        # 电池码输入（大幅扩展）
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(3)
        
        battery_label = QLabel("电池:")
        battery_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        battery_label.setMinimumWidth(15)  # 50%缩短: 30px → 15px
        battery_label.setMaximumWidth(20)  # 50%缩短: 40px → 20px
        battery_layout.addWidget(battery_label)
        
        # 长电池码测试
        long_codes = [
            "BATTERY_LONG_CODE_123456789ABCDEFGHIJKLMNOP_TEST_001",
            "CELL_ULTRA_LONG_BARCODE_987654321ZYXWVUTSRQPONMLKJI_002"
        ]
        
        battery_edit = QLineEdit(long_codes[channel_number - 1])
        battery_edit.setStyleSheet("""
            border: 1px solid #bdc3c7; border-radius: 3px;
            padding: 2px 10px; background-color: white;
            font-size: 11pt; max-height: 28px; color: #2c3e50;
            min-height: 26px;
        """)
        battery_edit.setMinimumWidth(300)  # 大幅扩展: 200px → 300px
        battery_edit.setMaximumWidth(600)  # 大幅扩展: 400px → 600px
        battery_layout.addWidget(battery_edit, 5)  # 给更大权重
        
        left_column.addLayout(battery_layout)
        
        # 电压显示（50%宽度缩短）
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(3)
        
        voltage_label = QLabel("电压:")
        voltage_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        voltage_label.setMinimumWidth(15)  # 50%缩短: 30px → 15px
        voltage_label.setMaximumWidth(20)  # 50%缩短: 40px → 20px
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel(f"{3.456 + channel_number * 0.123:.3f}")
        voltage_value.setStyleSheet("""
            font-size: 10pt; font-weight: 600; color: #1f2937;
            background-color: #f9fafb; border: 1px solid #e5e7eb;
            border-radius: 4px; padding: 2px 3px;
        """)
        voltage_value.setMinimumWidth(20)  # 50%缩短: 40px → 20px
        voltage_value.setMaximumWidth(30)  # 50%缩短: 60px → 30px
        voltage_layout.addWidget(voltage_value)
        
        voltage_layout.addStretch()
        left_column.addLayout(voltage_layout)
        
        left_column.addStretch()
        return left_column
    
    def _create_demo_right_column(self, channel_number):
        """创建演示右列（50%宽度缩短，向中心靠拢）"""
        right_column = QVBoxLayout()
        right_column.setSpacing(8)
        
        # RS显示（50%宽度缩短，向中心靠拢）
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(6)
        rs_layout.addStretch(3)  # 左侧弹性空间，向中心靠拢
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        rs_title.setMinimumWidth(30)  # 50%缩短: 60px → 30px
        rs_title.setMaximumWidth(35)  # 50%缩短: 70px → 35px
        rs_title.setAlignment(Qt.AlignRight)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("12345" if channel_number == 1 else "98765")  # 5位数测试
        rs_value.setStyleSheet("""
            font-size: 11pt; font-weight: 600; color: #065f46;
            background-color: #ecfdf5; border: 1px solid #10b981;
            border-radius: 4px; padding: 3px 4px; margin: 0px;
        """)
        rs_value.setMinimumWidth(35)  # 50%缩短: 70px → 35px，仍能显示5位数
        rs_value.setMaximumWidth(40)  # 50%缩短: 80px → 40px
        rs_value.setAlignment(Qt.AlignLeft)
        rs_layout.addWidget(rs_value)
        
        right_column.addLayout(rs_layout)
        
        # RCT显示（50%宽度缩短，向中心靠拢）
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(6)
        rct_layout.addStretch(3)  # 左侧弹性空间，向中心靠拢
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        rct_title.setMinimumWidth(30)  # 50%缩短: 60px → 30px
        rct_title.setMaximumWidth(35)  # 50%缩短: 70px → 35px
        rct_title.setAlignment(Qt.AlignRight)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("23456" if channel_number == 1 else "87654")  # 5位数测试
        rct_value.setStyleSheet("""
            font-size: 11pt; font-weight: 600; color: #1e40af;
            background-color: #eff6ff; border: 1px solid #3b82f6;
            border-radius: 4px; padding: 3px 4px; margin: 0px;
        """)
        rct_value.setMinimumWidth(35)  # 50%缩短: 70px → 35px，仍能显示5位数
        rct_value.setMaximumWidth(40)  # 50%缩短: 80px → 40px
        rct_value.setAlignment(Qt.AlignLeft)
        rct_layout.addWidget(rct_value)
        
        right_column.addLayout(rct_layout)
        
        right_column.addStretch()
        return right_column
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 测试长电池码按钮
        test_long_code_btn = QPushButton("验证长电池码显示效果")
        test_long_code_btn.clicked.connect(self._test_long_battery_code)
        test_long_code_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(test_long_code_btn)
        
        # 测试5位数阻抗值按钮
        test_5digit_btn = QPushButton("验证5位数阻抗值显示")
        test_5digit_btn.clicked.connect(self._test_5digit_impedance)
        test_5digit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(test_5digit_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _test_long_battery_code(self):
        """测试长电池码显示"""
        print("✅ 长电池码显示验证:")
        print("   • 48-52字符的电池码应该能完整显示")
        print("   • 电池码输入框应该明显扩展（300-600px）")
        print("   • 左侧所有组件宽度应该缩短50%")
    
    def _test_5digit_impedance(self):
        """测试5位数阻抗值显示"""
        print("✅ 5位数阻抗值显示验证:")
        print("   • RS和RCT应该能完整显示5位数字（12345, 98765等）")
        print("   • 数值框宽度应该缩短50%（35-40px）")
        print("   • RS/RCT区域应该向通道中心靠拢")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = Precise50PercentLayoutTest()
    window.show()
    
    print("🚀 JCY5001AS 精确50%宽度缩短测试启动")
    print("=" * 60)
    print("📋 测试验证内容:")
    print("   ✓ 所有数值框宽度精确缩短50%")
    print("   ✓ RS/RCT区域向通道中心靠拢")
    print("   ✓ 电池扫码框大幅扩展（300-600px）")
    print("   ✓ 使用!important强制CSS覆盖")
    print("   ✓ 5位数阻抗值完整显示测试")
    print("   ✓ 长条码（48-52字符）显示测试")
    print("=" * 60)
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
