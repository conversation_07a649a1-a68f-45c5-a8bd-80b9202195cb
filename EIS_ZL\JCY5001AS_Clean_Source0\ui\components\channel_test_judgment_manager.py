# -*- coding: utf-8 -*-
"""
通道测试结果判定管理器
负责测试结果的判定逻辑和档位计算

Author: Jack
Date: 2025-01-30
"""

import logging
from typing import Tuple, List, Optional
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class ChannelTestJudgmentManager:
    """通道测试结果判定管理器"""
    
    def __init__(self, channel_number: int, config_manager: ConfigManager):
        """
        初始化测试结果判定管理器
        
        Args:
            channel_number: 通道号
            config_manager: 配置管理器
        """
        self.channel_number = channel_number
        self.config_manager = config_manager
        
    def judge_test_result(self, voltage: float, rs_value: float, rct_value: float) -> Tuple[bool, List[str]]:
        """
        判定测试结果（兼容现有配置格式）

        Args:
            voltage: 电压值 (V)
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)

        Returns:
            <PERSON><PERSON>[bool, List[str]]: (是否合格, 失败项目列表)
        """
        try:
            fail_items = []

            # 1. 电压范围检查（兼容现有配置）
            voltage_min = self.config_manager.get('test_params.voltage_range.min', 2.889)
            voltage_max = self.config_manager.get('test_params.voltage_range.max', 3.531)

            if voltage < voltage_min or voltage > voltage_max:
                fail_items.append("电压")
                logger.debug(f"通道{self.channel_number}电压超范围: {voltage:.3f}V, 范围: {voltage_min}-{voltage_max}V")

            # 2. Rs值范围检查（兼容现有配置）
            rs_grade_count = self.config_manager.get('impedance.rs_grade_count', 3)
            if rs_grade_count == 1:
                rs_max = self.config_manager.get('impedance.rs_grade1_max', 50.0)
            elif rs_grade_count == 2:
                rs_max = self.config_manager.get('impedance.rs_grade2_max', 50.0)
            else:  # 3档
                rs_max = self.config_manager.get('impedance.rs_grade3_max', 50.0)

            rs_min = self.config_manager.get('impedance.rs_min', 0.5)

            if rs_value > rs_max or rs_value < rs_min:
                fail_items.append("Rs")
                logger.debug(f"通道{self.channel_number}Rs超范围: {rs_value:.3f}mΩ, 范围: {rs_min}-{rs_max}mΩ")

            # 3. Rct值范围检查（兼容现有配置）
            rct_max = self.config_manager.get('impedance.rct_grade3_max', 100.0)
            rct_min = self.config_manager.get('impedance.rct_min', 0.5)

            if rct_value > rct_max or rct_value < rct_min:
                fail_items.append("Rct")
                logger.debug(f"通道{self.channel_number}Rct超范围: {rct_value:.3f}mΩ, 范围: {rct_min}-{rct_max}mΩ")

            # 判定结果
            is_pass = len(fail_items) == 0

            logger.debug(f"通道{self.channel_number}测试判定: {'合格' if is_pass else '不合格'}, 失败项目: {fail_items}")

            return is_pass, fail_items

        except Exception as e:
            logger.error(f"通道{self.channel_number}测试结果判定失败: {e}")
            return False, ["系统错误"]
    
    def calculate_grades(self, rs_value: float, rct_value: float) -> Tuple[int, int]:
        """
        计算Rs和Rct档位（兼容现有配置格式）

        Args:
            rs_value: Rs值 (mΩ)
            rct_value: Rct值 (mΩ)

        Returns:
            Tuple[int, int]: (Rs档位, Rct档位)
        """
        try:
            # 获取Rs档位配置（兼容现有配置）
            rs_grade_count = self.config_manager.get('impedance.rs_grade_count', 3)

            # 计算Rs档位
            if rs_grade_count == 1:
                rs_grade1_max = self.config_manager.get('impedance.rs_grade1_max', 50.0)
                rs_grade = 1  # 1档模式下始终为1档
            elif rs_grade_count == 2:
                rs_grade1_max = self.config_manager.get('impedance.rs_grade1_max', 25.0)
                rs_grade2_max = self.config_manager.get('impedance.rs_grade2_max', 50.0)
                if rs_value <= rs_grade1_max:
                    rs_grade = 1
                elif rs_value <= rs_grade2_max:
                    rs_grade = 2
                else:
                    rs_grade = 2  # 超出范围仍为2档（最高档位）
            else:  # 3档
                rs_grade1_max = self.config_manager.get('impedance.rs_grade1_max', 17.0)
                rs_grade2_max = self.config_manager.get('impedance.rs_grade2_max', 33.5)
                rs_grade3_max = self.config_manager.get('impedance.rs_grade3_max', 50.0)
                if rs_value <= rs_grade1_max:
                    rs_grade = 1
                elif rs_value <= rs_grade2_max:
                    rs_grade = 2
                elif rs_value <= rs_grade3_max:
                    rs_grade = 3
                else:
                    rs_grade = 3  # 超出范围仍为3档（最高档位）

            # 计算Rct档位（固定3档）
            rct_grade1_max = self.config_manager.get('impedance.rct_grade1_max', 35.0)
            rct_grade2_max = self.config_manager.get('impedance.rct_grade2_max', 70.0)
            rct_grade3_max = self.config_manager.get('impedance.rct_grade3_max', 100.0)

            if rct_value <= rct_grade1_max:
                rct_grade = 1
            elif rct_value <= rct_grade2_max:
                rct_grade = 2
            elif rct_value <= rct_grade3_max:
                rct_grade = 3
            else:
                rct_grade = 3  # 超出范围仍为3档（最高档位）

            logger.debug(f"通道{self.channel_number}档位计算: Rs={rs_value:.3f}mΩ->档位{rs_grade}, Rct={rct_value:.3f}mΩ->档位{rct_grade}")

            return rs_grade, rct_grade

        except Exception as e:
            logger.error(f"通道{self.channel_number}档位计算失败: {e}")
            return 1, 1  # 默认档位
    
    def _calculate_single_grade(self, value: float, grade_config: List[dict]) -> int:
        """
        计算单个参数的档位
        
        Args:
            value: 参数值
            grade_config: 档位配置列表
            
        Returns:
            int: 档位
        """
        try:
            for grade_info in grade_config:
                min_val = grade_info.get('min', 0.0)
                max_val = grade_info.get('max', float('inf'))
                
                if min_val <= value < max_val:
                    return grade_info.get('grade', 1)
            
            # 如果没有匹配的档位，返回最高档位
            if grade_config:
                return max(grade_info.get('grade', 1) for grade_info in grade_config)
            
            return 1  # 默认档位
            
        except Exception as e:
            logger.error(f"计算单个档位失败: {e}")
            return 1
    
    def format_grade_display(self, rs_grade: int, rct_grade: int) -> str:
        """
        格式化档位显示文本
        
        Args:
            rs_grade: Rs档位
            rct_grade: Rct档位
            
        Returns:
            str: 格式化的档位显示文本
        """
        try:
            return f"Rs{rs_grade}/Rct{rct_grade}"
        except Exception as e:
            logger.error(f"格式化档位显示失败: {e}")
            return "Rs1/Rct1"
    
    def format_result_display(self, is_pass: bool, fail_items: Optional[List[str]] = None) -> str:
        """
        格式化测试结果显示文本
        
        Args:
            is_pass: 是否合格
            fail_items: 失败项目列表
            
        Returns:
            str: 格式化的结果显示文本
        """
        try:
            if is_pass:
                return "合格"
            else:
                if fail_items and len(fail_items) > 0:
                    # 显示主要失败原因
                    main_reason = fail_items[0]
                    if len(fail_items) > 1:
                        return f"不合格({main_reason}等)"
                    else:
                        return f"不合格({main_reason})"
                else:
                    return "不合格"
                    
        except Exception as e:
            logger.error(f"格式化结果显示失败: {e}")
            return "不合格"
    
    def get_result_style_class(self, is_pass: bool) -> str:
        """
        获取结果显示的样式类名
        
        Args:
            is_pass: 是否合格
            
        Returns:
            str: 样式类名
        """
        return "passResult" if is_pass else "failResult"
    
    def validate_test_data(self, voltage: float, rs_value: float, rct_value: float) -> bool:
        """
        验证测试数据的有效性
        
        Args:
            voltage: 电压值
            rs_value: Rs值
            rct_value: Rct值
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查数据是否为有效数值
            if not all(isinstance(val, (int, float)) for val in [voltage, rs_value, rct_value]):
                return False
            
            # 检查数据是否为正数（电压可以为0，但阻抗值应该大于0）
            if voltage < 0 or rs_value < 0 or rct_value < 0:
                return False
            
            # 检查数据是否在合理范围内
            if voltage > 10.0 or rs_value > 10000.0 or rct_value > 10000.0:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证测试数据失败: {e}")
            return False
