#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证通道布局优化修改
检查channel_display_widget.py中的关键修改是否正确应用
"""

import os
import re

def verify_layout_changes():
    """验证布局修改"""
    file_path = "ui/components/channel_display_widget.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 验证JCY5001AS通道布局优化修改...")
    print("=" * 60)
    
    # 验证项目列表
    checks = [
        {
            'name': '电池码标签压缩',
            'pattern': r'battery_label = QLabel\("电池:"\)',
            'description': '电池码标签文字简化为"电池:"'
        },
        {
            'name': '电池码输入框扩展',
            'pattern': r'setMinimumWidth\(180\).*扫码值框',
            'description': '电池码输入框最小宽度设置为180px'
        },
        {
            'name': '电池码输入框最大宽度',
            'pattern': r'setMaximumWidth\(300\).*扫码值框',
            'description': '电池码输入框最大宽度设置为300px'
        },
        {
            'name': 'RS/RCT数值框压缩',
            'pattern': r'setMinimumWidth\(70\).*5位数字',
            'description': 'RS/RCT数值框最小宽度压缩为70px'
        },
        {
            'name': 'RS/RCT数值框最大宽度限制',
            'pattern': r'setMaximumWidth\(85\).*扫码值框腾出空间',
            'description': 'RS/RCT数值框最大宽度限制为85px'
        },
        {
            'name': 'RS/RCT标签向右移动',
            'pattern': r'添加左侧弹性空间，将RS和RCT标签向右移动',
            'description': 'RS/RCT标签通过左侧弹性空间向右移动'
        },
        {
            'name': '测试计数标签压缩',
            'pattern': r'count_label = QLabel\("计数:"\)',
            'description': '测试计数标签简化为"计数:"'
        },
        {
            'name': '测试时间标签压缩',
            'pattern': r'time_label = QLabel\("用时:"\)',
            'description': '测试时间标签简化为"用时:"'
        },
        {
            'name': '电压标签压缩',
            'pattern': r'voltage_label = QLabel\("电压:"\)',
            'description': '电压标签简化为"电压:"'
        },
        {
            'name': '电池码输入框样式优化',
            'pattern': r'min-width: 180px.*确保最小宽度能显示长条码',
            'description': '电池码输入框CSS样式设置最小宽度180px'
        }
    ]
    
    passed = 0
    failed = 0
    
    for check in checks:
        if re.search(check['pattern'], content, re.DOTALL):
            print(f"✅ {check['name']}: {check['description']}")
            passed += 1
        else:
            print(f"❌ {check['name']}: {check['description']}")
            failed += 1
    
    print("=" * 60)
    print(f"验证结果: ✅ {passed} 项通过, ❌ {failed} 项失败")
    
    # 检查关键函数是否存在
    print("\n🔍 检查关键函数...")
    functions = [
        '_create_battery_code_area',
        '_create_compact_impedance_area',
        '_create_count_time_area'
    ]
    
    for func in functions:
        if f"def {func}" in content:
            print(f"✅ 函数 {func} 存在")
        else:
            print(f"❌ 函数 {func} 不存在")
    
    # 提取并显示关键配置
    print("\n📊 关键配置提取...")
    
    # 提取电池码输入框配置
    battery_min = re.search(r'setMinimumWidth\((\d+)\).*增加最小宽度确保长条码完整显示', content)
    battery_max = re.search(r'setMaximumWidth\((\d+)\).*增加最大宽度提供更多显示空间', content)
    if battery_min and battery_max:
        min_width = battery_min.group(1)
        max_width = battery_max.group(1)
        print(f"📱 电池码输入框: 最小宽度 {min_width}px, 最大宽度 {max_width}px")

    # 提取RS/RCT配置
    impedance_min = re.search(r'setMinimumWidth\((\d+)\).*设置为能容纳5位数字的最小空间', content)
    impedance_max = re.search(r'setMaximumWidth\((\d+)\).*限制最大宽度，为扫码值框腾出空间', content)
    if impedance_min and impedance_max:
        min_width = impedance_min.group(1)
        max_width = impedance_max.group(1)
        print(f"⚡ RS/RCT数值框: 最小宽度 {min_width}px, 最大宽度 {max_width}px")
    
    # 提取布局权重
    weight_config = re.search(r'addLayout\(left_column, (\d+)\).*左列.*\n.*addLayout\(right_column, (\d+)\)', content, re.DOTALL)
    if weight_config:
        left_weight, right_weight = weight_config.groups()
        print(f"⚖️ 布局权重: 左列 {left_weight}, 右列 {right_weight}")
    
    return failed == 0

def check_file_modifications():
    """检查文件修改时间和大小"""
    file_path = "ui/components/channel_display_widget.py"
    
    if os.path.exists(file_path):
        stat = os.stat(file_path)
        size = stat.st_size
        mtime = stat.st_mtime
        
        print(f"\n📁 文件信息:")
        print(f"   文件大小: {size:,} 字节")
        print(f"   修改时间: {mtime}")
        
        # 检查文件是否最近被修改
        import time
        current_time = time.time()
        if current_time - mtime < 3600:  # 1小时内
            print(f"✅ 文件在最近1小时内被修改")
        else:
            print(f"⚠️ 文件修改时间较早")

def main():
    """主函数"""
    print("JCY5001AS 通道布局优化验证工具")
    print("=" * 60)
    
    # 切换到正确的目录
    if os.path.exists("JCY5001AS_Clean_Source1"):
        os.chdir("JCY5001AS_Clean_Source1")
        print("📂 切换到项目目录: JCY5001AS_Clean_Source1")
    
    # 验证修改
    success = verify_layout_changes()
    
    # 检查文件信息
    check_file_modifications()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有布局优化修改验证通过！")
        print("\n📋 优化总结:")
        print("   • 电池扫码值框空间扩展 (180-300px)")
        print("   • RS/RCT数值框空间压缩 (70-85px)")
        print("   • RS/RCT标签向右移动")
        print("   • 左列组件标签文字简化")
        print("   • 整体布局权重优化")
    else:
        print("⚠️ 部分修改可能未正确应用，请检查上述失败项目")
    
    return success

if __name__ == "__main__":
    main()
