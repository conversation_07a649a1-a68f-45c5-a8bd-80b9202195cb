# 阻抗测量启动命令详细说明

## 1. 概述

本文档详细说明了JCY5001阻抗测试仪启动阻抗测量的命令格式和使用方法。阻抗测量启动命令支持两种模式：单通道启动和多通道启动。

## 2. 单通道启动命令

### 2.1 基本参数
- **功能码**：05H（控制单个通道）
- **地址范围**：0000H ~ 0007H（对应通道1~8）
- **数据值**：FF00H（启动）或0000H（停止）

### 2.2 命令格式

| 字段 | 设备地址 | 功能码 | 地址高字节 | 地址低字节 | 数据高字节 | 数据低字节 | CRC低字节 | CRC高字节 |
|------|---------|-------|-----------|-----------|-----------|-----------|----------|----------|
| 示例 | 01H     | 05H   | 00H       | 00H       | FFH       | 00H       | xxH      | xxH      |

### 2.3 具体例子

#### 例1：启动通道1的阻抗测量
```
发送命令：01 05 00 00 FF 00 8C 3A
返回数据：01 05 00 00 FF 00 8C 3A  (与发送命令相同)
```

#### 例2：启动通道2的阻抗测量
```
发送命令：01 05 00 01 FF 00 DD FA
返回数据：01 05 00 01 FF 00 DD FA  (与发送命令相同)
```

## 3. 多通道启动命令

### 3.1 基本参数
- **功能码**：0FH（控制多个通道）
- **起始地址**：0000H（固定值）
- **线圈数量**：0008H（8个通道）
- **字节数**：01H（1个字节）
- **数据字节**：根据需要启动的通道设置对应位

### 3.2 命令格式

| 字段 | 设备地址 | 功能码 | 起始地址高字节 | 起始地址低字节 | 线圈数量高字节 | 线圈数量低字节 | 字节数 | 数据字节 | CRC低字节 | CRC高字节 |
|------|---------|-------|--------------|--------------|--------------|--------------|-------|---------|----------|----------|
| 示例 | 01H     | 0FH   | 00H          | 00H          | 00H          | 08H          | 01H   | xxH     | xxH      | xxH      |

### 3.3 数据字节说明

数据字节中的每一位对应一个通道的启动状态，1表示启动，0表示不启动。

- 位0（最低位）对应通道1
- 位1对应通道2
- 位2对应通道3
- ...
- 位7（最高位）对应通道8

例如：
- 01H (00000001) 表示只启动通道1
- 02H (00000010) 表示只启动通道2
- 03H (00000011) 表示同时启动通道1和通道2
- 07H (00000111) 表示同时启动通道1、2和3
- E0H (11100000) 表示同时启动通道6、7和8
- FFH (11111111) 表示启动所有8个通道

### 3.4 具体例子

#### 例1：启动通道1和通道2
```
发送命令：01 0F 00 00 00 08 01 03 BE 94
返回数据：01 0F 00 00 00 08 BE 09  (返回不包含数据字节)
```

#### 例2：启动通道1、2和3
```
发送命令：01 0F 00 00 00 08 01 07 BF 57
返回数据：01 0F 00 00 00 08 BE 09  (返回不包含数据字节)
```

#### 例3：启动通道6、7和8
```
发送命令：01 0F 00 00 00 08 01 E0 FF 1D
返回数据：01 0F 00 00 00 08 BE 09  (返回不包含数据字节)
```

#### 例4：启动所有通道
```
发送命令：01 0F 00 00 00 08 01 FF BE D5
返回数据：01 0F 00 00 00 08 BE 09  (返回不包含数据字节)
```

## 4. 响应解析

### 4.1 单通道启动响应

单通道启动命令的响应与发送的命令完全相同，表示命令已被接受并执行。

### 4.2 多通道启动响应

多通道启动命令的响应格式如下：

| 字段 | 设备地址 | 功能码 | 起始地址高字节 | 起始地址低字节 | 线圈数量高字节 | 线圈数量低字节 | CRC低字节 | CRC高字节 |
|------|---------|-------|--------------|--------------|--------------|--------------|----------|----------|
| 示例 | 01H     | 0FH   | 00H          | 00H          | 00H          | 08H          | xxH      | xxH      |

响应中不包含数据字节，只返回起始地址和线圈数量，表示命令已被接受并执行。

## 5. 代码实现示例

### 5.1 构建启动单个通道的命令

```python
def build_start_zm_measurement_cmd(channel: int) -> bytes:
    """
    构建启动阻抗测量命令

    Args:
        channel: 通道号（0-7对应通道1-8）

    Returns:
        命令字节序列
    """
    device_addr = 0x01  # 设备地址
    reg_addr = 0x0000 + channel  # 寄存器地址

    cmd = bytearray([
        0x05,  # 功能码：控制单个通道
        (reg_addr >> 8) & 0xFF,  # 地址高字节
        reg_addr & 0xFF,         # 地址低字节
        0xFF,  # 数据高字节（FF表示启动）
        0x00   # 数据低字节
    ])

    # 计算CRC
    crc = calculate_crc16(bytearray([device_addr]) + cmd)
    cmd += bytearray([crc & 0xFF, (crc >> 8) & 0xFF])

    return bytearray([device_addr]) + cmd
```

### 5.2 构建启动多个通道的命令

```python
def build_start_zm_measurement_selected_channels_cmd(channels: list) -> bytes:
    """
    构建启动指定通道阻抗测量命令

    Args:
        channels: 要启动的通道列表，例如[0,1,3]表示启动通道1、2和4

    Returns:
        命令字节序列
    """
    device_addr = 0x01  # 设备地址
    start_addr = 0x0000  # 起始地址
    coil_count = 0x0008  # 线圈数量(8个通道)
    byte_count = 0x01    # 字节数

    # 创建线圈状态字节
    # 通道1对应最低位(bit 0)，通道8对应最高位(bit 7)
    coil_value = 0
    for channel in channels:
        if 0 <= channel < 8:  # 确保通道号有效
            coil_value |= (1 << channel)  # 设置对应位为1
    
    cmd = bytearray([
        0x0F,  # 功能码：控制多个通道
        (start_addr >> 8) & 0xFF,  # 起始地址高字节
        start_addr & 0xFF,         # 起始地址低字节
        (coil_count >> 8) & 0xFF,  # 线圈数量高字节
        coil_count & 0xFF,         # 线圈数量低字节
        byte_count,                # 字节数
        coil_value                 # 线圈状态字节
    ])

    # 计算CRC
    crc = calculate_crc16(bytearray([device_addr]) + cmd)
    cmd += bytearray([crc & 0xFF, (crc >> 8) & 0xFF])

    return bytearray([device_addr]) + cmd
```

## 6. 注意事项

1. **通道编号**：在命令中，通道编号从0开始，对应实际的通道1-8。
2. **数据字节位顺序**：数据字节中的位0（最低位）对应通道1，位7（最高位）对应通道8。
3. **CRC计算**：CRC校验采用Modbus-16标准，低字节在前，高字节在后。
4. **响应验证**：应始终验证响应是否正确，确保命令已被正确执行。
5. **多通道效率**：使用多通道启动命令比逐个启动单通道更高效。

## 7. 常见问题

1. **问题**：启动命令发送后没有响应。
   **解决方案**：检查设备连接、通信参数和命令格式是否正确。

2. **问题**：启动命令响应正确，但设备未开始测量。
   **解决方案**：检查设备状态、电池连接和测量参数设置。

3. **问题**：多通道启动时只有部分通道响应。
   **解决方案**：检查未响应通道的连接状态和设置。
