#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 档位分布表格布局优化测试脚本

测试目标：
1. 档位范围文字完整显示（不被截断）
2. 表格充分利用红色框区域的空间
3. 表格布局合理分布在整个区域

作者：weiwei
日期：2025-01-06
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QFrame, QTableWidget,
                             QTableWidgetItem, QHeaderView, QGridLayout, QGroupBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 尝试导入真实组件
REAL_COMPONENT_AVAILABLE = False
try:
    from core.config_manager import ConfigManager
    from ui.components.statistics_widget import StatisticsWidget
    REAL_COMPONENT_AVAILABLE = True
    print("✅ 成功导入真实统计组件")
except ImportError as e:
    print(f"⚠️  无法导入真实组件: {e}")
    print("将使用模拟组件进行演示")


class GradeTableLayoutTestWindow(QMainWindow):
    """档位分布表格布局优化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 档位分布表格布局优化测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化界面
        self._init_ui()
        
        # 如果有真实组件，设置测试数据
        if hasattr(self, 'statistics_widget'):
            self._set_test_data()
    
    def _init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加标题
        title_label = QLabel("📊 JCY5001AS 档位分布表格布局优化测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #ecf0f1; 
            padding: 15px; 
            border-radius: 10px; 
            border: 3px solid #3498db;
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明信息
        info_label = QLabel("""
        🎯 测试目标：
        • 档位范围文字完整显示（Rs档位范围、Rct档位范围不被截断）
        • 表格充分利用红色框区域的空间
        • 表格布局合理分布在整个区域
        
        🔧 主要优化：
        • 档位范围标签宽度：120px → 200px，最大宽度400px
        • 表格字体大小：14pt → 16pt
        • 表格行高：28px → 50px，列宽：60px → 80px
        • 表格项内边距：4px → 8px
        • 表格最小高度：200px
        
        ✅ 预期效果：
        • 档位范围文字完整显示，不被截断
        • 表格数字显示更大更清晰
        • 充分利用红色框标示的整个区域空间
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #e8f4fd; 
            padding: 15px; 
            border-radius: 8px; 
            border: 2px solid #3498db;
            line-height: 1.4;
        """)
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        self._create_test_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
    
    def _create_test_area(self, layout):
        """创建测试区域"""
        test_frame = QFrame()
        test_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        test_layout = QVBoxLayout(test_frame)
        
        if REAL_COMPONENT_AVAILABLE:
            # 使用真实统计组件
            try:
                config_manager = ConfigManager()
                
                # 创建统计组件
                self.statistics_widget = StatisticsWidget(config_manager)
                
                test_layout.addWidget(self.statistics_widget)
                
            except Exception as e:
                error_label = QLabel(f"❌ 创建真实组件失败: {str(e)}")
                error_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
                test_layout.addWidget(error_label)
                
                # 创建模拟组件
                self._create_mock_component(test_layout)
        else:
            # 创建模拟组件
            self._create_mock_component(test_layout)
        
        layout.addWidget(test_frame)
    
    def _create_mock_component(self, layout):
        """创建模拟统计组件"""
        mock_label = QLabel("📝 模拟档位分布组件（真实组件不可用时的演示）")
        mock_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        mock_label.setStyleSheet("color: #e67e22; padding: 10px;")
        layout.addWidget(mock_label)
        
        # 创建模拟的档位分布区域
        grade_group = QGroupBox("测试统计")
        grade_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 5px;
                margin-top: 0.2ex;
                padding-top: 2px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #27ae60;
                font-size: 13pt;
                font-weight: bold;
            }
        """)
        
        grade_layout = QHBoxLayout(grade_group)
        
        # 左侧：模拟统计数据
        left_widget = self._create_mock_statistics()
        grade_layout.addWidget(left_widget, 1)
        
        # 右侧：模拟档位分布
        right_widget = self._create_mock_grade_distribution()
        grade_layout.addWidget(right_widget, 1)
        
        layout.addWidget(grade_group)
    
    def _create_mock_statistics(self):
        """创建模拟统计数据"""
        stats_widget = QWidget()
        stats_layout = QGridLayout(stats_widget)
        stats_layout.setSpacing(4)
        
        # 模拟统计数据
        stats_data = [
            ("总测试数:", "1530", "#ecf0f1", "#2c3e50"),
            ("合格数:", "870", "#d5f4e6", "#27ae60"),
            ("不合格数:", "660", "#fadbd8", "#e74c3c"),
            ("良率:", "56.9%", "#ebf3fd", "#3498db")
        ]
        
        for i, (label_text, value_text, bg_color, text_color) in enumerate(stats_data):
            label = QLabel(label_text)
            label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
            stats_layout.addWidget(label, i, 0)
            
            value_label = QLabel(value_text)
            value_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setStyleSheet(f"""
                background-color: {bg_color};
                color: {text_color};
                border: 1px solid {text_color};
                border-radius: 4px;
                padding: 4px 12px;
                min-width: 100px;
                max-width: 100px;
            """)
            stats_layout.addWidget(value_label, i, 1)
        
        return stats_widget
    
    def _create_mock_grade_distribution(self):
        """创建模拟档位分布"""
        grade_widget = QWidget()
        grade_layout = QVBoxLayout(grade_widget)
        grade_layout.setSpacing(6)
        
        # 档位范围显示区域
        range_container = QWidget()
        range_layout = QGridLayout(range_container)
        range_layout.setContentsMargins(0, 0, 0, 0)
        range_layout.setSpacing(2)
        
        # Rs档位范围显示
        rs_title_label = QLabel("Rs档位范围:")
        range_layout.addWidget(rs_title_label, 0, 0)
        
        rs_range_label = QLabel("1档(16.000-17.000mΩ) | 2档(17.000-18.000mΩ) | 3档(18.000-19.000mΩ)")
        rs_range_label.setStyleSheet("""
            font-size: 9pt;
            font-weight: bold;
            color: #2c3e50;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 2px;
            padding: 2px 6px;
            min-width: 200px;
            max-width: 400px;
            max-height: 20px;
            word-wrap: break-word;
        """)
        range_layout.addWidget(rs_range_label, 0, 1)
        
        # Rct档位范围显示
        rct_title_label = QLabel("Rct档位范围:")
        range_layout.addWidget(rct_title_label, 1, 0)
        
        rct_range_label = QLabel("1档(1.000-1.667mΩ) | 2档(1.667-2.333mΩ) | 3档(2.333-3.000mΩ)")
        rct_range_label.setStyleSheet("""
            font-size: 9pt;
            font-weight: bold;
            color: #2c3e50;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 2px;
            padding: 2px 6px;
            min-width: 200px;
            max-width: 400px;
            max-height: 20px;
            word-wrap: break-word;
        """)
        range_layout.addWidget(rct_range_label, 1, 1)
        
        range_layout.setColumnStretch(1, 1)
        range_container.setMaximumHeight(60)
        
        grade_layout.addWidget(range_container, 0)
        
        # 档位分布表格
        grade_table = QTableWidget(3, 3)
        grade_table.setHorizontalHeaderLabels(["Rct1", "Rct2", "Rct3"])
        grade_table.setVerticalHeaderLabels(["Rs1", "Rs2", "Rs3"])
        
        # 填充测试数据
        test_data = [
            [114, 112, 90],
            [96, 106, 101],
            [88, 82, 81]
        ]
        
        for row in range(3):
            for col in range(3):
                item = QTableWidgetItem(str(test_data[row][col]))
                item.setTextAlignment(Qt.AlignCenter)
                grade_table.setItem(row, col, item)
        
        # 设置表格样式
        grade_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 16pt;
                font-weight: bold;
                min-height: 200px;
            }
            QTableWidget::item {
                padding: 8px;
                border: 1px solid #bdc3c7;
                font-size: 16pt;
                font-weight: bold;
                text-align: center;
                min-height: 40px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 6px;
                border: none;
                font-weight: bold;
                font-size: 14pt;
                min-height: 35px;
            }
        """)
        
        # 设置表格属性
        grade_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        grade_table.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        grade_table.setEditTriggers(QTableWidget.NoEditTriggers)
        grade_table.setSelectionMode(QTableWidget.NoSelection)
        
        # 设置行高和列宽
        grade_table.verticalHeader().setDefaultSectionSize(50)
        grade_table.horizontalHeader().setDefaultSectionSize(80)
        
        grade_layout.addWidget(grade_table, 1)
        
        return grade_widget
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 刷新显示按钮
        refresh_btn = QPushButton("🔄 刷新显示")
        refresh_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self._refresh_display)
        button_layout.addWidget(refresh_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _set_test_data(self):
        """设置测试数据"""
        if hasattr(self, 'statistics_widget'):
            try:
                # 设置测试统计数据
                self.statistics_widget.update_statistics({
                    'total_count': 1530,
                    'pass_count': 870,
                    'fail_count': 660,
                    'yield_rate': 56.9
                })
                
                # 设置测试档位分布数据
                grade_distribution = {
                    'Rs1-Rct1': 114, 'Rs1-Rct2': 112, 'Rs1-Rct3': 90,
                    'Rs2-Rct1': 96, 'Rs2-Rct2': 106, 'Rs2-Rct3': 101,
                    'Rs3-Rct1': 88, 'Rs3-Rct2': 82, 'Rs3-Rct3': 81
                }
                
                self.statistics_widget.grade_distribution = grade_distribution
                self.statistics_widget._update_grade_table_from_distribution()
                
                print("✅ 测试数据设置成功")
            except Exception as e:
                print(f"❌ 设置测试数据失败: {e}")
    
    def _refresh_display(self):
        """刷新显示"""
        if hasattr(self, 'statistics_widget'):
            self._set_test_data()
        print("🔄 显示已刷新")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建测试窗口
    window = GradeTableLayoutTestWindow()
    window.show()
    
    print("🚀 JCY5001AS 档位分布表格布局优化测试启动")
    print("📊 查看档位范围文字是否完整显示")
    print("📊 查看表格是否充分利用红色框区域空间")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
