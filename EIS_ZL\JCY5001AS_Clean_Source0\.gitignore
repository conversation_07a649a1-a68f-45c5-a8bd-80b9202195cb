# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Nuitka
*.build/
*.dist/
*.onefile-build/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
*_Clean_VEnv/
venv_build/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
*.exe
*.log
*.png
*.jpg
*.jpeg
*.gif
*.bmp
# *.ico
*.db-journal
*.tmp

# 测试和调试文件（保留backend目录下的测试文件）
# 注意：backend/test*.py 文件需要保留用于备份

# 忽略临时测试文件，但保留正式测试文件
*_test.py
debug_*.py
verify_*.py
*_debug.py
*_verification.py

# 保留backend目录下的test开头文件
!backend/test*.py

# 备份文件
*_backup.py
*_backup_*.py
*backup*.py
# *temp*.py
*.bak

# 报告和文档
*.md
!README.md
!CHANGELOG.md
!CONTRIBUTING.md

# 配置备份
*_backup_*.json
*backup*.json

# 数据文件
*.csv
*.xlsx
*.xls
data/backup/*.db
data/samples/*.json

# 构建脚本
*.bat
*.sh

# 许可证文件
license.dat
*.lic