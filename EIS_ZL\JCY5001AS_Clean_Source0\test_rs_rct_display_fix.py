#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JCY5001AS Rs和Rct数值显示修复效果
验证Rs和Rct数值标签能够完整显示，不被截断

Author: Assistant
Date: 2025-07-05
"""

import sys
import os
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QFrame, QLineEdit, QGroupBox,
                             QScrollArea, QPushButton)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RsRctDisplayFixTestWindow(QMainWindow):
    """Rs和Rct数值显示修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS Rs/Rct数值显示修复测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        self._add_description(main_layout)
        
        # 创建对比测试
        self._create_comparison_test(main_layout)
        
        # 添加动态测试
        self._add_dynamic_test(main_layout)
        
        logger.info("Rs/Rct数值显示修复测试窗口已启动")
        logger.info("主要修复：")
        logger.info("• 左右列权重：从3:2调整为1.5:3.5")
        logger.info("• Rs/Rct标题：最小宽度80px，最大宽度100px")
        logger.info("• Rs/Rct数值：最小宽度120px，最大宽度200px")
        logger.info("• 布局间距：增加到8px，确保显示清晰")
    
    def _add_description(self, layout):
        """添加说明文字"""
        desc_label = QLabel("""
        <h2>JCY5001AS Rs/Rct数值显示修复测试</h2>
        <p><b>修复目标：</b>解决Rs和Rct数值显示被截断的问题</p>
        <p><b>主要修复：</b></p>
        <ul>
        <li><b>布局权重调整：</b>从3:2调整为1.5:3.5，右列获得更多空间</li>
        <li><b>Rs/Rct标题：</b>最小宽度80px，确保"Rs(mΩ)"和"Rct(mΩ)"完整显示</li>
        <li><b>Rs/Rct数值：</b>最小宽度120px，最大宽度200px，确保数值完整显示</li>
        <li><b>对齐方式：</b>改为左对齐，避免居中对齐导致的显示问题</li>
        </ul>
        """)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 11pt;
            }
        """)
        layout.addWidget(desc_label)
    
    def _create_comparison_test(self, layout):
        """创建对比测试"""
        comparison_frame = QFrame()
        comparison_layout = QHBoxLayout(comparison_frame)
        comparison_layout.setSpacing(20)
        
        # 修复前的布局
        before_demo = self._create_demo_channel("修复前布局", {
            'left_weight': 3,
            'right_weight': 2,
            'title_width': '60px',
            'value_width': '80px',
            'value_max_width': '120px',
            'spacing': '4px',
            'alignment': 'center'
        })
        comparison_layout.addWidget(before_demo)
        
        # 修复后的布局
        after_demo = self._create_demo_channel("修复后布局", {
            'left_weight': 15,  # 1.5份权重
            'right_weight': 35,  # 3.5份权重
            'title_width': '80px',
            'value_width': '120px',
            'value_max_width': '200px',
            'spacing': '8px',
            'alignment': 'left'
        })
        comparison_layout.addWidget(after_demo)
        
        layout.addWidget(comparison_frame)
    
    def _create_demo_channel(self, title, config):
        """创建演示通道"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout(demo_widget)
        demo_layout.setContentsMargins(10, 10, 10, 10)
        demo_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        demo_layout.addWidget(title_label)
        
        # 通道组框
        channel_frame = QGroupBox("通道 1")
        channel_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        
        channel_layout = QVBoxLayout(channel_frame)
        channel_layout.setContentsMargins(6, 8, 6, 6)
        channel_layout.setSpacing(3)
        
        # 主内容区域
        main_content = QFrame()
        main_content_layout = QHBoxLayout(main_content)
        main_content_layout.setSpacing(int(config['spacing'].replace('px', '')))
        main_content_layout.setContentsMargins(4, 4, 4, 4)
        
        # 左列
        left_column = self._create_left_column_demo()
        main_content_layout.addLayout(left_column, config['left_weight'])
        
        # 右列
        right_column = self._create_right_column_demo(config)
        main_content_layout.addLayout(right_column, config['right_weight'])
        
        channel_layout.addWidget(main_content)
        
        # 添加配置信息
        info_text = f"""
        权重比例: {config['left_weight']}:{config['right_weight']}
        标题宽度: {config['title_width']} | 数值宽度: {config['value_width']}-{config['value_max_width']}
        间距: {config['spacing']} | 对齐: {config['alignment']}
        """
        
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Microsoft YaHei", 8))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        demo_layout.addWidget(info_label)
        demo_layout.addWidget(channel_frame)
        
        return demo_widget
    
    def _create_left_column_demo(self):
        """创建左列演示"""
        left_layout = QVBoxLayout()
        left_layout.setSpacing(2)
        
        # 电池码输入
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(4)
        
        battery_label = QLabel("电池:")
        battery_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        battery_layout.addWidget(battery_label)
        
        battery_edit = QLineEdit("JCY-20250705-6274")
        battery_edit.setStyleSheet("""
            QLineEdit {
                font-size: 9pt;
                padding: 4px 6px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
                min-width: 120px;
                min-height: 24px;
            }
        """)
        battery_layout.addWidget(battery_edit)
        
        left_layout.addLayout(battery_layout)
        
        # 电压显示
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(4)
        
        voltage_label = QLabel("电压:")
        voltage_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel("3.247")
        voltage_value.setStyleSheet("font-size: 9pt; color: #2c3e50; font-weight: bold;")
        voltage_layout.addWidget(voltage_value)
        voltage_layout.addStretch()
        
        left_layout.addLayout(voltage_layout)
        left_layout.addStretch()
        
        return left_layout
    
    def _create_right_column_demo(self, config):
        """创建右列演示"""
        right_layout = QVBoxLayout()
        right_layout.setSpacing(4)
        
        # Rs显示
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(int(config['spacing'].replace('px', '')))
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet(f"""
            font-size: 12pt; 
            color: #7f8c8d; 
            font-weight: bold;
            min-width: {config['title_width']};
        """)
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("17.807")
        alignment = Qt.AlignLeft if config['alignment'] == 'left' else Qt.AlignCenter
        rs_value.setAlignment(alignment)
        rs_value.setStyleSheet(f"""
            QLabel {{
                font-size: 14pt;
                font-weight: 600;
                color: #065f46;
                background-color: #ecfdf5;
                border: 1px solid #10b981;
                border-radius: 6px;
                padding: 6px 10px;
                min-width: {config['value_width']};
                max-width: {config['value_max_width']};
                min-height: 48px;
            }}
        """)
        rs_layout.addWidget(rs_value)
        rs_layout.addStretch()
        
        right_layout.addLayout(rs_layout)
        
        # Rct显示
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(int(config['spacing'].replace('px', '')))
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet(f"""
            font-size: 12pt; 
            color: #7f8c8d; 
            font-weight: bold;
            min-width: {config['title_width']};
        """)
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("2.224")
        rct_value.setAlignment(alignment)
        rct_value.setStyleSheet(f"""
            QLabel {{
                font-size: 14pt;
                font-weight: 600;
                color: #1e40af;
                background-color: #eff6ff;
                border: 1px solid #3b82f6;
                border-radius: 6px;
                padding: 6px 10px;
                min-width: {config['value_width']};
                max-width: {config['value_max_width']};
                min-height: 48px;
            }}
        """)
        rct_layout.addWidget(rct_value)
        rct_layout.addStretch()
        
        right_layout.addLayout(rct_layout)
        right_layout.addStretch()
        
        return right_layout
    
    def _add_dynamic_test(self, layout):
        """添加动态测试"""
        dynamic_frame = QFrame()
        dynamic_layout = QVBoxLayout(dynamic_frame)
        
        # 标题
        title_label = QLabel("动态数值测试")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        dynamic_layout.addWidget(title_label)
        
        # 测试不同长度的数值
        test_values = [
            ("短数值", "1.23", "0.45"),
            ("中等数值", "123.456", "78.901"),
            ("长数值", "1234.567", "890.123"),
            ("超长数值", "12345.678", "9012.345")
        ]
        
        for desc, rs_val, rct_val in test_values:
            test_widget = self._create_value_test(desc, rs_val, rct_val)
            dynamic_layout.addWidget(test_widget)
        
        layout.addWidget(dynamic_frame)
    
    def _create_value_test(self, description, rs_value, rct_value):
        """创建数值测试组件"""
        test_frame = QFrame()
        test_layout = QHBoxLayout(test_frame)
        test_layout.setSpacing(10)
        
        # 描述标签
        desc_label = QLabel(description)
        desc_label.setMinimumWidth(80)
        desc_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        test_layout.addWidget(desc_label)
        
        # Rs显示
        rs_layout = QHBoxLayout()
        rs_layout.setSpacing(8)
        
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet("font-size: 12pt; color: #7f8c8d; font-weight: bold; min-width: 80px;")
        rs_layout.addWidget(rs_title)
        
        rs_val_label = QLabel(rs_value)
        rs_val_label.setAlignment(Qt.AlignLeft)
        rs_val_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: 600;
                color: #065f46;
                background-color: #ecfdf5;
                border: 1px solid #10b981;
                border-radius: 6px;
                padding: 6px 10px;
                min-width: 120px;
                max-width: 200px;
                min-height: 48px;
            }
        """)
        rs_layout.addWidget(rs_val_label)
        
        test_layout.addLayout(rs_layout)
        
        # Rct显示
        rct_layout = QHBoxLayout()
        rct_layout.setSpacing(8)
        
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet("font-size: 12pt; color: #7f8c8d; font-weight: bold; min-width: 80px;")
        rct_layout.addWidget(rct_title)
        
        rct_val_label = QLabel(rct_value)
        rct_val_label.setAlignment(Qt.AlignLeft)
        rct_val_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: 600;
                color: #1e40af;
                background-color: #eff6ff;
                border: 1px solid #3b82f6;
                border-radius: 6px;
                padding: 6px 10px;
                min-width: 120px;
                max-width: 200px;
                min-height: 48px;
            }
        """)
        rct_layout.addWidget(rct_val_label)
        
        test_layout.addLayout(rct_layout)
        test_layout.addStretch()
        
        return test_frame


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = RsRctDisplayFixTestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
