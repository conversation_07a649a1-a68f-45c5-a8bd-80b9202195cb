#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实际测试执行过程
检查测试过程中频点被过滤或跳过的原因
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_test_results():
    """分析测试结果中的频点"""
    print("🔍 分析实际测试结果")
    print("=" * 80)
    
    # 从用户提供的测试结果中提取频点
    observed_frequencies = [5.722, 0.477, 0.238, 0.119, 7.629]
    
    print(f"📊 观察到的测试频点:")
    for i, freq in enumerate(sorted(observed_frequencies, reverse=True), 1):
        print(f"   {i}. {freq:>8.3f} Hz")
    
    print(f"\n📋 频点数量: {len(set(observed_frequencies))}个")
    
    # 对比配置中的频点
    try:
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        configured_frequencies = config.get('frequency', {}).get('list', [])
        
        print(f"\n📋 配置中的频点数量: {len(configured_frequencies)}个")
        print(f"📋 配置中的频点范围: {configured_frequencies[0]:.3f}Hz ~ {configured_frequencies[-1]:.3f}Hz")
        
        # 检查观察到的频点是否在配置中
        print(f"\n🔍 频点匹配检查:")
        matched_frequencies = []
        for obs_freq in observed_frequencies:
            for conf_freq in configured_frequencies:
                if abs(obs_freq - conf_freq) < 0.001:  # 允许小的浮点误差
                    matched_frequencies.append(conf_freq)
                    print(f"   ✅ {obs_freq:.3f}Hz 匹配配置中的 {conf_freq:.3f}Hz")
                    break
            else:
                print(f"   ❌ {obs_freq:.3f}Hz 不在配置中")
        
        # 检查缺失的频点
        missing_frequencies = [f for f in configured_frequencies if f not in matched_frequencies]
        print(f"\n❌ 缺失的频点 ({len(missing_frequencies)}个):")
        for i, freq in enumerate(missing_frequencies, 1):
            print(f"   {i:2d}. {freq:>8.3f} Hz")
        
        # 分析频点分布
        print(f"\n📈 频点分布分析:")
        high_freq_count = sum(1 for f in observed_frequencies if f > 15.0)
        low_freq_count = len(observed_frequencies) - high_freq_count
        
        print(f"   • 高频点 (>15Hz): {high_freq_count}个")
        print(f"   • 低频点 (≤15Hz): {low_freq_count}个")
        
        # 检查是否只测试了低频点
        if high_freq_count == 0:
            print(f"   ⚠️ 只测试了低频点！高频点可能被跳过")
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

def check_test_limitations():
    """检查可能的测试限制"""
    print(f"\n🔧 检查可能的测试限制:")
    print("-" * 60)
    
    try:
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查并行错频模式
        test_config = config.get('test', {})
        use_parallel_staggered = test_config.get('use_parallel_staggered_mode', False)
        critical_frequency = test_config.get('critical_frequency', 15.0)
        
        print(f"📋 并行错频模式: {'启用' if use_parallel_staggered else '禁用'}")
        print(f"📋 临界频率: {critical_frequency}Hz")
        
        if use_parallel_staggered:
            # 分析并行错频模式的影响
            frequencies = config.get('frequency', {}).get('list', [])
            high_freq_list = [f for f in frequencies if f > critical_frequency]
            low_freq_list = [f for f in frequencies if f <= critical_frequency]
            
            print(f"\n📊 并行错频模式分析:")
            print(f"   • 高频点 (>{critical_frequency}Hz): {len(high_freq_list)}个")
            print(f"   • 低频点 (≤{critical_frequency}Hz): {len(low_freq_list)}个")
            
            print(f"\n   高频点列表:")
            for i, freq in enumerate(high_freq_list, 1):
                print(f"   {i:2d}. {freq:>8.3f} Hz")
            
            print(f"\n   低频点列表:")
            for i, freq in enumerate(low_freq_list, 1):
                print(f"   {i:2d}. {freq:>8.3f} Hz")
            
            # 检查是否只测试了低频点
            observed_frequencies = [5.722, 0.477, 0.238, 0.119, 7.629]
            observed_high = [f for f in observed_frequencies if f > critical_frequency]
            observed_low = [f for f in observed_frequencies if f <= critical_frequency]
            
            print(f"\n🔍 实际测试结果分析:")
            print(f"   • 实际测试的高频点: {len(observed_high)}个")
            print(f"   • 实际测试的低频点: {len(observed_low)}个")
            
            if len(observed_high) == 0 and len(high_freq_list) > 0:
                print(f"   ❌ 高频点全部缺失！可能的原因:")
                print(f"      - 并行错频模式执行异常")
                print(f"      - 高频点测试被跳过")
                print(f"      - 设备通信问题")
        
        # 检查其他限制
        count_limit_enabled = test_config.get('count_limit_enabled', False)
        max_count = test_config.get('max_count', 100)
        timeout = test_config.get('timeout', 60)
        
        print(f"\n📋 其他测试限制:")
        print(f"   • 计数限制: {'启用' if count_limit_enabled else '禁用'}")
        print(f"   • 最大测试次数: {max_count}")
        print(f"   • 超时时间: {timeout}秒")
        
    except Exception as e:
        print(f"❌ 检查测试限制失败: {e}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n💡 解决方案建议:")
    print("=" * 60)
    
    print(f"1. 🔧 检查并行错频模式:")
    print(f"   • 临时禁用并行错频模式，使用传统模式测试")
    print(f"   • 检查高频点是否被错误跳过")
    
    print(f"\n2. 📊 检查测试执行日志:")
    print(f"   • 查看logs/app.log中的测试执行记录")
    print(f"   • 确认哪些频点被跳过及原因")
    
    print(f"\n3. 🔌 检查设备通信:")
    print(f"   • 确认设备能正确响应高频点设置")
    print(f"   • 检查是否有通信超时或错误")
    
    print(f"\n4. ⚙️ 简化测试配置:")
    print(f"   • 临时使用生产模式（4个频点）验证基本功能")
    print(f"   • 逐步增加频点数量定位问题")

def main():
    """主函数"""
    analyze_test_results()
    check_test_limitations()
    suggest_solutions()
    
    print(f"\n" + "=" * 80)
    print(f"🎯 总结: 测试只执行了5个低频点，高频点全部缺失")
    print(f"💡 建议: 重点检查并行错频模式的高频点处理逻辑")

if __name__ == "__main__":
    main()
