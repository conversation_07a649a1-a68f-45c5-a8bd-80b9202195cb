#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统计框向左移动效果
验证统计数值框紧贴左侧标签的布局调整
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QGroupBox, QFrame, QPushButton, QGridLayout)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
import random

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.components.statistics_widget import StatisticsWidget
    from utils.config_manager import ConfigManager
    REAL_COMPONENT_AVAILABLE = True
except ImportError as e:
    print(f"导入真实组件失败: {e}")
    REAL_COMPONENT_AVAILABLE = False

class StatisticsMoveLeftTest(QMainWindow):
    """统计框向左移动测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 统计框向左移动测试")
        self.setGeometry(100, 100, 1400, 800)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 统计框向左移动测试")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 添加详细说明
        info_label = QLabel("""
        🎯 统计框向左移动调整内容：
        
        📏 间距大幅减少：
        • GridLayout水平间距: 6px → 2px (减少67%)
        • 移除GridLayout内边距: setContentsMargins(0, 0, 0, 0)
        • 统计框内边距减少: padding从6px 8px减少到4px 6px
        • 统计框外边距移除: margin设为0px
        
        🎯 布局调整目标：
        • 让统计数值框紧贴左侧标签文字
        • 消除中间的大片空白区域
        • 形成"标签:数值"的紧密组合
        • 整体布局更加紧凑
        
        ✅ 预期效果：
        • 统计框明显向左移动，靠近标签
        • 中间空白区域大幅减少
        • 视觉上形成紧密的标签-数值组合
        • 保持数值框的可读性和样式
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #fff3cd; 
            padding: 15px; 
            border-radius: 8px; 
            border: 2px solid #ffc107;
            line-height: 1.4;
        """)
        main_layout.addWidget(info_label)
        
        # 创建对比测试区域
        self._create_comparison_area(main_layout)
        
        # 创建真实组件测试区域
        self._create_real_component_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
        
        # 初始化定时器
        self._init_timer()
    
    def _create_comparison_area(self, layout):
        """创建对比测试区域"""
        comparison_frame = QFrame()
        comparison_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        comparison_layout = QVBoxLayout(comparison_frame)
        
        # 标题
        title = QLabel("布局对比演示")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        comparison_layout.addWidget(title)
        
        # 对比布局
        compare_layout = QHBoxLayout()
        
        # 修改前的布局
        before_widget = self._create_before_layout()
        compare_layout.addWidget(before_widget)
        
        # 修改后的布局
        after_widget = self._create_after_layout()
        compare_layout.addWidget(after_widget)
        
        comparison_layout.addLayout(compare_layout)
        layout.addWidget(comparison_frame)
    
    def _create_before_layout(self):
        """创建修改前的布局演示"""
        before_group = QGroupBox("修改前 - 间距较大")
        before_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 5px;
                margin-top: 0.2ex;
                padding-top: 2px;
                background-color: white;
            }
            QGroupBox::title {
                color: #e74c3c;
                font-size: 12pt;
                font-weight: bold;
            }
        """)
        
        layout = QGridLayout(before_group)
        layout.setSpacing(8)  # 原来的间距
        layout.setHorizontalSpacing(6)  # 原来的水平间距
        
        # 添加统计项
        items = [
            ("总测试数:", "474"),
            ("合格数:", "265"),
            ("不合格数:", "209"),
            ("良率:", "55.9%")
        ]
        
        for i, (label_text, value_text) in enumerate(items):
            label = QLabel(label_text)
            layout.addWidget(label, i, 0)
            
            value = QLabel(value_text)
            value.setStyleSheet("""
                background-color: #ecf0f1; border: 1px solid #bdc3c7;
                border-radius: 4px; padding: 6px 8px;
                min-width: 48px; max-width: 48px; text-align: center;
                font-weight: bold;
            """)
            value.setAlignment(Qt.AlignCenter)
            layout.addWidget(value, i, 1)
        
        layout.setColumnStretch(0, 0)
        layout.setColumnStretch(1, 1)  # 原来有拉伸
        
        return before_group
    
    def _create_after_layout(self):
        """创建修改后的布局演示"""
        after_group = QGroupBox("修改后 - 紧贴标签")
        after_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 5px;
                margin-top: 0.2ex;
                padding-top: 2px;
                background-color: white;
            }
            QGroupBox::title {
                color: #27ae60;
                font-size: 12pt;
                font-weight: bold;
            }
        """)
        
        layout = QGridLayout(after_group)
        layout.setSpacing(4)  # 减少的间距
        layout.setHorizontalSpacing(2)  # 大幅减少的水平间距
        layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        
        # 添加统计项
        items = [
            ("总测试数:", "474", "#ecf0f1", "#bdc3c7"),
            ("合格数:", "265", "#d5f4e6", "#27ae60"),
            ("不合格数:", "209", "#fadbd8", "#e74c3c"),
            ("良率:", "55.9%", "#ebf3fd", "#3498db")
        ]
        
        for i, (label_text, value_text, bg_color, border_color) in enumerate(items):
            label = QLabel(label_text)
            layout.addWidget(label, i, 0)
            
            value = QLabel(value_text)
            value.setStyleSheet(f"""
                background-color: {bg_color}; border: 1px solid {border_color};
                border-radius: 4px; padding: 4px 6px;
                min-width: 48px; max-width: 48px; text-align: center;
                font-weight: bold; margin: 0px;
            """)
            value.setAlignment(Qt.AlignCenter)
            layout.addWidget(value, i, 1)
        
        layout.setColumnStretch(0, 0)
        layout.setColumnStretch(1, 0)  # 不拉伸，固定宽度
        
        return after_group
    
    def _create_real_component_area(self, layout):
        """创建真实组件测试区域"""
        real_frame = QFrame()
        real_frame.setStyleSheet("background-color: #f0f8ff; border-radius: 10px; padding: 15px;")
        real_layout = QVBoxLayout(real_frame)
        
        # 标题
        title = QLabel("真实统计组件测试")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        real_layout.addWidget(title)
        
        if REAL_COMPONENT_AVAILABLE:
            try:
                config_manager = ConfigManager()
                self.statistics_widget = StatisticsWidget(config_manager)
                self._set_test_data()
                real_layout.addWidget(self.statistics_widget)
            except Exception as e:
                print(f"创建真实统计组件失败: {e}")
                error_label = QLabel(f"无法加载真实组件: {e}")
                error_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                real_layout.addWidget(error_label)
        else:
            info_label = QLabel("真实组件不可用，请查看上方的对比演示")
            info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
            real_layout.addWidget(info_label)
        
        layout.addWidget(real_frame)
    
    def _set_test_data(self):
        """设置测试数据"""
        try:
            # 模拟测试数据
            for i in range(474):
                is_pass = i < 265  # 前265个通过
                rs_grade = random.randint(1, 3)
                rct_grade = random.randint(1, 3)
                self.statistics_widget.add_test_result(is_pass, rs_grade, rct_grade)
        except Exception as e:
            print(f"设置测试数据失败: {e}")
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 更新数据按钮
        update_btn = QPushButton("模拟数据更新")
        update_btn.clicked.connect(self._update_data)
        update_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(update_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置数据")
        reset_btn.clicked.connect(self._reset_data)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _init_timer(self):
        """初始化定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self._auto_update)
        self.timer.start(5000)  # 每5秒自动更新
    
    def _update_data(self):
        """更新数据"""
        if REAL_COMPONENT_AVAILABLE and hasattr(self, 'statistics_widget'):
            try:
                for _ in range(10):
                    is_pass = random.choice([True, True, False])
                    rs_grade = random.randint(1, 3)
                    rct_grade = random.randint(1, 3)
                    self.statistics_widget.add_test_result(is_pass, rs_grade, rct_grade)
                print("✅ 数据更新完成")
            except Exception as e:
                print(f"数据更新失败: {e}")
        else:
            print("✅ 演示模式：数据更新")
    
    def _reset_data(self):
        """重置数据"""
        if REAL_COMPONENT_AVAILABLE and hasattr(self, 'statistics_widget'):
            try:
                self.statistics_widget.reset_statistics()
                print("✅ 数据已重置")
            except Exception as e:
                print(f"数据重置失败: {e}")
        else:
            print("✅ 演示模式：数据重置")
    
    def _auto_update(self):
        """自动更新"""
        if REAL_COMPONENT_AVAILABLE and hasattr(self, 'statistics_widget'):
            try:
                for _ in range(random.randint(1, 3)):
                    is_pass = random.choice([True, True, False])
                    rs_grade = random.randint(1, 3)
                    rct_grade = random.randint(1, 3)
                    self.statistics_widget.add_test_result(is_pass, rs_grade, rct_grade)
            except Exception as e:
                print(f"自动更新失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = StatisticsMoveLeftTest()
    window.show()
    
    print("🚀 JCY5001AS 统计框向左移动测试启动")
    print("=" * 60)
    print("📋 测试验证内容:")
    print("   ✓ 统计框向左移动，紧贴标签")
    print("   ✓ 水平间距: 6px → 2px (减少67%)")
    print("   ✓ 移除GridLayout内边距")
    print("   ✓ 减少统计框内边距和外边距")
    print("   ✓ 消除中间空白区域")
    print("   ✓ 形成紧密的标签-数值组合")
    print("=" * 60)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
