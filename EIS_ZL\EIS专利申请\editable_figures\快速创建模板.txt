LibreOffice Draw 快速创建专利附图模板

=== 第一步：准备工作 ===
1. 关闭当前SVG文件
2. 文件 → 新建 → 绘图
3. 格式 → 页面 → 纸张：A4，方向：横向

=== 第二步：创建标题 ===
1. 点击文本工具 [T]
2. 在页面顶部中央点击
3. 输入：图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图
4. 选中文字 → 格式 → 字符 → 字号：16pt，加粗

=== 第三步：创建组件（按顺序） ===

【1-被测电池】
- 点击矩形工具 [□]
- 在左上角绘制矩形
- 右键 → 区域 → 颜色：浅蓝色
- 双击矩形 → 输入：1-被测电池\n1.9V-5.5V

【7-测试夹具】
- 在被测电池下方绘制矩形
- 右键 → 区域 → 颜色：小麦色
- 双击 → 输入：7-测试夹具\n四线制连接\n精确测量

【2-DNB1101BB芯片】
- 在中央绘制较大矩形
- 右键 → 区域 → 颜色：浅绿色
- 双击 → 输入：2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz

【4-外部电流源】
- 在左下角绘制矩形
- 右键 → 区域 → 颜色：浅红色
- 双击 → 输入：4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω

【3-STM32控制器】
- 在右侧中央绘制矩形
- 右键 → 区域 → 颜色：浅黄色
- 双击 → 输入：3-STM32F103RCT6\n主控制器\n72MHz ARM

【5-串口显示屏】
- 在右下角绘制矩形
- 右键 → 区域 → 颜色：浅灰色
- 双击 → 输入：5-串口显示屏\n实时显示\n测试结果

【6-PC上位机】
- 在右上角绘制矩形
- 右键 → 区域 → 颜色：浅蓝灰色
- 双击 → 输入：6-PC上位机\nModbus RTU\n数据分析

=== 第四步：添加连接线和箭头 ===

1. 点击连接线工具 [↗]
2. 从起点拖拽到终点
3. 右键连接线 → 线条 → 线条末端 → 设置箭头

连接关系：
- 电池 ↔ 测试夹具（双向箭头）
- 测试夹具 → DNB1101BB（蓝色箭头）
- DNB1101BB → STM32（紫色箭头）
- STM32 → PC（红色箭头）
- STM32 → 显示屏（绿色箭头）
- DNB1101BB → 电流源（橙色箭头）
- 电流源 → 测试夹具（红色箭头）

=== 第五步：添加标签文字 ===
在每条连接线旁边添加说明文字：
- "电气连接"
- "电压/电流测量信号"
- "SPI 1Mbps"
- "USB/UART"
- "UART 115200bps"
- "VSW/VDR控制信号"
- "激励电流"

=== 第六步：保存和导出 ===
1. 文件 → 另存为 → 格式：ODF绘图(.odg)
2. 文件 → 导出为PDF（用于专利申请）

=== 快捷键提示 ===
- 选择工具：Esc
- 矩形工具：R
- 文本工具：T
- 连接线：L
- 复制：Ctrl+C
- 粘贴：Ctrl+V
- 撤销：Ctrl+Z

=== 颜色代码参考 ===
- 浅蓝色：#ADD8E6
- 小麦色：#F5DEB3
- 浅绿色：#90EE90
- 浅红色：#F08080
- 浅黄色：#FFFFE0
- 浅灰色：#D3D3D3
- 浅蓝灰色：#B0C4DE
