#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实际使用的频点配置
检查系统各个组件实际读取到的频点数据
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.test_config_manager import TestConfigManager

def debug_actual_frequencies():
    """调试实际使用的频点配置"""
    print("🔍 调试实际使用的频点配置")
    print("=" * 80)
    
    try:
        # 1. 直接读取配置文件
        print("📋 1. 直接读取配置文件:")
        with open('config/app_config.json', 'r', encoding='utf-8') as f:
            raw_config = json.load(f)
        
        frequency_config = raw_config.get('frequency', {})
        preset_mode = frequency_config.get('preset_mode', 'unknown')
        frequency_list = frequency_config.get('list', [])
        custom_list = frequency_config.get('multi_freq', {}).get('custom_list', [])
        
        print(f"   • 预设模式: {preset_mode}")
        print(f"   • frequency.list: {len(frequency_list)}个频点")
        print(f"   • custom_list: {len(custom_list)}个频点")
        
        if frequency_list:
            print(f"   • frequency.list前5个: {frequency_list[:5]}")
            print(f"   • frequency.list后5个: {frequency_list[-5:]}")
        
        if custom_list:
            print(f"   • custom_list前5个: {custom_list[:5]}")
            print(f"   • custom_list后5个: {custom_list[-5:]}")
        
        # 2. 通过ConfigManager读取
        print(f"\n📋 2. 通过ConfigManager读取:")
        config_manager = ConfigManager()
        
        cm_preset_mode = config_manager.get('frequency.preset_mode', 'unknown')
        cm_frequency_list = config_manager.get('frequency.list', [])
        cm_custom_list = config_manager.get('frequency.multi_freq.custom_list', [])
        cm_single_mode = config_manager.get('frequency.single_mode', False)
        cm_mode = config_manager.get('frequency.mode', 'unknown')
        
        print(f"   • 预设模式: {cm_preset_mode}")
        print(f"   • 频率模式: {cm_mode}")
        print(f"   • 单频模式: {cm_single_mode}")
        print(f"   • frequency.list: {len(cm_frequency_list)}个频点")
        print(f"   • custom_list: {len(cm_custom_list)}个频点")
        
        # 3. 通过TestConfigManager读取
        print(f"\n📋 3. 通过TestConfigManager读取:")
        test_config_manager = TestConfigManager(config_manager)
        test_config = test_config_manager.load_test_config()
        
        tcm_frequencies = test_config.get('frequencies', [])
        tcm_test_mode = test_config.get('test_mode', 'unknown')
        tcm_single_mode = test_config.get('is_single_mode', False)
        
        print(f"   • 测试模式: {tcm_test_mode}")
        print(f"   • 单频模式: {tcm_single_mode}")
        print(f"   • 频点数量: {len(tcm_frequencies)}个")
        
        if tcm_frequencies:
            print(f"   • 频点前5个: {tcm_frequencies[:5]}")
            print(f"   • 频点后5个: {tcm_frequencies[-5:]}")
        
        # 4. 检查配置一致性
        print(f"\n🔧 4. 配置一致性检查:")
        
        # 检查预设模式
        modes_consistent = (preset_mode == cm_preset_mode)
        print(f"   • 预设模式一致: {'✅' if modes_consistent else '❌'}")
        if not modes_consistent:
            print(f"     - 配置文件: {preset_mode}")
            print(f"     - ConfigManager: {cm_preset_mode}")
        
        # 检查频点数量
        freq_counts = [len(frequency_list), len(custom_list), len(cm_frequency_list), len(cm_custom_list), len(tcm_frequencies)]
        freq_consistent = len(set(freq_counts)) == 1
        print(f"   • 频点数量一致: {'✅' if freq_consistent else '❌'}")
        if not freq_consistent:
            print(f"     - 配置文件 frequency.list: {len(frequency_list)}")
            print(f"     - 配置文件 custom_list: {len(custom_list)}")
            print(f"     - ConfigManager frequency.list: {len(cm_frequency_list)}")
            print(f"     - ConfigManager custom_list: {len(cm_custom_list)}")
            print(f"     - TestConfigManager frequencies: {len(tcm_frequencies)}")
        
        # 检查频点内容
        if tcm_frequencies and frequency_list:
            freq_content_consistent = tcm_frequencies == frequency_list
            print(f"   • 频点内容一致: {'✅' if freq_content_consistent else '❌'}")
            if not freq_content_consistent:
                print(f"     - 配置文件和TestConfigManager的频点内容不匹配")
        
        # 5. 分析问题
        print(f"\n🎯 5. 问题分析:")
        
        if len(tcm_frequencies) == 5:
            print("❌ 发现问题：TestConfigManager只读取到5个频点")
            print("   可能原因：")
            print("   • 系统可能从错误的配置路径读取频点")
            print("   • 可能存在配置缓存问题")
            print("   • 可能有其他配置覆盖了正确的频点列表")
            
            # 检查是否从custom_list读取
            if len(cm_custom_list) == 5:
                print("   • 检测到custom_list只有5个频点，可能是问题根源")
            
        elif len(tcm_frequencies) == 20:
            print("✅ TestConfigManager正确读取到20个频点")
            print("   如果测试仍然只有5个点，问题可能在：")
            print("   • 测试执行过程中的其他限制")
            print("   • 设备通信限制")
            print("   • 测试流程控制逻辑")
        
        else:
            print(f"⚠️ TestConfigManager读取到{len(tcm_frequencies)}个频点，数量异常")
        
        # 6. 显示实际使用的频点
        print(f"\n📊 6. 实际使用的频点列表:")
        if tcm_frequencies:
            print(f"   TestConfigManager提供的频点 ({len(tcm_frequencies)}个):")
            for i, freq in enumerate(tcm_frequencies, 1):
                print(f"   {i:2d}. {freq:>8.3f} Hz")
        else:
            print("   ❌ 未获取到频点数据")
        
        return tcm_frequencies
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    frequencies = debug_actual_frequencies()
    
    print(f"\n" + "=" * 80)
    if len(frequencies) == 20:
        print("🎉 频点配置正确！系统应该测试20个频点")
    elif len(frequencies) == 5:
        print("❌ 频点配置有问题！系统只会测试5个频点")
    else:
        print(f"⚠️ 频点配置异常！系统会测试{len(frequencies)}个频点")

if __name__ == "__main__":
    main()
