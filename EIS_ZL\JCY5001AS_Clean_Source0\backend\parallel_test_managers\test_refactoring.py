# -*- coding: utf-8 -*-
"""
并行错频测试管理器重构验证测试
验证重构后的管理器是否正常工作

Author: Jack
Date: 2025-01-30
"""

import logging
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

logger = logging.getLogger(__name__)


def test_manager_imports():
    """测试管理器导入"""
    try:
        from backend.parallel_test_managers import (
            FrequencyClassifier,
            StaggeredTestExecutor,
            SimultaneousTestExecutor,
            TestDataCollector,
            TestProgressTracker,
            TestErrorRecovery
        )
        
        print("✅ 所有并行测试管理器导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 并行测试管理器导入失败: {e}")
        return False


def test_frequency_classifier():
    """测试频率分类器"""
    try:
        from backend.parallel_test_managers import FrequencyClassifier
        
        classifier = FrequencyClassifier()
        
        # 测试频率分类
        frequencies = [0.01, 0.1, 1.0, 10.0, 100.0, 1000.0, 7800.0]
        critical_frequency = 100.0
        
        high_frequencies, low_frequencies = classifier.classify_frequencies(frequencies, critical_frequency)
        
        # 验证分类结果
        expected_high = [1000.0, 7800.0]
        expected_low = [0.01, 0.1, 1.0, 10.0, 100.0]
        
        if high_frequencies == expected_high and low_frequencies == expected_low:
            print("✅ 频率分类器测试成功")
            return True
        else:
            print(f"❌ 频率分类器测试失败: 高频{high_frequencies}, 低频{low_frequencies}")
            return False
            
    except Exception as e:
        print(f"❌ 频率分类器测试失败: {e}")
        return False


def test_data_collector():
    """测试数据收集器"""
    try:
        from backend.parallel_test_managers import TestDataCollector
        
        collector = TestDataCollector()
        
        # 模拟测试数据
        staggered_results = {
            1000.0: {0: {'real_impedance': 100.0, 'imaginary_impedance': 50.0}},
            7800.0: {0: {'real_impedance': 120.0, 'imaginary_impedance': 60.0}}
        }
        
        simultaneous_results = {
            0.01: {0: {'real_impedance': 80.0, 'imaginary_impedance': 40.0}},
            100.0: {0: {'real_impedance': 90.0, 'imaginary_impedance': 45.0}}
        }
        
        # 收集数据
        collector.collect_staggered_results(staggered_results)
        collector.collect_simultaneous_results(simultaneous_results)
        
        # 合并数据
        combined_results = collector.combine_all_results([0])
        
        if len(combined_results) > 0 and 0 in combined_results:
            print("✅ 数据收集器测试成功")
            return True
        else:
            print("❌ 数据收集器测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据收集器测试失败: {e}")
        return False


def test_progress_tracker():
    """测试进度跟踪器"""
    try:
        from backend.parallel_test_managers import TestProgressTracker
        
        tracker = TestProgressTracker()
        
        # 初始化测试
        tracker.initialize_test(10, [0, 1, 2])
        
        # 更新进度
        tracker.update_frequency_progress(1, 100.0)
        tracker.mark_frequency_completed(100.0)
        
        # 获取进度信息
        progress = tracker.get_overall_progress()
        
        if progress['completed_frequencies'] == 1 and progress['total_frequencies'] == 10:
            print("✅ 进度跟踪器测试成功")
            return True
        else:
            print(f"❌ 进度跟踪器测试失败: {progress}")
            return False
            
    except Exception as e:
        print(f"❌ 进度跟踪器测试失败: {e}")
        return False


def test_error_recovery():
    """测试错误恢复器"""
    try:
        from backend.parallel_test_managers import TestErrorRecovery
        
        # 模拟通信管理器
        class MockCommManager:
            pass
        
        recovery = TestErrorRecovery(MockCommManager())
        
        # 测试重试机制
        operation_key = "test_operation"
        
        # 第一次重试应该成功
        if recovery.should_retry_operation(operation_key):
            print("✅ 错误恢复器测试成功")
            return True
        else:
            print("❌ 错误恢复器测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 错误恢复器测试失败: {e}")
        return False


def test_main_manager_integration():
    """测试主管理器集成"""
    try:
        from backend.parallel_staggered_test_manager import ParallelStaggeredTestManager
        
        # 模拟通信管理器
        class MockCommManager:
            def is_connected(self):
                return True
        
        # 创建主管理器
        manager = ParallelStaggeredTestManager(MockCommManager())
        
        # 检查管理器是否正确初始化
        if (hasattr(manager, 'frequency_classifier') and 
            hasattr(manager, 'staggered_executor') and
            hasattr(manager, 'simultaneous_executor') and
            hasattr(manager, 'data_collector') and
            hasattr(manager, 'progress_tracker') and
            hasattr(manager, 'error_recovery')):
            print("✅ 主管理器集成测试成功")
            return True
        else:
            print("❌ 主管理器集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 主管理器集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 开始并行错频测试管理器重构验证测试...")
    
    tests = [
        ("管理器导入测试", test_manager_imports),
        ("频率分类器测试", test_frequency_classifier),
        ("数据收集器测试", test_data_collector),
        ("进度跟踪器测试", test_progress_tracker),
        ("错误恢复器测试", test_error_recovery),
        ("主管理器集成测试", test_main_manager_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 并行错频测试管理器重构验证成功！所有管理器工作正常")
        return True
    else:
        print("⚠️ 重构验证失败，需要修复问题")
        return False


if __name__ == "__main__":
    main()