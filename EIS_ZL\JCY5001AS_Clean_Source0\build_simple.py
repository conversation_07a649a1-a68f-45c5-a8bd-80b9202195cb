#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A简化打包脚本
用于快速测试Nuitka打包功能
"""

import os
import sys
import subprocess
from datetime import datetime

def main():
    """主函数"""
    print("🎯 JCY5001A简化打包测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    print("🚀 开始简化构建...")
    
    # 简化的构建命令
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",  # 独立模式
        "--enable-plugin=pyqt5",  # PyQt5插件
        "--include-data-dir=config=config",  # 配置目录
        "--include-data-dir=resources=resources",  # 资源目录
        "--include-package=PyQt5",  # PyQt5
        "--include-package=serial",  # 串口
        "--include-package=numpy",  # 科学计算
        "--include-package=scipy",  # 科学计算
        "--include-package=pandas",  # 数据处理
        "--include-package=matplotlib",  # 图表
        "--include-package=openpyxl",  # Excel
        "--include-package=SQLAlchemy",  # 数据库
        "--windows-console-mode=disable",  # 禁用控制台
        "--output-dir=test_dist",  # 测试输出目录
        "--output-filename=JCY5001A_Test.exe",  # 测试文件名
        "--assume-yes-for-downloads",  # 自动确认
        "--show-progress",  # 显示进度
        "main.py"  # 主文件
    ]
    
    print("📋 构建命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行构建
        result = subprocess.run(cmd, timeout=1800)  # 30分钟超时
        if result.returncode == 0:
            print("✅ 简化构建成功!")
            
            # 检查输出文件
            exe_path = "test_dist/main.dist/JCY5001A_Test.exe"
            if os.path.exists(exe_path):
                print(f"🚀 可执行文件: {exe_path}")
                print(f"📁 程序目录: test_dist/main.dist/")
            
            return True
        else:
            print(f"❌ 构建失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 构建超时（30分钟）")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 简化打包测试成功!")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包异常: {e}")
        sys.exit(1)
