"""
系统配置模板管理器
用于导入导出系统配置模板，包括频率设置、档位设置、测试参数等
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class ConfigTemplateManager:
    """系统配置模板管理器"""
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化配置模板管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.templates_dir = Path("config/templates")
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
    def export_config_template(self, template_name: str, description: str = "") -> Optional[str]:
        """
        导出当前系统配置为模板
        
        Args:
            template_name: 模板名称
            description: 模板描述
            
        Returns:
            导出的模板文件路径，失败返回None
        """
        try:
            # 收集关键配置项
            config_data = self._collect_config_data()
            
            # 创建模板数据结构
            template_data = {
                'template_info': {
                    'name': template_name,
                    'description': description,
                    'version': '1.0',
                    'created_time': datetime.now().isoformat(),
                    'system': 'JCY5001AS电池测试系统'
                },
                'config_data': config_data
            }
            
            # 生成文件名
            safe_name = "".join(c for c in template_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_name}_{timestamp}.json"
            file_path = self.templates_dir / filename
            
            # 保存模板文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 配置模板导出成功: {template_name} -> {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"导出配置模板失败: {e}")
            return None
    
    def import_config_template(self, file_path: str, apply_immediately: bool = False) -> Optional[Dict[str, Any]]:
        """
        导入配置模板
        
        Args:
            file_path: 模板文件路径
            apply_immediately: 是否立即应用配置
            
        Returns:
            模板数据，失败返回None
        """
        try:
            # 读取模板文件
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            # 验证模板格式
            if not self._validate_template_format(template_data):
                logger.error("配置模板格式验证失败")
                return None
            
            # 如果需要立即应用
            if apply_immediately:
                success = self._apply_config_data(template_data['config_data'])
                if not success:
                    logger.error("应用配置模板失败")
                    return None
            
            logger.info(f"✅ 配置模板导入成功: {file_path}")
            return template_data
            
        except Exception as e:
            logger.error(f"导入配置模板失败: {e}")
            return None
    
    def get_template_preview(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        获取模板预览信息
        
        Args:
            file_path: 模板文件路径
            
        Returns:
            模板预览信息
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            if not self._validate_template_format(template_data):
                return None
            
            # 生成预览信息
            preview = {
                'template_info': template_data.get('template_info', {}),
                'config_summary': self._generate_config_summary(template_data.get('config_data', {}))
            }
            
            return preview
            
        except Exception as e:
            logger.error(f"获取模板预览失败: {e}")
            return None
    
    def list_available_templates(self) -> List[Dict[str, Any]]:
        """
        列出可用的配置模板
        
        Returns:
            模板列表
        """
        templates = []
        
        try:
            for file_path in self.templates_dir.glob("*.json"):
                try:
                    preview = self.get_template_preview(str(file_path))
                    if preview:
                        preview['file_path'] = str(file_path)
                        preview['file_name'] = file_path.name
                        templates.append(preview)
                except Exception as e:
                    logger.warning(f"读取模板文件失败 {file_path}: {e}")
                    continue
            
            # 按创建时间排序
            templates.sort(key=lambda x: x.get('template_info', {}).get('created_time', ''), reverse=True)
            
        except Exception as e:
            logger.error(f"列出配置模板失败: {e}")
        
        return templates
    
    def _collect_config_data(self) -> Dict[str, Any]:
        """收集当前系统配置数据"""
        config_data = {}
        
        try:
            # 频率配置
            config_data['frequency'] = {
                'research_frequencies': self.config_manager.get('frequency.research_frequencies', []),
                'production_frequencies': self.config_manager.get('frequency.production_frequencies', []),
                'current_mode': self.config_manager.get('frequency.current_mode', 'production')
            }
            
            # 档位配置
            config_data['grade'] = {
                'rs_boundaries': self.config_manager.get('grade.rs_boundaries', {}),
                'rct_boundaries': self.config_manager.get('grade.rct_boundaries', {}),
                'grade_labels': self.config_manager.get('grade.grade_labels', {}),
                'enabled': self.config_manager.get('grade.enabled', True)
            }
            
            # 测试参数
            config_data['test'] = {
                'timeout': self.config_manager.get('test.timeout', 30),
                'retry_count': self.config_manager.get('test.retry_count', 3),
                'parallel_channels': self.config_manager.get('test.parallel_channels', 8),
                'auto_start': self.config_manager.get('test.auto_start', False)
            }
            
            # 通道配置
            config_data['channels'] = {
                'enabled_channels': self.config_manager.get('channels.enabled_channels', []),
                'channel_names': self.config_manager.get('channels.channel_names', {}),
                'error_skip_enabled': self.config_manager.get('channels.error_skip_enabled', True)
            }
            
            # 设备连接参数
            config_data['device'] = {
                'connection_timeout': self.config_manager.get('device.connection_timeout', 5),
                'retry_interval': self.config_manager.get('device.retry_interval', 1),
                'max_retries': self.config_manager.get('device.max_retries', 3),
                'auto_reconnect': self.config_manager.get('device.auto_reconnect', True)
            }
            
            # 数据存储配置
            config_data['storage'] = {
                'auto_backup': self.config_manager.get('storage.auto_backup', True),
                'backup_interval': self.config_manager.get('storage.backup_interval', 24),
                'max_backup_files': self.config_manager.get('storage.max_backup_files', 10),
                'data_retention_days': self.config_manager.get('storage.data_retention_days', 30)
            }
            
            # 打印机配置
            config_data['printer'] = {
                'name': self.config_manager.get('printer.name', ''),
                'quality': self.config_manager.get('printer.quality', '最佳'),
                'density': self.config_manager.get('printer.density', 'high'),
                'auto_print': self.config_manager.get('printer.auto_print', False)
            }
            
            # 界面配置
            config_data['ui'] = {
                'theme': self.config_manager.get('ui.theme', 'default'),
                'font_size': self.config_manager.get('ui.font_size', 12),
                'auto_refresh': self.config_manager.get('ui.auto_refresh', True),
                'show_debug': self.config_manager.get('ui.show_debug', False)
            }
            
        except Exception as e:
            logger.error(f"收集配置数据失败: {e}")
        
        return config_data
    
    def _apply_config_data(self, config_data: Dict[str, Any]) -> bool:
        """应用配置数据到系统"""
        try:
            for section, settings in config_data.items():
                if isinstance(settings, dict):
                    for key, value in settings.items():
                        config_key = f"{section}.{key}"
                        self.config_manager.set(config_key, value)
                        logger.debug(f"应用配置: {config_key} = {value}")
            
            logger.info("✅ 配置数据应用成功")
            return True
            
        except Exception as e:
            logger.error(f"应用配置数据失败: {e}")
            return False
    
    def _validate_template_format(self, template_data: Dict[str, Any]) -> bool:
        """验证模板格式"""
        try:
            # 检查必需字段
            if 'template_info' not in template_data:
                logger.error("模板缺少template_info字段")
                return False
            
            if 'config_data' not in template_data:
                logger.error("模板缺少config_data字段")
                return False
            
            # 检查模板信息
            template_info = template_data['template_info']
            required_info_fields = ['name', 'version', 'created_time']
            for field in required_info_fields:
                if field not in template_info:
                    logger.error(f"模板信息缺少字段: {field}")
                    return False
            
            # 检查配置数据格式
            config_data = template_data['config_data']
            if not isinstance(config_data, dict):
                logger.error("配置数据不是字典格式")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证模板格式失败: {e}")
            return False
    
    def _generate_config_summary(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成配置摘要"""
        summary = {}
        
        try:
            # 频率配置摘要
            if 'frequency' in config_data:
                freq_config = config_data['frequency']
                summary['频率配置'] = {
                    '研究模式频率数量': len(freq_config.get('research_frequencies', [])),
                    '生产模式频率数量': len(freq_config.get('production_frequencies', [])),
                    '当前模式': freq_config.get('current_mode', '未知')
                }
            
            # 档位配置摘要
            if 'grade' in config_data:
                grade_config = config_data['grade']
                summary['档位配置'] = {
                    'Rs档位数量': len(grade_config.get('rs_boundaries', {})),
                    'Rct档位数量': len(grade_config.get('rct_boundaries', {})),
                    '档位功能': '启用' if grade_config.get('enabled', False) else '禁用'
                }
            
            # 测试参数摘要
            if 'test' in config_data:
                test_config = config_data['test']
                summary['测试参数'] = {
                    '超时时间': f"{test_config.get('timeout', 0)}秒",
                    '重试次数': test_config.get('retry_count', 0),
                    '并行通道数': test_config.get('parallel_channels', 0)
                }
            
            # 通道配置摘要
            if 'channels' in config_data:
                channel_config = config_data['channels']
                summary['通道配置'] = {
                    '启用通道数': len(channel_config.get('enabled_channels', [])),
                    '错误跳过': '启用' if channel_config.get('error_skip_enabled', False) else '禁用'
                }
            
            # 设备配置摘要
            if 'device' in config_data:
                device_config = config_data['device']
                summary['设备配置'] = {
                    '连接超时': f"{device_config.get('connection_timeout', 0)}秒",
                    '最大重试': device_config.get('max_retries', 0),
                    '自动重连': '启用' if device_config.get('auto_reconnect', False) else '禁用'
                }
            
        except Exception as e:
            logger.error(f"生成配置摘要失败: {e}")
        
        return summary
