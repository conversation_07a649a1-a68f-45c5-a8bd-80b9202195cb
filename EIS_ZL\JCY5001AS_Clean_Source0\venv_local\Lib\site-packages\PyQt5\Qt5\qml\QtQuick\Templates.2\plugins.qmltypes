import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Templates 2.15'

Module {
    dependencies: ["QtQuick 2.9", "QtQuick.Window 2.2"]
    Component {
        name: "QQuickAbstractButton"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/AbstractButton 2.0",
            "QtQuick.Templates/AbstractButton 2.2",
            "QtQuick.Templates/AbstractButton 2.3",
            "QtQuick.Templates/AbstractButton 2.4",
            "QtQuick.Templates/AbstractButton 2.5"
        ]
        exportMetaObjectRevisions: [0, 2, 3, 4, 5]
        Enum {
            name: "Display"
            values: {
                "IconOnly": 0,
                "TextOnly": 1,
                "TextBesideIcon": 2,
                "TextUnderIcon": 3
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "down"; type: "bool" }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Property { name: "checked"; type: "bool" }
        Property { name: "checkable"; type: "bool" }
        Property { name: "autoExclusive"; type: "bool" }
        Property { name: "autoRepeat"; type: "bool" }
        Property { name: "indicator"; type: "QQuickItem"; isPointer: true }
        Property { name: "icon"; revision: 3; type: "QQuickIcon" }
        Property { name: "display"; revision: 3; type: "Display" }
        Property { name: "action"; revision: 3; type: "QQuickAction"; isPointer: true }
        Property { name: "autoRepeatDelay"; revision: 4; type: "int" }
        Property { name: "autoRepeatInterval"; revision: 4; type: "int" }
        Property { name: "pressX"; revision: 4; type: "double"; isReadonly: true }
        Property { name: "pressY"; revision: 4; type: "double"; isReadonly: true }
        Property { name: "implicitIndicatorWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitIndicatorHeight"; revision: 5; type: "double"; isReadonly: true }
        Signal { name: "pressed" }
        Signal { name: "released" }
        Signal { name: "canceled" }
        Signal { name: "clicked" }
        Signal { name: "pressAndHold" }
        Signal { name: "doubleClicked" }
        Signal { name: "toggled"; revision: 2 }
        Signal { name: "iconChanged"; revision: 3 }
        Signal { name: "displayChanged"; revision: 3 }
        Signal { name: "actionChanged"; revision: 3 }
        Signal { name: "autoRepeatDelayChanged"; revision: 4 }
        Signal { name: "autoRepeatIntervalChanged"; revision: 4 }
        Signal { name: "pressXChanged"; revision: 4 }
        Signal { name: "pressYChanged"; revision: 4 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 5 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 5 }
        Method { name: "toggle" }
    }
    Component {
        name: "QQuickAction"
        prototype: "QObject"
        exports: ["QtQuick.Templates/Action 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "text"; type: "string" }
        Property { name: "icon"; type: "QQuickIcon" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "checked"; type: "bool" }
        Property { name: "checkable"; type: "bool" }
        Property { name: "shortcut"; type: "QVariant" }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "iconChanged"
            Parameter { name: "icon"; type: "QQuickIcon" }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "checkedChanged"
            Parameter { name: "checked"; type: "bool" }
        }
        Signal {
            name: "checkableChanged"
            Parameter { name: "checkable"; type: "bool" }
        }
        Signal {
            name: "shortcutChanged"
            Parameter { name: "shortcut"; type: "QKeySequence" }
        }
        Signal {
            name: "toggled"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Signal { name: "toggled" }
        Signal {
            name: "triggered"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Signal { name: "triggered" }
        Method {
            name: "toggle"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Method { name: "toggle" }
        Method {
            name: "trigger"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Method { name: "trigger" }
    }
    Component {
        name: "QQuickActionGroup"
        defaultProperty: "actions"
        prototype: "QObject"
        exports: ["QtQuick.Templates/ActionGroup 2.3"]
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickActionGroupAttached"
        Property { name: "checkedAction"; type: "QQuickAction"; isPointer: true }
        Property { name: "actions"; type: "QQuickAction"; isList: true; isReadonly: true }
        Property { name: "exclusive"; type: "bool" }
        Property { name: "enabled"; type: "bool" }
        Signal {
            name: "triggered"
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "addAction"
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "removeAction"
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
    }
    Component {
        name: "QQuickActionGroupAttached"
        prototype: "QObject"
        Property { name: "group"; type: "QQuickActionGroup"; isPointer: true }
    }
    Component {
        name: "QQuickApplicationWindow"
        defaultProperty: "contentData"
        prototype: "QQuickWindowQmlImpl"
        exports: [
            "QtQuick.Templates/ApplicationWindow 2.0",
            "QtQuick.Templates/ApplicationWindow 2.3"
        ]
        exportMetaObjectRevisions: [0, 3]
        attachedType: "QQuickApplicationWindowAttached"
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "contentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "contentData"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "activeFocusControl"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "header"; type: "QQuickItem"; isPointer: true }
        Property { name: "footer"; type: "QQuickItem"; isPointer: true }
        Property { name: "overlay"; type: "QQuickOverlay"; isReadonly: true; isPointer: true }
        Property { name: "font"; type: "QFont" }
        Property { name: "locale"; type: "QLocale" }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "menuBar"; revision: 3; type: "QQuickItem"; isPointer: true }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "menuBarChanged"; revision: 3 }
    }
    Component {
        name: "QQuickApplicationWindowAttached"
        prototype: "QObject"
        Property { name: "window"; type: "QQuickApplicationWindow"; isReadonly: true; isPointer: true }
        Property { name: "contentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "activeFocusControl"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "header"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "footer"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "overlay"; type: "QQuickOverlay"; isReadonly: true; isPointer: true }
        Property { name: "menuBar"; type: "QQuickItem"; isReadonly: true; isPointer: true }
    }
    Component {
        name: "QQuickBusyIndicator"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["QtQuick.Templates/BusyIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "running"; type: "bool" }
    }
    Component {
        name: "QQuickButton"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/Button 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "highlighted"; type: "bool" }
        Property { name: "flat"; type: "bool" }
    }
    Component {
        name: "QQuickButtonGroup"
        prototype: "QObject"
        exports: [
            "QtQuick.Templates/ButtonGroup 2.0",
            "QtQuick.Templates/ButtonGroup 2.1",
            "QtQuick.Templates/ButtonGroup 2.3",
            "QtQuick.Templates/ButtonGroup 2.4"
        ]
        exportMetaObjectRevisions: [0, 1, 3, 4]
        attachedType: "QQuickButtonGroupAttached"
        Property { name: "checkedButton"; type: "QQuickAbstractButton"; isPointer: true }
        Property { name: "buttons"; type: "QQuickAbstractButton"; isList: true; isReadonly: true }
        Property { name: "exclusive"; revision: 3; type: "bool" }
        Property { name: "checkState"; revision: 4; type: "Qt::CheckState" }
        Signal {
            name: "clicked"
            revision: 1
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Signal { name: "exclusiveChanged"; revision: 3 }
        Signal { name: "checkStateChanged"; revision: 4 }
        Method {
            name: "addButton"
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Method {
            name: "removeButton"
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
    }
    Component {
        name: "QQuickButtonGroupAttached"
        prototype: "QObject"
        Property { name: "group"; type: "QQuickButtonGroup"; isPointer: true }
    }
    Component {
        name: "QQuickCheckBox"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/CheckBox 2.0",
            "QtQuick.Templates/CheckBox 2.4"
        ]
        exportMetaObjectRevisions: [0, 4]
        Property { name: "tristate"; type: "bool" }
        Property { name: "checkState"; type: "Qt::CheckState" }
        Property { name: "nextCheckState"; revision: 4; type: "QJSValue" }
        Signal { name: "nextCheckStateChanged"; revision: 4 }
    }
    Component {
        name: "QQuickCheckDelegate"
        defaultProperty: "data"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/CheckDelegate 2.0",
            "QtQuick.Templates/CheckDelegate 2.4"
        ]
        exportMetaObjectRevisions: [0, 4]
        Property { name: "tristate"; type: "bool" }
        Property { name: "checkState"; type: "Qt::CheckState" }
        Property { name: "nextCheckState"; revision: 4; type: "QJSValue" }
        Signal { name: "nextCheckStateChanged"; revision: 4 }
    }
    Component {
        name: "QQuickComboBox"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ComboBox 2.0",
            "QtQuick.Templates/ComboBox 2.1",
            "QtQuick.Templates/ComboBox 2.14",
            "QtQuick.Templates/ComboBox 2.15",
            "QtQuick.Templates/ComboBox 2.2",
            "QtQuick.Templates/ComboBox 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 14, 15, 2, 5]
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegateModel"; type: "QQmlInstanceModel"; isReadonly: true; isPointer: true }
        Property { name: "pressed"; type: "bool" }
        Property { name: "highlightedIndex"; type: "int"; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "currentText"; type: "string"; isReadonly: true }
        Property { name: "displayText"; type: "string" }
        Property { name: "textRole"; type: "string" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "indicator"; type: "QQuickItem"; isPointer: true }
        Property { name: "popup"; type: "QQuickPopup"; isPointer: true }
        Property { name: "flat"; revision: 1; type: "bool" }
        Property { name: "down"; revision: 2; type: "bool" }
        Property { name: "editable"; revision: 2; type: "bool" }
        Property { name: "editText"; revision: 2; type: "string" }
        Property { name: "validator"; revision: 2; type: "QValidator"; isPointer: true }
        Property { name: "inputMethodHints"; revision: 2; type: "Qt::InputMethodHints" }
        Property { name: "inputMethodComposing"; revision: 2; type: "bool"; isReadonly: true }
        Property { name: "acceptableInput"; revision: 2; type: "bool"; isReadonly: true }
        Property { name: "implicitIndicatorWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitIndicatorHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "currentValue"; revision: 14; type: "QVariant"; isReadonly: true }
        Property { name: "valueRole"; revision: 14; type: "string" }
        Property { name: "selectTextByMouse"; revision: 15; type: "bool" }
        Signal {
            name: "activated"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "highlighted"
            Parameter { name: "index"; type: "int" }
        }
        Signal { name: "flatChanged"; revision: 1 }
        Signal { name: "accepted"; revision: 2 }
        Signal { name: "downChanged"; revision: 2 }
        Signal { name: "editableChanged"; revision: 2 }
        Signal { name: "editTextChanged"; revision: 2 }
        Signal { name: "validatorChanged"; revision: 2 }
        Signal { name: "inputMethodHintsChanged"; revision: 2 }
        Signal { name: "inputMethodComposingChanged"; revision: 2 }
        Signal { name: "acceptableInputChanged"; revision: 2 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 5 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 5 }
        Signal { name: "valueRoleChanged"; revision: 14 }
        Signal { name: "currentValueChanged"; revision: 14 }
        Signal { name: "selectTextByMouseChanged"; revision: 15 }
        Method { name: "incrementCurrentIndex" }
        Method { name: "decrementCurrentIndex" }
        Method { name: "selectAll"; revision: 2 }
        Method {
            name: "textAt"
            type: "string"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "find"
            type: "int"
            Parameter { name: "text"; type: "string" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "find"
            type: "int"
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "valueAt"
            revision: 14
            type: "QVariant"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "indexOfValue"
            revision: 14
            type: "int"
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        name: "QQuickContainer"
        defaultProperty: "contentData"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Container 2.0",
            "QtQuick.Templates/Container 2.1",
            "QtQuick.Templates/Container 2.3",
            "QtQuick.Templates/Container 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 3, 5]
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "contentModel"; type: "QVariant"; isReadonly: true }
        Property { name: "contentData"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "contentChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "currentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "contentWidth"; revision: 5; type: "double" }
        Property { name: "contentHeight"; revision: 5; type: "double" }
        Signal { name: "contentWidthChanged"; revision: 5 }
        Signal { name: "contentHeightChanged"; revision: 5 }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "incrementCurrentIndex"; revision: 1 }
        Method { name: "decrementCurrentIndex"; revision: 1 }
        Method {
            name: "itemAt"
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "insertItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "moveItem"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
        }
        Method {
            name: "removeItem"
            Parameter { name: "item"; type: "QVariant" }
        }
        Method {
            name: "takeItem"
            revision: 3
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component { name: "QQuickContentItem"; defaultProperty: "data"; prototype: "QQuickItem" }
    Component {
        name: "QQuickControl"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Templates/Control 2.0",
            "QtQuick.Templates/Control 2.3",
            "QtQuick.Templates/Control 2.5"
        ]
        exportMetaObjectRevisions: [0, 3, 5]
        Property { name: "font"; type: "QFont" }
        Property { name: "availableWidth"; type: "double"; isReadonly: true }
        Property { name: "availableHeight"; type: "double"; isReadonly: true }
        Property { name: "padding"; type: "double" }
        Property { name: "topPadding"; type: "double" }
        Property { name: "leftPadding"; type: "double" }
        Property { name: "rightPadding"; type: "double" }
        Property { name: "bottomPadding"; type: "double" }
        Property { name: "spacing"; type: "double" }
        Property { name: "locale"; type: "QLocale" }
        Property { name: "mirrored"; type: "bool"; isReadonly: true }
        Property { name: "focusPolicy"; type: "Qt::FocusPolicy" }
        Property { name: "focusReason"; type: "Qt::FocusReason" }
        Property { name: "visualFocus"; type: "bool"; isReadonly: true }
        Property { name: "hovered"; type: "bool"; isReadonly: true }
        Property { name: "hoverEnabled"; type: "bool" }
        Property { name: "wheelEnabled"; type: "bool" }
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "horizontalPadding"; revision: 5; type: "double" }
        Property { name: "verticalPadding"; revision: 5; type: "double" }
        Property { name: "implicitContentWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitContentHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "topInset"; revision: 5; type: "double" }
        Property { name: "leftInset"; revision: 5; type: "double" }
        Property { name: "rightInset"; revision: 5; type: "double" }
        Property { name: "bottomInset"; revision: 5; type: "double" }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "horizontalPaddingChanged"; revision: 5 }
        Signal { name: "verticalPaddingChanged"; revision: 5 }
        Signal { name: "implicitContentWidthChanged"; revision: 5 }
        Signal { name: "implicitContentHeightChanged"; revision: 5 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 5 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 5 }
        Signal { name: "topInsetChanged"; revision: 5 }
        Signal { name: "leftInsetChanged"; revision: 5 }
        Signal { name: "rightInsetChanged"; revision: 5 }
        Signal { name: "bottomInsetChanged"; revision: 5 }
    }
    Component {
        name: "QQuickDelayButton"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/DelayButton 2.2"]
        exportMetaObjectRevisions: [0]
        Property { name: "delay"; type: "int" }
        Property { name: "progress"; type: "double" }
        Property { name: "transition"; type: "QQuickTransition"; isPointer: true }
        Signal { name: "activated" }
    }
    Component {
        name: "QQuickDial"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Dial 2.0",
            "QtQuick.Templates/Dial 2.2",
            "QtQuick.Templates/Dial 2.5"
        ]
        exportMetaObjectRevisions: [0, 2, 5]
        Enum {
            name: "SnapMode"
            values: {
                "NoSnap": 0,
                "SnapAlways": 1,
                "SnapOnRelease": 2
            }
        }
        Enum {
            name: "InputMode"
            values: {
                "Circular": 0,
                "Horizontal": 1,
                "Vertical": 2
            }
        }
        Property { name: "from"; type: "double" }
        Property { name: "to"; type: "double" }
        Property { name: "value"; type: "double" }
        Property { name: "position"; type: "double"; isReadonly: true }
        Property { name: "angle"; type: "double"; isReadonly: true }
        Property { name: "stepSize"; type: "double" }
        Property { name: "snapMode"; type: "SnapMode" }
        Property { name: "wrap"; type: "bool" }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Property { name: "handle"; type: "QQuickItem"; isPointer: true }
        Property { name: "live"; revision: 2; type: "bool" }
        Property { name: "inputMode"; revision: 5; type: "InputMode" }
        Signal { name: "moved"; revision: 2 }
        Signal { name: "liveChanged"; revision: 2 }
        Signal { name: "inputModeChanged"; revision: 5 }
        Method { name: "increase" }
        Method { name: "decrease" }
    }
    Component {
        name: "QQuickDialog"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: [
            "QtQuick.Templates/Dialog 2.1",
            "QtQuick.Templates/Dialog 2.3",
            "QtQuick.Templates/Dialog 2.5"
        ]
        exportMetaObjectRevisions: [0, 3, 5]
        Enum {
            name: "StandardCode"
            values: {
                "Rejected": 0,
                "Accepted": 1
            }
        }
        Property { name: "title"; type: "string" }
        Property { name: "header"; type: "QQuickItem"; isPointer: true }
        Property { name: "footer"; type: "QQuickItem"; isPointer: true }
        Property { name: "standardButtons"; type: "QPlatformDialogHelper::StandardButtons" }
        Property { name: "result"; revision: 3; type: "int" }
        Property { name: "implicitHeaderWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitHeaderHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitFooterWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitFooterHeight"; revision: 5; type: "double"; isReadonly: true }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "applied"; revision: 3 }
        Signal { name: "reset"; revision: 3 }
        Signal { name: "discarded"; revision: 3 }
        Signal { name: "helpRequested"; revision: 3 }
        Signal { name: "resultChanged"; revision: 3 }
        Method { name: "accept" }
        Method { name: "reject" }
        Method {
            name: "done"
            Parameter { name: "result"; type: "int" }
        }
        Method {
            name: "standardButton"
            revision: 3
            type: "QQuickAbstractButton*"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
    }
    Component {
        name: "QQuickDialogButtonBox"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/DialogButtonBox 2.1",
            "QtQuick.Templates/DialogButtonBox 2.3",
            "QtQuick.Templates/DialogButtonBox 2.5"
        ]
        exportMetaObjectRevisions: [0, 3, 5]
        attachedType: "QQuickDialogButtonBoxAttached"
        Enum {
            name: "Position"
            values: {
                "Header": 0,
                "Footer": 1
            }
        }
        Property { name: "position"; type: "Position" }
        Property { name: "alignment"; type: "Qt::Alignment" }
        Property { name: "standardButtons"; type: "QPlatformDialogHelper::StandardButtons" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "buttonLayout"; revision: 5; type: "QPlatformDialogHelper::ButtonLayout" }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "helpRequested" }
        Signal {
            name: "clicked"
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Signal { name: "applied"; revision: 3 }
        Signal { name: "reset"; revision: 3 }
        Signal { name: "discarded"; revision: 3 }
        Signal { name: "buttonLayoutChanged"; revision: 5 }
        Method {
            name: "standardButton"
            type: "QQuickAbstractButton*"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
    }
    Component {
        name: "QQuickDialogButtonBoxAttached"
        prototype: "QObject"
        Property { name: "buttonBox"; type: "QQuickDialogButtonBox"; isReadonly: true; isPointer: true }
        Property { name: "buttonRole"; type: "QPlatformDialogHelper::ButtonRole" }
    }
    Component {
        name: "QQuickDrawer"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: [
            "QtQuick.Templates/Drawer 2.0",
            "QtQuick.Templates/Drawer 2.2"
        ]
        exportMetaObjectRevisions: [0, 2]
        Property { name: "edge"; type: "Qt::Edge" }
        Property { name: "position"; type: "double" }
        Property { name: "dragMargin"; type: "double" }
        Property { name: "interactive"; revision: 2; type: "bool" }
        Signal { name: "interactiveChanged"; revision: 2 }
    }
    Component {
        name: "QQuickFrame"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: ["QtQuick.Templates/Frame 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickGroupBox"
        defaultProperty: "contentData"
        prototype: "QQuickFrame"
        exports: [
            "QtQuick.Templates/GroupBox 2.0",
            "QtQuick.Templates/GroupBox 2.5"
        ]
        exportMetaObjectRevisions: [0, 5]
        Property { name: "title"; type: "string" }
        Property { name: "label"; type: "QQuickItem"; isPointer: true }
        Property { name: "implicitLabelWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitLabelHeight"; revision: 5; type: "double"; isReadonly: true }
        Signal { name: "implicitLabelWidthChanged"; revision: 5 }
        Signal { name: "implicitLabelHeightChanged"; revision: 5 }
    }
    Component {
        name: "QQuickHeaderViewBase"
        defaultProperty: "flickableData"
        prototype: "QQuickTableView"
        Property { name: "textRole"; type: "string" }
    }
    Component {
        name: "QQuickHorizontalHeaderView"
        defaultProperty: "flickableData"
        prototype: "QQuickHeaderViewBase"
        exports: ["QtQuick.Templates/HorizontalHeaderView 2.15"]
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickTableViewAttached"
    }
    Component {
        name: "QQuickIcon"
        Property { name: "name"; type: "string" }
        Property { name: "source"; type: "QUrl" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Property { name: "color"; type: "QColor" }
        Property { name: "cache"; type: "bool" }
    }
    Component {
        name: "QQuickImplicitSizeItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Property { name: "implicitWidth"; type: "double"; isReadonly: true }
        Property { name: "implicitHeight"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickItem"
        defaultProperty: "data"
        prototype: "QObject"
        Enum {
            name: "Flags"
            values: {
                "ItemClipsChildrenToShape": 1,
                "ItemAcceptsInputMethod": 2,
                "ItemIsFocusScope": 4,
                "ItemHasContents": 8,
                "ItemAcceptsDrops": 16
            }
        }
        Enum {
            name: "TransformOrigin"
            values: {
                "TopLeft": 0,
                "Top": 1,
                "TopRight": 2,
                "Left": 3,
                "Center": 4,
                "Right": 5,
                "BottomLeft": 6,
                "Bottom": 7,
                "BottomRight": 8
            }
        }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "opacity"; type: "double" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Property { name: "childrenRect"; type: "QRectF"; isReadonly: true }
        Property { name: "anchors"; type: "QQuickAnchors"; isReadonly: true; isPointer: true }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusOnTab"; revision: 1; type: "bool" }
        Property { name: "rotation"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "transformOriginPoint"; type: "QPointF"; isReadonly: true }
        Property { name: "transform"; type: "QQuickTransform"; isList: true; isReadonly: true }
        Property { name: "smooth"; type: "bool" }
        Property { name: "antialiasing"; type: "bool" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "containmentMask"; revision: 11; type: "QObject"; isPointer: true }
        Property { name: "layer"; type: "QQuickItemLayer"; isReadonly: true; isPointer: true }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 1
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 1
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "containmentMaskChanged"; revision: 11 }
        Method { name: "update" }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 1
            type: "QQuickItem*"
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 1; type: "QQuickItem*" }
        Method {
            name: "childAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickItemDelegate"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/ItemDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "highlighted"; type: "bool" }
    }
    Component {
        name: "QQuickLabel"
        defaultProperty: "data"
        prototype: "QQuickText"
        exports: [
            "QtQuick.Templates/Label 2.0",
            "QtQuick.Templates/Label 2.3",
            "QtQuick.Templates/Label 2.5"
        ]
        exportMetaObjectRevisions: [0, 3, 5]
        Property { name: "font"; type: "QFont" }
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "implicitBackgroundWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "topInset"; revision: 5; type: "double" }
        Property { name: "leftInset"; revision: 5; type: "double" }
        Property { name: "rightInset"; revision: 5; type: "double" }
        Property { name: "bottomInset"; revision: 5; type: "double" }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 5 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 5 }
        Signal { name: "topInsetChanged"; revision: 5 }
        Signal { name: "leftInsetChanged"; revision: 5 }
        Signal { name: "rightInsetChanged"; revision: 5 }
        Signal { name: "bottomInsetChanged"; revision: 5 }
    }
    Component {
        name: "QQuickMenu"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: ["QtQuick.Templates/Menu 2.0", "QtQuick.Templates/Menu 2.3"]
        exportMetaObjectRevisions: [0, 3]
        Property { name: "contentModel"; type: "QVariant"; isReadonly: true }
        Property { name: "contentData"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "title"; type: "string" }
        Property { name: "count"; revision: 3; type: "int"; isReadonly: true }
        Property { name: "cascade"; revision: 3; type: "bool" }
        Property { name: "overlap"; revision: 3; type: "double" }
        Property { name: "delegate"; revision: 3; type: "QQmlComponent"; isPointer: true }
        Property { name: "currentIndex"; revision: 3; type: "int" }
        Signal {
            name: "titleChanged"
            Parameter { name: "title"; type: "string" }
        }
        Signal { name: "countChanged"; revision: 3 }
        Signal {
            name: "cascadeChanged"
            revision: 3
            Parameter { name: "cascade"; type: "bool" }
        }
        Signal { name: "overlapChanged"; revision: 3 }
        Signal { name: "delegateChanged"; revision: 3 }
        Signal { name: "currentIndexChanged"; revision: 3 }
        Method {
            name: "itemAt"
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "insertItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "moveItem"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
        }
        Method {
            name: "removeItem"
            Parameter { name: "item"; type: "QVariant" }
        }
        Method {
            name: "takeItem"
            revision: 3
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "menuAt"
            revision: 3
            type: "QQuickMenu*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addMenu"
            revision: 3
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "insertMenu"
            revision: 3
            Parameter { name: "index"; type: "int" }
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "removeMenu"
            revision: 3
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "takeMenu"
            revision: 3
            type: "QQuickMenu*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "actionAt"
            revision: 3
            type: "QQuickAction*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addAction"
            revision: 3
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "insertAction"
            revision: 3
            Parameter { name: "index"; type: "int" }
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "removeAction"
            revision: 3
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "takeAction"
            revision: 3
            type: "QQuickAction*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "popup"
            revision: 3
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "dismiss"; revision: 3 }
    }
    Component {
        name: "QQuickMenuBar"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: ["QtQuick.Templates/MenuBar 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "contentWidth"; type: "double" }
        Property { name: "contentHeight"; type: "double" }
        Property { name: "menus"; type: "QQuickMenu"; isList: true; isReadonly: true }
        Property { name: "contentData"; type: "QObject"; isList: true; isReadonly: true }
        Method {
            name: "menuAt"
            type: "QQuickMenu*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addMenu"
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "insertMenu"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "removeMenu"
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "takeMenu"
            type: "QQuickMenu*"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        name: "QQuickMenuBarItem"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/MenuBarItem 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "menuBar"; type: "QQuickMenuBar"; isReadonly: true; isPointer: true }
        Property { name: "menu"; type: "QQuickMenu"; isPointer: true }
        Property { name: "highlighted"; type: "bool" }
        Signal { name: "triggered" }
    }
    Component {
        name: "QQuickMenuItem"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/MenuItem 2.0",
            "QtQuick.Templates/MenuItem 2.3"
        ]
        exportMetaObjectRevisions: [0, 3]
        Property { name: "highlighted"; type: "bool" }
        Property { name: "arrow"; revision: 3; type: "QQuickItem"; isPointer: true }
        Property { name: "menu"; revision: 3; type: "QQuickMenu"; isReadonly: true; isPointer: true }
        Property { name: "subMenu"; revision: 3; type: "QQuickMenu"; isReadonly: true; isPointer: true }
        Signal { name: "triggered" }
        Signal { name: "arrowChanged"; revision: 3 }
        Signal { name: "menuChanged"; revision: 3 }
        Signal { name: "subMenuChanged"; revision: 3 }
    }
    Component {
        name: "QQuickMenuSeparator"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["QtQuick.Templates/MenuSeparator 2.1"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickOverlay"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Templates/Overlay 2.3"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickOverlayAttached"
        Property { name: "modal"; type: "QQmlComponent"; isPointer: true }
        Property { name: "modeless"; type: "QQmlComponent"; isPointer: true }
        Signal { name: "pressed" }
        Signal { name: "released" }
    }
    Component {
        name: "QQuickOverlayAttached"
        prototype: "QObject"
        Property { name: "overlay"; type: "QQuickOverlay"; isReadonly: true; isPointer: true }
        Property { name: "modal"; type: "QQmlComponent"; isPointer: true }
        Property { name: "modeless"; type: "QQmlComponent"; isPointer: true }
        Signal { name: "pressed" }
        Signal { name: "released" }
    }
    Component {
        name: "QQuickPage"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: [
            "QtQuick.Templates/Page 2.0",
            "QtQuick.Templates/Page 2.1",
            "QtQuick.Templates/Page 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 5]
        Property { name: "title"; type: "string" }
        Property { name: "header"; type: "QQuickItem"; isPointer: true }
        Property { name: "footer"; type: "QQuickItem"; isPointer: true }
        Property { name: "contentWidth"; revision: 1; type: "double" }
        Property { name: "contentHeight"; revision: 1; type: "double" }
        Property { name: "implicitHeaderWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitHeaderHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitFooterWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitFooterHeight"; revision: 5; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickPageIndicator"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["QtQuick.Templates/PageIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "count"; type: "int" }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "interactive"; type: "bool" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
    }
    Component {
        name: "QQuickPane"
        defaultProperty: "contentData"
        prototype: "QQuickControl"
        exports: ["QtQuick.Templates/Pane 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "contentWidth"; type: "double" }
        Property { name: "contentHeight"; type: "double" }
        Property { name: "contentData"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "contentChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
    }
    Component {
        name: "QQuickPopup"
        defaultProperty: "contentData"
        prototype: "QObject"
        exports: [
            "QtQuick.Templates/Popup 2.0",
            "QtQuick.Templates/Popup 2.1",
            "QtQuick.Templates/Popup 2.3",
            "QtQuick.Templates/Popup 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 3, 5]
        Enum {
            name: "ClosePolicy"
            values: {
                "NoAutoClose": 0,
                "CloseOnPressOutside": 1,
                "CloseOnPressOutsideParent": 2,
                "CloseOnReleaseOutside": 4,
                "CloseOnReleaseOutsideParent": 8,
                "CloseOnEscape": 16
            }
        }
        Enum {
            name: "TransformOrigin"
            values: {
                "TopLeft": 0,
                "Top": 1,
                "TopRight": 2,
                "Left": 3,
                "Center": 4,
                "Right": 5,
                "BottomLeft": 6,
                "Bottom": 7,
                "BottomRight": 8
            }
        }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "contentWidth"; type: "double" }
        Property { name: "contentHeight"; type: "double" }
        Property { name: "availableWidth"; type: "double"; isReadonly: true }
        Property { name: "availableHeight"; type: "double"; isReadonly: true }
        Property { name: "margins"; type: "double" }
        Property { name: "topMargin"; type: "double" }
        Property { name: "leftMargin"; type: "double" }
        Property { name: "rightMargin"; type: "double" }
        Property { name: "bottomMargin"; type: "double" }
        Property { name: "padding"; type: "double" }
        Property { name: "topPadding"; type: "double" }
        Property { name: "leftPadding"; type: "double" }
        Property { name: "rightPadding"; type: "double" }
        Property { name: "bottomPadding"; type: "double" }
        Property { name: "locale"; type: "QLocale" }
        Property { name: "font"; type: "QFont" }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
        Property { name: "contentData"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "contentChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "modal"; type: "bool" }
        Property { name: "dim"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "opacity"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "closePolicy"; type: "ClosePolicy" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "enter"; type: "QQuickTransition"; isPointer: true }
        Property { name: "exit"; type: "QQuickTransition"; isPointer: true }
        Property { name: "spacing"; revision: 1; type: "double" }
        Property { name: "opened"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "mirrored"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "enabled"; revision: 3; type: "bool" }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "horizontalPadding"; type: "double" }
        Property { name: "verticalPadding"; type: "double" }
        Property {
            name: "anchors"
            revision: 5
            type: "QQuickPopupAnchors"
            isReadonly: true
            isPointer: true
        }
        Property { name: "implicitContentWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitContentHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "topInset"; revision: 5; type: "double" }
        Property { name: "leftInset"; revision: 5; type: "double" }
        Property { name: "rightInset"; revision: 5; type: "double" }
        Property { name: "bottomInset"; revision: 5; type: "double" }
        Signal { name: "opened" }
        Signal { name: "closed" }
        Signal { name: "aboutToShow" }
        Signal { name: "aboutToHide" }
        Signal {
            name: "windowChanged"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "spacingChanged"; revision: 1 }
        Signal { name: "openedChanged"; revision: 3 }
        Signal { name: "mirroredChanged"; revision: 3 }
        Signal { name: "enabledChanged"; revision: 3 }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "horizontalPaddingChanged"; revision: 5 }
        Signal { name: "verticalPaddingChanged"; revision: 5 }
        Signal { name: "implicitContentWidthChanged"; revision: 5 }
        Signal { name: "implicitContentHeightChanged"; revision: 5 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 5 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 5 }
        Signal { name: "topInsetChanged"; revision: 5 }
        Signal { name: "leftInsetChanged"; revision: 5 }
        Signal { name: "rightInsetChanged"; revision: 5 }
        Signal { name: "bottomInsetChanged"; revision: 5 }
        Method { name: "open" }
        Method { name: "close" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method { name: "forceActiveFocus" }
    }
    Component {
        name: "QQuickPopupAnchors"
        prototype: "QObject"
        Property { name: "centerIn"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        name: "QQuickProgressBar"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["QtQuick.Templates/ProgressBar 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "from"; type: "double" }
        Property { name: "to"; type: "double" }
        Property { name: "value"; type: "double" }
        Property { name: "position"; type: "double"; isReadonly: true }
        Property { name: "visualPosition"; type: "double"; isReadonly: true }
        Property { name: "indeterminate"; type: "bool" }
    }
    Component {
        name: "QQuickRadioButton"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/RadioButton 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickRadioDelegate"
        defaultProperty: "data"
        prototype: "QQuickItemDelegate"
        exports: ["QtQuick.Templates/RadioDelegate 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickRangeSlider"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/RangeSlider 2.0",
            "QtQuick.Templates/RangeSlider 2.1",
            "QtQuick.Templates/RangeSlider 2.2",
            "QtQuick.Templates/RangeSlider 2.3",
            "QtQuick.Templates/RangeSlider 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 2, 3, 5]
        Enum {
            name: "SnapMode"
            values: {
                "NoSnap": 0,
                "SnapAlways": 1,
                "SnapOnRelease": 2
            }
        }
        Property { name: "from"; type: "double" }
        Property { name: "to"; type: "double" }
        Property { name: "first"; type: "QQuickRangeSliderNode"; isReadonly: true; isPointer: true }
        Property { name: "second"; type: "QQuickRangeSliderNode"; isReadonly: true; isPointer: true }
        Property { name: "stepSize"; type: "double" }
        Property { name: "snapMode"; type: "SnapMode" }
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "live"; revision: 2; type: "bool" }
        Property { name: "horizontal"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "vertical"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "touchDragThreshold"; revision: 5; type: "double" }
        Signal { name: "liveChanged"; revision: 2 }
        Signal { name: "touchDragThresholdChanged"; revision: 5 }
        Method {
            name: "setValues"
            Parameter { name: "firstValue"; type: "double" }
            Parameter { name: "secondValue"; type: "double" }
        }
        Method {
            name: "valueAt"
            revision: 5
            type: "double"
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        name: "QQuickRangeSliderNode"
        prototype: "QObject"
        Property { name: "value"; type: "double" }
        Property { name: "position"; type: "double"; isReadonly: true }
        Property { name: "visualPosition"; type: "double"; isReadonly: true }
        Property { name: "handle"; type: "QQuickItem"; isPointer: true }
        Property { name: "pressed"; type: "bool" }
        Property { name: "hovered"; revision: 1; type: "bool" }
        Property { name: "implicitHandleWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitHandleHeight"; revision: 5; type: "double"; isReadonly: true }
        Signal { name: "hoveredChanged"; revision: 1 }
        Signal { name: "moved" }
        Method { name: "increase" }
        Method { name: "decrease" }
    }
    Component {
        name: "QQuickRoundButton"
        defaultProperty: "data"
        prototype: "QQuickButton"
        exports: ["QtQuick.Templates/RoundButton 2.1"]
        exportMetaObjectRevisions: [0]
        Property { name: "radius"; type: "double" }
    }
    Component {
        name: "QQuickScrollBar"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ScrollBar 2.0",
            "QtQuick.Templates/ScrollBar 2.2",
            "QtQuick.Templates/ScrollBar 2.3",
            "QtQuick.Templates/ScrollBar 2.4"
        ]
        exportMetaObjectRevisions: [0, 2, 3, 4]
        attachedType: "QQuickScrollBarAttached"
        Enum {
            name: "SnapMode"
            values: {
                "NoSnap": 0,
                "SnapAlways": 1,
                "SnapOnRelease": 2
            }
        }
        Enum {
            name: "Policy"
            values: {
                "AsNeeded": 0,
                "AlwaysOff": 1,
                "AlwaysOn": 2
            }
        }
        Property { name: "size"; type: "double" }
        Property { name: "position"; type: "double" }
        Property { name: "stepSize"; type: "double" }
        Property { name: "active"; type: "bool" }
        Property { name: "pressed"; type: "bool" }
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "snapMode"; revision: 2; type: "SnapMode" }
        Property { name: "interactive"; revision: 2; type: "bool" }
        Property { name: "policy"; revision: 2; type: "Policy" }
        Property { name: "horizontal"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "vertical"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "minimumSize"; revision: 4; type: "double" }
        Property { name: "visualSize"; revision: 4; type: "double"; isReadonly: true }
        Property { name: "visualPosition"; revision: 4; type: "double"; isReadonly: true }
        Signal { name: "snapModeChanged"; revision: 2 }
        Signal { name: "interactiveChanged"; revision: 2 }
        Signal { name: "policyChanged"; revision: 2 }
        Signal { name: "minimumSizeChanged"; revision: 4 }
        Signal { name: "visualSizeChanged"; revision: 4 }
        Signal { name: "visualPositionChanged"; revision: 4 }
        Method { name: "increase" }
        Method { name: "decrease" }
        Method {
            name: "setSize"
            Parameter { name: "size"; type: "double" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        name: "QQuickScrollBarAttached"
        prototype: "QObject"
        Property { name: "horizontal"; type: "QQuickScrollBar"; isPointer: true }
        Property { name: "vertical"; type: "QQuickScrollBar"; isPointer: true }
    }
    Component {
        name: "QQuickScrollIndicator"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ScrollIndicator 2.0",
            "QtQuick.Templates/ScrollIndicator 2.3",
            "QtQuick.Templates/ScrollIndicator 2.4"
        ]
        exportMetaObjectRevisions: [0, 3, 4]
        attachedType: "QQuickScrollIndicatorAttached"
        Property { name: "size"; type: "double" }
        Property { name: "position"; type: "double" }
        Property { name: "active"; type: "bool" }
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "horizontal"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "vertical"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "minimumSize"; revision: 4; type: "double" }
        Property { name: "visualSize"; revision: 4; type: "double"; isReadonly: true }
        Property { name: "visualPosition"; revision: 4; type: "double"; isReadonly: true }
        Signal { name: "minimumSizeChanged"; revision: 4 }
        Signal { name: "visualSizeChanged"; revision: 4 }
        Signal { name: "visualPositionChanged"; revision: 4 }
        Method {
            name: "setSize"
            Parameter { name: "size"; type: "double" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        name: "QQuickScrollIndicatorAttached"
        prototype: "QObject"
        Property { name: "horizontal"; type: "QQuickScrollIndicator"; isPointer: true }
        Property { name: "vertical"; type: "QQuickScrollIndicator"; isPointer: true }
    }
    Component {
        name: "QQuickScrollView"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: ["QtQuick.Templates/ScrollView 2.2"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickSlider"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Slider 2.0",
            "QtQuick.Templates/Slider 2.1",
            "QtQuick.Templates/Slider 2.2",
            "QtQuick.Templates/Slider 2.3",
            "QtQuick.Templates/Slider 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 2, 3, 5]
        Enum {
            name: "SnapMode"
            values: {
                "NoSnap": 0,
                "SnapAlways": 1,
                "SnapOnRelease": 2
            }
        }
        Property { name: "from"; type: "double" }
        Property { name: "to"; type: "double" }
        Property { name: "value"; type: "double" }
        Property { name: "position"; type: "double"; isReadonly: true }
        Property { name: "visualPosition"; type: "double"; isReadonly: true }
        Property { name: "stepSize"; type: "double" }
        Property { name: "snapMode"; type: "SnapMode" }
        Property { name: "pressed"; type: "bool" }
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "handle"; type: "QQuickItem"; isPointer: true }
        Property { name: "live"; revision: 2; type: "bool" }
        Property { name: "horizontal"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "vertical"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "touchDragThreshold"; revision: 5; type: "double" }
        Property { name: "implicitHandleWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitHandleHeight"; revision: 5; type: "double"; isReadonly: true }
        Signal { name: "moved"; revision: 2 }
        Signal { name: "liveChanged"; revision: 2 }
        Signal { name: "touchDragThresholdChanged"; revision: 5 }
        Signal { name: "implicitHandleWidthChanged"; revision: 5 }
        Signal { name: "implicitHandleHeightChanged"; revision: 5 }
        Method { name: "increase" }
        Method { name: "decrease" }
        Method {
            name: "valueAt"
            revision: 1
            type: "double"
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        name: "QQuickSpinBox"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/SpinBox 2.0",
            "QtQuick.Templates/SpinBox 2.1",
            "QtQuick.Templates/SpinBox 2.2",
            "QtQuick.Templates/SpinBox 2.3",
            "QtQuick.Templates/SpinBox 2.4",
            "QtQuick.Templates/SpinBox 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 2, 3, 4, 5]
        Property { name: "from"; type: "int" }
        Property { name: "to"; type: "int" }
        Property { name: "value"; type: "int" }
        Property { name: "stepSize"; type: "int" }
        Property { name: "editable"; type: "bool" }
        Property { name: "validator"; type: "QValidator"; isPointer: true }
        Property { name: "textFromValue"; type: "QJSValue" }
        Property { name: "valueFromText"; type: "QJSValue" }
        Property { name: "up"; type: "QQuickSpinButton"; isReadonly: true; isPointer: true }
        Property { name: "down"; type: "QQuickSpinButton"; isReadonly: true; isPointer: true }
        Property { name: "inputMethodHints"; revision: 2; type: "Qt::InputMethodHints" }
        Property { name: "inputMethodComposing"; revision: 2; type: "bool"; isReadonly: true }
        Property { name: "wrap"; revision: 3; type: "bool" }
        Property { name: "displayText"; revision: 4; type: "string"; isReadonly: true }
        Signal { name: "valueModified"; revision: 2 }
        Signal { name: "inputMethodHintsChanged"; revision: 2 }
        Signal { name: "inputMethodComposingChanged"; revision: 2 }
        Signal { name: "wrapChanged"; revision: 3 }
        Signal { name: "displayTextChanged"; revision: 4 }
        Method { name: "increase" }
        Method { name: "decrease" }
    }
    Component {
        name: "QQuickSpinButton"
        prototype: "QObject"
        Property { name: "pressed"; type: "bool" }
        Property { name: "indicator"; type: "QQuickItem"; isPointer: true }
        Property { name: "hovered"; revision: 1; type: "bool" }
        Property { name: "implicitIndicatorWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitIndicatorHeight"; revision: 5; type: "double"; isReadonly: true }
        Signal { name: "hoveredChanged"; revision: 1 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 5 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 5 }
    }
    Component {
        name: "QQuickSplitHandleAttached"
        prototype: "QObject"
        exports: ["QtQuick.Templates/SplitHandle 2.13"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "hovered"; type: "bool"; isReadonly: true }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
    }
    Component {
        name: "QQuickSplitView"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: ["QtQuick.Templates/SplitView 2.13"]
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickSplitViewAttached"
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "resizing"; type: "bool"; isReadonly: true }
        Property { name: "handle"; type: "QQmlComponent"; isPointer: true }
        Method { name: "saveState"; type: "QVariant" }
        Method {
            name: "restoreState"
            type: "bool"
            Parameter { name: "state"; type: "QVariant" }
        }
    }
    Component {
        name: "QQuickSplitViewAttached"
        prototype: "QObject"
        Property { name: "view"; type: "QQuickSplitView"; isReadonly: true; isPointer: true }
        Property { name: "minimumWidth"; type: "double" }
        Property { name: "minimumHeight"; type: "double" }
        Property { name: "preferredWidth"; type: "double" }
        Property { name: "preferredHeight"; type: "double" }
        Property { name: "maximumWidth"; type: "double" }
        Property { name: "maximumHeight"; type: "double" }
        Property { name: "fillHeight"; type: "bool" }
        Property { name: "fillWidth"; type: "bool" }
    }
    Component {
        name: "QQuickStackView"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/StackView 2.0",
            "QtQuick.Templates/StackView 2.1"
        ]
        exportMetaObjectRevisions: [0, 1]
        attachedType: "QQuickStackViewAttached"
        Enum {
            name: "Status"
            values: {
                "Inactive": 0,
                "Deactivating": 1,
                "Activating": 2,
                "Active": 3
            }
        }
        Enum {
            name: "LoadBehavior"
            values: {
                "DontLoad": 0,
                "ForceLoad": 1
            }
        }
        Enum {
            name: "Operation"
            values: {
                "Transition": -1,
                "Immediate": 0,
                "PushTransition": 1,
                "ReplaceTransition": 2,
                "PopTransition": 3
            }
        }
        Property { name: "busy"; type: "bool"; isReadonly: true }
        Property { name: "depth"; type: "int"; isReadonly: true }
        Property { name: "currentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "initialItem"; type: "QJSValue" }
        Property { name: "popEnter"; type: "QQuickTransition"; isPointer: true }
        Property { name: "popExit"; type: "QQuickTransition"; isPointer: true }
        Property { name: "pushEnter"; type: "QQuickTransition"; isPointer: true }
        Property { name: "pushExit"; type: "QQuickTransition"; isPointer: true }
        Property { name: "replaceEnter"; type: "QQuickTransition"; isPointer: true }
        Property { name: "replaceExit"; type: "QQuickTransition"; isPointer: true }
        Property { name: "empty"; revision: 3; type: "bool"; isReadonly: true }
        Signal { name: "emptyChanged"; revision: 3 }
        Method {
            name: "clear"
            Parameter { name: "operation"; type: "Operation" }
        }
        Method { name: "clear" }
        Method {
            name: "get"
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "behavior"; type: "LoadBehavior" }
        }
        Method {
            name: "get"
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "find"
            type: "QQuickItem*"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "behavior"; type: "LoadBehavior" }
        }
        Method {
            name: "find"
            type: "QQuickItem*"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "push"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "pop"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "replace"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        name: "QQuickStackViewAttached"
        prototype: "QObject"
        Property { name: "index"; type: "int"; isReadonly: true }
        Property { name: "view"; type: "QQuickStackView"; isReadonly: true; isPointer: true }
        Property { name: "status"; type: "QQuickStackView::Status"; isReadonly: true }
        Property { name: "visible"; type: "bool" }
        Signal { name: "activated" }
        Signal { name: "activating" }
        Signal { name: "deactivated" }
        Signal { name: "deactivating" }
        Signal { name: "removed" }
    }
    Component {
        name: "QQuickSwipe"
        prototype: "QObject"
        Property { name: "position"; type: "double" }
        Property { name: "complete"; type: "bool"; isReadonly: true }
        Property { name: "left"; type: "QQmlComponent"; isPointer: true }
        Property { name: "behind"; type: "QQmlComponent"; isPointer: true }
        Property { name: "right"; type: "QQmlComponent"; isPointer: true }
        Property { name: "leftItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "behindItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "rightItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "transition"; type: "QQuickTransition"; isPointer: true }
        Signal { name: "completed" }
        Signal { name: "opened" }
        Signal { name: "closed" }
        Method { name: "close"; revision: 1 }
        Method {
            name: "open"
            revision: 2
            Parameter { name: "side"; type: "QQuickSwipeDelegate::Side" }
        }
    }
    Component {
        name: "QQuickSwipeDelegate"
        defaultProperty: "data"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/SwipeDelegate 2.0",
            "QtQuick.Templates/SwipeDelegate 2.1",
            "QtQuick.Templates/SwipeDelegate 2.2"
        ]
        exportMetaObjectRevisions: [0, 1, 2]
        attachedType: "QQuickSwipeDelegateAttached"
        Enum {
            name: "Side"
            values: {
                "Left": 1,
                "Right": -1
            }
        }
        Property { name: "swipe"; type: "QQuickSwipe"; isReadonly: true; isPointer: true }
    }
    Component {
        name: "QQuickSwipeDelegateAttached"
        prototype: "QObject"
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Signal { name: "clicked" }
    }
    Component {
        name: "QQuickSwipeView"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/SwipeView 2.0",
            "QtQuick.Templates/SwipeView 2.1",
            "QtQuick.Templates/SwipeView 2.2"
        ]
        exportMetaObjectRevisions: [0, 1, 2]
        attachedType: "QQuickSwipeViewAttached"
        Property { name: "interactive"; revision: 1; type: "bool" }
        Property { name: "orientation"; revision: 2; type: "Qt::Orientation" }
        Property { name: "horizontal"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "vertical"; revision: 3; type: "bool"; isReadonly: true }
        Signal { name: "interactiveChanged"; revision: 1 }
        Signal { name: "orientationChanged"; revision: 2 }
    }
    Component {
        name: "QQuickSwipeViewAttached"
        prototype: "QObject"
        Property { name: "index"; type: "int"; isReadonly: true }
        Property { name: "isCurrentItem"; type: "bool"; isReadonly: true }
        Property { name: "view"; type: "QQuickSwipeView"; isReadonly: true; isPointer: true }
        Property { name: "isNextItem"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "isPreviousItem"; revision: 1; type: "bool"; isReadonly: true }
    }
    Component {
        name: "QQuickSwitch"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/Switch 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "position"; type: "double" }
        Property { name: "visualPosition"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickSwitchDelegate"
        defaultProperty: "data"
        prototype: "QQuickItemDelegate"
        exports: ["QtQuick.Templates/SwitchDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "position"; type: "double" }
        Property { name: "visualPosition"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickTabBar"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/TabBar 2.0",
            "QtQuick.Templates/TabBar 2.2"
        ]
        exportMetaObjectRevisions: [0, 2]
        attachedType: "QQuickTabBarAttached"
        Enum {
            name: "Position"
            values: {
                "Header": 0,
                "Footer": 1
            }
        }
        Property { name: "position"; type: "Position" }
        Property { name: "contentWidth"; revision: 2; type: "double" }
        Property { name: "contentHeight"; revision: 2; type: "double" }
    }
    Component {
        name: "QQuickTabBarAttached"
        prototype: "QObject"
        Property { name: "index"; type: "int"; isReadonly: true }
        Property { name: "tabBar"; type: "QQuickTabBar"; isReadonly: true; isPointer: true }
        Property { name: "position"; type: "QQuickTabBar::Position"; isReadonly: true }
    }
    Component {
        name: "QQuickTabButton"
        defaultProperty: "data"
        prototype: "QQuickAbstractButton"
        exports: ["QtQuick.Templates/TabButton 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickTableView"
        defaultProperty: "flickableData"
        prototype: "QQuickFlickable"
        exports: ["QtQuick.Templates/__TableView__ 2.15"]
        exportMetaObjectRevisions: [15]
        attachedType: "QQuickTableViewAttached"
        Property { name: "rows"; type: "int"; isReadonly: true }
        Property { name: "columns"; type: "int"; isReadonly: true }
        Property { name: "rowSpacing"; type: "double" }
        Property { name: "columnSpacing"; type: "double" }
        Property { name: "rowHeightProvider"; type: "QJSValue" }
        Property { name: "columnWidthProvider"; type: "QJSValue" }
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "reuseItems"; type: "bool" }
        Property { name: "contentWidth"; type: "double" }
        Property { name: "contentHeight"; type: "double" }
        Property { name: "syncView"; revision: 14; type: "QQuickTableView"; isPointer: true }
        Property { name: "syncDirection"; revision: 14; type: "Qt::Orientations" }
        Signal { name: "syncViewChanged"; revision: 14 }
        Signal { name: "syncDirectionChanged"; revision: 14 }
        Method { name: "forceLayout" }
    }
    Component {
        name: "QQuickTableViewAttached"
        prototype: "QObject"
        Property { name: "view"; type: "QQuickTableView"; isReadonly: true; isPointer: true }
        Signal { name: "pooled" }
        Signal { name: "reused" }
    }
    Component {
        name: "QQuickText"
        defaultProperty: "data"
        prototype: "QQuickImplicitSizeItem"
        Enum {
            name: "HAlignment"
            values: {
                "AlignLeft": 1,
                "AlignRight": 2,
                "AlignHCenter": 4,
                "AlignJustify": 8
            }
        }
        Enum {
            name: "VAlignment"
            values: {
                "AlignTop": 32,
                "AlignBottom": 64,
                "AlignVCenter": 128
            }
        }
        Enum {
            name: "TextStyle"
            values: {
                "Normal": 0,
                "Outline": 1,
                "Raised": 2,
                "Sunken": 3
            }
        }
        Enum {
            name: "TextFormat"
            values: {
                "PlainText": 0,
                "RichText": 1,
                "MarkdownText": 3,
                "AutoText": 2,
                "StyledText": 4
            }
        }
        Enum {
            name: "TextElideMode"
            values: {
                "ElideLeft": 0,
                "ElideRight": 1,
                "ElideMiddle": 2,
                "ElideNone": 3
            }
        }
        Enum {
            name: "WrapMode"
            values: {
                "NoWrap": 0,
                "WordWrap": 1,
                "WrapAnywhere": 3,
                "WrapAtWordBoundaryOrAnywhere": 4,
                "Wrap": 4
            }
        }
        Enum {
            name: "RenderType"
            values: {
                "QtRendering": 0,
                "NativeRendering": 1
            }
        }
        Enum {
            name: "LineHeightMode"
            values: {
                "ProportionalHeight": 0,
                "FixedHeight": 1
            }
        }
        Enum {
            name: "FontSizeMode"
            values: {
                "FixedSize": 0,
                "HorizontalFit": 1,
                "VerticalFit": 2,
                "Fit": 3
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Property { name: "color"; type: "QColor" }
        Property { name: "linkColor"; type: "QColor" }
        Property { name: "style"; type: "TextStyle" }
        Property { name: "styleColor"; type: "QColor" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "lineCount"; type: "int"; isReadonly: true }
        Property { name: "truncated"; type: "bool"; isReadonly: true }
        Property { name: "maximumLineCount"; type: "int" }
        Property { name: "textFormat"; type: "TextFormat" }
        Property { name: "elide"; type: "TextElideMode" }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "lineHeight"; type: "double" }
        Property { name: "lineHeightMode"; type: "LineHeightMode" }
        Property { name: "baseUrl"; type: "QUrl" }
        Property { name: "minimumPixelSize"; type: "int" }
        Property { name: "minimumPointSize"; type: "int" }
        Property { name: "fontSizeMode"; type: "FontSizeMode" }
        Property { name: "renderType"; type: "RenderType" }
        Property { name: "hoveredLink"; revision: 2; type: "string"; isReadonly: true }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Property { name: "fontInfo"; revision: 9; type: "QJSValue"; isReadonly: true }
        Property { name: "advance"; revision: 10; type: "QSizeF"; isReadonly: true }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "linkHovered"
            revision: 2
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "styleChanged"
            Parameter { name: "style"; type: "QQuickText::TextStyle" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::VAlignment" }
        }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickText::TextFormat" }
        }
        Signal {
            name: "elideModeChanged"
            Parameter { name: "mode"; type: "QQuickText::TextElideMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "contentWidthChanged"
            Parameter { name: "contentWidth"; type: "double" }
        }
        Signal {
            name: "contentHeightChanged"
            Parameter { name: "contentHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightChanged"
            Parameter { name: "lineHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightModeChanged"
            Parameter { name: "mode"; type: "LineHeightMode" }
        }
        Signal {
            name: "lineLaidOut"
            Parameter { name: "line"; type: "QQuickTextLine"; isPointer: true }
        }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Signal { name: "fontInfoChanged"; revision: 9 }
        Method { name: "doLayout" }
        Method { name: "forceLayout"; revision: 9 }
        Method {
            name: "linkAt"
            revision: 3
            type: "string"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickTextArea"
        defaultProperty: "data"
        prototype: "QQuickTextEdit"
        exports: [
            "QtQuick.Templates/TextArea 2.0",
            "QtQuick.Templates/TextArea 2.1",
            "QtQuick.Templates/TextArea 2.3",
            "QtQuick.Templates/TextArea 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 3, 5]
        attachedType: "QQuickTextAreaAttached"
        Property { name: "font"; type: "QFont" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "placeholderText"; type: "string" }
        Property { name: "focusReason"; type: "Qt::FocusReason" }
        Property { name: "hovered"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "hoverEnabled"; revision: 1; type: "bool" }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "placeholderTextColor"; revision: 5; type: "QColor" }
        Property { name: "implicitBackgroundWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "topInset"; revision: 5; type: "double" }
        Property { name: "leftInset"; revision: 5; type: "double" }
        Property { name: "rightInset"; revision: 5; type: "double" }
        Property { name: "bottomInset"; revision: 5; type: "double" }
        Signal { name: "implicitWidthChanged3" }
        Signal { name: "implicitHeightChanged3" }
        Signal {
            name: "pressAndHold"
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            revision: 1
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            revision: 1
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal { name: "hoveredChanged"; revision: 1 }
        Signal { name: "hoverEnabledChanged"; revision: 1 }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "placeholderTextColorChanged"; revision: 5 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 5 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 5 }
        Signal { name: "topInsetChanged"; revision: 5 }
        Signal { name: "leftInsetChanged"; revision: 5 }
        Signal { name: "rightInsetChanged"; revision: 5 }
        Signal { name: "bottomInsetChanged"; revision: 5 }
    }
    Component {
        name: "QQuickTextAreaAttached"
        prototype: "QObject"
        Property { name: "flickable"; type: "QQuickTextArea"; isPointer: true }
    }
    Component {
        name: "QQuickTextEdit"
        defaultProperty: "data"
        prototype: "QQuickImplicitSizeItem"
        Enum {
            name: "HAlignment"
            values: {
                "AlignLeft": 1,
                "AlignRight": 2,
                "AlignHCenter": 4,
                "AlignJustify": 8
            }
        }
        Enum {
            name: "VAlignment"
            values: {
                "AlignTop": 32,
                "AlignBottom": 64,
                "AlignVCenter": 128
            }
        }
        Enum {
            name: "TextFormat"
            values: {
                "PlainText": 0,
                "RichText": 1,
                "AutoText": 2,
                "MarkdownText": 3
            }
        }
        Enum {
            name: "WrapMode"
            values: {
                "NoWrap": 0,
                "WordWrap": 1,
                "WrapAnywhere": 3,
                "WrapAtWordBoundaryOrAnywhere": 4,
                "Wrap": 4
            }
        }
        Enum {
            name: "SelectionMode"
            values: {
                "SelectCharacters": 0,
                "SelectWords": 1
            }
        }
        Enum {
            name: "RenderType"
            values: {
                "QtRendering": 0,
                "NativeRendering": 1
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "color"; type: "QColor" }
        Property { name: "selectionColor"; type: "QColor" }
        Property { name: "selectedTextColor"; type: "QColor" }
        Property { name: "font"; type: "QFont" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "lineCount"; type: "int"; isReadonly: true }
        Property { name: "length"; type: "int"; isReadonly: true }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "textFormat"; type: "TextFormat" }
        Property { name: "readOnly"; type: "bool" }
        Property { name: "cursorVisible"; type: "bool" }
        Property { name: "cursorPosition"; type: "int" }
        Property { name: "cursorRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "cursorDelegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "overwriteMode"; type: "bool" }
        Property { name: "selectionStart"; type: "int"; isReadonly: true }
        Property { name: "selectionEnd"; type: "int"; isReadonly: true }
        Property { name: "selectedText"; type: "string"; isReadonly: true }
        Property { name: "activeFocusOnPress"; type: "bool" }
        Property { name: "persistentSelection"; type: "bool" }
        Property { name: "textMargin"; type: "double" }
        Property { name: "inputMethodHints"; type: "Qt::InputMethodHints" }
        Property { name: "selectByKeyboard"; revision: 1; type: "bool" }
        Property { name: "selectByMouse"; type: "bool" }
        Property { name: "mouseSelectionMode"; type: "SelectionMode" }
        Property { name: "canPaste"; type: "bool"; isReadonly: true }
        Property { name: "canUndo"; type: "bool"; isReadonly: true }
        Property { name: "canRedo"; type: "bool"; isReadonly: true }
        Property { name: "inputMethodComposing"; type: "bool"; isReadonly: true }
        Property { name: "baseUrl"; type: "QUrl" }
        Property { name: "renderType"; type: "RenderType" }
        Property {
            name: "textDocument"
            revision: 1
            type: "QQuickTextDocument"
            isReadonly: true
            isPointer: true
        }
        Property { name: "hoveredLink"; revision: 2; type: "string"; isReadonly: true }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Property { name: "preeditText"; revision: 7; type: "string"; isReadonly: true }
        Property { name: "tabStopDistance"; revision: 10; type: "double" }
        Signal { name: "preeditTextChanged"; revision: 7 }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectionColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectedTextColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextEdit::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextEdit::VAlignment" }
        }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickTextEdit::TextFormat" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "isReadOnly"; type: "bool" }
        }
        Signal {
            name: "cursorVisibleChanged"
            Parameter { name: "isCursorVisible"; type: "bool" }
        }
        Signal {
            name: "overwriteModeChanged"
            Parameter { name: "overwriteMode"; type: "bool" }
        }
        Signal {
            name: "activeFocusOnPressChanged"
            Parameter { name: "activeFocusOnPressed"; type: "bool" }
        }
        Signal {
            name: "persistentSelectionChanged"
            Parameter { name: "isPersistentSelection"; type: "bool" }
        }
        Signal {
            name: "textMarginChanged"
            Parameter { name: "textMargin"; type: "double" }
        }
        Signal {
            name: "selectByKeyboardChanged"
            revision: 1
            Parameter { name: "selectByKeyboard"; type: "bool" }
        }
        Signal {
            name: "selectByMouseChanged"
            Parameter { name: "selectByMouse"; type: "bool" }
        }
        Signal {
            name: "mouseSelectionModeChanged"
            Parameter { name: "mode"; type: "QQuickTextEdit::SelectionMode" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "linkHovered"
            revision: 2
            Parameter { name: "link"; type: "string" }
        }
        Signal { name: "editingFinished"; revision: 6 }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Signal {
            name: "tabStopDistanceChanged"
            revision: 10
            Parameter { name: "distance"; type: "double" }
        }
        Method { name: "selectAll" }
        Method { name: "selectWord" }
        Method {
            name: "select"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "deselect" }
        Method {
            name: "isRightToLeft"
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "cut" }
        Method { name: "copy" }
        Method { name: "paste" }
        Method { name: "undo" }
        Method { name: "redo" }
        Method {
            name: "insert"
            Parameter { name: "position"; type: "int" }
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "remove"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "append"
            revision: 2
            Parameter { name: "text"; type: "string" }
        }
        Method { name: "clear"; revision: 7 }
        Method {
            name: "inputMethodQuery"
            revision: 4
            type: "QVariant"
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
        Method {
            name: "positionToRectangle"
            type: "QRectF"
            Parameter { type: "int" }
        }
        Method {
            name: "positionAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "mode"; type: "SelectionMode" }
        }
        Method {
            name: "getText"
            type: "string"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "getFormattedText"
            type: "string"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "linkAt"
            revision: 3
            type: "string"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickTextField"
        defaultProperty: "data"
        prototype: "QQuickTextInput"
        exports: [
            "QtQuick.Templates/TextField 2.0",
            "QtQuick.Templates/TextField 2.1",
            "QtQuick.Templates/TextField 2.3",
            "QtQuick.Templates/TextField 2.5"
        ]
        exportMetaObjectRevisions: [0, 1, 3, 5]
        Property { name: "font"; type: "QFont" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "placeholderText"; type: "string" }
        Property { name: "focusReason"; type: "Qt::FocusReason" }
        Property { name: "hovered"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "hoverEnabled"; revision: 1; type: "bool" }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "placeholderTextColor"; revision: 5; type: "QColor" }
        Property { name: "implicitBackgroundWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "topInset"; revision: 5; type: "double" }
        Property { name: "leftInset"; revision: 5; type: "double" }
        Property { name: "rightInset"; revision: 5; type: "double" }
        Property { name: "bottomInset"; revision: 5; type: "double" }
        Signal { name: "implicitWidthChanged3" }
        Signal { name: "implicitHeightChanged3" }
        Signal {
            name: "pressAndHold"
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            revision: 1
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            revision: 1
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal { name: "hoveredChanged"; revision: 1 }
        Signal { name: "hoverEnabledChanged"; revision: 1 }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "placeholderTextColorChanged"; revision: 5 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 5 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 5 }
        Signal { name: "topInsetChanged"; revision: 5 }
        Signal { name: "leftInsetChanged"; revision: 5 }
        Signal { name: "rightInsetChanged"; revision: 5 }
        Signal { name: "bottomInsetChanged"; revision: 5 }
    }
    Component {
        name: "QQuickTextInput"
        defaultProperty: "data"
        prototype: "QQuickImplicitSizeItem"
        Enum {
            name: "EchoMode"
            values: {
                "Normal": 0,
                "NoEcho": 1,
                "Password": 2,
                "PasswordEchoOnEdit": 3
            }
        }
        Enum {
            name: "HAlignment"
            values: {
                "AlignLeft": 1,
                "AlignRight": 2,
                "AlignHCenter": 4
            }
        }
        Enum {
            name: "VAlignment"
            values: {
                "AlignTop": 32,
                "AlignBottom": 64,
                "AlignVCenter": 128
            }
        }
        Enum {
            name: "WrapMode"
            values: {
                "NoWrap": 0,
                "WordWrap": 1,
                "WrapAnywhere": 3,
                "WrapAtWordBoundaryOrAnywhere": 4,
                "Wrap": 4
            }
        }
        Enum {
            name: "SelectionMode"
            values: {
                "SelectCharacters": 0,
                "SelectWords": 1
            }
        }
        Enum {
            name: "CursorPosition"
            values: {
                "CursorBetweenCharacters": 0,
                "CursorOnCharacter": 1
            }
        }
        Enum {
            name: "RenderType"
            values: {
                "QtRendering": 0,
                "NativeRendering": 1
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "length"; type: "int"; isReadonly: true }
        Property { name: "color"; type: "QColor" }
        Property { name: "selectionColor"; type: "QColor" }
        Property { name: "selectedTextColor"; type: "QColor" }
        Property { name: "font"; type: "QFont" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "readOnly"; type: "bool" }
        Property { name: "cursorVisible"; type: "bool" }
        Property { name: "cursorPosition"; type: "int" }
        Property { name: "cursorRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "cursorDelegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "overwriteMode"; type: "bool" }
        Property { name: "selectionStart"; type: "int"; isReadonly: true }
        Property { name: "selectionEnd"; type: "int"; isReadonly: true }
        Property { name: "selectedText"; type: "string"; isReadonly: true }
        Property { name: "maximumLength"; type: "int" }
        Property { name: "validator"; type: "QValidator"; isPointer: true }
        Property { name: "inputMask"; type: "string" }
        Property { name: "inputMethodHints"; type: "Qt::InputMethodHints" }
        Property { name: "acceptableInput"; type: "bool"; isReadonly: true }
        Property { name: "echoMode"; type: "EchoMode" }
        Property { name: "activeFocusOnPress"; type: "bool" }
        Property { name: "passwordCharacter"; type: "string" }
        Property { name: "passwordMaskDelay"; revision: 4; type: "int" }
        Property { name: "displayText"; type: "string"; isReadonly: true }
        Property { name: "preeditText"; revision: 7; type: "string"; isReadonly: true }
        Property { name: "autoScroll"; type: "bool" }
        Property { name: "selectByMouse"; type: "bool" }
        Property { name: "mouseSelectionMode"; type: "SelectionMode" }
        Property { name: "persistentSelection"; type: "bool" }
        Property { name: "canPaste"; type: "bool"; isReadonly: true }
        Property { name: "canUndo"; type: "bool"; isReadonly: true }
        Property { name: "canRedo"; type: "bool"; isReadonly: true }
        Property { name: "inputMethodComposing"; type: "bool"; isReadonly: true }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "renderType"; type: "RenderType" }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Signal { name: "accepted" }
        Signal { name: "editingFinished"; revision: 2 }
        Signal { name: "textEdited"; revision: 9 }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextInput::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextInput::VAlignment" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "isReadOnly"; type: "bool" }
        }
        Signal {
            name: "cursorVisibleChanged"
            Parameter { name: "isCursorVisible"; type: "bool" }
        }
        Signal {
            name: "overwriteModeChanged"
            Parameter { name: "overwriteMode"; type: "bool" }
        }
        Signal {
            name: "maximumLengthChanged"
            Parameter { name: "maximumLength"; type: "int" }
        }
        Signal {
            name: "inputMaskChanged"
            Parameter { name: "inputMask"; type: "string" }
        }
        Signal {
            name: "echoModeChanged"
            Parameter { name: "echoMode"; type: "QQuickTextInput::EchoMode" }
        }
        Signal {
            name: "passwordMaskDelayChanged"
            revision: 4
            Parameter { name: "delay"; type: "int" }
        }
        Signal { name: "preeditTextChanged"; revision: 7 }
        Signal {
            name: "activeFocusOnPressChanged"
            Parameter { name: "activeFocusOnPress"; type: "bool" }
        }
        Signal {
            name: "autoScrollChanged"
            Parameter { name: "autoScroll"; type: "bool" }
        }
        Signal {
            name: "selectByMouseChanged"
            Parameter { name: "selectByMouse"; type: "bool" }
        }
        Signal {
            name: "mouseSelectionModeChanged"
            Parameter { name: "mode"; type: "QQuickTextInput::SelectionMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Method { name: "selectAll" }
        Method { name: "selectWord" }
        Method {
            name: "select"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "deselect" }
        Method {
            name: "isRightToLeft"
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "cut" }
        Method { name: "copy" }
        Method { name: "paste" }
        Method { name: "undo" }
        Method { name: "redo" }
        Method {
            name: "insert"
            Parameter { name: "position"; type: "int" }
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "remove"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "ensureVisible"
            revision: 4
            Parameter { name: "position"; type: "int" }
        }
        Method { name: "clear"; revision: 7 }
        Method {
            name: "positionAt"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "positionToRectangle"
            type: "QRectF"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "mode"; type: "SelectionMode" }
        }
        Method {
            name: "inputMethodQuery"
            revision: 4
            type: "QVariant"
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
        Method {
            name: "getText"
            type: "string"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
    }
    Component {
        name: "QQuickToolBar"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: ["QtQuick.Templates/ToolBar 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Position"
            values: {
                "Header": 0,
                "Footer": 1
            }
        }
        Property { name: "position"; type: "Position" }
    }
    Component {
        name: "QQuickToolButton"
        defaultProperty: "data"
        prototype: "QQuickButton"
        exports: ["QtQuick.Templates/ToolButton 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickToolSeparator"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["QtQuick.Templates/ToolSeparator 2.1"]
        exportMetaObjectRevisions: [0]
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "horizontal"; type: "bool"; isReadonly: true }
        Property { name: "vertical"; type: "bool"; isReadonly: true }
    }
    Component {
        name: "QQuickToolTip"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: [
            "QtQuick.Templates/ToolTip 2.0",
            "QtQuick.Templates/ToolTip 2.5"
        ]
        exportMetaObjectRevisions: [0, 5]
        attachedType: "QQuickToolTipAttached"
        Property { name: "delay"; type: "int" }
        Property { name: "timeout"; type: "int" }
        Property { name: "text"; type: "string" }
        Method {
            name: "show"
            revision: 5
            Parameter { name: "text"; type: "string" }
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "show"
            revision: 5
            Parameter { name: "text"; type: "string" }
        }
        Method { name: "hide"; revision: 5 }
    }
    Component {
        name: "QQuickToolTipAttached"
        prototype: "QObject"
        Property { name: "text"; type: "string" }
        Property { name: "delay"; type: "int" }
        Property { name: "timeout"; type: "int" }
        Property { name: "visible"; type: "bool" }
        Property { name: "toolTip"; type: "QQuickToolTip"; isReadonly: true; isPointer: true }
        Method {
            name: "show"
            Parameter { name: "text"; type: "string" }
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "show"
            Parameter { name: "text"; type: "string" }
        }
        Method { name: "hide" }
    }
    Component {
        name: "QQuickTumbler"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Tumbler 2.0",
            "QtQuick.Templates/Tumbler 2.1",
            "QtQuick.Templates/Tumbler 2.2"
        ]
        exportMetaObjectRevisions: [0, 1, 2]
        attachedType: "QQuickTumblerAttached"
        Enum {
            name: "PositionMode"
            values: {
                "Beginning": 0,
                "Center": 1,
                "End": 2,
                "Visible": 3,
                "Contain": 4,
                "SnapPosition": 5
            }
        }
        Property { name: "model"; type: "QVariant" }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "currentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "visibleItemCount"; type: "int" }
        Property { name: "wrap"; revision: 1; type: "bool" }
        Property { name: "moving"; revision: 2; type: "bool"; isReadonly: true }
        Signal { name: "wrapChanged"; revision: 1 }
        Signal { name: "movingChanged"; revision: 2 }
        Method {
            name: "positionViewAtIndex"
            revision: 5
            Parameter { name: "index"; type: "int" }
            Parameter { name: "mode"; type: "PositionMode" }
        }
    }
    Component {
        name: "QQuickTumblerAttached"
        prototype: "QObject"
        Property { name: "tumbler"; type: "QQuickTumbler"; isReadonly: true; isPointer: true }
        Property { name: "displacement"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickVerticalHeaderView"
        defaultProperty: "flickableData"
        prototype: "QQuickHeaderViewBase"
        exports: ["QtQuick.Templates/VerticalHeaderView 2.15"]
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickTableViewAttached"
    }
    Component {
        name: "QQuickWindow"
        defaultProperty: "data"
        prototype: "QWindow"
        Enum {
            name: "CreateTextureOptions"
            values: {
                "TextureHasAlphaChannel": 1,
                "TextureHasMipmaps": 2,
                "TextureOwnsGLTexture": 4,
                "TextureCanUseAtlas": 8,
                "TextureIsOpaque": 16
            }
        }
        Enum {
            name: "SceneGraphError"
            values: {
                "ContextNotAvailable": 1
            }
        }
        Enum {
            name: "TextRenderType"
            values: {
                "QtTextRendering": 0,
                "NativeTextRendering": 1
            }
        }
        Enum {
            name: "NativeObjectType"
            values: {
                "NativeObjectTexture": 0
            }
        }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "color"; type: "QColor" }
        Property { name: "contentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property {
            name: "activeFocusItem"
            revision: 1
            type: "QQuickItem"
            isReadonly: true
            isPointer: true
        }
        Signal { name: "frameSwapped" }
        Signal {
            name: "openglContextCreated"
            revision: 2
            Parameter { name: "context"; type: "QOpenGLContext"; isPointer: true }
        }
        Signal { name: "sceneGraphInitialized" }
        Signal { name: "sceneGraphInvalidated" }
        Signal { name: "beforeSynchronizing" }
        Signal { name: "afterSynchronizing"; revision: 2 }
        Signal { name: "beforeRendering" }
        Signal { name: "afterRendering" }
        Signal { name: "afterAnimating"; revision: 2 }
        Signal { name: "sceneGraphAboutToStop"; revision: 2 }
        Signal {
            name: "closing"
            revision: 1
            Parameter { name: "close"; type: "QQuickCloseEvent"; isPointer: true }
        }
        Signal {
            name: "colorChanged"
            Parameter { type: "QColor" }
        }
        Signal { name: "activeFocusItemChanged"; revision: 1 }
        Signal {
            name: "sceneGraphError"
            revision: 2
            Parameter { name: "error"; type: "QQuickWindow::SceneGraphError" }
            Parameter { name: "message"; type: "string" }
        }
        Signal { name: "beforeRenderPassRecording"; revision: 14 }
        Signal { name: "afterRenderPassRecording"; revision: 14 }
        Method { name: "update" }
        Method { name: "releaseResources" }
    }
    Component {
        name: "QQuickWindowQmlImpl"
        defaultProperty: "data"
        prototype: "QQuickWindow"
        Property { name: "visible"; type: "bool" }
        Property { name: "visibility"; type: "Visibility" }
        Property { name: "screen"; revision: 3; type: "QObject"; isPointer: true }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "screenChanged"; revision: 3 }
    }
    Component {
        name: "QWindow"
        prototype: "QObject"
        Enum {
            name: "Visibility"
            values: {
                "Hidden": 0,
                "AutomaticVisibility": 1,
                "Windowed": 2,
                "Minimized": 3,
                "Maximized": 4,
                "FullScreen": 5
            }
        }
        Enum {
            name: "AncestorMode"
            values: {
                "ExcludeTransients": 0,
                "IncludeTransients": 1
            }
        }
        Property { name: "title"; type: "string" }
        Property { name: "modality"; type: "Qt::WindowModality" }
        Property { name: "flags"; type: "Qt::WindowFlags" }
        Property { name: "x"; type: "int" }
        Property { name: "y"; type: "int" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Property { name: "minimumWidth"; type: "int" }
        Property { name: "minimumHeight"; type: "int" }
        Property { name: "maximumWidth"; type: "int" }
        Property { name: "maximumHeight"; type: "int" }
        Property { name: "visible"; type: "bool" }
        Property { name: "active"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "visibility"; revision: 1; type: "Visibility" }
        Property { name: "contentOrientation"; type: "Qt::ScreenOrientation" }
        Property { name: "opacity"; revision: 1; type: "double" }
        Property { name: "transientParent"; revision: 13; type: "QWindow"; isPointer: true }
        Signal {
            name: "screenChanged"
            Parameter { name: "screen"; type: "QScreen"; isPointer: true }
        }
        Signal {
            name: "modalityChanged"
            Parameter { name: "modality"; type: "Qt::WindowModality" }
        }
        Signal {
            name: "windowStateChanged"
            Parameter { name: "windowState"; type: "Qt::WindowState" }
        }
        Signal {
            name: "windowTitleChanged"
            revision: 2
            Parameter { name: "title"; type: "string" }
        }
        Signal {
            name: "xChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            revision: 1
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "activeChanged"; revision: 1 }
        Signal {
            name: "contentOrientationChanged"
            Parameter { name: "orientation"; type: "Qt::ScreenOrientation" }
        }
        Signal {
            name: "focusObjectChanged"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            revision: 1
            Parameter { name: "opacity"; type: "double" }
        }
        Signal {
            name: "transientParentChanged"
            revision: 13
            Parameter { name: "transientParent"; type: "QWindow"; isPointer: true }
        }
        Method { name: "requestActivate"; revision: 1 }
        Method {
            name: "setVisible"
            Parameter { name: "visible"; type: "bool" }
        }
        Method { name: "show" }
        Method { name: "hide" }
        Method { name: "showMinimized" }
        Method { name: "showMaximized" }
        Method { name: "showFullScreen" }
        Method { name: "showNormal" }
        Method { name: "close"; type: "bool" }
        Method { name: "raise" }
        Method { name: "lower" }
        Method {
            name: "startSystemResize"
            type: "bool"
            Parameter { name: "edges"; type: "Qt::Edges" }
        }
        Method { name: "startSystemMove"; type: "bool" }
        Method {
            name: "setTitle"
            Parameter { type: "string" }
        }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "posx"; type: "int" }
            Parameter { name: "posy"; type: "int" }
            Parameter { name: "w"; type: "int" }
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "rect"; type: "QRect" }
        }
        Method {
            name: "setMinimumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMinimumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setMaximumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMaximumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "alert"
            revision: 1
            Parameter { name: "msec"; type: "int" }
        }
        Method { name: "requestUpdate"; revision: 3 }
    }
}
