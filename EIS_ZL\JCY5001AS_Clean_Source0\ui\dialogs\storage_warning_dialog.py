#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储警告对话框
当存储空间接近或超过限制时显示警告信息和清理建议

功能：
1. 显示存储使用情况
2. 提供清理建议
3. 快速清理操作
4. 跳转到存储管理设置

作者：Jack
日期：2025-01-31
"""

import logging
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QProgressBar, QTextEdit,
    QGroupBox, QFrame, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette

logger = logging.getLogger(__name__)


class StorageWarningDialog(QDialog):
    """存储警告对话框"""
    
    # 信号定义
    cleanup_requested = pyqtSignal(str)  # 清理请求信号
    settings_requested = pyqtSignal()    # 设置请求信号
    
    def __init__(self, warning_data: Dict[str, Any], parent=None):
        """
        初始化存储警告对话框
        
        Args:
            warning_data: 警告数据字典
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.warning_data = warning_data
        
        # 设置对话框属性
        self.setWindowTitle("存储空间警告")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # 初始化UI
        self._init_ui()
        
        # 应用样式
        self._apply_styles()
        
        logger.debug("存储警告对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 警告标题
        title_layout = QHBoxLayout()
        
        # 警告图标
        icon_label = QLabel("⚠️")
        icon_label.setFont(QFont("", 24))
        title_layout.addWidget(icon_label)
        
        # 警告标题文本
        title_label = QLabel("存储空间警告")
        title_label.setFont(QFont("", 16, QFont.Bold))
        title_label.setStyleSheet("color: #e74c3c;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # 存储状态组
        layout.addWidget(self._create_storage_status_group())
        
        # 清理建议组
        layout.addWidget(self._create_cleanup_suggestions_group())
        
        # 按钮区域
        layout.addWidget(self._create_button_area())
    
    def _create_storage_status_group(self) -> QGroupBox:
        """创建存储状态组"""
        group = QGroupBox("当前存储使用情况")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 根据警告数据显示相应的存储状态
        warning_type = self.warning_data.get('type', 'unknown')
        
        if warning_type == 'database':
            self._add_database_status(layout)
        elif warning_type == 'logs':
            self._add_logs_status(layout)
        else:
            self._add_general_status(layout)
        
        return group
    
    def _add_database_status(self, layout: QGridLayout):
        """添加数据库状态显示"""
        usage_percent = self.warning_data.get('usage_percent', 0)
        size_mb = self.warning_data.get('size_mb', 0)
        limit_mb = self.warning_data.get('limit_mb', 500)
        
        # 数据库文件状态
        layout.addWidget(QLabel("数据库文件:"), 0, 0)
        
        size_label = QLabel(f"{size_mb:.1f} MB / {limit_mb} MB")
        layout.addWidget(size_label, 0, 1)
        
        progress = QProgressBar()
        progress.setMaximum(100)
        progress.setValue(int(usage_percent))
        
        # 设置进度条颜色
        if usage_percent >= 100:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
        elif usage_percent >= 90:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
        else:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")
        
        layout.addWidget(progress, 0, 2)
        
        # 使用率标签
        usage_label = QLabel(f"{usage_percent:.1f}%")
        usage_label.setFont(QFont("", 10, QFont.Bold))
        if usage_percent >= 100:
            usage_label.setStyleSheet("color: #e74c3c;")
        elif usage_percent >= 90:
            usage_label.setStyleSheet("color: #f39c12;")
        layout.addWidget(usage_label, 0, 3)
    
    def _add_logs_status(self, layout: QGridLayout):
        """添加日志状态显示"""
        usage_percent = self.warning_data.get('usage_percent', 0)
        size_mb = self.warning_data.get('size_mb', 0)
        limit_mb = self.warning_data.get('limit_mb', 100)
        
        # 日志文件状态
        layout.addWidget(QLabel("日志文件:"), 0, 0)
        
        size_label = QLabel(f"{size_mb:.1f} MB / {limit_mb} MB")
        layout.addWidget(size_label, 0, 1)
        
        progress = QProgressBar()
        progress.setMaximum(100)
        progress.setValue(int(usage_percent))
        
        # 设置进度条颜色
        if usage_percent >= 100:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
        elif usage_percent >= 90:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
        else:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")
        
        layout.addWidget(progress, 0, 2)
        
        # 使用率标签
        usage_label = QLabel(f"{usage_percent:.1f}%")
        usage_label.setFont(QFont("", 10, QFont.Bold))
        if usage_percent >= 100:
            usage_label.setStyleSheet("color: #e74c3c;")
        elif usage_percent >= 90:
            usage_label.setStyleSheet("color: #f39c12;")
        layout.addWidget(usage_label, 0, 3)
    
    def _add_general_status(self, layout: QGridLayout):
        """添加通用状态显示"""
        message = self.warning_data.get('message', '存储空间不足')
        
        status_label = QLabel(message)
        status_label.setFont(QFont("", 10))
        status_label.setWordWrap(True)
        layout.addWidget(status_label, 0, 0, 1, 4)
    
    def _create_cleanup_suggestions_group(self) -> QGroupBox:
        """创建清理建议组"""
        group = QGroupBox("清理建议")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 建议文本
        suggestions_text = QTextEdit()
        suggestions_text.setMaximumHeight(100)
        suggestions_text.setReadOnly(True)
        
        # 根据警告类型生成建议
        suggestions = self._generate_suggestions()
        suggestions_text.setPlainText(suggestions)
        
        layout.addWidget(suggestions_text)
        
        return group
    
    def _generate_suggestions(self) -> str:
        """生成清理建议"""
        warning_type = self.warning_data.get('type', 'unknown')
        usage_percent = self.warning_data.get('usage_percent', 0)
        
        suggestions = []
        
        if warning_type == 'database':
            if usage_percent >= 100:
                suggestions.append("🔴 数据库文件已超过限制！建议立即清理：")
            else:
                suggestions.append("🟡 数据库文件接近限制，建议清理：")
            
            suggestions.append("• 删除旧的测试记录")
            suggestions.append("• 归档历史数据")
            suggestions.append("• 增加数据库大小限制")
            
        elif warning_type == 'logs':
            if usage_percent >= 100:
                suggestions.append("🔴 日志文件已超过限制！建议立即清理：")
            else:
                suggestions.append("🟡 日志文件接近限制，建议清理：")
            
            suggestions.append("• 删除旧的日志文件")
            suggestions.append("• 启用日志轮转")
            suggestions.append("• 减少日志保留天数")
        
        else:
            suggestions.append("🟡 存储空间不足，建议：")
            suggestions.append("• 清理临时文件")
            suggestions.append("• 清理缓存文件")
            suggestions.append("• 检查存储配置")
        
        return "\n".join(suggestions)
    
    def _create_button_area(self) -> QFrame:
        """创建按钮区域"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)
        
        # 快速清理按钮
        quick_cleanup_button = QPushButton("快速清理")
        quick_cleanup_button.setMinimumWidth(100)
        quick_cleanup_button.clicked.connect(self._on_quick_cleanup)
        quick_cleanup_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(quick_cleanup_button)
        
        # 完整清理按钮
        full_cleanup_button = QPushButton("完整清理")
        full_cleanup_button.setMinimumWidth(100)
        full_cleanup_button.clicked.connect(self._on_full_cleanup)
        full_cleanup_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        layout.addWidget(full_cleanup_button)
        
        # 存储设置按钮
        settings_button = QPushButton("存储设置")
        settings_button.setMinimumWidth(100)
        settings_button.clicked.connect(self._on_settings)
        layout.addWidget(settings_button)
        
        # 稍后处理按钮
        later_button = QPushButton("稍后处理")
        later_button.setMinimumWidth(100)
        later_button.clicked.connect(self.reject)
        layout.addWidget(later_button)
        
        return frame
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #5a6268;
            }
            
            QPushButton:pressed {
                background-color: #495057;
            }
            
            QTextEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
                font-family: "Microsoft YaHei";
            }
        """)
    
    def _on_quick_cleanup(self):
        """快速清理按钮点击处理"""
        try:
            # 发送快速清理请求
            self.cleanup_requested.emit("temp_files")
            
            # 显示确认消息
            QMessageBox.information(
                self, "清理请求",
                "已发送快速清理请求，将清理临时文件和缓存。"
            )
            
            self.accept()
            
        except Exception as e:
            logger.error(f"快速清理请求失败: {e}")
            QMessageBox.warning(self, "错误", f"快速清理请求失败: {e}")
    
    def _on_full_cleanup(self):
        """完整清理按钮点击处理"""
        try:
            # 确认对话框
            reply = QMessageBox.question(
                self, "确认清理",
                "完整清理将删除临时文件、旧日志和归档数据。\n确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 发送完整清理请求
                self.cleanup_requested.emit("full_cleanup")
                
                # 显示确认消息
                QMessageBox.information(
                    self, "清理请求",
                    "已发送完整清理请求，请等待清理完成。"
                )
                
                self.accept()
            
        except Exception as e:
            logger.error(f"完整清理请求失败: {e}")
            QMessageBox.warning(self, "错误", f"完整清理请求失败: {e}")
    
    def _on_settings(self):
        """存储设置按钮点击处理"""
        try:
            # 发送设置请求信号
            self.settings_requested.emit()
            self.accept()
            
        except Exception as e:
            logger.error(f"打开存储设置失败: {e}")
            QMessageBox.warning(self, "错误", f"打开存储设置失败: {e}")


def show_storage_warning(warning_data: Dict[str, Any], parent=None) -> StorageWarningDialog:
    """
    显示存储警告对话框
    
    Args:
        warning_data: 警告数据字典
        parent: 父窗口
        
    Returns:
        存储警告对话框实例
    """
    dialog = StorageWarningDialog(warning_data, parent)
    return dialog
