# JCY5001A 电池交流阻抗测试系统

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-V0.80.06-orange.svg)](CHANGELOG.md)

## 📋 **项目简介**

JCY5001A是一款专业的电池交流阻抗测试系统，支持8通道并行测试，适用于锂电池、磷酸铁锂电池等各类电池的阻抗特性分析。

### **主要功能**
- 🔋 **多通道测试**: 支持1-8通道同时测试
- 📊 **EIS分析**: 电化学阻抗谱分析，计算Rs、Rct、Rsei等参数
- 📈 **数据可视化**: Nyquist图、Bode图、统计图表
- 📄 **报告生成**: Excel导出、标签打印、测试报告
- 🔍 **质量控制**: 离群检测、统计分析、合格判定
- ⚙️ **灵活配置**: 研究模式、生产模式、自定义频率

## 🚀 **快速开始**

### **系统要求**
- Windows 10/11 (64位)
- Python 3.10+
- 4GB+ 内存
- 串口设备(RS485/USB转串口)

### **安装步骤**
```bash
# 1. 克隆项目
git clone https://github.com/JACKCHENPANG/JCY5001A.git
cd JCY5001A

# 2. 安装依赖
pip install -r requirements.txt

# 3. 运行程序
python main.py
```

### **配置设备**
1. 连接测试设备到计算机串口
2. 在设置中配置串口参数(COM口、波特率等)
3. 点击"连接设备"按钮
4. 确认设备状态显示"已连接"

## 📖 **使用指南**

### **基本操作流程**
1. **设备连接**: 配置并连接测试设备
2. **参数设置**: 选择测试模式和频率范围
3. **电池安装**: 将电池安装到测试夹具
4. **开始测试**: 点击"开始测试"按钮
5. **查看结果**: 测试完成后查看参数和图表
6. **导出数据**: 导出Excel报告或打印标签

### **测试模式**
- **研究模式**: 20个频点(0.01Hz-7.8kHz)，详细分析
- **生产模式**: 5-10个频点(1Hz-1kHz)，快速检测
- **自定义模式**: 用户自定义频率列表

### **参数说明**
- **Rs**: 串联电阻(mΩ) - 电池内阻
- **Rct**: 电荷传递电阻(mΩ) - 电化学反应阻抗
- **Rsei**: SEI膜电阻(mΩ) - 固体电解质界面阻抗
- **W**: Warburg阻抗(mΩ) - 扩散阻抗

## 🏗️ **项目结构**

```
JCY5001A/
├── backend/                   # 后端业务逻辑
│   ├── communication_manager.py      # 设备通信
│   ├── test_executor.py             # 测试执行
│   ├── eis_analyzer.py              # EIS分析
│   └── impedance_data_manager.py    # 数据管理
├── ui/                        # 用户界面
│   ├── main_window.py               # 主窗口
│   ├── components/                  # UI组件
│   └── dialogs/                     # 对话框
├── utils/                     # 工具类
│   ├── config_manager.py            # 配置管理
│   └── logger_helper.py             # 日志工具
├── config/                    # 配置文件
│   └── app_config.json              # 应用配置
├── data/                      # 数据存储
│   └── test_results.db              # 测试数据库
├── resources/                 # 资源文件
│   ├── icons/                       # 图标
│   └── styles/                      # 样式
└── main.py                    # 主入口
```

## 🔧 **开发指南**

### **代码规范**
- 遵循PEP 8编码规范
- 使用类型注解
- 添加详细的文档字符串
- 单元测试覆盖率>80%

### **构建打包**
```bash
# 清理代码
python cleanup_codebase.py --execute

# 运行测试
python -m pytest tests/

# 构建可执行文件
python build_nuitka_v082.py
```

### **贡献代码**
1. Fork项目到个人仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

## 📊 **技术栈**

| 组件 | 技术 | 版本 | 说明 |
|------|------|------|------|
| 界面框架 | PyQt5 | 5.15+ | 跨平台GUI框架 |
| 数据分析 | NumPy/SciPy | 1.21+ | 科学计算库 |
| 数据可视化 | Matplotlib | 3.5+ | 图表绘制 |
| 数据库 | SQLite3 | 内置 | 轻量级数据库 |
| 通信协议 | PySerial | 3.5+ | 串口通信 |
| 报表生成 | XlsxWriter | 3.0+ | Excel文件生成 |
| 打包工具 | Nuitka | 1.8+ | Python编译器 |

## 📞 **技术支持**

### **联系方式**
- **开发者**: Jack Chen (张海)
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/JACKCHENPANG/JCY5001A

### **问题反馈**
- **Bug报告**: [GitHub Issues](https://github.com/JACKCHENPANG/JCY5001A/issues)
- **功能建议**: [GitHub Discussions](https://github.com/JACKCHENPANG/JCY5001A/discussions)
- **技术交流**: 邮件联系

### **文档资源**
- [用户手册](docs/USER_GUIDE.md)
- [API文档](docs/API_REFERENCE.md)
- [更新日志](docs/CHANGELOG.md)
- [故障排除](docs/TROUBLESHOOTING.md)

## 📄 **许可证**

本项目为专有软件，版权归开发者所有。未经授权不得复制、分发或修改。

## 🙏 **致谢**

感谢所有为本项目做出贡献的开发者和用户。

---

**最后更新**: 2025-01-01  
**版本**: V0.80.06  
**维护者**: Jack Chen
