#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样本管理管理器
负责样本信息的管理和对话框处理

Author: Jack
Date: 2025-06-04
"""

import logging
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QSpinBox, 
    QDoubleSpinBox, QComboBox, QTextEdit, QLabel, QWidget, QDialogButtonBox,
    QMessageBox
)
from PyQt5.QtCore import Qt

logger = logging.getLogger(__name__)


class SampleInfoDialog(QDialog):
    """样本信息输入对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("样本信息")
        self.setModal(True)
        self.resize(500, 400)

        self._init_ui()

    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 创建表单
        form_layout = QFormLayout()

        # 电芯品牌
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("例如：宁德时代")
        form_layout.addRow("电芯品牌:", self.brand_edit)

        # 电芯类型
        self.cell_type_edit = QLineEdit()
        self.cell_type_edit.setPlaceholderText("例如：磷酸铁锂")
        form_layout.addRow("电芯类型:", self.cell_type_edit)

        # 电芯容量（带单位选择）
        capacity_layout = QHBoxLayout()
        self.capacity_spinbox = QSpinBox()
        self.capacity_spinbox.setRange(1, 100000)
        self.capacity_spinbox.setValue(3000)
        capacity_layout.addWidget(self.capacity_spinbox)

        self.capacity_unit_combo = QComboBox()
        self.capacity_unit_combo.addItems(["mAh", "Ah"])
        self.capacity_unit_combo.setCurrentText("mAh")
        self.capacity_unit_combo.currentTextChanged.connect(self._on_capacity_unit_changed)
        capacity_layout.addWidget(self.capacity_unit_combo)

        capacity_widget = QWidget()
        capacity_widget.setLayout(capacity_layout)
        form_layout.addRow("电芯容量:", capacity_widget)

        # 标准电阻值（分为标准值和容差）
        impedance_layout = QVBoxLayout()

        # 标准值输入
        standard_value_layout = QHBoxLayout()
        self.standard_value_spinbox = QDoubleSpinBox()
        self.standard_value_spinbox.setRange(0.1, 100.0)
        self.standard_value_spinbox.setValue(1.5)
        self.standard_value_spinbox.setDecimals(3)
        self.standard_value_spinbox.setSuffix(" mΩ")
        standard_value_layout.addWidget(QLabel("标准值:"))
        standard_value_layout.addWidget(self.standard_value_spinbox)

        # 容差百分比输入
        tolerance_layout = QHBoxLayout()
        self.tolerance_spinbox = QDoubleSpinBox()
        self.tolerance_spinbox.setRange(1.0, 50.0)
        self.tolerance_spinbox.setValue(5.0)
        self.tolerance_spinbox.setDecimals(1)
        self.tolerance_spinbox.setSuffix(" %")
        tolerance_layout.addWidget(QLabel("容差:"))
        tolerance_layout.addWidget(self.tolerance_spinbox)

        impedance_layout.addLayout(standard_value_layout)
        impedance_layout.addLayout(tolerance_layout)

        # 显示格式化的标准电阻值
        self.formatted_impedance_label = QLabel()
        self._update_formatted_impedance()
        self.standard_value_spinbox.valueChanged.connect(self._update_formatted_impedance)
        self.tolerance_spinbox.valueChanged.connect(self._update_formatted_impedance)
        impedance_layout.addWidget(self.formatted_impedance_label)

        impedance_widget = QWidget()
        impedance_widget.setLayout(impedance_layout)
        form_layout.addRow("标准内阻(1kHz):", impedance_widget)

        # 样本名称
        self.sample_name_edit = QLineEdit()
        self.sample_name_edit.setPlaceholderText("例如：宁德时代_磷酸铁锂_3000mAh_标准样本")
        form_layout.addRow("样本名称:", self.sample_name_edit)

        # 样本描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("样本的详细描述信息...")
        form_layout.addRow("样本描述:", self.description_edit)

        layout.addLayout(form_layout)

        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def _on_capacity_unit_changed(self, unit: str):
        """容量单位改变时的处理"""
        current_value = self.capacity_spinbox.value()

        if unit == "Ah":
            # 切换到Ah，调整范围和值
            if self.capacity_spinbox.maximum() > 1000:  # 当前是mAh范围
                self.capacity_spinbox.setRange(1, 1000)
                self.capacity_spinbox.setValue(int(min(current_value / 1000, 1000)))
        else:  # mAh
            # 切换到mAh，调整范围和值
            if self.capacity_spinbox.maximum() <= 1000:  # 当前是Ah范围
                self.capacity_spinbox.setRange(1, 100000)
                self.capacity_spinbox.setValue(int(min(current_value * 1000, 100000)))

    def _update_formatted_impedance(self):
        """更新格式化的标准电阻值显示"""
        standard_value = self.standard_value_spinbox.value()
        tolerance = self.tolerance_spinbox.value()
        formatted_text = f"显示格式: {standard_value:.3f}mΩ ± {tolerance:.1f}%"
        self.formatted_impedance_label.setText(formatted_text)
        self.formatted_impedance_label.setStyleSheet("color: #666; font-style: italic;")

    def get_sample_info(self) -> Dict[str, Any]:
        """获取样本信息"""
        # 获取容量值和单位
        capacity_value = self.capacity_spinbox.value()
        capacity_unit = self.capacity_unit_combo.currentText()

        return {
            'brand': self.brand_edit.text().strip(),
            'cell_type': self.cell_type_edit.text().strip(),
            'capacity_value': capacity_value,
            'capacity_unit': capacity_unit,
            'capacity_display': f"{capacity_value}{capacity_unit}",  # 用于显示的完整容量
            'standard_value': self.standard_value_spinbox.value(),
            'tolerance_percent': self.tolerance_spinbox.value(),
            'standard_impedance_display': f"{self.standard_value_spinbox.value():.3f}mΩ±{self.tolerance_spinbox.value():.1f}%",
            'sample_name': self.sample_name_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip()
        }
        
    def validate_input(self) -> bool:
        """验证输入"""
        info = self.get_sample_info()
        
        if not info['brand']:
            QMessageBox.warning(self, "警告", "请输入电芯品牌")
            return False
            
        if not info['cell_type']:
            QMessageBox.warning(self, "警告", "请输入电芯类型")
            return False
            
        if not info['sample_name']:
            QMessageBox.warning(self, "警告", "请输入样本名称")
            return False
            
        return True
        
    def accept(self):
        """确认按钮点击"""
        if self.validate_input():
            super().accept()


class SampleManagementManager:
    """
    样本管理管理器
    
    职责：
    - 管理样本信息对话框
    - 处理样本数据
    - 管理样本状态
    """
    
    def __init__(self, parent_widget=None):
        """
        初始化样本管理管理器
        
        Args:
            parent_widget: 父窗口部件
        """
        self.parent_widget = parent_widget
        self.current_sample_data = {}
        
        logger.debug("样本管理管理器初始化完成")
    
    def show_sample_info_dialog(self) -> Optional[Dict[str, Any]]:
        """
        显示样本信息输入对话框
        
        Returns:
            Dict: 样本信息，如果取消则返回None
        """
        try:
            dialog = SampleInfoDialog(self.parent_widget)
            
            if dialog.exec_() == QDialog.Accepted:
                sample_info = dialog.get_sample_info()
                logger.debug(f"获取样本信息: {sample_info['sample_name']}")
                return sample_info
            else:
                logger.debug("用户取消了样本信息输入")
                return None
                
        except Exception as e:
            logger.error(f"显示样本信息对话框失败: {e}")
            return None
    
    def set_current_sample_data(self, sample_data: Dict[str, Any]):
        """设置当前样本数据"""
        self.current_sample_data = sample_data.copy()
        logger.debug(f"设置当前样本数据: {sample_data.get('info', {}).get('sample_name', 'Unknown')}")
    
    def get_current_sample_data(self) -> Dict[str, Any]:
        """获取当前样本数据"""
        return self.current_sample_data.copy()
    
    def clear_current_sample_data(self):
        """清除当前样本数据"""
        self.current_sample_data = {}
        logger.debug("当前样本数据已清除")
    
    def has_current_sample(self) -> bool:
        """检查是否有当前样本"""
        return bool(self.current_sample_data)
    
    def get_sample_info(self) -> Dict[str, Any]:
        """获取样本信息部分"""
        return self.current_sample_data.get('info', {})
    
    def get_sample_analysis_result(self) -> Dict[str, Any]:
        """获取样本分析结果"""
        return self.current_sample_data.get('analysis_result', {})
    
    def cleanup(self):
        """清理资源"""
        try:
            self.clear_current_sample_data()
            logger.debug("样本管理管理器资源清理完成")
        except Exception as e:
            logger.error(f"样本管理管理器清理失败: {e}")
