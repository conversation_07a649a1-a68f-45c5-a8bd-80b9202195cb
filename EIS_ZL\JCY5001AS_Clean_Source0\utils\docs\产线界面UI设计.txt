产线界面UI设计
1. 采用上下视窗显示风格  最顶端 显示 一行  JCY5001A 鲸测云8路EIS阻抗筛选仪 带logo，然后后面带一个试用使用倒计时：几天几小时几分这样的显示
2. 上层视窗内部分成3个区域
    2-1 批次显示区域： 显示 批次号 电芯规格  当前时间 测试时长 操作员 等信息，批次号是设置页面里面的批次信息设置里面设置，还有电芯规格也是设置出来的，当前时长是根据系统时间显示，测试时长 是开机后累计运行时间
    2-2 统计区域： 显示 总测试数，合格数 不合格数  良率  信息，另外并排显示一个档位信息，用Rs-Rct的二维形式显示成一个档位分布图，Rs1~Rs3做为行名，Rct1~Rct3是列名显示，对应的 1-1或者2-2 显示在这个二维表中，落在Rs1一行的是绿色，Rs2一行的是黄色，Rs3一行的是红色，由于这个Rs可能是1档也可以是2档或者3档，这个要根据设置页面的档位设置来同步修改
    2-3 测试控制区域： 显示开始按键，这个开始按键按下后就变成停止按键，可以切换显示的，再按下就恢复到开始
                                     统计清理，按下后统计显示区域都清理，但是需要弹窗提醒用户下
                                     导出数据： 按下后进入数据库操作界面，可以导出测试结果和测试明细数据，采用SQL数据库形式
                                     设置：按下后进入设置页面
3. 下层视窗显示8个通道的测试结果
             按照通道号（醒目显示），测试用时：从测试开始计算，到结束测试停止， 电池码：可以选择扫码或者不选择扫码，扫码时候弹出来对应要输入的通道号，然后等待用户输入，输入后对应通道号开始测试
        电压(V).显示小数点后3位，从设备中采集来的数据，
        Rs(mΩ）和Rct（mΩ）是根据测试数据计算出来的，显示到小数点后三位数
       带一个进度显示条，表示测试到那个频点了
        最后测试结果，显示合格，或者不合格-V(代表电压不合格），或者不合格-Rs（Rs阻抗范围不合格）或者不合格-Rct（代表Rct阻抗范围不合格）

界面底部状态 显示设备连接状态，以及打印机等外设连接状态


设置页面按键：
  1. 可以把设置页面设置的全部配置导出，然后导入到其他设备上，方便快速设置
  2. 每次修改完后，如果有更改，需要提醒用户按下保存按键，不然就不会保存
3. 设置页面显示几个选项卡页面
      3-1 参数配置  里面针对每个通道的参数设置  
                   增益： 有1倍 4倍 16倍和自动，1倍就是针对全频范围内的增益放大，4倍 和16倍针对10hz以上的频点，越小的内阻就需要越大的增益，比如1mΩ欧姆以下用16倍的， 选择自动时候就是小于10hz用1倍增益，大于10hz以上频点时候，根据标准配置中的Rs值做参考，10mΩ以上采用4倍，小于10mΩ以下的做16倍。做一个鼠标悬停功能解释
                  平均次数： 默认是1次，根据客户选择，多次测试会时间久，建议做一个鼠标悬停的解释说明
                  电阻： 改成电池量程： 里面有3个选项 1mΩ以下  10mΩ以下 100mΩ以下，对应的就是1R  5R  10R 做一个鼠标悬停说明

                  
     3-2 频率设置： 频点类型  多频点和单频点      多频点选中后，有几个默认的配置频点，可以选择，然后对应的频点展示出来（原来的频点预设和测试参数下的频点设置做整合） 单频点就只展示一个点 可以修改这个列表
    3-3 标准设置和批次信息合并   变成产品信息显示
          批次号： 用户自己填入，不做校验
          操作员，用户自己填入，不做校验
          电池类型： 可以选择磷酸铁锂或者三元锂这些电芯类型
          电池规格：可以自己输入 21700或者18650或者其他的
         电池标准电压：根据电池规格选择后，对应的标准电压会自动带出来，（这个AI可以协助）
        标准容量：自己写入，由于不同的电池参数，不能锁定mAh或者Ah单位，可以选择的，
         New内阻：这个默认对应我们的Rs，一般是1khz时候的频点，单位mΩ，出厂值
         EOL内阻：可以直接设置，一般是New阻抗的2倍到2.5倍算EOL,比如6mΩ，那就是12mΩ算EOL
         
   3-3 流程改成测试配置
                    连续测试； 这个在这里设置，选中后就会自动测试不停止
                  自动侦测： 设置一个使能的开关，如果关闭后，就是测试完就停止测试（针对自动化产线需要停止测试），如果使能，就是自动侦测换了新电池开始测试（针对人工操作），增加鼠标悬停使能说明，
                  错频：名字改成数据优化 选中后，就会当下发同一个高频点后，大于1hz以上，启动时候做一个延时时间，后面再带一个延时时间的设置，默认是0.5秒，可以最小0.2秒到3秒这样 ，也要增加悬停解释
               
                  电池编码： 
                           方式: 扫码 使能 或者 自动生成，二选一，即便没有扫码也可以自动生成编码 ，但这个自动生产编码的规则要能自己定义
                     	长度检查： 开启后，可以设置是多少位的，核对下防止扫描错误
               标签打印：
                      开启标签打印 使能或者关闭   使能后，每次测试完都会打印出来标签
                   编码类型 就是打印时候对应的是用二维码还是条形码打印

      3-4 设备设置
                
           打印机可以手动刷新获取，可以连接也可以主动断开，可以预览打印的结果 也可以测试打印
           设备连接地址JCY5001 可以手动刷新获得，第一次进入时候，如果没有连接设备，要自动弹出来设备设置窗口，连接后，关闭，下次再进入就不用再连接了

     3-5 关于
          增加一个logo。以及一个二维码，微信公众号
          显示 设备名称 JCY5001A 鲸测云 8路电池阻抗筛选仪
          软件 版本号：可以动态获得
         固件版本：可以动态获得
        设备通道数量：可以从设备中动态获得
                 
    
4  导出数据：进入后显示一个数据显示界面，用于做数据分析 需要做2个表 一个是测试结果表，一个是数据的明细表，每次测试时候对应的保存下来，测试结果的数据格式列名：   批次号   操作员  电池类型 电池规格 标准电压  标准容量 标准内阻  分容值 电池电池码  通道  电压 Rs
 