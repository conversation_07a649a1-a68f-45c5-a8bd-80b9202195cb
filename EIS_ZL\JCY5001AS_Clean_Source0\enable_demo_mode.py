#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示模式启用脚本
用于在没有实际设备连接时启用演示模式，允许测试功能正常运行

Author: Assistant
Date: 2025-07-04
"""

import json
import os
import sys

def enable_demo_mode():
    """启用演示模式"""
    try:
        config_path = "config/app_config.json"
        
        # 读取现有配置
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        # 添加演示模式配置
        config['demo_mode'] = {
            'enabled': True,
            'skip_device_check': True,
            'use_simulated_data': True,
            'simulated_voltages': [3.2, 3.21, 3.19, 3.22, 3.18, 3.23, 3.20, 3.21]
        }
        
        # 修改设备连接配置为演示模式
        if 'device' not in config:
            config['device'] = {}
        if 'connection' not in config['device']:
            config['device']['connection'] = {}
        
        config['device']['connection']['demo_mode'] = True
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 演示模式已启用")
        print("📝 配置已保存到:", config_path)
        print("🔧 现在可以在没有实际设备的情况下测试应用程序功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 启用演示模式失败: {e}")
        return False

def disable_demo_mode():
    """禁用演示模式"""
    try:
        config_path = "config/app_config.json"
        
        if not os.path.exists(config_path):
            print("⚠️ 配置文件不存在")
            return True
        
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 移除演示模式配置
        if 'demo_mode' in config:
            del config['demo_mode']
        
        if 'device' in config and 'connection' in config['device']:
            if 'demo_mode' in config['device']['connection']:
                del config['device']['connection']['demo_mode']
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 演示模式已禁用")
        print("📝 配置已保存到:", config_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 禁用演示模式失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 JCY5001AS 演示模式配置工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1].lower() == 'disable':
            disable_demo_mode()
        else:
            enable_demo_mode()
    else:
        print("选择操作:")
        print("1. 启用演示模式")
        print("2. 禁用演示模式")
        
        choice = input("请输入选择 (1/2): ").strip()
        
        if choice == '1':
            enable_demo_mode()
        elif choice == '2':
            disable_demo_mode()
        else:
            print("❌ 无效选择")
