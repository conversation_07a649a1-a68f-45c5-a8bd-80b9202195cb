#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试通道异常状态
检查哪些通道被标记为异常，导致频点被跳过
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.communication_manager import CommunicationManager
from backend.channel_exception_manager import ChannelExceptionManager

def debug_channel_exceptions():
    """调试通道异常状态"""
    print("🔍 调试通道异常状态")
    print("=" * 80)
    
    try:
        # 1. 创建配置管理器
        config_manager = ConfigManager()
        
        # 2. 创建通信管理器
        print("📋 1. 创建通信管理器...")
        comm_manager = CommunicationManager(config_manager)
        
        # 3. 尝试连接设备
        print("🔌 2. 尝试连接设备...")
        if comm_manager.connect():
            print("✅ 设备连接成功")
            
            # 4. 获取通道状态
            print(f"\n📊 3. 获取通道状态...")
            enabled_channels = [1, 2, 3, 4, 5, 6, 7, 8]
            
            try:
                # 获取所有通道状态
                status_codes = []
                for channel in enabled_channels:
                    channel_idx = channel - 1
                    status = comm_manager.get_measurement_status(channel_idx)
                    status_codes.append(status)
                    print(f"   通道{channel}: 状态码 0x{status:04X}")
                
                # 5. 创建异常管理器并检查状态
                print(f"\n🔧 4. 检查通道异常状态...")
                exception_manager = ChannelExceptionManager()
                
                normal_channels = []
                exception_channels = []
                
                for i, channel in enumerate(enabled_channels):
                    status_code = status_codes[i]
                    is_normal = exception_manager.check_channel_status(
                        channel, status_code, current_frequency=1000.0
                    )
                    
                    if is_normal:
                        normal_channels.append(channel)
                        print(f"   ✅ 通道{channel}: 正常 (0x{status_code:04X})")
                    else:
                        exception_channels.append(channel)
                        exception_info = exception_manager.get_exception_info(channel)
                        if exception_info:
                            print(f"   ❌ 通道{channel}: 异常 - {exception_info.error_message} (0x{status_code:04X})")
                        else:
                            print(f"   ❌ 通道{channel}: 异常 (0x{status_code:04X})")
                
                # 6. 分析结果
                print(f"\n🎯 5. 分析结果:")
                print(f"   • 启用通道: {enabled_channels}")
                print(f"   • 正常通道: {normal_channels}")
                print(f"   • 异常通道: {exception_channels}")
                
                normal_channels_from_manager = exception_manager.get_normal_channels(enabled_channels)
                skipped_channels = exception_manager.get_skipped_channels()
                
                print(f"   • 异常管理器返回的正常通道: {normal_channels_from_manager}")
                print(f"   • 异常管理器跳过的通道: {skipped_channels}")
                
                # 7. 模拟频点测试逻辑
                print(f"\n🧪 6. 模拟频点测试逻辑:")
                test_frequencies = [1007.083, 625.612, 389.1, 242.234, 150.681]
                
                for freq in test_frequencies:
                    normal_for_freq = exception_manager.get_normal_channels(enabled_channels)
                    if not normal_for_freq:
                        print(f"   ❌ 频点 {freq}Hz: 所有通道异常，跳过该频点")
                    else:
                        print(f"   ✅ 频点 {freq}Hz: 可测试通道 {normal_for_freq}")
                
                # 8. 检查问题根源
                print(f"\n🔍 7. 问题根源分析:")
                
                if len(exception_channels) == 0:
                    print("✅ 所有通道都正常，问题不在通道异常管理器")
                elif len(normal_channels) == 0:
                    print("❌ 所有通道都被标记为异常！这就是只测试5个点的原因！")
                    print("   可能原因：")
                    print("   • 设备未正确连接")
                    print("   • 电池未安装或电压异常")
                    print("   • 硬件故障")
                    print("   • 接触不良检测过于敏感")
                elif len(normal_channels) < len(enabled_channels):
                    print(f"⚠️ 部分通道异常：{len(exception_channels)}个通道异常")
                    print("   这可能导致某些频点被跳过")
                
                # 9. 提供解决方案
                print(f"\n💡 8. 解决方案建议:")
                
                if len(normal_channels) == 0:
                    print("   1. 检查设备连接和电池安装")
                    print("   2. 临时禁用接触不良检测")
                    print("   3. 重置异常管理器状态")
                    print("   4. 检查设备状态码定义是否正确")
                
                # 10. 尝试重置异常状态
                print(f"\n🔄 9. 尝试重置异常状态...")
                exception_manager.reset_for_new_test()
                print("   异常状态已重置")
                
                # 重新检查
                normal_after_reset = exception_manager.get_normal_channels(enabled_channels)
                print(f"   重置后正常通道: {normal_after_reset}")
                
            except Exception as e:
                print(f"❌ 获取通道状态失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 断开连接
            comm_manager.disconnect()
            print("🔌 设备连接已断开")
            
        else:
            print("❌ 设备连接失败")
            print("💡 无法获取实际通道状态，但可以分析异常管理器逻辑")
            
            # 模拟分析
            print(f"\n🧪 模拟分析异常管理器逻辑:")
            exception_manager = ChannelExceptionManager()
            
            # 模拟不同状态码
            test_status_codes = [0x0001, 0x0003, 0x0004, 0x0005, 0x0006]
            status_names = ["测试中", "电池错误", "设置错误", "硬件错误", "测试完成"]
            
            for status_code, name in zip(test_status_codes, status_names):
                is_normal = exception_manager.check_channel_status(1, status_code)
                print(f"   状态码 0x{status_code:04X} ({name}): {'正常' if is_normal else '异常'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = debug_channel_exceptions()
    
    print(f"\n" + "=" * 80)
    if success:
        print("🎯 调试完成！请检查上述分析结果")
        print("💡 如果所有通道都被标记为异常，这就是只测试5个点的根本原因")
    else:
        print("❌ 调试失败，请检查错误信息")

if __name__ == "__main__":
    main()
