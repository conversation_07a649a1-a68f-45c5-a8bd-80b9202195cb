#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制修复测试限制问题
解决运行时配置被意外修改的问题
"""

import json
import sys
import os

def force_fix_config():
    """强制修复配置文件中的测试限制"""
    config_path = "config/app_config.json"
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("🔧 强制修复测试限制配置...")
        print("=" * 60)
        
        # 确保test节点存在
        if 'test' not in config:
            config['test'] = {}
        
        # 强制设置关键配置
        config['test']['count_limit_enabled'] = False
        config['test']['max_count'] = 100
        config['test']['use_parallel_staggered_mode'] = False
        config['test']['critical_frequency'] = 15.0
        
        # 确保test_params节点存在并设置正确
        if 'test_params' not in config:
            config['test_params'] = {}
        
        config['test_params']['test_mode'] = 'simultaneous'
        
        # 确保frequency节点设置正确
        if 'frequency' not in config:
            config['frequency'] = {}
        
        config['frequency']['preset_mode'] = '研究模式'
        config['frequency']['mode'] = 'multi'
        config['frequency']['single_mode'] = False
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 配置修复完成:")
        print(f"   • count_limit_enabled: {config['test']['count_limit_enabled']}")
        print(f"   • max_count: {config['test']['max_count']}")
        print(f"   • use_parallel_staggered_mode: {config['test']['use_parallel_staggered_mode']}")
        print(f"   • test_mode: {config['test_params']['test_mode']}")
        print(f"   • preset_mode: {config['frequency']['preset_mode']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置修复失败: {e}")
        return False

def check_runtime_config_override():
    """检查可能导致运行时配置覆盖的代码"""
    print("\n🔍 检查可能的运行时配置覆盖...")
    print("=" * 60)
    
    # 检查可能的问题文件
    problem_files = [
        "backend/test_config_manager.py",
        "ui/components/test_control_widget.py",
        "ui/test_flow_managers/test_configuration_manager.py"
    ]
    
    issues_found = []
    
    for file_path in problem_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查可能的问题代码
                if 'max_count' in content and '5' in content:
                    issues_found.append(f"{file_path}: 可能包含硬编码的max_count=5")
                
                if 'use_parallel_staggered_mode' in content and 'True' in content:
                    issues_found.append(f"{file_path}: 可能强制启用并行错频模式")
                
                if 'count_limit_enabled' in content and 'True' in content:
                    issues_found.append(f"{file_path}: 可能强制启用计数限制")
                    
            except Exception as e:
                print(f"   ⚠️ 无法检查文件 {file_path}: {e}")
    
    if issues_found:
        print("⚠️ 发现可能的问题:")
        for issue in issues_found:
            print(f"   • {issue}")
        print("\n💡 建议:")
        print("   1. 检查这些文件中的硬编码配置")
        print("   2. 确保配置从配置文件读取而不是硬编码")
        print("   3. 重启应用程序以应用新配置")
    else:
        print("✅ 未发现明显的配置覆盖问题")

def create_config_lock():
    """创建配置锁定机制"""
    print("\n🔒 创建配置锁定机制...")
    print("=" * 60)
    
    lock_content = """# 配置锁定标记
# 此文件存在时，禁止运行时修改关键测试配置
# 
# 锁定的配置项：
# - count_limit_enabled: false
# - max_count: 100
# - use_parallel_staggered_mode: false
# - test_mode: simultaneous
# - preset_mode: 研究模式
#
# 如需修改这些配置，请删除此文件后重启应用程序
"""
    
    try:
        with open('.config_lock', 'w', encoding='utf-8') as f:
            f.write(lock_content)
        print("✅ 配置锁定文件已创建: .config_lock")
        print("   此文件将防止运行时配置被意外修改")
        return True
    except Exception as e:
        print(f"❌ 创建配置锁定文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 强制修复测试限制问题")
    print("=" * 80)
    
    # 1. 强制修复配置文件
    config_fixed = force_fix_config()
    
    # 2. 检查运行时配置覆盖
    check_runtime_config_override()
    
    # 3. 创建配置锁定
    lock_created = create_config_lock()
    
    # 总结
    print("\n🎯 修复总结")
    print("=" * 80)
    
    if config_fixed:
        print("✅ 配置文件已修复")
        print("   • 禁用了计数限制 (count_limit_enabled: false)")
        print("   • 设置最大计数为100 (max_count: 100)")
        print("   • 禁用了并行错频模式 (use_parallel_staggered_mode: false)")
        print("   • 设置为同时启动模式 (test_mode: simultaneous)")
        print("   • 设置为研究模式 (preset_mode: 研究模式)")
    else:
        print("❌ 配置文件修复失败")
    
    if lock_created:
        print("✅ 配置锁定机制已启用")
    else:
        print("❌ 配置锁定机制创建失败")
    
    print("\n📋 下一步操作:")
    print("1. 重新启动应用程序")
    print("2. 进行研究模式测试")
    print("3. 验证是否测试了所有20个频点")
    print("4. 确认测试时间约为3-4分钟")
    
    return config_fixed and lock_created

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
