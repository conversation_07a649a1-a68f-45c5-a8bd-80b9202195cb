<?xml version="1.0" encoding="UTF-8"?>
<!-- PowerPoint模板文件 - 可以用Microsoft PowerPoint或LibreOffice Impress打开 -->
<!-- 这是一个简化的XML格式，包含了图1的基本结构 -->

<presentation>
    <slide title="图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图">
        
        <!-- 组件定义 -->
        <shapes>
            <!-- 1-被测电池 -->
            <shape id="battery" type="rectangle" x="100" y="150" width="200" height="120" 
                   fill="#ADD8E6" stroke="#000000" stroke-width="2">
                <text>1-被测电池
1.9V-5.5V</text>
            </shape>
            
            <!-- 7-测试夹具 -->
            <shape id="fixture" type="rectangle" x="100" y="350" width="200" height="120" 
                   fill="#F5DEB3" stroke="#000000" stroke-width="2">
                <text>7-测试夹具
四线制连接
精确测量</text>
            </shape>
            
            <!-- 2-DNB1101BB芯片 -->
            <shape id="chip" type="rectangle" x="450" y="300" width="300" height="200" 
                   fill="#90EE90" stroke="#000000" stroke-width="2">
                <text>2-DNB1101BB
EIS测试芯片
0.0075Hz-7800Hz</text>
            </shape>
            
            <!-- 4-外部电流源 -->
            <shape id="current_source" type="rectangle" x="100" y="600" width="200" height="150" 
                   fill="#F08080" stroke="#000000" stroke-width="2">
                <text>4-外部电流源
PMV28UNEA
20Ω/10Ω/6.67Ω/5Ω</text>
            </shape>
            
            <!-- 3-STM32控制器 -->
            <shape id="mcu" type="rectangle" x="850" y="300" width="250" height="200" 
                   fill="#FFFFE0" stroke="#000000" stroke-width="2">
                <text>3-STM32F103RCT6
主控制器
72MHz ARM</text>
            </shape>
            
            <!-- 5-串口显示屏 -->
            <shape id="display" type="rectangle" x="850" y="600" width="250" height="150" 
                   fill="#D3D3D3" stroke="#000000" stroke-width="2">
                <text>5-串口显示屏
实时显示
测试结果</text>
            </shape>
            
            <!-- 6-PC上位机 -->
            <shape id="pc" type="rectangle" x="850" y="100" width="250" height="120" 
                   fill="#B0C4DE" stroke="#000000" stroke-width="2">
                <text>6-PC上位机
Modbus RTU
数据分析</text>
            </shape>
        </shapes>
        
        <!-- 连接线定义 -->
        <connections>
            <arrow from="battery" to="fixture" type="double" label="电气连接" color="black"/>
            <arrow from="fixture" to="chip" type="single" label="电压/电流测量信号" color="blue"/>
            <arrow from="chip" to="mcu" type="single" label="SPI 1Mbps" color="purple"/>
            <arrow from="mcu" to="pc" type="single" label="USB/UART" color="red"/>
            <arrow from="mcu" to="display" type="single" label="UART 115200bps" color="green"/>
            <arrow from="chip" to="current_source" type="single" label="VSW/VDR控制信号" color="orange"/>
            <arrow from="current_source" to="fixture" type="single" label="激励电流" color="red"/>
        </connections>
        
        <!-- 说明文本 -->
        <textbox x="400" y="800" width="600" height="120" fill="#FFFACD">
            <text>信号流向说明：
1. 电池通过测试夹具连接到系统
2. DNB1101BB芯片测量电池的电压和电流
3. 外部电流源提供EIS测试所需的激励信号
4. STM32控制器处理测试数据和系统控制
5. 测试结果同时显示在本地屏幕和上位机</text>
        </textbox>
        
        <!-- 技术参数 -->
        <textbox x="400" y="970" width="600" height="30">
            <text style="italic">系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C</text>
        </textbox>
        
    </slide>
</presentation>