#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简单的可编辑专利附图文件
生成多种格式供用户选择和编辑

Author: Augment Agent
Date: 2025-01-09
"""

import os

class SimpleEditableGenerator:
    """简单可编辑文件生成器"""
    
    def __init__(self, output_dir="editable_figures"):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def create_powerpoint_template(self):
        """创建PowerPoint模板文件（XML格式）"""
        # 创建简单的PowerPoint XML模板
        pptx_content = """<?xml version="1.0" encoding="UTF-8"?>
<!-- PowerPoint模板文件 - 可以用Microsoft PowerPoint或LibreOffice Impress打开 -->
<!-- 这是一个简化的XML格式，包含了图1的基本结构 -->

<presentation>
    <slide title="图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图">
        
        <!-- 组件定义 -->
        <shapes>
            <!-- 1-被测电池 -->
            <shape id="battery" type="rectangle" x="100" y="150" width="200" height="120" 
                   fill="#ADD8E6" stroke="#000000" stroke-width="2">
                <text>1-被测电池\n1.9V-5.5V</text>
            </shape>
            
            <!-- 7-测试夹具 -->
            <shape id="fixture" type="rectangle" x="100" y="350" width="200" height="120" 
                   fill="#F5DEB3" stroke="#000000" stroke-width="2">
                <text>7-测试夹具\n四线制连接\n精确测量</text>
            </shape>
            
            <!-- 2-DNB1101BB芯片 -->
            <shape id="chip" type="rectangle" x="450" y="300" width="300" height="200" 
                   fill="#90EE90" stroke="#000000" stroke-width="2">
                <text>2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz</text>
            </shape>
            
            <!-- 4-外部电流源 -->
            <shape id="current_source" type="rectangle" x="100" y="600" width="200" height="150" 
                   fill="#F08080" stroke="#000000" stroke-width="2">
                <text>4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω</text>
            </shape>
            
            <!-- 3-STM32控制器 -->
            <shape id="mcu" type="rectangle" x="850" y="300" width="250" height="200" 
                   fill="#FFFFE0" stroke="#000000" stroke-width="2">
                <text>3-STM32F103RCT6\n主控制器\n72MHz ARM</text>
            </shape>
            
            <!-- 5-串口显示屏 -->
            <shape id="display" type="rectangle" x="850" y="600" width="250" height="150" 
                   fill="#D3D3D3" stroke="#000000" stroke-width="2">
                <text>5-串口显示屏\n实时显示\n测试结果</text>
            </shape>
            
            <!-- 6-PC上位机 -->
            <shape id="pc" type="rectangle" x="850" y="100" width="250" height="120" 
                   fill="#B0C4DE" stroke="#000000" stroke-width="2">
                <text>6-PC上位机\nModbus RTU\n数据分析</text>
            </shape>
        </shapes>
        
        <!-- 连接线定义 -->
        <connections>
            <arrow from="battery" to="fixture" type="double" label="电气连接" color="black"/>
            <arrow from="fixture" to="chip" type="single" label="电压/电流测量信号" color="blue"/>
            <arrow from="chip" to="mcu" type="single" label="SPI 1Mbps" color="purple"/>
            <arrow from="mcu" to="pc" type="single" label="USB/UART" color="red"/>
            <arrow from="mcu" to="display" type="single" label="UART 115200bps" color="green"/>
            <arrow from="chip" to="current_source" type="single" label="VSW/VDR控制信号" color="orange"/>
            <arrow from="current_source" to="fixture" type="single" label="激励电流" color="red"/>
        </connections>
        
        <!-- 说明文本 -->
        <textbox x="400" y="800" width="600" height="120" fill="#FFFACD">
            <text>信号流向说明：
1. 电池通过测试夹具连接到系统
2. DNB1101BB芯片测量电池的电压和电流
3. 外部电流源提供EIS测试所需的激励信号
4. STM32控制器处理测试数据和系统控制
5. 测试结果同时显示在本地屏幕和上位机</text>
        </textbox>
        
        <!-- 技术参数 -->
        <textbox x="400" y="970" width="600" height="30">
            <text style="italic">系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C</text>
        </textbox>
        
    </slide>
</presentation>"""
        
        filepath = os.path.join(self.output_dir, 'figure1_powerpoint_template.xml')
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(pptx_content)
        
        print(f"PowerPoint模板已保存: {filepath}")
    
    def create_visio_template(self):
        """创建Visio模板文件"""
        visio_content = """<?xml version="1.0" encoding="UTF-8"?>
<!-- Microsoft Visio模板文件 -->
<!-- 可以导入到Visio或其他支持的绘图软件中 -->

<visio:drawing xmlns:visio="http://schemas.microsoft.com/visio/2003/core">
    <visio:pages>
        <visio:page name="图1-系统架构图" width="1400" height="1000">
            
            <!-- 标题 -->
            <visio:shape id="title" type="text" x="700" y="50" width="800" height="40">
                <visio:text font-size="24" font-weight="bold" text-align="center">
                    图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图
                </visio:text>
            </visio:shape>
            
            <!-- 系统组件 -->
            <visio:shape id="battery" type="rectangle" x="100" y="150" width="200" height="120" 
                        fill="#ADD8E6" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>1-被测电池\n1.9V-5.5V</visio:text>
            </visio:shape>
            
            <visio:shape id="fixture" type="rectangle" x="100" y="350" width="200" height="120" 
                        fill="#F5DEB3" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>7-测试夹具\n四线制连接\n精确测量</visio:text>
            </visio:shape>
            
            <visio:shape id="chip" type="rectangle" x="450" y="300" width="300" height="200" 
                        fill="#90EE90" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz</visio:text>
            </visio:shape>
            
            <visio:shape id="current_source" type="rectangle" x="100" y="600" width="200" height="150" 
                        fill="#F08080" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω</visio:text>
            </visio:shape>
            
            <visio:shape id="mcu" type="rectangle" x="850" y="300" width="250" height="200" 
                        fill="#FFFFE0" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>3-STM32F103RCT6\n主控制器\n72MHz ARM</visio:text>
            </visio:shape>
            
            <visio:shape id="display" type="rectangle" x="850" y="600" width="250" height="150" 
                        fill="#D3D3D3" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>5-串口显示屏\n实时显示\n测试结果</visio:text>
            </visio:shape>
            
            <visio:shape id="pc" type="rectangle" x="850" y="100" width="250" height="120" 
                        fill="#B0C4DE" stroke="#000000" stroke-width="2" corner-radius="10">
                <visio:text>6-PC上位机\nModbus RTU\n数据分析</visio:text>
            </visio:shape>
            
            <!-- 连接器 -->
            <visio:connector id="conn1" from="battery" to="fixture" type="double-arrow" 
                           stroke="black" stroke-width="2" label="电气连接"/>
            
            <visio:connector id="conn2" from="fixture" to="chip" type="arrow" 
                           stroke="blue" stroke-width="2" label="电压/电流测量信号"/>
            
            <visio:connector id="conn3" from="chip" to="mcu" type="arrow" 
                           stroke="purple" stroke-width="2" label="SPI 1Mbps"/>
            
            <visio:connector id="conn4" from="mcu" to="pc" type="arrow" 
                           stroke="red" stroke-width="2" label="USB/UART"/>
            
            <visio:connector id="conn5" from="mcu" to="display" type="arrow" 
                           stroke="green" stroke-width="2" label="UART 115200bps"/>
            
            <visio:connector id="conn6" from="chip" to="current_source" type="arrow" 
                           stroke="orange" stroke-width="2" label="VSW/VDR控制信号"/>
            
            <visio:connector id="conn7" from="current_source" to="fixture" type="arrow" 
                           stroke="red" stroke-width="2" label="激励电流"/>
            
            <!-- 说明框 -->
            <visio:shape id="description" type="rectangle" x="400" y="800" width="600" height="120" 
                        fill="#FFFACD" stroke="gray" stroke-width="1">
                <visio:text>信号流向说明：
1. 电池通过测试夹具连接到系统
2. DNB1101BB芯片测量电池的电压和电流
3. 外部电流源提供EIS测试所需的激励信号
4. STM32控制器处理测试数据和系统控制
5. 测试结果同时显示在本地屏幕和上位机</visio:text>
            </visio:shape>
            
            <!-- 技术参数 -->
            <visio:shape id="params" type="text" x="400" y="970" width="600" height="30">
                <visio:text font-style="italic" text-align="center">
                    系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C
                </visio:text>
            </visio:shape>
            
        </visio:page>
    </visio:pages>
</visio:drawing>"""
        
        filepath = os.path.join(self.output_dir, 'figure1_visio_template.xml')
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(visio_content)
        
        print(f"Visio模板已保存: {filepath}")
    
    def create_text_template(self):
        """创建纯文本模板，便于手动编辑"""
        text_content = """# 图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图

## 组件列表及位置坐标

### 1. 被测电池
- 位置: (100, 150)
- 尺寸: 200×120
- 颜色: 浅蓝色 (#ADD8E6)
- 文本: "1-被测电池\n1.9V-5.5V"

### 2. 测试夹具
- 位置: (100, 350)
- 尺寸: 200×120
- 颜色: 小麦色 (#F5DEB3)
- 文本: "7-测试夹具\n四线制连接\n精确测量"

### 3. DNB1101BB芯片
- 位置: (450, 300)
- 尺寸: 300×200
- 颜色: 浅绿色 (#90EE90)
- 文本: "2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz"

### 4. 外部电流源
- 位置: (100, 600)
- 尺寸: 200×150
- 颜色: 浅珊瑚色 (#F08080)
- 文本: "4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω"

### 5. STM32控制器
- 位置: (850, 300)
- 尺寸: 250×200
- 颜色: 浅黄色 (#FFFFE0)
- 文本: "3-STM32F103RCT6\n主控制器\n72MHz ARM"

### 6. 串口显示屏
- 位置: (850, 600)
- 尺寸: 250×150
- 颜色: 浅灰色 (#D3D3D3)
- 文本: "5-串口显示屏\n实时显示\n测试结果"

### 7. PC上位机
- 位置: (850, 100)
- 尺寸: 250×120
- 颜色: 浅钢蓝色 (#B0C4DE)
- 文本: "6-PC上位机\nModbus RTU\n数据分析"

## 连接关系

### 箭头连接列表
1. 被测电池 ↔ 测试夹具 (双向，黑色) - "电气连接"
2. 测试夹具 → DNB1101BB芯片 (单向，蓝色) - "电压/电流测量信号"
3. DNB1101BB芯片 → STM32控制器 (单向，紫色) - "SPI 1Mbps"
4. STM32控制器 → PC上位机 (单向，红色) - "USB/UART"
5. STM32控制器 → 串口显示屏 (单向，绿色) - "UART 115200bps"
6. DNB1101BB芯片 → 外部电流源 (单向，橙色) - "VSW/VDR控制信号"
7. 外部电流源 → 测试夹具 (单向，红色) - "激励电流"

## 说明文本

### 信号流向说明框
- 位置: (400, 800)
- 尺寸: 600×120
- 背景色: 浅黄色 (#FFFACD)
- 内容:
  1. 电池通过测试夹具连接到系统
  2. DNB1101BB芯片测量电池的电压和电流
  3. 外部电流源提供EIS测试所需的激励信号
  4. STM32控制器处理测试数据和系统控制
  5. 测试结果同时显示在本地屏幕和上位机

### 技术参数
- 位置: (400, 970)
- 文本: "系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C"
- 样式: 斜体，居中对齐

## 编辑说明

1. 可以使用任何绘图软件（如LibreOffice Draw、Microsoft Visio、PowerPoint等）
2. 按照上述坐标和尺寸创建矩形框
3. 设置相应的填充颜色和边框
4. 添加文本标签
5. 绘制箭头连接线
6. 调整位置和样式以获得最佳视觉效果

## 导出建议

- 推荐导出为PNG或PDF格式用于专利申请
- 分辨率建议设置为300 DPI
- 确保文字清晰可读
- 保持专业的视觉效果"""
        
        filepath = os.path.join(self.output_dir, 'figure1_editing_guide.txt')
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(text_content)
        
        print(f"编辑指南已保存: {filepath}")
    
    def generate_all_templates(self):
        """生成所有模板文件"""
        print("开始生成可编辑的模板文件...")
        self.create_powerpoint_template()
        self.create_visio_template()
        self.create_text_template()
        print("\n所有模板文件生成完成！")
        print(f"文件保存在: {self.output_dir}")
        print("\n可用的编辑选项:")
        print("1. PowerPoint模板 (.xml) - 可导入Microsoft PowerPoint或LibreOffice Impress")
        print("2. Visio模板 (.xml) - 可导入Microsoft Visio或其他流程图软件")
        print("3. 编辑指南 (.txt) - 包含详细的坐标和样式信息，可手动在任何绘图软件中重建")
        print("4. SVG文件 - 可用Inkscape、Adobe Illustrator或LibreOffice Draw编辑")
        print("5. HTML文件 - 可在浏览器中查看和编辑")

if __name__ == "__main__":
    generator = SimpleEditableGenerator()
    generator.generate_all_templates()
