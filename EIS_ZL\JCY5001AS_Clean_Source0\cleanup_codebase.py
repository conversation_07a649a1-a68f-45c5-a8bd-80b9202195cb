#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A 代码库清理脚本

功能描述:
- 自动清理备份文件、构建产物、临时文件
- 重组目录结构
- 生成清理报告

作者: <PERSON>
创建时间: 2025-01-01
版本: V1.0.0
"""

import os
import shutil
import glob
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Set

class CodebaseCleanup:
    """代码库清理工具"""
    
    def __init__(self, project_root: str = "."):
        """初始化清理工具"""
        self.project_root = Path(project_root).resolve()
        self.cleanup_report = {
            "start_time": datetime.now().isoformat(),
            "deleted_files": [],
            "deleted_dirs": [],
            "moved_files": [],
            "errors": [],
            "summary": {}
        }
        
    def run_cleanup(self, dry_run: bool = True) -> Dict:
        """执行完整清理流程"""
        print(f"🧹 开始清理代码库: {self.project_root}")
        print(f"📋 模式: {'预览模式' if dry_run else '执行模式'}")
        print("=" * 60)
        
        try:
            # 第一阶段：删除备份文件
            self._cleanup_backup_files(dry_run)
            
            # 第二阶段：删除构建产物
            self._cleanup_build_artifacts(dry_run)
            
            # 第三阶段：删除临时文件
            self._cleanup_temp_files(dry_run)
            
            # 第四阶段：清理Python缓存
            self._cleanup_python_cache(dry_run)
            
            # 第五阶段：清理日志文件
            self._cleanup_log_files(dry_run)
            
            # 生成清理报告
            self._generate_report()
            
        except Exception as e:
            self.cleanup_report["errors"].append(f"清理过程出错: {str(e)}")
            print(f"❌ 清理过程出错: {e}")
        
        return self.cleanup_report
    
    def _cleanup_backup_files(self, dry_run: bool):
        """清理备份文件"""
        print("🗂️  清理备份文件...")
        
        # 备份文件模式
        backup_patterns = [
            "*.backup_*",
            "*.py.backup*",
            "backup*/",
            "backup_*/",
            "*_backup_*/",
            "*.bak",
            "*.orig"
        ]
        
        deleted_count = 0
        for pattern in backup_patterns:
            matches = list(self.project_root.rglob(pattern))
            for path in matches:
                if self._should_delete_path(path):
                    deleted_count += 1
                    if not dry_run:
                        self._safe_delete(path)
                    self.cleanup_report["deleted_files" if path.is_file() else "deleted_dirs"].append(str(path))
        
        print(f"   ✅ 备份文件: {deleted_count} 个")
    
    def _cleanup_build_artifacts(self, dry_run: bool):
        """清理构建产物"""
        print("🔨 清理构建产物...")
        
        # 构建产物目录和文件
        build_patterns = [
            "main.build/",
            "main.dist/",
            "dist/",
            "build/",
            "*.egg-info/",
            "*.spec",
            "nuitka-crash-report.xml"
        ]
        
        deleted_count = 0
        for pattern in build_patterns:
            matches = list(self.project_root.rglob(pattern))
            for path in matches:
                deleted_count += 1
                if not dry_run:
                    self._safe_delete(path)
                self.cleanup_report["deleted_files" if path.is_file() else "deleted_dirs"].append(str(path))
        
        print(f"   ✅ 构建产物: {deleted_count} 个")
    
    def _cleanup_temp_files(self, dry_run: bool):
        """清理临时文件"""
        print("🗑️  清理临时文件...")
        
        # 临时文件模式
        temp_patterns = [
            "test_*.py",
            "fix_*.py",
            "optimize_*.py",
            "enhanced_*.py",
            "execute_*.py",
            "simple_*.py",
            "*.tmp",
            "*.temp",
            "algorithm_comparison.png",
            "equivalent_circuit_real_data_analysis.png",
            "individual_methods_test.png",
            "rct_debug_analysis.png",
            "rs_calculation_comparison.png",
            "rs_debug_nyquist.png",
            "smoothing_test_results.png"
        ]
        
        deleted_count = 0
        for pattern in temp_patterns:
            matches = list(self.project_root.glob(pattern))
            for path in matches:
                # 排除重要的测试文件
                if not self._is_important_file(path):
                    deleted_count += 1
                    if not dry_run:
                        self._safe_delete(path)
                    self.cleanup_report["deleted_files"].append(str(path))
        
        print(f"   ✅ 临时文件: {deleted_count} 个")
    
    def _cleanup_python_cache(self, dry_run: bool):
        """清理Python缓存"""
        print("🐍 清理Python缓存...")
        
        cache_patterns = [
            "__pycache__/",
            "*.pyc",
            "*.pyo",
            "*.pyd"
        ]
        
        deleted_count = 0
        for pattern in cache_patterns:
            matches = list(self.project_root.rglob(pattern))
            for path in matches:
                deleted_count += 1
                if not dry_run:
                    self._safe_delete(path)
                self.cleanup_report["deleted_files" if path.is_file() else "deleted_dirs"].append(str(path))
        
        print(f"   ✅ Python缓存: {deleted_count} 个")
    
    def _cleanup_log_files(self, dry_run: bool):
        """清理日志文件"""
        print("📝 清理日志文件...")
        
        log_patterns = [
            "logs/",
            "*.log",
            "test_ui_fix.log"
        ]
        
        deleted_count = 0
        for pattern in log_patterns:
            matches = list(self.project_root.rglob(pattern))
            for path in matches:
                deleted_count += 1
                if not dry_run:
                    self._safe_delete(path)
                self.cleanup_report["deleted_files" if path.is_file() else "deleted_dirs"].append(str(path))
        
        print(f"   ✅ 日志文件: {deleted_count} 个")
    
    def _should_delete_path(self, path: Path) -> bool:
        """判断是否应该删除路径"""
        # 排除重要目录
        important_dirs = {
            "config", "data", "resources", "docs", 
            "backend", "ui", "utils", "templates"
        }
        
        # 检查是否在重要目录中
        for part in path.parts:
            if part in important_dirs:
                return False
        
        return True
    
    def _is_important_file(self, path: Path) -> bool:
        """判断是否是重要文件"""
        important_files = {
            "test_config_widget.py",  # 重要的测试配置文件
            "requirements.txt",
            "main.py",
            "README.md"
        }
        
        return path.name in important_files
    
    def _safe_delete(self, path: Path):
        """安全删除文件或目录"""
        try:
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
        except Exception as e:
            self.cleanup_report["errors"].append(f"删除失败 {path}: {str(e)}")
    
    def _generate_report(self):
        """生成清理报告"""
        self.cleanup_report["end_time"] = datetime.now().isoformat()
        self.cleanup_report["summary"] = {
            "deleted_files_count": len(self.cleanup_report["deleted_files"]),
            "deleted_dirs_count": len(self.cleanup_report["deleted_dirs"]),
            "moved_files_count": len(self.cleanup_report["moved_files"]),
            "errors_count": len(self.cleanup_report["errors"])
        }
        
        # 保存报告
        report_file = self.project_root / "cleanup_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_report, f, indent=2, ensure_ascii=False)
        
        print("\n📊 清理报告:")
        print(f"   删除文件: {self.cleanup_report['summary']['deleted_files_count']} 个")
        print(f"   删除目录: {self.cleanup_report['summary']['deleted_dirs_count']} 个")
        print(f"   移动文件: {self.cleanup_report['summary']['moved_files_count']} 个")
        print(f"   错误数量: {self.cleanup_report['summary']['errors_count']} 个")
        print(f"   报告文件: {report_file}")

def create_gitignore():
    """创建.gitignore文件"""
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyQt
*.ui

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Data
data/test_results.db
data/statistics_counter.json
data/continuous_test_count.json

# Build artifacts
main.build/
main.dist/
nuitka-crash-report.xml

# Backup files
*.backup_*
*.bak
*.orig
backup*/

# Temporary files
*.tmp
*.temp
test_*.py
fix_*.py
optimize_*.py

# Images (temporary)
*.png
!resources/images/*.png
!docs/images/*.png
"""
    
    with open('.gitignore', 'w', encoding='utf-8') as f:
        f.write(gitignore_content)
    
    print("✅ 已创建 .gitignore 文件")

def create_project_structure():
    """创建标准项目结构"""
    print("🏗️  创建标准项目结构...")

    # 标准目录结构
    directories = [
        "src",
        "docs",
        "tests/unit",
        "tests/integration",
        "scripts",
        "data/samples",
        "config/templates",
        "resources/icons",
        "resources/images",
        "resources/styles"
    ]

    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        # 创建__init__.py文件(如果是Python包)
        if dir_path.startswith("src") or dir_path.startswith("tests"):
            init_file = Path(dir_path) / "__init__.py"
            if not init_file.exists():
                init_file.touch()

    print("   ✅ 项目结构创建完成")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="JCY5001A 代码库清理工具")
    parser.add_argument("--execute", action="store_true", help="执行清理(默认为预览模式)")
    parser.add_argument("--create-gitignore", action="store_true", help="创建.gitignore文件")
    parser.add_argument("--create-structure", action="store_true", help="创建标准项目结构")

    args = parser.parse_args()

    if args.create_gitignore:
        create_gitignore()

    if args.create_structure:
        create_project_structure()

    # 执行清理
    cleanup = CodebaseCleanup()
    report = cleanup.run_cleanup(dry_run=not args.execute)

    if not args.execute:
        print("\n💡 这是预览模式，没有实际删除文件")
        print("   使用 --execute 参数执行实际清理")

if __name__ == "__main__":
    main()
