#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 绿框字体补全和边框线条修复测试
测试"测试统计"标题字体完整显示和绿色边框线条完整
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.components.statistics_widget import StatisticsWidget

class TestGreenFrameFixWindow(QMainWindow):
    """绿框字体补全和边框线条修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS 绿框字体补全和边框线条修复测试")
        self.setGeometry(100, 100, 1400, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 绿框字体补全和边框线条修复测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
测试内容：
1. ✅ 补全绿框处的字体显示 - 测试统计
   - "测试统计"标题字体完整显示，不被截断
   - 增加标题字体到14pt，确保清晰可见
   - 设置标题最小宽度80px，确保"测试统计"四个字完整显示
   - 添加内边距，确保文字不贴边

2. ✅ 补全绿色边框线条
   - 绿色边框宽度增加到4px，确保边框线条清晰可见
   - 添加外边距2px，确保边框不被裁剪
   - 添加内边距8px，确保内容不贴边框
   - 设置box-sizing: border-box，确保边框完整计算
   - 强制边框样式和颜色，确保四周边框线条完整

3. ✅ 整体优化
   - 设置最小宽度800px，确保整个统计区域有足够空间
   - 控制最大高度350px，避免过度拉伸
   - 确保边框不被父容器裁剪
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 创建统计显示组件
        self.statistics_widget = StatisticsWidget()
        main_layout.addWidget(self.statistics_widget)
        
        # 添加测试数据
        self.load_test_data()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 更新统计数据
            self.statistics_widget.update_statistics(
                total_count=1530,
                qualified_count=870,
                unqualified_count=660,
                yield_rate=56.9
            )
            
            # 更新档位分布数据
            grade_data = {
                (0, 0): 114, (0, 1): 112, (0, 2): 90,   # Rs1行
                (1, 0): 96,  (1, 1): 106, (1, 2): 101,  # Rs2行  
                (2, 0): 88,  (2, 1): 82,  (2, 2): 81    # Rs3行
            }
            
            self.statistics_widget.update_grade_distribution(grade_data)
            
            print("✅ 测试数据加载完成")
            print("📊 统计数据: 总数=1530, 合格=870, 不合格=660, 良率=56.9%")
            print("📋 档位分布: 3x3表格数据已填充")
            print("🎯 重点观察:")
            print("   1. '测试统计'标题是否完整显示，无缺失")
            print("   2. 绿色边框是否四周完整，线条清晰")
            print("   3. 边框是否不被裁剪，完整可见")
            print("   4. 标题字体是否清晰，大小适中")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("JCY5001AS绿框修复测试")
    app.setApplicationVersion("1.0")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = TestGreenFrameFixWindow()
    window.show()
    
    print("🚀 JCY5001AS 绿框字体补全和边框线条修复测试启动")
    print("📝 测试重点:")
    print("   1. 绿框处的字体是否完整显示")
    print("   2. 绿色边框线条是否四周完整")
    print("   3. 标题'测试统计'是否清晰可见")
    print("   4. 边框是否不被裁剪")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
