#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统计框优化布局效果
验证统计框宽度适度增加和间距调整的效果
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QGroupBox, QFrame, QPushButton, QGridLayout)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
import random

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.components.statistics_widget import StatisticsWidget
    from utils.config_manager import ConfigManager
    REAL_COMPONENT_AVAILABLE = True
except ImportError as e:
    print(f"导入真实组件失败: {e}")
    REAL_COMPONENT_AVAILABLE = False

class StatisticsOptimizedLayoutTest(QMainWindow):
    """统计框优化布局测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 统计框优化布局测试")
        self.setGeometry(100, 100, 1400, 800)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 统计框优化布局测试")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 添加详细说明
        info_label = QLabel("""
        🎯 统计框优化布局调整内容：
        
        📊 统计框宽度适度增加（约70%原宽度）：
        • 总测试数框: 35px → 48px (+37%, 约70%原宽度)
        • 合格数框: 35px → 48px (+37%, 约70%原宽度)  
        • 不合格数框: 28px → 38px (+36%, 约70%原宽度)
        • 良率框: 28px → 38px (+36%, 约70%原宽度)
        
        📏 间距优化调整：
        • GridLayout整体间距: 8px → 4px (减少50%)
        • 水平间距: 默认 → 6px (紧凑布局)
        • 标签列和统计框列都设为固定宽度，避免过度拉伸
        
        ✅ 预期效果：
        • 统计框有足够空间显示数字内容
        • 统计框更靠近左侧文字标签
        • 整体布局更加紧凑协调
        • 保持原有颜色和样式不变
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #e8f5e8; 
            padding: 15px; 
            border-radius: 8px; 
            border: 2px solid #27ae60;
            line-height: 1.4;
        """)
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        self._create_test_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
        
        # 初始化定时器用于模拟数据更新
        self._init_timer()
    
    def _create_test_area(self, layout):
        """创建测试区域"""
        test_frame = QFrame()
        test_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        test_layout = QVBoxLayout(test_frame)
        
        if REAL_COMPONENT_AVAILABLE:
            # 使用真实统计组件
            try:
                config_manager = ConfigManager()
                
                # 创建统计组件
                self.statistics_widget = StatisticsWidget(config_manager)
                
                # 设置测试数据
                self._set_test_statistics_data()
                
                test_layout.addWidget(self.statistics_widget)
                
            except Exception as e:
                print(f"创建真实统计组件失败: {e}")
                # 回退到演示版本
                demo_widget = self._create_demo_statistics()
                test_layout.addWidget(demo_widget)
        else:
            # 使用演示版本
            demo_widget = self._create_demo_statistics()
            test_layout.addWidget(demo_widget)
        
        layout.addWidget(test_frame)
    
    def _set_test_statistics_data(self):
        """设置测试统计数据"""
        try:
            # 模拟一些测试数据
            for i in range(235):  # 模拟235个测试
                is_pass = random.choice([True, True, False])  # 约67%通过率
                rs_grade = random.randint(1, 3)
                rct_grade = random.randint(1, 3)
                self.statistics_widget.add_test_result(is_pass, rs_grade, rct_grade)
                
        except Exception as e:
            print(f"设置测试统计数据失败: {e}")
    
    def _create_demo_statistics(self):
        """创建演示统计组件（当无法加载真实组件时）"""
        # 创建统计组框
        stats_group = QGroupBox("测试统计 - 优化布局演示")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 5px;
                margin-top: 0.2ex;
                padding-top: 2px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #27ae60;
                font-size: 13pt;
                font-weight: bold;
            }
        """)
        
        # 创建内容布局
        content_layout = QHBoxLayout(stats_group)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(12)
        
        # 左侧：统计数据
        stats_widget = self._create_demo_statistics_section()
        content_layout.addWidget(stats_widget, 1)
        
        # 右侧：说明文字
        info_widget = self._create_demo_info_section()
        content_layout.addWidget(info_widget, 1)
        
        return stats_group
    
    def _create_demo_statistics_section(self):
        """创建演示统计数据区域"""
        stats_widget = QWidget()
        stats_layout = QGridLayout(stats_widget)
        stats_layout.setSpacing(4)  # 减少间距
        
        # 总测试数 - 优化后宽度
        stats_layout.addWidget(QLabel("总测试数:"), 0, 0)
        total_label = QLabel("235")
        total_label.setStyleSheet("""
            font-size: 16pt; font-weight: bold; color: #2c3e50;
            background-color: #ecf0f1; border: 1px solid #bdc3c7;
            border-radius: 4px; padding: 6px 8px;
            min-width: 48px; max-width: 48px; text-align: center;
        """)
        total_label.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(total_label, 0, 1)
        
        # 合格数 - 优化后宽度
        stats_layout.addWidget(QLabel("合格数:"), 1, 0)
        pass_label = QLabel("118")
        pass_label.setStyleSheet("""
            font-size: 16pt; font-weight: bold; color: #27ae60;
            background-color: #d5f4e6; border: 1px solid #27ae60;
            border-radius: 4px; padding: 6px 8px;
            min-width: 48px; max-width: 48px; text-align: center;
        """)
        pass_label.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(pass_label, 1, 1)
        
        # 不合格数 - 优化后宽度
        stats_layout.addWidget(QLabel("不合格数:"), 2, 0)
        fail_label = QLabel("117")
        fail_label.setStyleSheet("""
            font-size: 14pt; font-weight: bold; color: #e74c3c;
            background-color: #fadbd8; border: 1px solid #e74c3c;
            border-radius: 2px; padding: 2px 5px;
            min-width: 38px; max-width: 38px; text-align: center;
        """)
        fail_label.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(fail_label, 2, 1)
        
        # 良率 - 优化后宽度
        stats_layout.addWidget(QLabel("良率:"), 3, 0)
        yield_label = QLabel("50.2%")
        yield_label.setStyleSheet("""
            font-size: 14pt; font-weight: bold; color: #3498db;
            background-color: #ebf3fd; border: 1px solid #3498db;
            border-radius: 2px; padding: 2px 5px;
            min-width: 38px; max-width: 38px; text-align: center;
        """)
        yield_label.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(yield_label, 3, 1)
        
        # 设置列拉伸和间距 - 优化布局
        stats_layout.setColumnStretch(0, 0)  # 标签列固定宽度
        stats_layout.setColumnStretch(1, 0)  # 统计框列也固定宽度
        stats_layout.setHorizontalSpacing(6)  # 减少水平间距
        
        return stats_widget
    
    def _create_demo_info_section(self):
        """创建演示信息区域"""
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        info_title = QLabel("布局优化对比")
        info_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        info_layout.addWidget(info_title)
        
        info_text = QLabel("""
优化前后对比：

📊 宽度调整：
   • 总测试数: 35px → 48px
   • 合格数: 35px → 48px  
   • 不合格数: 28px → 38px
   • 良率: 28px → 38px

📏 间距优化：
   • 整体间距: 8px → 4px
   • 水平间距: 设置为6px
   • 列拉伸: 改为固定宽度

✅ 优化效果：
   • 数字显示空间充足
   • 统计框更靠近标签
   • 布局更加紧凑
   • 保持原有样式
        """)
        info_text.setFont(QFont("Microsoft YaHei", 9))
        info_text.setStyleSheet("""
            color: #34495e; 
            background-color: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px;
            border: 1px solid #dee2e6;
        """)
        info_layout.addWidget(info_text)
        
        info_layout.addStretch()
        return info_widget
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 更新数据按钮
        update_data_btn = QPushButton("模拟数据更新")
        update_data_btn.clicked.connect(self._simulate_data_update)
        update_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(update_data_btn)
        
        # 重置数据按钮
        reset_data_btn = QPushButton("重置统计数据")
        reset_data_btn.clicked.connect(self._reset_statistics)
        reset_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(reset_data_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _init_timer(self):
        """初始化定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._auto_update_data)
        self.update_timer.start(4000)  # 每4秒自动更新一次数据
    
    def _simulate_data_update(self):
        """模拟数据更新"""
        if REAL_COMPONENT_AVAILABLE and hasattr(self, 'statistics_widget'):
            try:
                # 添加一些新的测试结果
                for _ in range(8):
                    is_pass = random.choice([True, True, False])
                    rs_grade = random.randint(1, 3)
                    rct_grade = random.randint(1, 3)
                    self.statistics_widget.add_test_result(is_pass, rs_grade, rct_grade)
                
                print("✅ 模拟数据更新完成")
            except Exception as e:
                print(f"模拟数据更新失败: {e}")
        else:
            print("✅ 演示模式：模拟数据更新")
    
    def _reset_statistics(self):
        """重置统计数据"""
        if REAL_COMPONENT_AVAILABLE and hasattr(self, 'statistics_widget'):
            try:
                self.statistics_widget.reset_statistics()
                print("✅ 统计数据已重置")
            except Exception as e:
                print(f"重置统计数据失败: {e}")
        else:
            print("✅ 演示模式：统计数据已重置")
    
    def _auto_update_data(self):
        """自动更新数据"""
        if REAL_COMPONENT_AVAILABLE and hasattr(self, 'statistics_widget'):
            try:
                # 随机添加1-3个测试结果
                for _ in range(random.randint(1, 3)):
                    is_pass = random.choice([True, True, False])  # 67%通过率
                    rs_grade = random.randint(1, 3)
                    rct_grade = random.randint(1, 3)
                    self.statistics_widget.add_test_result(is_pass, rs_grade, rct_grade)
            except Exception as e:
                print(f"自动更新数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = StatisticsOptimizedLayoutTest()
    window.show()
    
    print("🚀 JCY5001AS 统计框优化布局测试启动")
    print("=" * 60)
    print("📋 测试验证内容:")
    print("   ✓ 统计框宽度适度增加到约70%原宽度")
    print("   ✓ 总测试数和合格数框: 35px → 48px")
    print("   ✓ 不合格数和良率框: 28px → 38px")
    print("   ✓ 减少统计框与标签间距")
    print("   ✓ GridLayout间距: 8px → 4px")
    print("   ✓ 水平间距设置为6px")
    print("   ✓ 保持原有颜色和样式")
    print("=" * 60)
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
