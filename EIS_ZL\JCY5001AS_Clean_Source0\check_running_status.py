#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JCY5001AS应用程序运行状态
"""

import psutil
import time

def check_python_processes():
    """检查Python进程"""
    print("🔍 检查JCY5001AS相关Python进程...")
    print("=" * 50)
    
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and len(cmdline) > 1:
                    script_name = cmdline[1] if len(cmdline) > 1 else 'Unknown'
                    if any(keyword in script_name.lower() for keyword in ['main.py', 'test_', 'jcy5001']):
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'script': script_name,
                            'status': proc.info['status']
                        })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if python_processes:
        print(f"✅ 发现 {len(python_processes)} 个相关Python进程:")
        for i, proc in enumerate(python_processes, 1):
            script_name = proc['script'].split('\\')[-1] if '\\' in proc['script'] else proc['script']
            print(f"   {i}. PID: {proc['pid']} - {script_name} ({proc['status']})")
        
        print("\n💡 程序状态说明:")
        print("   • main.py - JCY5001AS主程序")
        print("   • test_statistics_move_left.py - 统计框移动测试")
        print("   • 其他test_*.py - 各种布局测试程序")
        
        print("\n🖥️ 如果看不到窗口:")
        print("   • 检查任务栏是否有程序图标")
        print("   • 使用Alt+Tab切换窗口")
        print("   • 程序可能在后台运行")
        
    else:
        print("❌ 未发现相关Python进程")
        print("💡 程序可能已经退出或启动失败")
    
    return python_processes

def main():
    """主函数"""
    print("🚀 JCY5001AS 程序运行状态检查")
    print("当前时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    processes = check_python_processes()
    
    print("\n" + "=" * 50)
    if processes:
        main_processes = [p for p in processes if 'main.py' in p['script']]
        test_processes = [p for p in processes if 'test_' in p['script']]
        
        if main_processes:
            print("✅ JCY5001AS主程序正在运行")
            print("   您应该能看到主界面窗口")
            print("   请查看统计区域的布局变化")
        
        if test_processes:
            print("✅ 测试程序正在运行")
            print("   您可以查看布局对比效果")
        
        print("\n📋 验证要点:")
        print("   • 统计框是否向左移动")
        print("   • 数值框是否紧贴标签")
        print("   • 中间空白是否减少")
        
    else:
        print("⚠️ 没有检测到运行中的程序")
        print("💡 建议重新启动程序")

if __name__ == "__main__":
    main()
