#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动计算EIS数据的关键参数
"""

import numpy as np

# 提供的数据（转换为数组）
freq = np.array([100.000030517578, 79.4328994750977, 63.0956497192383, 50.1187973022461, 
                39.810676574707, 31.622766494751, 25.1188793182373, 19.9526309967041, 
                15.8489456176758, 12.5892715454102, 10.0000076293945, 7.94327640533447, 
                6.30957412719727, 5.0118727684021, 3.98107433319092, 3.16227889060974, 
                2.51188802719116, 1.99526083469391, 1.58489120006561, 1.25892543792725, 
                1.00000083446503, 0.794327080249786, 0.630957663059235, 0.501187026500702, 
                0.398107141256332, 0.31622776389122, 0.251188218593597, 0.199526086449623, 
                0.158489406108856, 0.125892534852028, 0.100000008940697])

# 实部 (mΩ转换为Ω)
z_real_mohm = np.array([2.20E-01, 2.25E-01, 2.31E-01, 2.37E-01, 2.43E-01, 2.48E-01, 
                       2.52E-01, 2.56E-01, 2.59E-01, 2.62E-01, 2.65E-01, 2.67E-01, 
                       2.69E-01, 2.71E-01, 2.72E-01, 2.74E-01, 2.76E-01, 2.77E-01, 
                       2.78E-01, 2.80E-01, 2.81E-01, 2.83E-01, 2.85E-01, 2.87E-01, 
                       2.89E-01, 2.91E-01, 2.93E-01, 2.96E-01, 2.99E-01, 3.02E-01, 
                       3.06E-01])

# 虚部 (mΩ转换为Ω)
z_imag_mohm = np.array([-4.55E-02, -2.91E-02, -1.60E-02, -6.84E-03, -7.44E-04, 3.78E-03, 
                       7.34E-03, 9.10E-03, 9.80E-03, 1.03E-02, 1.07E-02, 1.08E-02, 
                       1.05E-02, 1.07E-02, 1.07E-02, 1.05E-02, 1.05E-02, 1.08E-02, 
                       1.10E-02, 1.17E-02, 1.18E-02, 1.21E-02, 1.30E-02, 1.35E-02, 
                       1.43E-02, 1.55E-02, 1.66E-02, 1.86E-02, 2.03E-02, 2.24E-02, 
                       2.50E-02])

# 转换为Ω
z_real = z_real_mohm / 1000
z_imag = z_imag_mohm / 1000

# 计算衍生参数
z_mag = np.sqrt(z_real**2 + z_imag**2)
phase_deg = np.arctan2(-z_imag, z_real) * 180 / np.pi
omega = 2 * np.pi * freq

print("=== EIS数据手动计算分析 ===\n")

print("1. 数据基本统计:")
print(f"   数据点数: {len(freq)}")
print(f"   频率范围: {freq.min():.3f} - {freq.max():.3f} Hz")
print(f"   实部范围: {z_real.min():.6f} - {z_real.max():.6f} Ω")
print(f"   虚部范围: {z_imag.min():.6f} - {z_imag.max():.6f} Ω")
print(f"   幅值范围: {z_mag.min():.6f} - {z_mag.max():.6f} Ω")
print(f"   相位范围: {phase_deg.min():.2f} - {phase_deg.max():.2f} °")

print(f"\n2. 关键频率点分析:")
# 高频点 (100 Hz)
print(f"   高频 (100 Hz): Z' = {z_real[0]:.6f} Ω, Z'' = {z_imag[0]:.6f} Ω")
# 中频点 (约10 Hz)
mid_idx = np.argmin(np.abs(freq - 10))
print(f"   中频 (~10 Hz): Z' = {z_real[mid_idx]:.6f} Ω, Z'' = {z_imag[mid_idx]:.6f} Ω")
# 低频点 (0.1 Hz)
print(f"   低频 (0.1 Hz): Z' = {z_real[-1]:.6f} Ω, Z'' = {z_imag[-1]:.6f} Ω")

print(f"\n3. 电化学参数估算:")
# 溶液电阻 (高频实部)
Rs_est = z_real[0]
print(f"   Rs (溶液电阻) ≈ {Rs_est:.6f} Ω")

# 电荷转移电阻 (低频-高频实部差)
Rct_est = z_real[-1] - z_real[0]
print(f"   Rct (电荷转移电阻) ≈ {Rct_est:.6f} Ω")

# 总阻抗
total_R = z_real[-1]
print(f"   总阻抗 ≈ {total_R:.6f} Ω")

print(f"\n4. 特征频率分析:")
# 虚部最大值对应的频率
max_imag_idx = np.argmax(-z_imag)
char_freq = freq[max_imag_idx]
print(f"   虚部最大值频率: {char_freq:.3f} Hz")
print(f"   对应阻抗: Z' = {z_real[max_imag_idx]:.6f} Ω, Z'' = {z_imag[max_imag_idx]:.6f} Ω")

# 相位零点频率
zero_phase_idx = np.argmin(np.abs(phase_deg))
zero_phase_freq = freq[zero_phase_idx]
print(f"   相位零点频率: {zero_phase_freq:.3f} Hz")

print(f"\n5. 数据质量检查:")
# 检查实部单调性
real_monotonic = np.all(np.diff(z_real) >= 0)
print(f"   实部单调递增: {real_monotonic}")

# 检查频率单调性
freq_monotonic = np.all(np.diff(freq) < 0)
print(f"   频率单调递减: {freq_monotonic}")

# 检查是否有负实部
negative_real = np.any(z_real < 0)
print(f"   存在负实部: {negative_real}")

print(f"\n6. 简化的等效电路参数估算:")
# 基于简单的几何关系估算CPE参数
# 假设在特征频率处，CPE阻抗与Rct相等
char_omega = 2 * np.pi * char_freq
# |Z_CPE| = 1/(T*ω^n) = Rct，假设n=0.8
n_est = 0.8
T_est = 1 / (Rct_est * char_omega**n_est)

print(f"   估算的CPE_T ≈ {T_est:.6e} F·s^(n-1)")
print(f"   估算的CPE_n ≈ {n_est:.3f}")
print(f"   估算的时间常数 τ = Rct×T ≈ {Rct_est * T_est:.6e} s")

print(f"\n7. 电池性能评估:")
# 内阻分析
ohmic_resistance = Rs_est
polarization_resistance = Rct_est
total_resistance = ohmic_resistance + polarization_resistance

print(f"   欧姆内阻: {ohmic_resistance*1000:.3f} mΩ")
print(f"   极化内阻: {polarization_resistance*1000:.3f} mΩ")
print(f"   总内阻: {total_resistance*1000:.3f} mΩ")
print(f"   极化阻抗占比: {(polarization_resistance/total_resistance)*100:.1f}%")

# 基于阻抗的电池状态评估
if total_resistance < 0.001:  # < 1 mΩ
    battery_status = "优秀"
elif total_resistance < 0.002:  # < 2 mΩ
    battery_status = "良好"
elif total_resistance < 0.005:  # < 5 mΩ
    battery_status = "一般"
else:
    battery_status = "较差"

print(f"   电池状态评估: {battery_status}")

print(f"\n8. 算法拟合预期:")
print(f"   Randles模型适用性: 高 (数据显示典型的RC特征)")
print(f"   预期拟合精度: R² > 0.95")
print(f"   建议拟合算法: Levenberg-Marquardt")
print(f"   参数边界建议:")
print(f"     Rs: [0, {Rs_est*2:.6f}] Ω")
print(f"     Rct: [0, {Rct_est*2:.6f}] Ω")
print(f"     CPE_T: [1e-6, 1e-2] F·s^(n-1)")
print(f"     CPE_n: [0.5, 1.0]")

print(f"\n=== 分析完成 ===")
print(f"建议使用完整的Python分析工具进行精确拟合和可视化")