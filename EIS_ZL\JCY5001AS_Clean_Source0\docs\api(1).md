# 电池阻抗测试系统 API 文档

## 1. 通用与系统接口

| 路径      | 方法 | 说明       | 认证 |
|-----------|------|------------|------|
| `/health` | GET  | 健康检查   | 否   |
| `/api`    | GET  | API信息    | 否   |

---

## 2. 用户认证相关

| 路径                  | 方法   | 说明           | 认证 |
|-----------------------|--------|----------------|------|
| `/api/auth/login`     | POST   | 用户登录       | 否   |
| `/api/auth/register`  | POST   | 用户注册       | 否   |
| `/api/auth/logout`    | POST   | 用户登出       | 是   |
| `/api/auth/refresh`   | POST   | 刷新Token      | 是   |
| `/api/auth/profile`   | GET    | 获取用户信息   | 是   |

---

## 3. 设备管理

| 路径                    | 方法   | 说明           | 认证 |
|-------------------------|--------|----------------|------|
| `/api/devices`          | GET    | 获取设备列表   | 是   |
| `/api/devices`          | POST   | 新增设备       | 是   |
| `/api/devices/<id>`     | GET    | 获取设备详情   | 是   |
| `/api/devices/<id>`     | PUT    | 更新设备信息   | 是   |
| `/api/devices/<id>`     | DELETE | 删除设备       | 是   |

---

## 4. 电池管理

| 路径                      | 方法   | 说明           | 认证 |
|---------------------------|--------|----------------|------|
| `/api/batteries`          | GET    | 获取电池列表   | 是   |
| `/api/batteries`          | POST   | 新增电池       | 是   |
| `/api/batteries/<id>`     | GET    | 获取电池详情   | 是   |
| `/api/batteries/<id>`     | PUT    | 更新电池信息   | 是   |
| `/api/batteries/<id>`     | DELETE | 删除电池       | 是   |

---

## 5. 测试批次与结果

| 路径                              | 方法   | 说明           | 认证 |
|-----------------------------------|--------|----------------|------|
| `/api/test-batches`               | GET    | 获取批次列表   | 是   |
| `/api/test-batches`               | POST   | 新增测试批次   | 是   |
| `/api/test-batches/<id>`          | GET    | 获取批次详情   | 是   |
| `/api/test-batches/<id>`          | PUT    | 更新批次信息   | 是   |
| `/api/test-batches/<id>`          | DELETE | 删除测试批次   | 是   |
| `/api/test-results`               | GET    | 获取测试结果   | 是   |
| `/api/test-results/<id>`          | GET    | 获取结果详情   | 是   |

---

## 6. 数据导入导出

| 路径                | 方法   | 说明           | 认证 |
|---------------------|--------|----------------|------|
| `/api/data/import`  | POST   | 导入数据       | 是   |
| `/api/data/export`  | GET    | 导出数据       | 是   |

---

## 7. 预测与智能分析

| 路径                              | 方法   | 说明                   | 认证 |
|-----------------------------------|--------|------------------------|------|
| `/api/predict/performance`        | POST   | 单个电池性能预测       | 是   |
| `/api/predict/batch`              | POST   | 批量电池性能预测       | 是   |
| `/api/predict/history`            | GET    | 获取预测历史           | 是   |
| `/api/predict/models/info`        | GET    | 获取模型信息           | 是   |
| `/api/predict/battery-matching`   | POST   | 电池组智能匹配推荐     | 是   |

---

## 8. 管理员功能（需管理员权限）

| 路径                                      | 方法   | 说明                   | 认证 |
|-------------------------------------------|--------|------------------------|------|
| `/api/admin/models/train`                 | POST   | 触发模型训练           | 管理员 |
| `/api/admin/models/train/status`          | GET    | 获取训练状态           | 管理员 |
| `/api/admin/models`                       | GET    | 获取模型列表           | 管理员 |
| `/api/admin/models/<model_type>/status`   | PUT    | 激活/停用模型          | 管理员 |
| `/api/admin/system/stats`                 | GET    | 获取系统统计信息       | 管理员 |

---

> 如需详细参数和返回示例，请参考项目内各路由文件或联系开发者。 