#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 档位分布区域宽度扩展测试脚本

测试目标：
1. 右边的档位分布区域向左扩展，占用红色框标示的空间
2. 档位范围文字完整显示，不被截断
3. 整体布局从1:1调整为1:2，档位分布区域获得更多空间

作者：weiwei
日期：2025-01-06
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QFrame, QTableWidget,
                             QTableWidgetItem, QHeaderView, QGridLayout, QGroupBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 尝试导入真实组件
REAL_COMPONENT_AVAILABLE = False
try:
    from core.config_manager import ConfigManager
    from ui.components.statistics_widget import StatisticsWidget
    REAL_COMPONENT_AVAILABLE = True
    print("✅ 成功导入真实统计组件")
except ImportError as e:
    print(f"⚠️  无法导入真实组件: {e}")
    print("将使用模拟组件进行演示")


class LayoutWidthExpansionTestWindow(QMainWindow):
    """档位分布区域宽度扩展测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 档位分布区域宽度扩展测试")
        self.setGeometry(100, 100, 1400, 800)
        
        # 初始化界面
        self._init_ui()
        
        # 如果有真实组件，设置测试数据
        if hasattr(self, 'statistics_widget'):
            self._set_test_data()
    
    def _init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加标题
        title_label = QLabel("📊 JCY5001AS 档位分布区域宽度扩展测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #ecf0f1; 
            padding: 15px; 
            border-radius: 10px; 
            border: 3px solid #3498db;
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明信息
        info_label = QLabel("""
        🎯 测试目标：让右边的档位分布区域向左扩展，占用红色框标示的空间
        
        🔧 主要调整：
        • 布局权重调整：从1:1调整为1:2（左侧统计数据:右侧档位分布）
        • 档位范围标签最小宽度：200px → 300px
        • 档位范围标签最大宽度：400px → 600px
        • 档位范围标签内边距：6px → 8px
        
        📊 布局变化对比：
        • 修改前：左侧统计数据占50%，右侧档位分布占50%
        • 修改后：左侧统计数据占33%，右侧档位分布占67%
        
        ✅ 预期效果：
        • 右侧档位分布区域明显变宽
        • 档位范围文字完整显示，不被截断
        • 表格有更多空间显示数据
        • 整体布局更合理，充分利用可用空间
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #e8f4fd; 
            padding: 15px; 
            border-radius: 8px; 
            border: 2px solid #3498db;
            line-height: 1.4;
        """)
        main_layout.addWidget(info_label)
        
        # 创建对比测试区域
        self._create_comparison_area(main_layout)
        
        # 创建真实组件测试区域
        self._create_real_component_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
    
    def _create_comparison_area(self, layout):
        """创建对比测试区域"""
        comparison_frame = QFrame()
        comparison_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        comparison_layout = QVBoxLayout(comparison_frame)
        
        # 标题
        title = QLabel("📊 布局对比演示")
        title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; padding: 5px;")
        comparison_layout.addWidget(title)
        
        # 修改前布局（1:1）
        before_label = QLabel("修改前布局（1:1比例）")
        before_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        before_label.setStyleSheet("color: #e74c3c; padding: 5px;")
        comparison_layout.addWidget(before_label)
        
        before_layout = self._create_mock_layout(1, 1, "修改前")
        comparison_layout.addWidget(before_layout)
        
        # 修改后布局（1:2）
        after_label = QLabel("修改后布局（1:2比例）")
        after_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        after_label.setStyleSheet("color: #27ae60; padding: 5px;")
        comparison_layout.addWidget(after_label)
        
        after_layout = self._create_mock_layout(1, 2, "修改后")
        comparison_layout.addWidget(after_layout)
        
        layout.addWidget(comparison_frame)
    
    def _create_mock_layout(self, left_weight, right_weight, version):
        """创建模拟布局"""
        container = QFrame()
        container.setStyleSheet("border: 2px solid #27ae60; border-radius: 5px; background-color: white;")
        container_layout = QHBoxLayout(container)
        container_layout.setSpacing(12)
        
        # 左侧统计数据模拟
        left_widget = QFrame()
        left_widget.setStyleSheet("background-color: #ecf0f1; border: 1px solid #bdc3c7; border-radius: 3px;")
        left_layout = QVBoxLayout(left_widget)
        left_layout.addWidget(QLabel(f"统计数据区域\n权重: {left_weight}\n宽度: {left_weight/(left_weight+right_weight)*100:.0f}%"))
        container_layout.addWidget(left_widget, left_weight)
        
        # 右侧档位分布模拟
        right_widget = QFrame()
        right_widget.setStyleSheet("background-color: #e8f4fd; border: 1px solid #3498db; border-radius: 3px;")
        right_layout = QVBoxLayout(right_widget)
        
        # 档位范围文字
        range_text = f"档位分布区域\n权重: {right_weight}\n宽度: {right_weight/(left_weight+right_weight)*100:.0f}%"
        if version == "修改后":
            range_text += "\n档位范围文字有更多空间显示"
        else:
            range_text += "\n档位范围文字可能被截断"
        
        range_label = QLabel(range_text)
        range_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(range_label)
        
        container_layout.addWidget(right_widget, right_weight)
        
        return container
    
    def _create_real_component_area(self, layout):
        """创建真实组件测试区域"""
        real_frame = QFrame()
        real_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        real_layout = QVBoxLayout(real_frame)
        
        # 标题
        title = QLabel("🔧 真实组件测试")
        title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; padding: 5px;")
        real_layout.addWidget(title)
        
        if REAL_COMPONENT_AVAILABLE:
            # 使用真实统计组件
            try:
                config_manager = ConfigManager()
                
                # 创建统计组件
                self.statistics_widget = StatisticsWidget(config_manager)
                
                real_layout.addWidget(self.statistics_widget)
                
            except Exception as e:
                error_label = QLabel(f"❌ 创建真实组件失败: {str(e)}")
                error_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
                real_layout.addWidget(error_label)
        else:
            # 显示无法使用真实组件的信息
            info_label = QLabel("⚠️ 真实组件不可用，请运行主程序查看实际效果")
            info_label.setStyleSheet("color: #e67e22; font-weight: bold; padding: 10px;")
            real_layout.addWidget(info_label)
        
        layout.addWidget(real_frame)
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 刷新显示按钮
        refresh_btn = QPushButton("🔄 刷新显示")
        refresh_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self._refresh_display)
        button_layout.addWidget(refresh_btn)
        
        # 运行主程序按钮
        main_btn = QPushButton("🚀 运行主程序")
        main_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        main_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        main_btn.clicked.connect(self._run_main_program)
        button_layout.addWidget(main_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _set_test_data(self):
        """设置测试数据"""
        if hasattr(self, 'statistics_widget'):
            try:
                # 设置测试统计数据
                self.statistics_widget.update_statistics({
                    'total_count': 1530,
                    'pass_count': 870,
                    'fail_count': 660,
                    'yield_rate': 56.9
                })
                
                # 设置测试档位分布数据
                grade_distribution = {
                    'Rs1-Rct1': 114, 'Rs1-Rct2': 112, 'Rs1-Rct3': 90,
                    'Rs2-Rct1': 96, 'Rs2-Rct2': 106, 'Rs2-Rct3': 101,
                    'Rs3-Rct1': 88, 'Rs3-Rct2': 82, 'Rs3-Rct3': 81
                }
                
                self.statistics_widget.grade_distribution = grade_distribution
                self.statistics_widget._update_grade_table_from_distribution()
                
                print("✅ 测试数据设置成功")
            except Exception as e:
                print(f"❌ 设置测试数据失败: {e}")
    
    def _refresh_display(self):
        """刷新显示"""
        if hasattr(self, 'statistics_widget'):
            self._set_test_data()
        print("🔄 显示已刷新")
    
    def _run_main_program(self):
        """运行主程序"""
        import subprocess
        try:
            subprocess.Popen(["python", "main.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
            print("🚀 主程序已启动")
        except Exception as e:
            print(f"❌ 启动主程序失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建测试窗口
    window = LayoutWidthExpansionTestWindow()
    window.show()
    
    print("🚀 JCY5001AS 档位分布区域宽度扩展测试启动")
    print("📊 查看布局权重调整效果（1:1 → 1:2）")
    print("📊 查看档位范围文字是否有更多空间显示")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
