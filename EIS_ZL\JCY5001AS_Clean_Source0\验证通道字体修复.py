#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证JCY5001AS通道显示组件字体显示修复
检查关键布局参数和样式是否已正确应用

Author: Assistant
Date: 2025-01-27
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def verify_channel_font_fixes():
    """验证通道字体显示修复是否正确应用"""
    print("🔍 验证JCY5001AS通道显示组件字体显示修复")
    print("=" * 60)
    
    try:
        # 1. 检查通道布局管理器
        print("\n1. 检查通道布局管理器...")
        from ui.components.channel_ui_layout_manager import ChannelUILayoutManager
        print("✅ 通道布局管理器导入成功")
        
        # 2. 检查通道样式管理器
        print("\n2. 检查通道样式管理器...")
        from ui.components.channel_style_manager import ChannelStyleManager
        print("✅ 通道样式管理器导入成功")
        
        # 3. 验证布局文件修改
        print("\n3. 验证布局文件修改...")
        layout_file = os.path.join(project_root, 'ui', 'components', 'channel_ui_layout_manager.py')
        if os.path.exists(layout_file):
            with open(layout_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查左右列权重调整
                if 'main_layout.addLayout(left_column, 2)  # 左列占2份权重（减少）' in content:
                    print("✅ 左列权重已调整为2份")
                else:
                    print("❌ 左列权重调整未找到")
                
                if 'main_layout.addLayout(right_column, 3)  # 右列占3份权重（增加）' in content:
                    print("✅ 右列权重已调整为3份")
                else:
                    print("❌ 右列权重调整未找到")
                
                # 检查Rs和Rct显示优化
                if 'title_label.setMinimumWidth(60)  # 设置最小宽度确保Rs(mΩ)和Rct(mΩ)完整显示' in content:
                    print("✅ Rs/Rct标题最小宽度已设置为60px")
                else:
                    print("❌ Rs/Rct标题宽度设置未找到")
                
                if 'value_label.setMinimumWidth(80)  # 设置最小宽度确保数值完整显示' in content:
                    print("✅ Rs/Rct数值最小宽度已设置为80px")
                else:
                    print("❌ Rs/Rct数值宽度设置未找到")
                
                # 检查电池码输入优化
                if 'battery_code_edit.setMinimumWidth(120)  # 设置最小宽度确保电池码完整显示' in content:
                    print("✅ 电池码输入框最小宽度已设置为120px")
                else:
                    print("❌ 电池码输入框宽度设置未找到")
                
                # 检查电压显示优化
                if 'voltage_value_label.setMinimumWidth(60)  # 设置最小宽度' in content:
                    print("✅ 电压数值最小宽度已设置为60px")
                else:
                    print("❌ 电压数值宽度设置未找到")
        
        # 4. 验证样式文件修改
        print("\n4. 验证样式文件修改...")
        style_file = os.path.join(project_root, 'ui', 'components', 'channel_style_manager.py')
        if os.path.exists(style_file):
            with open(style_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查Rs和Rct样式优化
                if 'font-size: 12pt;  /* 增加字体大小 */' in content:
                    print("✅ Rs/Rct字体大小已增加到12pt")
                else:
                    print("❌ Rs/Rct字体大小调整未找到")
                
                if 'min-width: 80px;  /* 设置最小宽度确保完整显示 */' in content:
                    print("✅ Rs/Rct样式最小宽度已设置为80px")
                else:
                    print("❌ Rs/Rct样式宽度设置未找到")
                
                if 'padding: 3px 6px;  /* 增加内边距 */' in content:
                    print("✅ Rs/Rct内边距已增加")
                else:
                    print("❌ Rs/Rct内边距调整未找到")
                
                # 检查电池码输入框样式
                if 'min-width: 120px;  /* 设置最小宽度确保电池码完整显示 */' in content:
                    print("✅ 电池码输入框样式最小宽度已设置为120px")
                else:
                    print("❌ 电池码输入框样式宽度设置未找到")
                
                if 'padding: 5px 8px;  /* 增加内边距 */' in content:
                    print("✅ 电池码输入框内边距已增加")
                else:
                    print("❌ 电池码输入框内边距调整未找到")
        
        print("\n" + "=" * 60)
        print("🎉 通道字体显示修复验证完成！")
        print("\n📋 修复总结:")
        print("• 左右列权重调整: 左列3→2份, 右列2→3份")
        print("• Rs/Rct显示优化: 标题60px, 数值80px, 字体12pt")
        print("• 电池码输入优化: 输入框120px, 增加内边距")
        print("• 电压显示优化: 数值60px最小宽度")
        print("• 样式改进: 增加内边距、圆角，确保文字完整显示")
        print("\n✨ 所有修复已成功应用，Rs和Rct阻抗值以及电池码将完整显示！")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = verify_channel_font_fixes()
    
    if success:
        print("\n🚀 建议:")
        print("1. 运行主程序查看实际效果: python main.py")
        print("2. 运行字体修复对比测试: python test_channel_font_fix.py")
        print("3. 检查通道卡片中Rs和Rct数值是否完整显示")
        print("4. 验证电池码输入框是否有足够宽度")
    else:
        print("\n❌ 验证失败，请检查代码修复是否正确")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
