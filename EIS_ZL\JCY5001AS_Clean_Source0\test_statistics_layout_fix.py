#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 统计显示区域布局修复测试
测试RS档位和RCT档位向左移动、表格边框完整显示、文字补全等功能
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                            QWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.components.statistics_widget import StatisticsWidget

class TestStatisticsLayoutWindow(QMainWindow):
    """统计显示区域布局修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS 统计显示区域布局修复测试")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 统计显示区域布局修复测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
测试内容：
1. ✅ RS档位和RCT档位向左移动
2. ✅ 修复表格显示问题，确保所有数据完整显示
3. ✅ 补充缺失的底部边框线，让表格显示完整
4. ✅ 最顶端的档位范围文字补全
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #34495e;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 创建统计显示组件
        self.statistics_widget = StatisticsWidget()
        main_layout.addWidget(self.statistics_widget)
        
        # 添加测试数据
        self.load_test_data()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 更新统计数据
            self.statistics_widget.update_statistics(
                total_count=1530,
                qualified_count=870,
                unqualified_count=660,
                yield_rate=56.9
            )
            
            # 更新档位分布数据
            grade_data = {
                (0, 0): 114, (0, 1): 112, (0, 2): 90,   # Rs1行
                (1, 0): 96,  (1, 1): 106, (1, 2): 101,  # Rs2行  
                (2, 0): 88,  (2, 1): 82,  (2, 2): 81    # Rs3行
            }
            
            self.statistics_widget.update_grade_distribution(grade_data)
            
            print("✅ 测试数据加载完成")
            print("📊 统计数据: 总测试数=1530, 合格数=870, 不合格数=660, 良率=56.9%")
            print("📋 档位分布: 3x3表格数据已填充")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("JCY5001AS统计显示测试")
    app.setApplicationVersion("1.0")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = TestStatisticsLayoutWindow()
    window.show()
    
    print("🚀 JCY5001AS 统计显示区域布局修复测试启动")
    print("📝 测试重点:")
    print("   1. RS档位和RCT档位是否向左移动")
    print("   2. 表格是否有完整的边框（包括底部边框）")
    print("   3. 档位范围文字是否完整显示")
    print("   4. 3x3表格是否完整显示所有数据")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
