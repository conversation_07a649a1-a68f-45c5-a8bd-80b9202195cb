#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Nuitka打包脚本 - JCY5001A
解决启动卡死和依赖项问题

版权所有：鲸测云
作者：weiwei
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def clean_build_dirs():
    """清理之前的构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [
        'main.dist',
        'main.build',
        'main.onefile-build',
        '__pycache__',
        'build',
        'dist/main.dist',
        'dist/main.build'
    ]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"  ✅ 已删除: {dir_name}")
            except Exception as e:
                print(f"  ⚠️ 删除失败 {dir_name}: {e}")

def build_with_nuitka():
    """使用Nuitka构建可执行文件"""
    print("🚀 开始使用Nuitka构建修复版...")
    
    # 构建命令 - 修复版本，解决启动问题
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",  # 独立模式
        "--enable-plugin=pyqt5",  # 启用PyQt5插件
        "--include-data-dir=config=config",  # 包含配置目录
        "--include-data-dir=resources=resources",  # 包含资源目录
        "--include-data-dir=templates=templates",  # 包含模板目录
        "--include-data-dir=data=data",  # 包含数据目录
        "--include-package=PyQt5",  # 包含PyQt5包
        "--include-package=serial",  # 包含串口通信包
        "--include-package=numpy",  # 包含numpy
        "--include-package=scipy",  # 包含scipy
        "--include-package=matplotlib",  # 包含matplotlib
        "--include-package=openpyxl",  # 包含Excel处理包
        "--include-package=xlsxwriter",  # 包含Excel写入包
        "--include-package=PIL",  # 包含图像处理包
        "--include-package=sqlalchemy",  # 包含数据库ORM
        "--include-package=pandas",  # 包含数据分析包
        "--include-package=psutil",  # 包含系统工具包
        "--windows-console-mode=disable",  # 禁用控制台
        "--windows-icon-from-ico=resources/icons/app_icon.ico",  # 设置图标
        "--output-dir=dist",  # 输出目录
        "--output-filename=JCY5001A_Fixed.exe",  # 输出文件名
        "--company-name=JingCeYun",  # 公司名称
        "--product-name=JCY5001A Battery Impedance Tester Fixed",  # 产品名称
        "--file-version=*******",  # 文件版本
        "--product-version=*******",  # 产品版本
        "--file-description=JCY5001A Battery Impedance Testing System Fixed",  # 文件描述
        "--copyright=Copyright (C) 2025 JingCeYun",  # 版权信息
        "--assume-yes-for-downloads",  # 自动确认下载
        "--show-progress",  # 显示进度
        "--show-memory",  # 显示内存使用
        "--jobs=2",  # 使用2个并行作业，减少内存压力
        "--low-memory",  # 低内存模式
        "--remove-output",  # 移除之前的输出
        "main.py"  # 主文件
    ]
    
    print("📋 构建命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行构建
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("✅ Nuitka构建完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka构建失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到输出目录"""
    print("📁 复制额外文件...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        print(f"❌ 输出目录不存在: {dist_dir}")
        return False
    
    # 需要复制的文件和目录
    files_to_copy = [
        ("README.md", "README.md"),
        ("requirements.txt", "requirements.txt"),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(dist_dir, dst)
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
            try:
                if os.path.isdir(src):
                    shutil.copytree(src, dst_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(src, dst_path)
                print(f"  ✅ 已复制: {src} -> {dst}")
            except Exception as e:
                print(f"  ⚠️ 复制失败 {src}: {e}")
    
    return True

def create_installer_info():
    """创建安装信息文件"""
    print("📝 创建安装信息...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        return False
    
    info_content = f"""JCY5001A鲸测云8路EIS阻抗筛选仪 v0.83 (修复版)
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
构建工具: Nuitka (修复版)

修复内容:
- 修复启动时卡在"配置加载"阶段的问题
- 优化日志级别设置，确保启动进度可见
- 修复依赖项包含问题
- 改善启动性能和稳定性

安装说明:
1. 解压所有文件到目标目录
2. 运行 JCY5001A_Fixed.exe 启动程序
3. 首次运行会创建配置文件

系统要求:
- Windows 10/11 (64位)
- 至少4GB内存
- 至少1GB磁盘空间

技术支持:
- 公司: 鲸测云
- 版本: v0.83 (修复版)
- 构建日期: {datetime.now().strftime('%Y-%m-%d')}
"""
    
    try:
        with open(os.path.join(dist_dir, "安装说明.txt"), "w", encoding="utf-8") as f:
            f.write(info_content)
        print("  ✅ 安装说明已创建")
        return True
    except Exception as e:
        print(f"  ❌ 创建安装说明失败: {e}")
        return False

def create_startup_bat():
    """创建启动批处理文件"""
    print("📝 创建启动批处理文件...")
    
    dist_dir = "dist/main.dist"
    if not os.path.exists(dist_dir):
        return False
    
    bat_content = """@echo off
echo 启动JCY5001A鲸测云8路EIS阻抗筛选仪...
echo.
echo 如果程序无法启动，请检查：
echo 1. 是否有杀毒软件阻止运行
echo 2. 是否有足够的系统权限
echo 3. 是否缺少必要的运行库
echo.
pause
JCY5001A_Fixed.exe
pause
"""
    
    try:
        with open(os.path.join(dist_dir, "启动程序.bat"), "w", encoding="gbk") as f:
            f.write(bat_content)
        print("  ✅ 启动批处理文件已创建")
        return True
    except Exception as e:
        print(f"  ❌ 创建启动批处理文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 JCY5001A v0.83 Nuitka修复版打包工具")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    # 步骤1: 清理构建目录
    clean_build_dirs()
    print()
    
    # 步骤2: 使用Nuitka构建
    if not build_with_nuitka():
        print("❌ 构建失败，停止打包")
        return False
    print()
    
    # 步骤3: 复制额外文件
    if not copy_additional_files():
        print("⚠️ 复制额外文件失败，但构建已完成")
    print()
    
    # 步骤4: 创建安装信息
    if not create_installer_info():
        print("⚠️ 创建安装信息失败，但构建已完成")
    print()
    
    # 步骤5: 创建启动批处理文件
    if not create_startup_bat():
        print("⚠️ 创建启动批处理文件失败，但构建已完成")
    print()
    
    # 完成
    print("🎉 修复版打包完成!")
    print(f"📦 输出目录: dist/main.dist/")
    print(f"🚀 可执行文件: dist/main.dist/JCY5001A_Fixed.exe")
    print(f"📋 启动脚本: dist/main.dist/启动程序.bat")
    print()
    print("💡 修复说明:")
    print("  - 修复了启动时卡在配置加载阶段的问题")
    print("  - 优化了日志级别，确保启动进度可见")
    print("  - 改善了依赖项包含和启动性能")
    print("  - 可以使用启动脚本进行调试")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
