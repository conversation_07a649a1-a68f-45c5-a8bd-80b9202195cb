#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将专利附图插入到DOCX文档中
将生成的10张专利附图插入到相应的位置

Author: AI Assistant
Date: 2025-08-09
"""

import os
from docx import Document
from docx.shared import Inches, Cm
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re

class DocxFigureInserter:
    """DOCX图片插入器"""
    
    def __init__(self, docx_path, figures_dir):
        """
        初始化插入器
        
        Args:
            docx_path: DOCX文件路径
            figures_dir: 图片文件夹路径
        """
        self.docx_path = docx_path
        self.figures_dir = figures_dir
        self.doc = Document(docx_path)
        
        # 图片文件映射
        self.figure_files = {
            "图1": "图1_系统整体架构图.png",
            "图2": "图2_DNB1101BB芯片连接电路图.png", 
            "图3": "图3_外部电流源电路详细图.png",
            "图4": "图4_EIS测试完整流程图.png",
            "图5": "图5_典型电池奈奎斯特曲线示例图.png",
            "图6": "图6_多维参数提取算法流程图.png",
            "图7": "图7_九档智能分组决策流程图.png",
            "图8": "图8_Modbus RTU通信协议时序图.png",
            "图9": "图9_频率扫描序列设置图.png",
            "图10": "图10_增益自适应调节流程图.png"
        }
        
    def insert_figures(self):
        """插入所有图片到文档中"""
        print("开始插入图片到DOCX文档...")
        
        # 遍历文档段落，查找图片插入位置
        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()
            
            # 查找图片标题
            for fig_key, fig_file in self.figure_files.items():
                if self._is_figure_title(text, fig_key):
                    print(f"找到{fig_key}插入位置：{text[:50]}...")
                    self._insert_figure_after_paragraph(i, fig_file, fig_key)
                    break
        
        # 保存文档
        output_path = self.docx_path.replace('.docx', '_含附图.docx')
        self.doc.save(output_path)
        print(f"文档已保存为：{output_path}")
        
        return output_path
    
    def _is_figure_title(self, text, fig_key):
        """判断是否是图片标题"""
        # 匹配模式：图X：标题 或 ### 图X：标题
        patterns = [
            f"^#{fig_key}：",  # ### 图1：标题
            f"^{fig_key}：",   # 图1：标题
            f"图{fig_key[1:]}：", # 图1：标题（处理图号）
        ]
        
        for pattern in patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def _insert_figure_after_paragraph(self, paragraph_index, figure_file, figure_key):
        """在指定段落后插入图片"""
        try:
            # 图片文件路径
            figure_path = os.path.join(self.figures_dir, figure_file)

            if not os.path.exists(figure_path):
                print(f"警告：图片文件不存在 - {figure_path}")
                return

            # 创建新段落用于插入图片
            new_paragraph = self.doc.add_paragraph()

            # 设置段落居中对齐
            new_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

            # 插入图片
            run = new_paragraph.add_run()

            # 根据图片类型设置不同的尺寸
            width = self._get_figure_width(figure_key)
            run.add_picture(figure_path, width=width)

            # 添加图片说明段落
            caption_paragraph = self.doc.add_paragraph()
            caption_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            caption_run = caption_paragraph.add_run(f"{figure_key}：{self._get_figure_caption(figure_key)}")
            caption_run.italic = True

            # 添加空行
            self.doc.add_paragraph()

            print(f"成功插入{figure_key}")

        except Exception as e:
            print(f"插入{figure_key}时出错：{e}")
    
    def _insert_paragraph_after(self, paragraph):
        """在指定段落后插入新段落"""
        try:
            # 简单方法：在文档末尾添加段落
            new_paragraph = self.doc.add_paragraph()
            return new_paragraph
        except Exception as e:
            print(f"插入段落时出错：{e}")
            return None
    
    def _get_figure_width(self, figure_key):
        """根据图片类型返回合适的宽度"""
        # 不同类型图片的宽度设置
        width_settings = {
            "图1": Inches(6),    # 系统架构图 - 较大
            "图2": Inches(6),    # 芯片电路图 - 较大
            "图3": Inches(5),    # 电流源电路 - 中等
            "图4": Inches(4),    # 流程图 - 中等
            "图5": Inches(5),    # 奈奎斯特曲线 - 中等
            "图6": Inches(6),    # 算法流程 - 较大
            "图7": Inches(6),    # 分组算法 - 较大
            "图8": Inches(5),    # 通信时序 - 中等
            "图9": Inches(5),    # 频率扫描 - 中等
            "图10": Inches(5),   # 增益控制 - 中等
        }
        
        return width_settings.get(figure_key, Inches(5))  # 默认5英寸
    
    def _get_figure_caption(self, figure_key):
        """获取图片说明"""
        captions = {
            "图1": "基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图",
            "图2": "DNB1101BB芯片引脚连接电路图及外围器件配置图",
            "图3": "外部电流源电路详细图（含MOSFET驱动电路）",
            "图4": "电化学阻抗谱(EIS)测试完整流程图",
            "图5": "典型电池奈奎斯特曲线示例图（含参数标注）",
            "图6": "多维参数提取算法流程图",
            "图7": "基于Rs和Rp的九档智能分组决策流程图",
            "图8": "Modbus RTU通信协议时序图",
            "图9": "频率扫描序列设置图",
            "图10": "增益自适应调节流程图"
        }
        
        return captions.get(figure_key, "专利附图")

def main():
    """主函数"""
    print("专利附图插入工具")
    print("=" * 50)
    
    # 文件路径
    docx_file = "DNB1101BB发明专利申请完整技术文档.docx"
    figures_dir = "patent_figures"
    
    # 检查文件是否存在
    if not os.path.exists(docx_file):
        print(f"错误：找不到DOCX文件 - {docx_file}")
        return
    
    if not os.path.exists(figures_dir):
        print(f"错误：找不到图片文件夹 - {figures_dir}")
        return
    
    # 创建插入器并执行
    inserter = DocxFigureInserter(docx_file, figures_dir)
    output_file = inserter.insert_figures()
    
    print("\n" + "=" * 50)
    print("图片插入完成！")
    print(f"输出文件：{output_file}")
    print("\n请检查生成的文档，确认图片位置和大小是否合适。")

if __name__ == "__main__":
    main()
