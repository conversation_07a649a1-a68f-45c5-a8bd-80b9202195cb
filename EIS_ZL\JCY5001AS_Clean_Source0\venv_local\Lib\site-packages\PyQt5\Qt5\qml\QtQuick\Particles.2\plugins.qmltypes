import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "qquickage_p.h"
        name: "QQuickAgeAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Age 2.0",
            "QtQuick.Particles/Age 2.1",
            "QtQuick.Particles/Age 2.11",
            "QtQuick.Particles/Age 2.4",
            "QtQuick.Particles/Age 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "lifeLeft"; type: "int" }
        Property { name: "advancePosition"; type: "bool" }
        Signal {
            name: "lifeLeftChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "advancePositionChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setLifeLeft"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setAdvancePosition"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquickangledirection_p.h"
        name: "QQuickAngleDirection"
        prototype: "QQuickDirection"
        exports: ["QtQuick.Particles/AngleDirection 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "angle"; type: "double" }
        Property { name: "magnitude"; type: "double" }
        Property { name: "angleVariation"; type: "double" }
        Property { name: "magnitudeVariation"; type: "double" }
        Signal {
            name: "angleChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "magnitudeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "angleVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "magnitudeVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAngle"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAngleVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitudeVariation"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "qquickpointattractor_p.h"
        name: "QQuickAttractorAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Attractor 2.0",
            "QtQuick.Particles/Attractor 2.1",
            "QtQuick.Particles/Attractor 2.11",
            "QtQuick.Particles/Attractor 2.4",
            "QtQuick.Particles/Attractor 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Proportion"
            values: [
                "Constant",
                "Linear",
                "Quadratic",
                "InverseLinear",
                "InverseQuadratic"
            ]
        }
        Enum {
            name: "AffectableParameters"
            values: ["Position", "Velocity", "Acceleration"]
        }
        Property { name: "strength"; type: "double" }
        Property { name: "pointX"; type: "double" }
        Property { name: "pointY"; type: "double" }
        Property { name: "affectedParameter"; type: "AffectableParameters" }
        Property { name: "proportionalToDistance"; type: "Proportion" }
        Signal {
            name: "strengthChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "pointXChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "pointYChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "affectedParameterChanged"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
        Signal {
            name: "proportionalToDistanceChanged"
            Parameter { name: "arg"; type: "Proportion" }
        }
        Method {
            name: "setStrength"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setPointX"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setPointY"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAffectedParameter"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
        Method {
            name: "setProportionalToDistance"
            Parameter { name: "arg"; type: "Proportion" }
        }
    }
    Component {
        file: "qquickcumulativedirection_p.h"
        name: "QQuickCumulativeDirection"
        defaultProperty: "directions"
        prototype: "QQuickDirection"
        exports: ["QtQuick.Particles/CumulativeDirection 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "directions"; type: "QQuickDirection"; isList: true; isReadonly: true }
    }
    Component {
        file: "qquickcustomaffector_p.h"
        name: "QQuickCustomAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Affector 2.0",
            "QtQuick.Particles/Affector 2.1",
            "QtQuick.Particles/Affector 2.11",
            "QtQuick.Particles/Affector 2.4",
            "QtQuick.Particles/Affector 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "relative"; type: "bool" }
        Property { name: "position"; type: "QQuickDirection"; isPointer: true }
        Property { name: "velocity"; type: "QQuickDirection"; isPointer: true }
        Property { name: "acceleration"; type: "QQuickDirection"; isPointer: true }
        Signal {
            name: "affectParticles"
            Parameter { name: "particles"; type: "QJSValue" }
            Parameter { name: "dt"; type: "double" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "velocityChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "relativeChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setVelocity"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setRelative"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquickcustomparticle_p.h"
        name: "QQuickCustomParticle"
        prototype: "QQuickParticlePainter"
        exports: [
            "QtQuick.Particles/CustomParticle 2.0",
            "QtQuick.Particles/CustomParticle 2.1",
            "QtQuick.Particles/CustomParticle 2.11",
            "QtQuick.Particles/CustomParticle 2.4",
            "QtQuick.Particles/CustomParticle 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "fragmentShader"; type: "QByteArray" }
        Property { name: "vertexShader"; type: "QByteArray" }
        Method {
            name: "sourceDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "qquickdirection_p.h"
        name: "QQuickDirection"
        prototype: "QObject"
        exports: ["QtQuick.Particles/NullVector 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        file: "qquickellipseextruder_p.h"
        name: "QQuickEllipseExtruder"
        prototype: "QQuickParticleExtruder"
        exports: ["QtQuick.Particles/EllipseShape 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "fill"; type: "bool" }
        Signal {
            name: "fillChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFill"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquickfriction_p.h"
        name: "QQuickFrictionAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Friction 2.0",
            "QtQuick.Particles/Friction 2.1",
            "QtQuick.Particles/Friction 2.11",
            "QtQuick.Particles/Friction 2.4",
            "QtQuick.Particles/Friction 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "factor"; type: "double" }
        Property { name: "threshold"; type: "double" }
        Signal {
            name: "factorChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "thresholdChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFactor"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setThreshold"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "qquickgravity_p.h"
        name: "QQuickGravityAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Gravity 2.0",
            "QtQuick.Particles/Gravity 2.1",
            "QtQuick.Particles/Gravity 2.11",
            "QtQuick.Particles/Gravity 2.4",
            "QtQuick.Particles/Gravity 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "magnitude"; type: "double" }
        Property { name: "acceleration"; type: "double" }
        Property { name: "angle"; type: "double" }
        Signal {
            name: "magnitudeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "angleChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAngle"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "qquickgroupgoal_p.h"
        name: "QQuickGroupGoalAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/GroupGoal 2.0",
            "QtQuick.Particles/GroupGoal 2.1",
            "QtQuick.Particles/GroupGoal 2.11",
            "QtQuick.Particles/GroupGoal 2.4",
            "QtQuick.Particles/GroupGoal 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "goalState"; type: "string" }
        Property { name: "jump"; type: "bool" }
        Signal {
            name: "goalStateChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "jumpChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setGoalState"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setJump"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquickimageparticle_p.h"
        name: "QQuickImageParticle"
        prototype: "QQuickParticlePainter"
        exports: [
            "QtQuick.Particles/ImageParticle 2.0",
            "QtQuick.Particles/ImageParticle 2.1",
            "QtQuick.Particles/ImageParticle 2.11",
            "QtQuick.Particles/ImageParticle 2.4",
            "QtQuick.Particles/ImageParticle 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Enum {
            name: "EntryEffect"
            values: ["None", "Fade", "Scale"]
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "sprites"; type: "QQuickSprite"; isList: true; isReadonly: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "colorTable"; type: "QUrl" }
        Property { name: "sizeTable"; type: "QUrl" }
        Property { name: "opacityTable"; type: "QUrl" }
        Property { name: "color"; type: "QColor" }
        Property { name: "colorVariation"; type: "double" }
        Property { name: "redVariation"; type: "double" }
        Property { name: "greenVariation"; type: "double" }
        Property { name: "blueVariation"; type: "double" }
        Property { name: "alpha"; type: "double" }
        Property { name: "alphaVariation"; type: "double" }
        Property { name: "rotation"; type: "double" }
        Property { name: "rotationVariation"; type: "double" }
        Property { name: "rotationVelocity"; type: "double" }
        Property { name: "rotationVelocityVariation"; type: "double" }
        Property { name: "autoRotation"; type: "bool" }
        Property { name: "xVector"; type: "QQuickDirection"; isPointer: true }
        Property { name: "yVector"; type: "QQuickDirection"; isPointer: true }
        Property { name: "spritesInterpolate"; type: "bool" }
        Property { name: "entryEffect"; type: "EntryEffect" }
        Signal { name: "imageChanged" }
        Signal { name: "colortableChanged" }
        Signal { name: "sizetableChanged" }
        Signal { name: "opacitytableChanged" }
        Signal {
            name: "alphaVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "alphaChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "redVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "greenVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "blueVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationVelocityChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "rotationVelocityVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "autoRotationChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "xVectorChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "yVectorChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "spritesInterpolateChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "bypassOptimizationsChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "entryEffectChanged"
            Parameter { name: "arg"; type: "EntryEffect" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "arg"; type: "Status" }
        }
        Method {
            name: "reloadColor"
            Parameter { name: "c"; type: "Color4ub" }
            Parameter { name: "d"; type: "QQuickParticleData"; isPointer: true }
        }
        Method {
            name: "setAlphaVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAlpha"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRedVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setGreenVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setBlueVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotationVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotationVelocity"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setRotationVelocityVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAutoRotation"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setXVector"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setYVector"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setSpritesInterpolate"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setBypassOptimizations"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setEntryEffect"
            Parameter { name: "arg"; type: "EntryEffect" }
        }
        Method { name: "createEngine" }
        Method {
            name: "spriteAdvance"
            Parameter { name: "spriteIndex"; type: "int" }
        }
        Method {
            name: "spritesUpdate"
            Parameter { name: "time"; type: "double" }
        }
        Method { name: "spritesUpdate" }
        Method { name: "mainThreadFetchImageData" }
        Method {
            name: "finishBuildParticleNodes"
            Parameter { name: "n"; type: "QSGNode*"; isPointer: true }
        }
    }
    Component {
        file: "qquickitemparticle_p.h"
        name: "QQuickItemParticle"
        prototype: "QQuickParticlePainter"
        exports: [
            "QtQuick.Particles/ItemParticle 2.0",
            "QtQuick.Particles/ItemParticle 2.1",
            "QtQuick.Particles/ItemParticle 2.11",
            "QtQuick.Particles/ItemParticle 2.4",
            "QtQuick.Particles/ItemParticle 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        attachedType: "QQuickItemParticleAttached"
        Property { name: "fade"; type: "bool" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Signal {
            name: "delegateChanged"
            Parameter { name: "arg"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "freeze"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "unfreeze"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "take"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "prioritize"; type: "bool" }
        }
        Method {
            name: "take"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "give"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setFade"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setDelegate"
            Parameter { name: "arg"; type: "QQmlComponent"; isPointer: true }
        }
    }
    Component {
        name: "QQuickItemParticleAttached"
        prototype: "QObject"
        Property { name: "particle"; type: "QQuickItemParticle"; isReadonly: true; isPointer: true }
        Signal { name: "detached" }
        Signal { name: "attached" }
    }
    Component {
        file: "qquicklineextruder_p.h"
        name: "QQuickLineExtruder"
        prototype: "QQuickParticleExtruder"
        exports: ["QtQuick.Particles/LineShape 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "mirrored"; type: "bool" }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquickmaskextruder_p.h"
        name: "QQuickMaskExtruder"
        prototype: "QQuickParticleExtruder"
        exports: ["QtQuick.Particles/MaskShape 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QUrl" }
        Signal {
            name: "sourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method { name: "startMaskLoading" }
        Method { name: "finishMaskLoading" }
    }
    Component {
        file: "qquickparticleaffector_p.h"
        name: "QQuickParticleAffector"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/ParticleAffector 2.0",
            "QtQuick.Particles/ParticleAffector 2.1",
            "QtQuick.Particles/ParticleAffector 2.11",
            "QtQuick.Particles/ParticleAffector 2.4",
            "QtQuick.Particles/ParticleAffector 2.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "system"; type: "QQuickParticleSystem"; isPointer: true }
        Property { name: "groups"; type: "QStringList" }
        Property { name: "whenCollidingWith"; type: "QStringList" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "once"; type: "bool" }
        Property { name: "shape"; type: "QQuickParticleExtruder"; isPointer: true }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Signal {
            name: "groupsChanged"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "onceChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "shapeChanged"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Signal {
            name: "affected"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Signal {
            name: "whenCollidingWithChanged"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setGroups"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setOnceOff"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setShape"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method {
            name: "setWhenCollidingWith"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method { name: "updateOffsets" }
    }
    Component {
        file: "qquickparticleemitter_p.h"
        name: "QQuickParticleEmitter"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/Emitter 2.0",
            "QtQuick.Particles/Emitter 2.1",
            "QtQuick.Particles/Emitter 2.11",
            "QtQuick.Particles/Emitter 2.4",
            "QtQuick.Particles/Emitter 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Lifetime"
            values: ["InfiniteLife"]
        }
        Property { name: "system"; type: "QQuickParticleSystem"; isPointer: true }
        Property { name: "group"; type: "string" }
        Property { name: "shape"; type: "QQuickParticleExtruder"; isPointer: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "startTime"; type: "int" }
        Property { name: "emitRate"; type: "double" }
        Property { name: "lifeSpan"; type: "int" }
        Property { name: "lifeSpanVariation"; type: "int" }
        Property { name: "maximumEmitted"; type: "int" }
        Property { name: "size"; type: "double" }
        Property { name: "endSize"; type: "double" }
        Property { name: "sizeVariation"; type: "double" }
        Property { name: "velocity"; type: "QQuickDirection"; isPointer: true }
        Property { name: "acceleration"; type: "QQuickDirection"; isPointer: true }
        Property { name: "velocityFromMovement"; type: "double" }
        Signal {
            name: "emitParticles"
            Parameter { name: "particles"; type: "QJSValue" }
        }
        Signal {
            name: "particlesPerSecondChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "particleDurationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "enabledChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Signal {
            name: "groupChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "particleDurationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "extruderChanged"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Signal {
            name: "particleSizeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "particleEndSizeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "particleSizeVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "velocityChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Signal {
            name: "maximumEmittedChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal { name: "particleCountChanged" }
        Signal {
            name: "startTimeChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "pulse"
            Parameter { name: "milliseconds"; type: "int" }
        }
        Method {
            name: "burst"
            Parameter { name: "num"; type: "int" }
        }
        Method {
            name: "burst"
            Parameter { name: "num"; type: "int" }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setParticlesPerSecond"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setParticleDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setGroup"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setParticleDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setExtruder"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method {
            name: "setParticleSize"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setParticleEndSize"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setParticleSizeVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setVelocity"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "arg"; type: "QQuickDirection"; isPointer: true }
        }
        Method {
            name: "setMaxParticleCount"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setStartTime"
            Parameter { name: "arg"; type: "int" }
        }
        Method { name: "reset" }
    }
    Component {
        file: "qquickparticleextruder_p.h"
        name: "QQuickParticleExtruder"
        prototype: "QObject"
        exports: ["QtQuick.Particles/ParticleExtruder 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        file: "qquickparticlegroup_p.h"
        name: "QQuickParticleGroup"
        defaultProperty: "particleChildren"
        prototype: "QQuickStochasticState"
        exports: ["QtQuick.Particles/ParticleGroup 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "system"; type: "QQuickParticleSystem"; isPointer: true }
        Property { name: "particleChildren"; type: "QObject"; isList: true; isReadonly: true }
        Signal {
            name: "maximumAliveChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setMaximumAlive"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "delayRedirect"
            Parameter { name: "obj"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "qquickparticlepainter_p.h"
        name: "QQuickParticlePainter"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/ParticlePainter 2.0",
            "QtQuick.Particles/ParticlePainter 2.1",
            "QtQuick.Particles/ParticlePainter 2.11",
            "QtQuick.Particles/ParticlePainter 2.4",
            "QtQuick.Particles/ParticlePainter 2.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "system"; type: "QQuickParticleSystem"; isPointer: true }
        Property { name: "groups"; type: "QStringList" }
        Signal { name: "countChanged" }
        Signal {
            name: "systemChanged"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Signal {
            name: "groupsChanged"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "setSystem"
            Parameter { name: "arg"; type: "QQuickParticleSystem"; isPointer: true }
        }
        Method {
            name: "setGroups"
            Parameter { name: "arg"; type: "QStringList" }
        }
        Method {
            name: "calcSystemOffset"
            Parameter { name: "resetPending"; type: "bool" }
        }
        Method { name: "calcSystemOffset" }
        Method { name: "sceneGraphInvalidated" }
    }
    Component {
        file: "qquickparticlesystem_p.h"
        name: "QQuickParticleSystem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Particles/ParticleSystem 2.0",
            "QtQuick.Particles/ParticleSystem 2.1",
            "QtQuick.Particles/ParticleSystem 2.11",
            "QtQuick.Particles/ParticleSystem 2.4",
            "QtQuick.Particles/ParticleSystem 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "running"; type: "bool" }
        Property { name: "paused"; type: "bool" }
        Property { name: "empty"; type: "bool"; isReadonly: true }
        Signal { name: "systemInitialized" }
        Signal {
            name: "runningChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "pausedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "emptyChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "restart" }
        Method { name: "pause" }
        Method { name: "resume" }
        Method { name: "reset" }
        Method {
            name: "setRunning"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setPaused"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "duration"; type: "int" }
        Method { name: "emittersChanged" }
        Method {
            name: "loadPainter"
            Parameter { name: "p"; type: "QQuickParticlePainter"; isPointer: true }
        }
        Method { name: "createEngine" }
        Method {
            name: "particleStateChange"
            Parameter { name: "idx"; type: "int" }
        }
    }
    Component {
        file: "qquickpointdirection_p.h"
        name: "QQuickPointDirection"
        prototype: "QQuickDirection"
        exports: ["QtQuick.Particles/PointDirection 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "xVariation"; type: "double" }
        Property { name: "yVariation"; type: "double" }
        Signal {
            name: "xChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "xVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "yVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setXVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setYVariation"
            Parameter { name: "arg"; type: "double" }
        }
    }
    Component {
        file: "qquickrectangleextruder_p.h"
        name: "QQuickRectangleExtruder"
        prototype: "QQuickParticleExtruder"
        exports: ["QtQuick.Particles/RectangleShape 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "fill"; type: "bool" }
        Signal {
            name: "fillChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFill"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquickspritegoal_p.h"
        name: "QQuickSpriteGoalAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/SpriteGoal 2.0",
            "QtQuick.Particles/SpriteGoal 2.1",
            "QtQuick.Particles/SpriteGoal 2.11",
            "QtQuick.Particles/SpriteGoal 2.4",
            "QtQuick.Particles/SpriteGoal 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "goalState"; type: "string" }
        Property { name: "jump"; type: "bool" }
        Property { name: "systemStates"; type: "bool" }
        Signal {
            name: "goalStateChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "jumpChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "systemStatesChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setGoalState"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setJump"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setSystemStates"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        name: "QQuickStochasticState"
        prototype: "QObject"
        Property { name: "duration"; type: "int" }
        Property { name: "durationVariation"; type: "int" }
        Property { name: "randomStart"; type: "bool" }
        Property { name: "to"; type: "QVariantMap" }
        Property { name: "name"; type: "string" }
        Signal {
            name: "durationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "toChanged"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Signal {
            name: "durationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal { name: "entered" }
        Signal {
            name: "randomStartChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setName"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setTo"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Method {
            name: "setDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setRandomStart"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "qquicktargetdirection_p.h"
        name: "QQuickTargetDirection"
        prototype: "QQuickDirection"
        exports: ["QtQuick.Particles/TargetDirection 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "targetX"; type: "double" }
        Property { name: "targetY"; type: "double" }
        Property { name: "targetItem"; type: "QQuickItem"; isPointer: true }
        Property { name: "targetVariation"; type: "double" }
        Property { name: "proportionalMagnitude"; type: "bool" }
        Property { name: "magnitude"; type: "double" }
        Property { name: "magnitudeVariation"; type: "double" }
        Signal {
            name: "targetXChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "targetYChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "targetVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "magnitudeChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "proprotionalMagnitudeChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "magnitudeVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "targetItemChanged"
            Parameter { name: "arg"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setTargetX"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setTargetY"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setTargetVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setProportionalMagnitude"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setMagnitudeVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setTargetItem"
            Parameter { name: "arg"; type: "QQuickItem"; isPointer: true }
        }
    }
    Component {
        file: "qquicktrailemitter_p.h"
        name: "QQuickTrailEmitter"
        prototype: "QQuickParticleEmitter"
        exports: [
            "QtQuick.Particles/TrailEmitter 2.0",
            "QtQuick.Particles/TrailEmitter 2.1",
            "QtQuick.Particles/TrailEmitter 2.11",
            "QtQuick.Particles/TrailEmitter 2.4",
            "QtQuick.Particles/TrailEmitter 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "EmitSize"
            values: ["ParticleSize"]
        }
        Property { name: "follow"; type: "string" }
        Property { name: "emitRatePerParticle"; type: "int" }
        Property { name: "emitShape"; type: "QQuickParticleExtruder"; isPointer: true }
        Property { name: "emitHeight"; type: "double" }
        Property { name: "emitWidth"; type: "double" }
        Signal {
            name: "emitFollowParticles"
            Parameter { name: "particles"; type: "QJSValue" }
            Parameter { name: "followed"; type: "QJSValue" }
        }
        Signal {
            name: "particlesPerParticlePerSecondChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "emitterXVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "emitterYVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "followChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "emissionShapeChanged"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method {
            name: "setParticlesPerParticlePerSecond"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setEmitterXVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setEmitterYVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFollow"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setEmissionShape"
            Parameter { name: "arg"; type: "QQuickParticleExtruder"; isPointer: true }
        }
        Method { name: "recalcParticlesPerSecond" }
    }
    Component {
        file: "qquickturbulence_p.h"
        name: "QQuickTurbulenceAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Turbulence 2.0",
            "QtQuick.Particles/Turbulence 2.1",
            "QtQuick.Particles/Turbulence 2.11",
            "QtQuick.Particles/Turbulence 2.4",
            "QtQuick.Particles/Turbulence 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "strength"; type: "double" }
        Property { name: "noiseSource"; type: "QUrl" }
        Signal {
            name: "strengthChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "noiseSourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setStrength"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setNoiseSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
    }
    Component {
        file: "qquickwander_p.h"
        name: "QQuickWanderAffector"
        prototype: "QQuickParticleAffector"
        exports: [
            "QtQuick.Particles/Wander 2.0",
            "QtQuick.Particles/Wander 2.1",
            "QtQuick.Particles/Wander 2.11",
            "QtQuick.Particles/Wander 2.4",
            "QtQuick.Particles/Wander 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "AffectableParameters"
            values: ["Position", "Velocity", "Acceleration"]
        }
        Property { name: "pace"; type: "double" }
        Property { name: "xVariance"; type: "double" }
        Property { name: "yVariance"; type: "double" }
        Property { name: "affectedParameter"; type: "AffectableParameters" }
        Signal {
            name: "xVarianceChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "yVarianceChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "paceChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "affectedParameterChanged"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
        Method {
            name: "setXVariance"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setYVariance"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setPace"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setAffectedParameter"
            Parameter { name: "arg"; type: "AffectableParameters" }
        }
    }
}
