#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离群检测UI管理器
负责离群检测相关的界面管理

Author: Jack
Date: 2025-06-04
"""

import logging
from typing import Dict, Any, List
from PyQt5.QtWidgets import (
    QGroupBox, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QComboBox, QCheckBox, QPushButton
)
from PyQt5.QtCore import pyqtSignal, QObject
from PyQt5.QtGui import QFont

# 导入安全数值输入框
from .safe_double_spinbox import SafeDoubleSpinBox

logger = logging.getLogger(__name__)


class OutlierDetectionUIManager(QObject):
    """
    离群检测UI管理器

    职责：
    - 管理离群检测相关的UI组件
    - 处理离群检测设置的用户交互
    - 管理基准数据的界面操作
    - 使用平均模式进行离群检测
    """
    
    # 信号定义
    settings_changed = pyqtSignal()  # 设置变更信号
    baseline_refresh_requested = pyqtSignal()  # 基准刷新请求
    baseline_manage_requested = pyqtSignal()  # 基准管理请求
    
    def __init__(self, parent=None):
        """初始化离群检测UI管理器"""
        super().__init__(parent)
        
        # UI组件
        self.outlier_enabled_checkbox = None
        self.outlier_threshold_spin = None
        self.outlier_baseline_combo = None
        self.outlier_status_label = None
        self.refresh_baselines_btn = None
        self.manage_baselines_btn = None
        
        self._loading = False  # 防止加载时触发变更信号
        
        logger.debug("离群检测UI管理器初始化完成")
    
    def create_outlier_detection_group(self) -> QGroupBox:
        """创建离群检测设置组（启用）"""
        try:
            group = QGroupBox("离群值检测设置")
            group.setFont(QFont("", 10, QFont.Weight.Bold))
            group.setEnabled(True)  # 启用离群检测功能

            layout = QVBoxLayout(group)
            layout.setSpacing(12)
            layout.setContentsMargins(15, 15, 15, 15)

            # 基本设置区域
            basic_layout = QGridLayout()
            basic_layout.setSpacing(12)

            # 启用离群检测
            basic_layout.addWidget(QLabel("启用离群检测:"), 0, 0)
            self.outlier_enabled_checkbox = QCheckBox()
            self.outlier_enabled_checkbox.setToolTip("启用后将根据设定的基准进行离群检测")
            basic_layout.addWidget(self.outlier_enabled_checkbox, 0, 1)

            # 偏差阈值设置
            basic_layout.addWidget(QLabel("偏差阈值:"), 0, 2)
            self.outlier_threshold_spin = SafeDoubleSpinBox()
            self.outlier_threshold_spin.setRange(1.0, 100.0)
            self.outlier_threshold_spin.setValue(10.0)
            self.outlier_threshold_spin.setSuffix(" %")
            self.outlier_threshold_spin.setToolTip("设置Z值偏差的百分比阈值，超过此值将判定为离群")
            basic_layout.addWidget(self.outlier_threshold_spin, 0, 3)

            # 基准选择（占用整行）
            basic_layout.addWidget(QLabel("选择基准:"), 1, 0)
            self.outlier_baseline_combo = QComboBox()
            self.outlier_baseline_combo.setToolTip("选择用于离群检测的基准数据（来自学习功能模块）")
            self.outlier_baseline_combo.setMinimumWidth(300)  # 设置最小宽度以显示完整的基准名称

            # 添加默认选项
            self.outlier_baseline_combo.addItem("请选择基准", None)

            basic_layout.addWidget(self.outlier_baseline_combo, 1, 1, 1, 3)  # 跨3列显示

            layout.addLayout(basic_layout)

            # 基准管理区域
            management_layout = QHBoxLayout()

            self.refresh_baselines_btn = QPushButton("刷新基准列表")
            self.refresh_baselines_btn.setMaximumWidth(120)
            self.refresh_baselines_btn.setToolTip("重新加载基准数据列表")
            management_layout.addWidget(self.refresh_baselines_btn)

            self.manage_baselines_btn = QPushButton("管理基准数据")
            self.manage_baselines_btn.setMaximumWidth(120)
            self.manage_baselines_btn.setToolTip("打开基准数据管理界面")
            management_layout.addWidget(self.manage_baselines_btn)

            management_layout.addStretch()

            # 状态显示
            self.outlier_status_label = QLabel("离群检测未启用")
            self.outlier_status_label.setObjectName("statusDisplay")
            management_layout.addWidget(self.outlier_status_label)

            layout.addLayout(management_layout)

            # 连接信号
            self._connect_signals()

            # 确保组件可见
            group.setVisible(True)
            if self.outlier_baseline_combo:
                self.outlier_baseline_combo.setVisible(True)

            logger.debug("离群检测设置组创建完成")
            return group
            
        except Exception as e:
            logger.error(f"创建离群检测设置组失败: {e}")
            return QGroupBox("离群检测设置（创建失败）")
    
    def _connect_signals(self):
        """连接信号"""
        try:
            if self.outlier_enabled_checkbox:
                self.outlier_enabled_checkbox.toggled.connect(self._on_outlier_settings_changed)

            if self.outlier_threshold_spin:
                self.outlier_threshold_spin.valueChanged.connect(self._on_outlier_settings_changed)

            if self.outlier_baseline_combo:
                self.outlier_baseline_combo.currentTextChanged.connect(self._on_baseline_selection_changed)

            if self.refresh_baselines_btn:
                self.refresh_baselines_btn.clicked.connect(self.baseline_refresh_requested.emit)

            if self.manage_baselines_btn:
                self.manage_baselines_btn.clicked.connect(self.baseline_manage_requested.emit)

            logger.debug("离群检测UI信号连接完成")

        except Exception as e:
            logger.error(f"连接离群检测UI信号失败: {e}")
    
    def _on_outlier_settings_changed(self):
        """离群检测设置变更处理"""
        try:
            if not self._loading:
                self.update_outlier_status()
                self.settings_changed.emit()
        except Exception as e:
            logger.error(f"离群检测设置变更处理失败: {e}")

    def _on_baseline_selection_changed(self):
        """基准选择变化处理"""
        try:
            if not self._loading:
                # 验证选中的基准
                baseline_id = self.outlier_baseline_combo.currentData() if self.outlier_baseline_combo else None
                if baseline_id:
                    validation_result = self.validate_selected_baseline()
                    if not validation_result.get('valid'):
                        logger.warning(f"基准验证失败: {validation_result.get('error')}")
                        # 更新状态显示为错误状态
                        if self.outlier_status_label:
                            self.outlier_status_label.setText(f"基准验证失败: {validation_result.get('error')}")
                            self.outlier_status_label.setStyleSheet("color: red; font-weight: bold;")
                    elif validation_result.get('warning'):
                        logger.warning(f"基准验证警告: {validation_result.get('warning')}")
                        # 更新状态显示为警告状态
                        if self.outlier_status_label:
                            baseline_name = self.outlier_baseline_combo.currentText() if self.outlier_baseline_combo else ""
                            self.outlier_status_label.setText(f"基准: {baseline_name.split('(')[0].strip()} (频率不匹配)")
                            self.outlier_status_label.setStyleSheet("color: orange; font-weight: bold;")
                    else:
                        # 验证通过，正常更新状态
                        self.update_outlier_status()
                else:
                    # 未选择基准，更新状态
                    self.update_outlier_status()

                # 发送设置变更信号
                self.settings_changed.emit()

        except Exception as e:
            logger.error(f"基准选择变化处理失败: {e}")
    
    def update_outlier_status(self):
        """更新离群检测状态显示"""
        try:
            if not self.outlier_status_label:
                return

            if self.outlier_enabled_checkbox and self.outlier_enabled_checkbox.isChecked():
                baseline_id = self.outlier_baseline_combo.currentData() if self.outlier_baseline_combo else None
                if baseline_id:
                    baseline_name = self.outlier_baseline_combo.currentText() if self.outlier_baseline_combo else ""
                    threshold = self.outlier_threshold_spin.value() if self.outlier_threshold_spin else 10.0
                    self.outlier_status_label.setText(
                        f"已启用 - 基准: {baseline_name.split('(')[0].strip()}, "
                        f"阈值: ±{threshold}%"
                    )
                    self.outlier_status_label.setStyleSheet("color: green; font-weight: bold;")
                else:
                    self.outlier_status_label.setText("已启用但未选择基准")
                    self.outlier_status_label.setStyleSheet("color: orange; font-weight: bold;")
            else:
                self.outlier_status_label.setText("离群检测未启用")
                self.outlier_status_label.setStyleSheet("color: gray;")

        except Exception as e:
            logger.error(f"更新离群检测状态失败: {e}")
    
    def load_baseline_list(self, baselines: list):
        """加载基准列表"""
        try:
            if not self.outlier_baseline_combo:
                return

            self._loading = True

            # 保存当前选中的基准ID
            current_baseline_id = self.outlier_baseline_combo.currentData()

            # 清空并重新填充下拉框
            self.outlier_baseline_combo.clear()
            self.outlier_baseline_combo.addItem("请选择基准", None)

            for baseline in baselines:
                # 格式化显示文本，包含更详细信息
                created_at = baseline.get('created_at', '')
                if created_at:
                    # 提取日期部分
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        date_str = dt.strftime('%m-%d %H:%M')
                    except:
                        date_str = created_at[:10]  # 取前10个字符作为日期
                else:
                    date_str = '未知时间'

                # 频率范围信息
                min_freq = baseline.get('min_frequency')
                max_freq = baseline.get('max_frequency')
                freq_range = ""
                if min_freq is not None and max_freq is not None:
                    if min_freq == max_freq:
                        freq_range = f" @{min_freq}Hz"
                    else:
                        freq_range = f" ({min_freq}-{max_freq}Hz)"

                display_text = f"{baseline['baseline_name']} ({baseline['detail_count']}点{freq_range}) - {date_str}"
                self.outlier_baseline_combo.addItem(display_text, baseline['id'])

            # 恢复之前选中的基准
            if current_baseline_id:
                for i in range(self.outlier_baseline_combo.count()):
                    if self.outlier_baseline_combo.itemData(i) == current_baseline_id:
                        self.outlier_baseline_combo.setCurrentIndex(i)
                        break

            self._loading = False

            logger.debug(f"基准列表加载完成，共{len(baselines)}个基准")

        except Exception as e:
            logger.error(f"加载基准列表失败: {e}")
            self._loading = False
    
    def load_outlier_config(self, config: Dict[str, Any]):
        """加载离群检测配置"""
        try:
            self._loading = True

            # 设置控件值
            if self.outlier_enabled_checkbox:
                self.outlier_enabled_checkbox.setChecked(config.get('is_enabled', False))

            if self.outlier_threshold_spin:
                self.outlier_threshold_spin.setValue(config.get('deviation_threshold', 10.0))

            # 设置选中的基准
            if self.outlier_baseline_combo:
                active_baseline_id = config.get('active_baseline_id')
                if active_baseline_id:
                    for i in range(self.outlier_baseline_combo.count()):
                        if self.outlier_baseline_combo.itemData(i) == active_baseline_id:
                            self.outlier_baseline_combo.setCurrentIndex(i)
                            break

            self._loading = False

            # 更新状态显示
            self.update_outlier_status()

            logger.debug("离群检测配置加载完成")

        except Exception as e:
            logger.error(f"加载离群检测配置失败: {e}")
            self._loading = False
    
    def get_outlier_config(self) -> Dict[str, Any]:
        """获取离群检测配置"""
        try:
            return {
                'is_enabled': self.outlier_enabled_checkbox.isChecked() if self.outlier_enabled_checkbox else False,
                'active_baseline_id': self.outlier_baseline_combo.currentData() if self.outlier_baseline_combo else None,
                'channel_mode': 'average_all',  # 使用平均模式
                'deviation_threshold': self.outlier_threshold_spin.value() if self.outlier_threshold_spin else 10.0
            }
        except Exception as e:
            logger.error(f"获取离群检测配置失败: {e}")
            return {}
    
    def set_loading(self, loading: bool):
        """设置加载状态"""
        self._loading = loading

    def validate_selected_baseline(self) -> Dict[str, Any]:
        """
        验证当前选中的基准

        Returns:
            验证结果字典
        """
        try:
            if not self.outlier_baseline_combo:
                return {'valid': False, 'error': '基准选择控件未初始化'}

            baseline_id = self.outlier_baseline_combo.currentData()
            if not baseline_id:
                return {'valid': False, 'error': '未选择基准'}

            # 获取基准详细信息
            from backend.outlier_detection_manager import OutlierDetectionManager
            outlier_manager = OutlierDetectionManager()

            try:
                baseline_details = outlier_manager.get_baseline_details(baseline_id)
                if not baseline_details:
                    return {'valid': False, 'error': '基准数据为空或已被删除'}

                # 获取基准的频率点
                baseline_frequencies = sorted(set(detail['frequency'] for detail in baseline_details))

                # 获取当前测试配置的频率点
                current_frequencies = self._get_current_test_frequencies()

                if not current_frequencies:
                    return {
                        'valid': True,
                        'warning': '无法获取当前测试频率配置，请确认频率设置正确',
                        'baseline_frequencies': baseline_frequencies,
                        'baseline_frequency_count': len(baseline_frequencies)
                    }

                # 检查频率点匹配
                frequency_match = self._check_frequency_match(baseline_frequencies, current_frequencies)

                result = {
                    'valid': True,
                    'baseline_frequencies': baseline_frequencies,
                    'current_frequencies': current_frequencies,
                    'baseline_frequency_count': len(baseline_frequencies),
                    'current_frequency_count': len(current_frequencies),
                    'frequency_match': frequency_match
                }

                if not frequency_match['is_match']:
                    result['warning'] = frequency_match['message']

                return result

            except Exception as e:
                return {'valid': False, 'error': f'获取基准数据失败: {e}'}

        except Exception as e:
            logger.error(f"验证基准失败: {e}")
            return {'valid': False, 'error': f'验证失败: {e}'}

    def _get_current_test_frequencies(self) -> List[float]:
        """获取当前测试配置的频率点"""
        try:
            # 尝试从配置管理器获取频率设置
            from utils.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 检查是否为单频模式
            is_single_mode = config_manager.get('frequency.single_mode', False)

            if is_single_mode:
                # 单频模式
                single_freq = config_manager.get('frequency.single_frequency', 1000.0)
                return [float(single_freq)]
            else:
                # 多频模式
                frequency_list = config_manager.get('frequency.list', [])
                if frequency_list:
                    return [float(f) for f in frequency_list]

                # 如果没有自定义频率列表，尝试获取预设模式
                preset_mode = config_manager.get('frequency.preset_mode', '')
                if preset_mode:
                    preset_frequencies = self._get_preset_frequencies(preset_mode)
                    if preset_frequencies:
                        return preset_frequencies

            logger.warning("无法获取当前测试频率配置")
            return []

        except Exception as e:
            logger.error(f"获取当前测试频率失败: {e}")
            return []

    def _get_preset_frequencies(self, preset_mode: str) -> List[float]:
        """获取预设模式的频率点"""
        preset_frequencies = {
            '研究模式': [0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50, 100, 200, 500, 1000, 2000, 5000, 7800],
            '生产模式': [1000.0],
            '快速模式': [100, 1000, 5000],
            '标准模式': [10, 100, 1000, 5000]
        }
        return preset_frequencies.get(preset_mode, [])

    def _check_frequency_match(self, baseline_frequencies: List[float], current_frequencies: List[float]) -> Dict[str, Any]:
        """检查频率点匹配"""
        try:
            # 转换为集合进行比较（允许小的浮点误差）
            baseline_set = set(round(f, 2) for f in baseline_frequencies)
            current_set = set(round(f, 2) for f in current_frequencies)

            if baseline_set == current_set:
                return {
                    'is_match': True,
                    'message': '频率点完全匹配'
                }

            # 检查包含关系
            if baseline_set.issubset(current_set):
                missing_in_baseline = current_set - baseline_set
                return {
                    'is_match': False,
                    'message': f'基准频率点是当前配置的子集，缺少频率点: {sorted(missing_in_baseline)}'
                }

            if current_set.issubset(baseline_set):
                extra_in_baseline = baseline_set - current_set
                return {
                    'is_match': False,
                    'message': f'当前配置频率点是基准的子集，基准包含额外频率点: {sorted(extra_in_baseline)}'
                }

            # 完全不匹配
            only_in_baseline = baseline_set - current_set
            only_in_current = current_set - baseline_set

            return {
                'is_match': False,
                'message': f'频率点不匹配。基准独有: {sorted(only_in_baseline)}，当前配置独有: {sorted(only_in_current)}'
            }

        except Exception as e:
            logger.error(f"检查频率点匹配失败: {e}")
            return {
                'is_match': False,
                'message': f'频率点匹配检查失败: {e}'
            }
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理UI组件引用
            self.outlier_enabled_checkbox = None
            self.outlier_threshold_spin = None
            self.outlier_baseline_combo = None
            self.outlier_status_label = None
            self.refresh_baselines_btn = None
            self.manage_baselines_btn = None

            logger.debug("离群检测UI管理器资源清理完成")

        except Exception as e:
            logger.error(f"离群检测UI管理器清理失败: {e}")
