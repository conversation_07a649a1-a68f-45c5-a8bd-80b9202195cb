#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试验证脚本
验证配置修复后的测试效果
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from backend.test_config_manager import TestConfigManager
from backend.communication_manager import CommunicationManager
from backend.test_executor import TestExecutor
from backend.test_engine_adapter import TestEngineAdapter

class AutoTestVerification:
    """自动化测试验证类"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.test_config_manager = TestConfigManager(self.config_manager)
        self.comm_manager = None
        self.test_engine = None
        self.test_results = {}
        self.test_start_time = None
        self.test_end_time = None
        self.frequency_count = 0
        self.completed_frequencies = 0
        
    def setup_communication(self):
        """设置通信连接"""
        try:
            print("🔌 正在建立设备连接...")
            self.comm_manager = CommunicationManager()
            
            # 尝试连接设备
            if self.comm_manager.connect():
                print("✅ 设备连接成功")
                return True
            else:
                print("❌ 设备连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 设备连接异常: {e}")
            return False
    
    def verify_test_config(self):
        """验证测试配置"""
        print("\n📋 验证测试配置...")
        
        test_config = self.test_config_manager.load_test_config()
        
        # 检查关键配置
        count_limit_enabled = test_config.get('count_limit_enabled', True)
        max_count = test_config.get('max_count', 5)
        timeout = test_config.get('timeout', 52)
        use_parallel_staggered = test_config.get('use_parallel_staggered_mode', False)
        frequencies = test_config.get('frequencies', [])
        
        print(f"   • 计数限制: {'禁用' if not count_limit_enabled else '启用'}")
        print(f"   • 最大测试次数: {max_count}")
        print(f"   • 超时时间: {timeout}秒")
        print(f"   • 并行错频模式: {'启用' if use_parallel_staggered else '禁用'}")
        print(f"   • 频点数量: {len(frequencies)}个")
        
        self.frequency_count = len(frequencies)
        
        # 验证配置是否正确
        config_ok = (
            not count_limit_enabled and
            max_count >= 100 and
            timeout <= 10 and
            use_parallel_staggered and
            len(frequencies) == 20
        )
        
        if config_ok:
            print("✅ 测试配置验证通过")
            return True
        else:
            print("❌ 测试配置存在问题")
            return False
    
    def progress_callback(self, channel, progress_data):
        """进度回调函数"""
        try:
            frequency = progress_data.get('frequency', 0)
            frequency_index = progress_data.get('frequency_index', 0)
            total_frequencies = progress_data.get('total_frequencies', 0)
            state = progress_data.get('state', 'unknown')
            
            if frequency_index > self.completed_frequencies:
                self.completed_frequencies = frequency_index
                print(f"📊 进度更新: 频点 {frequency_index}/{total_frequencies} ({frequency:.3f}Hz)")
                
        except Exception as e:
            print(f"⚠️ 进度回调异常: {e}")
    
    def status_callback(self, status_data):
        """状态回调函数"""
        try:
            action = status_data.get('action', 'unknown')
            
            if action == 'test_completed':
                self.test_end_time = time.time()
                print(f"✅ 测试完成通知收到")
                
            elif action == 'frequency_completed':
                frequency = status_data.get('frequency', 0)
                print(f"🎯 频点完成: {frequency:.3f}Hz")
                
        except Exception as e:
            print(f"⚠️ 状态回调异常: {e}")
    
    def run_test(self):
        """运行测试"""
        try:
            print("\n🚀 开始自动化测试...")
            
            # 创建测试引擎
            self.test_engine = TestEngineAdapter(self.comm_manager)
            
            # 设置回调函数
            self.test_engine.set_progress_callback(self.progress_callback)
            self.test_engine.set_status_callback(self.status_callback)
            
            # 准备测试数据
            battery_codes = [f"AUTO-TEST-{i+1:02d}" for i in range(8)]
            batch_info = "AUTO-VERIFICATION-BATCH"
            
            print(f"📋 电池码: {battery_codes}")
            print(f"📋 批次信息: {batch_info}")
            
            # 记录开始时间
            self.test_start_time = time.time()
            start_time_str = datetime.now().strftime("%H:%M:%S")
            print(f"⏰ 测试开始时间: {start_time_str}")
            
            # 启动测试
            success = self.test_engine.start_batch_test(battery_codes, batch_info)
            
            if success:
                print("✅ 测试启动成功")
                return True
            else:
                print("❌ 测试启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 测试运行异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def monitor_test_progress(self, timeout_minutes=5):
        """监控测试进度"""
        print(f"\n👀 监控测试进度 (最长等待{timeout_minutes}分钟)...")
        
        start_time = time.time()
        timeout_seconds = timeout_minutes * 60
        last_progress = 0
        
        while time.time() - start_time < timeout_seconds:
            try:
                # 检查测试是否完成
                if self.test_end_time:
                    break
                
                # 显示进度
                if self.completed_frequencies > last_progress:
                    elapsed = time.time() - self.test_start_time
                    progress_percent = (self.completed_frequencies / self.frequency_count) * 100
                    print(f"📈 测试进度: {self.completed_frequencies}/{self.frequency_count} ({progress_percent:.1f}%) - 已用时: {elapsed:.1f}秒")
                    last_progress = self.completed_frequencies
                
                time.sleep(2)  # 每2秒检查一次
                
            except KeyboardInterrupt:
                print("\n🛑 用户中断测试监控")
                break
            except Exception as e:
                print(f"⚠️ 监控异常: {e}")
                break
        
        # 检查测试结果
        if self.test_end_time:
            total_time = self.test_end_time - self.test_start_time
            print(f"✅ 测试完成！总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
            return True
        else:
            print(f"⏰ 测试超时或未完成 (等待了{timeout_minutes}分钟)")
            return False
    
    def generate_report(self):
        """生成测试报告"""
        print("\n📊 测试验证报告")
        print("=" * 60)
        
        if self.test_start_time and self.test_end_time:
            total_time = self.test_end_time - self.test_start_time
            print(f"⏰ 测试时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
        else:
            print("⏰ 测试时间: 未完成或异常")
        
        print(f"📊 预期频点数量: {self.frequency_count}")
        print(f"📊 完成频点数量: {self.completed_frequencies}")
        
        if self.completed_frequencies == self.frequency_count:
            print("✅ 频点测试完整性: 通过")
        else:
            print("❌ 频点测试完整性: 不完整")
        
        # 验证时间是否合理
        if self.test_start_time and self.test_end_time:
            total_time = self.test_end_time - self.test_start_time
            if 60 <= total_time <= 180:  # 1-3分钟
                print("✅ 测试时间合理性: 通过 (1-3分钟范围内)")
            else:
                print(f"⚠️ 测试时间合理性: 异常 ({total_time:.1f}秒)")
        
        print("=" * 60)

def main():
    """主函数"""
    print("🔍 JCY5001AS 自动化测试验证工具")
    print("=" * 60)
    
    verifier = AutoTestVerification()
    
    try:
        # 1. 验证配置
        if not verifier.verify_test_config():
            print("❌ 配置验证失败，退出测试")
            return
        
        # 2. 建立设备连接
        if not verifier.setup_communication():
            print("❌ 设备连接失败，退出测试")
            return
        
        # 3. 运行测试
        if not verifier.run_test():
            print("❌ 测试启动失败，退出验证")
            return
        
        # 4. 监控测试进度
        test_completed = verifier.monitor_test_progress(timeout_minutes=5)
        
        # 5. 生成报告
        verifier.generate_report()
        
        if test_completed:
            print("\n🎉 自动化验证完成！配置修复生效！")
        else:
            print("\n⚠️ 自动化验证未完全完成，请检查测试状态")
            
    except Exception as e:
        print(f"❌ 验证过程异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        if verifier.comm_manager:
            try:
                verifier.comm_manager.disconnect()
                print("🔌 设备连接已断开")
            except:
                pass

if __name__ == "__main__":
    main()
