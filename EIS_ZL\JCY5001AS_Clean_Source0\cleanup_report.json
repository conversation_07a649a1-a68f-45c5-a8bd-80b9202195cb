{"start_time": "2025-07-01T10:15:33.667930", "deleted_files": ["E:\\Code\\JCY5001AS_Clean_Source\\test_baseline_delete_function.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_baseline_display_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_data_analysis_info.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_duplicate_statistics_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_learning_multi_channel_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_learning_ui_layout.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_nyquist_improvements.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_print_data_transfer.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_print_timing_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_settings_sync.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_statistics_final_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\test_stop_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_continuous_count_logic.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_continuous_test_count_display.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_continuous_test_startup.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_data_consistency.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_duplicate_statistics.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_log_duplicates.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_print_data_transfer.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_print_timing_issue.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_second_test_results.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_statistics_duplicate_final.py", "E:\\Code\\JCY5001AS_Clean_Source\\fix_unit_conversion.py", "E:\\Code\\JCY5001AS_Clean_Source\\optimize_battery_detection_time.py", "E:\\Code\\JCY5001AS_Clean_Source\\optimize_serial_log.py", "E:\\Code\\JCY5001AS_Clean_Source\\optimize_serial_numbers.py", "E:\\Code\\JCY5001AS_Clean_Source\\enhanced_stop_test_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\execute_data_consistency_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\simple_continuous_count_fix.py", "E:\\Code\\JCY5001AS_Clean_Source\\algorithm_comparison.png", "E:\\Code\\JCY5001AS_Clean_Source\\equivalent_circuit_real_data_analysis.png", "E:\\Code\\JCY5001AS_Clean_Source\\individual_methods_test.png", "E:\\Code\\JCY5001AS_Clean_Source\\rct_debug_analysis.png", "E:\\Code\\JCY5001AS_Clean_Source\\rs_calculation_comparison.png", "E:\\Code\\JCY5001AS_Clean_Source\\rs_debug_nyquist.png", "E:\\Code\\JCY5001AS_Clean_Source\\smoothing_test_results.png"], "deleted_dirs": ["E:\\Code\\JCY5001AS_Clean_Source\\backup", "E:\\Code\\JCY5001AS_Clean_Source\\backup_continuous_count_fix_20250621_213628", "E:\\Code\\JCY5001AS_Clean_Source\\backup_continuous_startup_fix_20250621_215101", "E:\\Code\\JCY5001AS_Clean_Source\\backup_count_logic_fix_20250621_214542", "E:\\Code\\JCY5001AS_Clean_Source\\backup_duplicate_stats_fix_20250624_102905", "E:\\Code\\JCY5001AS_Clean_Source\\backup_low_freq_fix", "E:\\Code\\JCY5001AS_Clean_Source\\backup_print_fix_20250623_065522", "E:\\Code\\JCY5001AS_Clean_Source\\backup_print_timing_fix_20250623_070951", "E:\\Code\\JCY5001AS_Clean_Source\\backup_simple_count_fix_20250621_215605", "E:\\Code\\JCY5001AS_Clean_Source\\backup_statistics_final_fix_20250624_115906", "E:\\Code\\JCY5001AS_Clean_Source\\backup_statistics_triple_fix_20250624_103351", "E:\\Code\\JCY5001AS_Clean_Source\\backup_unit_fix", "E:\\Code\\JCY5001AS_Clean_Source\\main.build", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.09_20250626_223438\\main.build", "E:\\Code\\JCY5001AS_Clean_Source\\main.dist", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.09_20250626_223438\\main.dist", "E:\\Code\\JCY5001AS_Clean_Source\\dist", "E:\\Code\\JCY5001AS_Clean_Source\\nuitka-crash-report.xml", "E:\\Code\\JCY5001AS_Clean_Source\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\backend\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\backend\\parallel_test_managers\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\data\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\ui\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\ui\\components\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\ui\\dialogs\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\ui\\main_window_managers\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\ui\\test_flow_managers\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\ui\\widgets\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\utils\\__pycache__", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pyexpat.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\select.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\unicodedata.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\win32api.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\win32evtlog.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\win32print.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\win32ui.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_asyncio.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_bz2.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_cffi_backend.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_ctypes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_decimal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_elementtree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_hashlib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_lzma.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_multiprocessing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_overlapped.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_queue.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_socket.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_sqlite3.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_ssl.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\_uuid.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\greenlet\\_greenlet.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\kiwisolver\\_cext.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\lxml\\builder.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\lxml\\etree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\lxml\\objectify.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\lxml\\sax.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\lxml\\_elementpath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\markupsafe\\_speedups.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\ft2font.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\_c_internal_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\_image.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\_path.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\_qhull.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\_tri.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\matplotlib\\backends\\_backend_agg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\core\\_multiarray_tests.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\core\\_multiarray_umath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\fft\\_pocketfft_internal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\linalg\\_umath_linalg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\bit_generator.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\mtrand.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_bounded_integers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_common.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_generator.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_mt19937.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_pcg64.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_philox.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\numpy\\random\\_sfc64.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\io\\sas\\_byteswap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\io\\sas\\_sas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\algos.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\arrays.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\groupby.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\hashing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\hashtable.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\index.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\indexing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\internals.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\interval.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\join.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\json.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\lib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\missing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\ops.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\ops_dispatch.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\parsers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\properties.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\reduction.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\reshape.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\sparse.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\writers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\base.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\ccalendar.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\conversion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\dtypes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\fields.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\nattype.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\np_datetime.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\offsets.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\parsing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\period.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\strptime.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\timedeltas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\timestamps.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\timezones.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\tzconversion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\tslibs\\vectorized.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\window\\aggregations.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\pandas\\_libs\\window\\indexers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PIL\\_imaging.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PIL\\_imagingcms.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PIL\\_imagingft.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PIL\\_imagingmath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PIL\\_imagingtk.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PIL\\_webp.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\psutil\\_psutil_windows.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\psycopg2\\_psycopg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PyQt5\\QtCore.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PyQt5\\QtGui.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PyQt5\\QtWidgets.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\PyQt5\\sip.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\cluster\\_hierarchy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\cluster\\_optimal_leaf_ordering.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\cluster\\_vq.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\fft\\_pocketfft\\pypocketfft.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\integrate\\_dop.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\integrate\\_lsoda.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\integrate\\_odepack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\integrate\\_quadpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\integrate\\_vode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_bspl.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_dfitpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_dierckx.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_fitpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_interpnd.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_ppoly.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_rbfinterp_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\interpolate\\_rgi_cython.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\cython_blas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\cython_lapack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_cythonized_array_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_decomp_interpolative.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_decomp_lu_cython.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_decomp_update.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_fblas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_flapack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_linalg_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_matfuncs_expm.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_matfuncs_sqrtm_triu.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\linalg\\_solve_toeplitz.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\ndimage\\_nd_image.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\ndimage\\_ni_label.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\ndimage\\_rank_filter_1d.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_bglu_dense.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_cobyla.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_cython_nnls.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_direct.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_group_columns.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_lbfgsb.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_lsap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_minpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_moduleTNC.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_pava_pybind.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_slsqp.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_zeros.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_highspy\\_core.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_highspy\\_highs_options.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_lsq\\givens_elimination.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\optimize\\_trlib\\_trlib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\signal\\_max_len_seq_inner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\signal\\_peak_finding_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\signal\\_sigtools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\signal\\_sosfilt.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\signal\\_spline.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\signal\\_upfirdn_apply.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\_csparsetools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\_sparsetools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_flow.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_matching.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_min_spanning_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_reordering.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_shortest_path.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_tools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\csgraph\\_traversal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\linalg\\_dsolve\\_superlu.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\linalg\\_propack\\_cpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\linalg\\_propack\\_dpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\linalg\\_propack\\_spropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\sparse\\linalg\\_propack\\_zpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\_ckdtree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\_distance_pybind.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\_distance_wrap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\_hausdorff.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\_qhull.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\_voronoi.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\spatial\\transform\\_rotation.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\cython_special.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_comb.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_ellip_harm_2.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_gufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_specfun.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_special_ufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_ufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\special\\_ufuncs_cxx.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_ansari_swilk_statistics.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_biasedurn.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_mvn.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_qmc_cy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_sobol.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_stats.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_stats_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_levy_stable\\levyst.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\stats\\_rcont\\rcont.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\_lib\\messagestream.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\_lib\\_ccallback_c.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\scipy\\_lib\\_uarray\\_uarray.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\shiboken6\\Shiboken.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\_isotonic.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_dbscan_inner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_hierarchical_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_k_means_common.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_k_means_elkan.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_k_means_lloyd.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_k_means_minibatch.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_hdbscan\\_linkage.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_hdbscan\\_reachability.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\cluster\\_hdbscan\\_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\decomposition\\_cdnmf_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\decomposition\\_online_lda_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\linear_model\\_cd_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\linear_model\\_sag_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\linear_model\\_sgd_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\manifold\\_barnes_hut_tsne.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\manifold\\_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_dist_metrics.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\neighbors\\_ball_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\neighbors\\_kd_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\neighbors\\_partition_nodes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\neighbors\\_quad_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\preprocessing\\_csr_polynomial_expansion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\preprocessing\\_target_encoder_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\svm\\_liblinear.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\svm\\_libsvm.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\svm\\_libsvm_sparse.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\tree\\_criterion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\tree\\_partitioner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\tree\\_splitter.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\tree\\_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\tree\\_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\arrayfuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\murmurhash.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\sparsefuncs_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_cython_blas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_fast_dict.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_heap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_isfinite.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_openmp_helpers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_random.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_seq_dataset.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_sorting.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_typedefs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_vector_sentinel.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\utils\\_weight_vector.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\_loss\\_loss.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sklearn\\__check_build\\_check_build.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sqlalchemy\\cyextension\\collections.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sqlalchemy\\cyextension\\immutabledict.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sqlalchemy\\cyextension\\processors.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sqlalchemy\\cyextension\\resultproxy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\sqlalchemy\\cyextension\\util.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pyexpat.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\select.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\unicodedata.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\win32api.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\win32evtlog.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\win32print.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\win32ui.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_asyncio.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_bz2.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_cffi_backend.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_ctypes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_decimal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_elementtree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_hashlib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_lzma.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_multiprocessing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_overlapped.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_queue.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_socket.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_sqlite3.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_ssl.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\_uuid.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\bcrypt\\_bcrypt.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\contourpy\\_contourpy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\cryptography\\hazmat\\bindings\\_rust.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\greenlet\\_greenlet.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\kiwisolver\\_cext.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\lxml\\builder.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\lxml\\etree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\lxml\\objectify.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\lxml\\sax.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\lxml\\_elementpath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\markupsafe\\_speedups.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\ft2font.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\_c_internal_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\_image.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\_path.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\_qhull.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\_tri.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\matplotlib\\backends\\_backend_agg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\core\\_multiarray_tests.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\core\\_multiarray_umath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\fft\\_pocketfft_internal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\linalg\\_umath_linalg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\bit_generator.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\mtrand.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_bounded_integers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_common.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_generator.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_mt19937.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_pcg64.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_philox.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\numpy\\random\\_sfc64.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\io\\sas\\_byteswap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\io\\sas\\_sas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\algos.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\arrays.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\groupby.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\hashing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\hashtable.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\index.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\indexing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\internals.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\interval.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\join.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\json.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\lib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\missing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\ops.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\ops_dispatch.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\parsers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\properties.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\reduction.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\reshape.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\sparse.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\writers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\base.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\ccalendar.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\conversion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\dtypes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\fields.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\nattype.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\np_datetime.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\offsets.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\parsing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\period.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\strptime.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\timedeltas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\timestamps.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\timezones.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\tzconversion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\tslibs\\vectorized.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\window\\aggregations.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\pandas\\_libs\\window\\indexers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PIL\\_imaging.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PIL\\_imagingcms.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PIL\\_imagingft.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PIL\\_imagingmath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PIL\\_imagingtk.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PIL\\_webp.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\psutil\\_psutil_windows.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\psycopg2\\_psycopg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PyQt5\\QtCore.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PyQt5\\QtGui.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PyQt5\\QtWidgets.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\PyQt5\\sip.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\cluster\\_hierarchy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\cluster\\_optimal_leaf_ordering.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\cluster\\_vq.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\fft\\_pocketfft\\pypocketfft.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\integrate\\_dop.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\integrate\\_lsoda.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\integrate\\_odepack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\integrate\\_quadpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\integrate\\_vode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_bspl.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_dfitpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_dierckx.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_fitpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_interpnd.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_ppoly.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_rbfinterp_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\interpolate\\_rgi_cython.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\cython_blas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\cython_lapack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_cythonized_array_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_decomp_interpolative.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_decomp_lu_cython.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_decomp_update.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_fblas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_flapack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_linalg_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_matfuncs_expm.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_matfuncs_sqrtm_triu.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\linalg\\_solve_toeplitz.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\ndimage\\_nd_image.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\ndimage\\_ni_label.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\ndimage\\_rank_filter_1d.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_bglu_dense.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_cobyla.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_cython_nnls.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_direct.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_group_columns.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_lbfgsb.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_lsap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_minpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_moduleTNC.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_pava_pybind.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_slsqp.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_zeros.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_highspy\\_core.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_highspy\\_highs_options.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_lsq\\givens_elimination.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\optimize\\_trlib\\_trlib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\signal\\_max_len_seq_inner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\signal\\_peak_finding_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\signal\\_sigtools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\signal\\_sosfilt.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\signal\\_spline.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\signal\\_upfirdn_apply.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\_csparsetools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\_sparsetools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_flow.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_matching.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_min_spanning_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_reordering.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_shortest_path.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_tools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\csgraph\\_traversal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\linalg\\_dsolve\\_superlu.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\linalg\\_propack\\_cpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\linalg\\_propack\\_dpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\linalg\\_propack\\_spropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\sparse\\linalg\\_propack\\_zpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\_ckdtree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\_distance_pybind.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\_distance_wrap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\_hausdorff.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\_qhull.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\_voronoi.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\spatial\\transform\\_rotation.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\cython_special.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_comb.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_ellip_harm_2.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_gufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_specfun.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_special_ufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_ufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\special\\_ufuncs_cxx.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_ansari_swilk_statistics.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_biasedurn.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_mvn.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_qmc_cy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_sobol.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_stats.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_stats_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_levy_stable\\levyst.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\stats\\_rcont\\rcont.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\_lib\\messagestream.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\_lib\\_ccallback_c.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\scipy\\_lib\\_uarray\\_uarray.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\shiboken6\\Shiboken.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\_isotonic.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_dbscan_inner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_hierarchical_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_k_means_common.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_k_means_elkan.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_k_means_lloyd.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_k_means_minibatch.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_hdbscan\\_linkage.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_hdbscan\\_reachability.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\cluster\\_hdbscan\\_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\decomposition\\_cdnmf_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\decomposition\\_online_lda_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\linear_model\\_cd_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\linear_model\\_sag_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\linear_model\\_sgd_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\manifold\\_barnes_hut_tsne.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\manifold\\_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_dist_metrics.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\neighbors\\_ball_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\neighbors\\_kd_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\neighbors\\_partition_nodes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\neighbors\\_quad_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\preprocessing\\_csr_polynomial_expansion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\preprocessing\\_target_encoder_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\svm\\_liblinear.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\svm\\_libsvm.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\svm\\_libsvm_sparse.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\tree\\_criterion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\tree\\_partitioner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\tree\\_splitter.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\tree\\_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\tree\\_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\arrayfuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\murmurhash.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\sparsefuncs_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_cython_blas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_fast_dict.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_heap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_isfinite.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_openmp_helpers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_random.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_seq_dataset.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_sorting.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_typedefs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_vector_sentinel.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\utils\\_weight_vector.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\_loss\\_loss.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sklearn\\__check_build\\_check_build.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sqlalchemy\\cyextension\\collections.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sqlalchemy\\cyextension\\immutabledict.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sqlalchemy\\cyextension\\processors.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sqlalchemy\\cyextension\\resultproxy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\sqlalchemy\\cyextension\\util.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pyexpat.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\select.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\unicodedata.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\win32api.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\win32evtlog.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\win32print.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\win32ui.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_asyncio.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_bz2.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_cffi_backend.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_ctypes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_decimal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_elementtree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_hashlib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_lzma.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_multiprocessing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_overlapped.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_queue.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_socket.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_sqlite3.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_ssl.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\_uuid.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\bcrypt\\_bcrypt.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\contourpy\\_contourpy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\cryptography\\hazmat\\bindings\\_rust.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\greenlet\\_greenlet.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\kiwisolver\\_cext.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\lxml\\builder.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\lxml\\etree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\lxml\\objectify.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\lxml\\sax.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\lxml\\_elementpath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\markupsafe\\_speedups.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\ft2font.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\_c_internal_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\_image.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\_path.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\_qhull.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\_tri.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\matplotlib\\backends\\_backend_agg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\core\\_multiarray_tests.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\core\\_multiarray_umath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\fft\\_pocketfft_internal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\linalg\\_umath_linalg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\bit_generator.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\mtrand.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_bounded_integers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_common.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_generator.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_mt19937.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_pcg64.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_philox.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\numpy\\random\\_sfc64.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\io\\sas\\_byteswap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\io\\sas\\_sas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\algos.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\arrays.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\groupby.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\hashing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\hashtable.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\index.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\indexing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\internals.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\interval.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\join.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\json.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\lib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\missing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\ops.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\ops_dispatch.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\parsers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\properties.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\reduction.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\reshape.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\sparse.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\writers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\base.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\ccalendar.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\conversion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\dtypes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\fields.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\nattype.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\np_datetime.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\offsets.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\parsing.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\period.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\strptime.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\timedeltas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\timestamps.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\timezones.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\tzconversion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\tslibs\\vectorized.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\window\\aggregations.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\pandas\\_libs\\window\\indexers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PIL\\_imaging.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PIL\\_imagingcms.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PIL\\_imagingft.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PIL\\_imagingmath.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PIL\\_imagingtk.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PIL\\_webp.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\psutil\\_psutil_windows.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\psycopg2\\_psycopg.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PyQt5\\QtCore.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PyQt5\\QtGui.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PyQt5\\QtWidgets.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\PyQt5\\sip.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\cluster\\_hierarchy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\cluster\\_optimal_leaf_ordering.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\cluster\\_vq.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\fft\\_pocketfft\\pypocketfft.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\integrate\\_dop.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\integrate\\_lsoda.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\integrate\\_odepack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\integrate\\_quadpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\integrate\\_vode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_bspl.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_dfitpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_dierckx.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_fitpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_interpnd.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_ppoly.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_rbfinterp_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\interpolate\\_rgi_cython.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\cython_blas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\cython_lapack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_cythonized_array_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_decomp_interpolative.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_decomp_lu_cython.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_decomp_update.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_fblas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_flapack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_linalg_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_matfuncs_expm.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_matfuncs_sqrtm_triu.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\linalg\\_solve_toeplitz.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\ndimage\\_nd_image.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\ndimage\\_ni_label.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\ndimage\\_rank_filter_1d.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_bglu_dense.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_cobyla.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_cython_nnls.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_direct.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_group_columns.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_lbfgsb.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_lsap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_minpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_moduleTNC.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_pava_pybind.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_slsqp.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_zeros.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_highspy\\_core.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_highspy\\_highs_options.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_lsq\\givens_elimination.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\optimize\\_trlib\\_trlib.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\signal\\_max_len_seq_inner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\signal\\_peak_finding_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\signal\\_sigtools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\signal\\_sosfilt.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\signal\\_spline.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\signal\\_upfirdn_apply.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\_csparsetools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\_sparsetools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_flow.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_matching.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_min_spanning_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_reordering.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_shortest_path.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_tools.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\csgraph\\_traversal.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\linalg\\_dsolve\\_superlu.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\linalg\\_propack\\_cpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\linalg\\_propack\\_dpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\linalg\\_propack\\_spropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\sparse\\linalg\\_propack\\_zpropack.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\_ckdtree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\_distance_pybind.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\_distance_wrap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\_hausdorff.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\_qhull.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\_voronoi.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\spatial\\transform\\_rotation.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\cython_special.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_comb.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_ellip_harm_2.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_gufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_specfun.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_special_ufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_ufuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\special\\_ufuncs_cxx.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_ansari_swilk_statistics.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_biasedurn.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_mvn.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_qmc_cy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_sobol.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_stats.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_stats_pythran.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_levy_stable\\levyst.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\stats\\_rcont\\rcont.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\_lib\\messagestream.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\_lib\\_ccallback_c.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\scipy\\_lib\\_uarray\\_uarray.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\shiboken6\\Shiboken.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\_isotonic.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_dbscan_inner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_hierarchical_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_k_means_common.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_k_means_elkan.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_k_means_lloyd.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_k_means_minibatch.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_hdbscan\\_linkage.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_hdbscan\\_reachability.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\cluster\\_hdbscan\\_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\decomposition\\_cdnmf_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\decomposition\\_online_lda_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\linear_model\\_cd_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\linear_model\\_sag_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\linear_model\\_sgd_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\manifold\\_barnes_hut_tsne.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\manifold\\_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_dist_metrics.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\neighbors\\_ball_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\neighbors\\_kd_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\neighbors\\_partition_nodes.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\neighbors\\_quad_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\preprocessing\\_csr_polynomial_expansion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\preprocessing\\_target_encoder_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\svm\\_liblinear.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\svm\\_libsvm.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\svm\\_libsvm_sparse.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\tree\\_criterion.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\tree\\_partitioner.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\tree\\_splitter.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\tree\\_tree.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\tree\\_utils.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\arrayfuncs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\murmurhash.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\sparsefuncs_fast.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_cython_blas.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_fast_dict.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_heap.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_isfinite.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_openmp_helpers.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_random.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_seq_dataset.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_sorting.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_typedefs.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_vector_sentinel.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\utils\\_weight_vector.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\_loss\\_loss.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sklearn\\__check_build\\_check_build.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sqlalchemy\\cyextension\\collections.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sqlalchemy\\cyextension\\immutabledict.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sqlalchemy\\cyextension\\processors.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sqlalchemy\\cyextension\\resultproxy.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\sqlalchemy\\cyextension\\util.pyd", "E:\\Code\\JCY5001AS_Clean_Source\\logs", "E:\\Code\\JCY5001AS_Clean_Source\\.git\\logs", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.06\\logs", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\logs", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.08_20250626_151511\\logs", "E:\\Code\\JCY5001AS_Clean_Source\\test_stop_fix.log", "E:\\Code\\JCY5001AS_Clean_Source\\test_ui_fix.log", "E:\\Code\\JCY5001AS_Clean_Source\\data\\license_operations.log", "E:\\Code\\JCY5001AS_Clean_Source\\dist\\JCY5001A_V0.80.07\\data\\license_operations.log"], "moved_files": [], "errors": ["删除失败 E:\\Code\\JCY5001AS_Clean_Source\\dist: [WinError 5] 拒绝访问。: 'E:\\\\Code\\\\JCY5001AS_Clean_Source\\\\dist\\\\JCY5001A_V0.80.06\\\\docs\\\\api(1).md'"], "summary": {"deleted_files_count": 36, "deleted_dirs_count": 833, "moved_files_count": 0, "errors_count": 1}, "end_time": "2025-07-01T10:15:37.784903"}