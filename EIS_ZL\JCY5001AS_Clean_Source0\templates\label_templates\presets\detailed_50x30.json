{"template_id": "detailed_50x30", "name": "详细模板", "description": "50x30mm详细标签模板，包含离群率等扩展信息", "size": "50x30mm", "elements": [{"element_id": "title", "element_type": "text", "x": 12, "y": 5, "width": 200, "height": 18, "content": "JCY5001AS 详细测试报告", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "battery_code", "element_type": "text", "x": 12, "y": 25, "width": 200, "height": 16, "content": "电池: {battery_code}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "channel_voltage", "element_type": "text", "x": 12, "y": 42, "width": 200, "height": 16, "content": "CH{channel_number} 电压:{voltage:.2f}V", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "rs_info", "element_type": "text", "x": 12, "y": 59, "width": 200, "height": 16, "content": "Rs:{rs_value:.3f}mΩ G{rs_grade}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "rct_info", "element_type": "text", "x": 12, "y": 76, "width": 200, "height": 16, "content": "Rct:{rct_value:.3f}mΩ G{rct_grade}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "outlier_rate", "element_type": "text", "x": 12, "y": 93, "width": 200, "height": 16, "content": "离群率: {outlier_rate}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "batch_info", "element_type": "text", "x": 12, "y": 110, "width": 200, "height": 16, "content": "批次:{batch_number} {operator}", "font_family": "微软雅黑", "font_size": 10, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "status", "element_type": "text", "x": 12, "y": 130, "width": 100, "height": 20, "content": "{is_pass}", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "timestamp", "element_type": "text", "x": 12, "y": 155, "width": 200, "height": 14, "content": "{timestamp}", "font_family": "微软雅黑", "font_size": 10, "font_style": "normal", "text_color": "gray", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "qr_code", "element_type": "qr_code", "x": 315, "y": 60, "width": 75, "height": 75, "content": "{battery_code}", "font_family": "微软雅黑", "font_size": 14, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}], "version": "1.0", "created_time": "2025-06-05T17:20:57.712871", "modified_time": "2025-06-05T17:20:57.712871", "author": "系统预设"}