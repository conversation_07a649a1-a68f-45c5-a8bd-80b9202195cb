#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产线测试系统与研发测试系统数据对比验证脚本

功能：
1. 对比同一电池在两个系统中的测试结果
2. 分析数据差异和偏差模式
3. 生成详细的对比报告
4. 识别数据不一致的根本原因

Author: Augment Agent
Date: 2025-01-28
"""

import os
import sys
import json
import logging
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataComparisonValidator:
    """数据对比验证器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.production_db = os.path.join(project_root, 'data', 'test_results.db')
        self.research_db = os.path.join(project_root, 'research_software', 'data', 'research_data.db')
        self.report_dir = os.path.join(project_root, 'comparison_reports', datetime.now().strftime('%Y%m%d_%H%M%S'))
        
        os.makedirs(self.report_dir, exist_ok=True)
    
    def run_validation(self):
        """执行验证"""
        try:
            logger.info("开始数据对比验证...")
            
            # 1. 检查数据库连接
            self._check_database_connections()
            
            # 2. 获取对比数据
            production_data = self._get_production_data()
            research_data = self._get_research_data()
            
            # 3. 数据预处理和匹配
            matched_data = self._match_test_data(production_data, research_data)
            
            # 4. 执行对比分析
            comparison_results = self._perform_comparison_analysis(matched_data)
            
            # 5. 生成对比报告
            self._generate_comparison_report(comparison_results)
            
            # 6. 生成可视化图表
            self._generate_visualization_charts(comparison_results)
            
            logger.info(f"数据对比验证完成！报告保存在: {self.report_dir}")
            
        except Exception as e:
            logger.error(f"验证失败: {e}")
            raise
    
    def _check_database_connections(self):
        """检查数据库连接"""
        logger.info("检查数据库连接...")
        
        if not os.path.exists(self.production_db):
            raise FileNotFoundError(f"产线数据库不存在: {self.production_db}")
        
        if not os.path.exists(self.research_db):
            raise FileNotFoundError(f"研发数据库不存在: {self.research_db}")
        
        # 测试连接
        try:
            with sqlite3.connect(self.production_db) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_results")
                prod_count = cursor.fetchone()[0]
                logger.info(f"产线数据库连接成功，测试记录数: {prod_count}")
        except Exception as e:
            raise ConnectionError(f"产线数据库连接失败: {e}")
        
        try:
            with sqlite3.connect(self.research_db) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_results")
                research_count = cursor.fetchone()[0]
                logger.info(f"研发数据库连接成功，测试记录数: {research_count}")
        except Exception as e:
            raise ConnectionError(f"研发数据库连接失败: {e}")
    
    def _get_production_data(self) -> pd.DataFrame:
        """获取产线测试数据"""
        logger.info("获取产线测试数据...")
        
        query = """
        SELECT 
            tr.id,
            tr.batch_id,
            tr.channel_number,
            tr.battery_code,
            tr.test_start_time,
            tr.voltage,
            tr.rs_value,
            tr.rct_value,
            tr.is_pass,
            id.frequency,
            id.impedance_real,
            id.impedance_imag,
            id.test_sequence
        FROM test_results tr
        LEFT JOIN impedance_details id ON tr.id = id.test_result_id
        WHERE tr.test_start_time >= datetime('now', '-7 days')
        ORDER BY tr.test_start_time DESC, id.test_sequence
        """
        
        with sqlite3.connect(self.production_db) as conn:
            df = pd.read_sql_query(query, conn)
        
        logger.info(f"获取产线数据: {len(df)} 条记录")
        return df
    
    def _get_research_data(self) -> pd.DataFrame:
        """获取研发测试数据"""
        logger.info("获取研发测试数据...")
        
        query = """
        SELECT 
            tr.id,
            tr.project_id,
            tr.channel_number,
            tr.battery_code,
            tr.test_time,
            tr.voltage,
            tr.rs_value,
            tr.rct_value,
            tr.is_pass,
            id.frequency,
            id.impedance_real,
            id.impedance_imag,
            id.test_sequence
        FROM test_results tr
        LEFT JOIN impedance_details id ON tr.id = id.test_result_id
        WHERE tr.test_time >= datetime('now', '-7 days')
        ORDER BY tr.test_time DESC, id.test_sequence
        """
        
        with sqlite3.connect(self.research_db) as conn:
            df = pd.read_sql_query(query, conn)
        
        logger.info(f"获取研发数据: {len(df)} 条记录")
        return df
    
    def _match_test_data(self, production_data: pd.DataFrame, research_data: pd.DataFrame) -> Dict:
        """匹配测试数据"""
        logger.info("匹配测试数据...")
        
        # 基于电池码和通道号进行匹配
        matched_pairs = []
        
        # 获取产线数据的唯一测试
        prod_tests = production_data.groupby(['battery_code', 'channel_number']).first().reset_index()
        
        for _, prod_test in prod_tests.iterrows():
            battery_code = prod_test['battery_code']
            channel = prod_test['channel_number']
            
            # 查找对应的研发测试
            research_match = research_data[
                (research_data['battery_code'] == battery_code) & 
                (research_data['channel_number'] == channel)
            ]
            
            if not research_match.empty:
                # 获取详细数据
                prod_details = production_data[
                    (production_data['battery_code'] == battery_code) & 
                    (production_data['channel_number'] == channel)
                ].copy()
                
                research_details = research_match.copy()
                
                matched_pairs.append({
                    'battery_code': battery_code,
                    'channel_number': channel,
                    'production_summary': prod_test,
                    'research_summary': research_match.iloc[0],
                    'production_details': prod_details,
                    'research_details': research_details
                })
        
        logger.info(f"匹配到 {len(matched_pairs)} 对测试数据")
        return {'matched_pairs': matched_pairs}
    
    def _perform_comparison_analysis(self, matched_data: Dict) -> Dict:
        """执行对比分析"""
        logger.info("执行对比分析...")
        
        results = {
            'summary_comparison': [],
            'frequency_comparison': [],
            'statistical_analysis': {},
            'deviation_analysis': {}
        }
        
        for pair in matched_data['matched_pairs']:
            # 汇总数据对比
            summary_comp = self._compare_summary_data(pair)
            results['summary_comparison'].append(summary_comp)
            
            # 频率响应对比
            freq_comp = self._compare_frequency_response(pair)
            results['frequency_comparison'].append(freq_comp)
        
        # 统计分析
        results['statistical_analysis'] = self._perform_statistical_analysis(results['summary_comparison'])
        
        # 偏差分析
        results['deviation_analysis'] = self._analyze_deviations(results['frequency_comparison'])
        
        return results
    
    def _compare_summary_data(self, pair: Dict) -> Dict:
        """对比汇总数据"""
        prod_summary = pair['production_summary']
        research_summary = pair['research_summary']
        
        # 计算差异
        rs_diff = abs(prod_summary['rs_value'] - research_summary['rs_value'])
        rct_diff = abs(prod_summary['rct_value'] - research_summary['rct_value'])
        voltage_diff = abs(prod_summary['voltage'] - research_summary['voltage'])
        
        # 计算相对误差
        rs_rel_error = (rs_diff / prod_summary['rs_value']) * 100 if prod_summary['rs_value'] != 0 else 0
        rct_rel_error = (rct_diff / prod_summary['rct_value']) * 100 if prod_summary['rct_value'] != 0 else 0
        
        return {
            'battery_code': pair['battery_code'],
            'channel_number': pair['channel_number'],
            'production_rs': prod_summary['rs_value'],
            'research_rs': research_summary['rs_value'],
            'rs_difference': rs_diff,
            'rs_relative_error': rs_rel_error,
            'production_rct': prod_summary['rct_value'],
            'research_rct': research_summary['rct_value'],
            'rct_difference': rct_diff,
            'rct_relative_error': rct_rel_error,
            'production_voltage': prod_summary['voltage'],
            'research_voltage': research_summary['voltage'],
            'voltage_difference': voltage_diff,
            'pass_status_match': prod_summary['is_pass'] == research_summary['is_pass']
        }
    
    def _compare_frequency_response(self, pair: Dict) -> Dict:
        """对比频率响应"""
        prod_details = pair['production_details']
        research_details = pair['research_details']
        
        # 按频率匹配数据点
        frequency_comparisons = []
        
        for _, prod_point in prod_details.iterrows():
            freq = prod_point['frequency']
            
            # 查找对应的研发数据点
            research_point = research_details[
                abs(research_details['frequency'] - freq) < 0.01
            ]
            
            if not research_point.empty:
                research_point = research_point.iloc[0]
                
                # 计算阻抗差异
                real_diff = abs(prod_point['impedance_real'] - research_point['impedance_real'])
                imag_diff = abs(prod_point['impedance_imag'] - research_point['impedance_imag'])
                
                frequency_comparisons.append({
                    'frequency': freq,
                    'production_real': prod_point['impedance_real'],
                    'research_real': research_point['impedance_real'],
                    'real_difference': real_diff,
                    'production_imag': prod_point['impedance_imag'],
                    'research_imag': research_point['impedance_imag'],
                    'imag_difference': imag_diff
                })
        
        return {
            'battery_code': pair['battery_code'],
            'channel_number': pair['channel_number'],
            'frequency_points': frequency_comparisons
        }
    
    def _perform_statistical_analysis(self, summary_comparisons: List[Dict]) -> Dict:
        """执行统计分析"""
        if not summary_comparisons:
            return {}
        
        df = pd.DataFrame(summary_comparisons)
        
        return {
            'rs_statistics': {
                'mean_difference': df['rs_difference'].mean(),
                'std_difference': df['rs_difference'].std(),
                'max_difference': df['rs_difference'].max(),
                'mean_relative_error': df['rs_relative_error'].mean()
            },
            'rct_statistics': {
                'mean_difference': df['rct_difference'].mean(),
                'std_difference': df['rct_difference'].std(),
                'max_difference': df['rct_difference'].max(),
                'mean_relative_error': df['rct_relative_error'].mean()
            },
            'voltage_statistics': {
                'mean_difference': df['voltage_difference'].mean(),
                'std_difference': df['voltage_difference'].std(),
                'max_difference': df['voltage_difference'].max()
            },
            'pass_rate_consistency': df['pass_status_match'].mean() * 100
        }
    
    def _analyze_deviations(self, frequency_comparisons: List[Dict]) -> Dict:
        """分析偏差模式"""
        all_real_diffs = []
        all_imag_diffs = []
        frequency_patterns = {}
        
        for comp in frequency_comparisons:
            for point in comp['frequency_points']:
                freq = point['frequency']
                real_diff = point['real_difference']
                imag_diff = point['imag_difference']
                
                all_real_diffs.append(real_diff)
                all_imag_diffs.append(imag_diff)
                
                if freq not in frequency_patterns:
                    frequency_patterns[freq] = {'real_diffs': [], 'imag_diffs': []}
                
                frequency_patterns[freq]['real_diffs'].append(real_diff)
                frequency_patterns[freq]['imag_diffs'].append(imag_diff)
        
        # 分析频率相关的偏差模式
        freq_analysis = {}
        for freq, diffs in frequency_patterns.items():
            freq_analysis[freq] = {
                'mean_real_diff': np.mean(diffs['real_diffs']),
                'mean_imag_diff': np.mean(diffs['imag_diffs']),
                'std_real_diff': np.std(diffs['real_diffs']),
                'std_imag_diff': np.std(diffs['imag_diffs'])
            }
        
        return {
            'overall_real_deviation': {
                'mean': np.mean(all_real_diffs),
                'std': np.std(all_real_diffs),
                'max': np.max(all_real_diffs) if all_real_diffs else 0
            },
            'overall_imag_deviation': {
                'mean': np.mean(all_imag_diffs),
                'std': np.std(all_imag_diffs),
                'max': np.max(all_imag_diffs) if all_imag_diffs else 0
            },
            'frequency_patterns': freq_analysis
        }
    
    def _generate_comparison_report(self, results: Dict):
        """生成对比报告"""
        logger.info("生成对比报告...")
        
        report_path = os.path.join(self.report_dir, 'comparison_report.md')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 产线测试系统与研发测试系统数据对比报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 统计分析结果
            stats = results['statistical_analysis']
            if stats:
                f.write("## 统计分析结果\n\n")
                f.write("### Rs值对比\n")
                f.write(f"- 平均差异: {stats['rs_statistics']['mean_difference']:.6f} mΩ\n")
                f.write(f"- 标准差: {stats['rs_statistics']['std_difference']:.6f} mΩ\n")
                f.write(f"- 最大差异: {stats['rs_statistics']['max_difference']:.6f} mΩ\n")
                f.write(f"- 平均相对误差: {stats['rs_statistics']['mean_relative_error']:.2f}%\n\n")
                
                f.write("### Rct值对比\n")
                f.write(f"- 平均差异: {stats['rct_statistics']['mean_difference']:.6f} mΩ\n")
                f.write(f"- 标准差: {stats['rct_statistics']['std_difference']:.6f} mΩ\n")
                f.write(f"- 最大差异: {stats['rct_statistics']['max_difference']:.6f} mΩ\n")
                f.write(f"- 平均相对误差: {stats['rct_statistics']['mean_relative_error']:.2f}%\n\n")
                
                f.write("### 电压对比\n")
                f.write(f"- 平均差异: {stats['voltage_statistics']['mean_difference']:.3f} V\n")
                f.write(f"- 标准差: {stats['voltage_statistics']['std_difference']:.3f} V\n")
                f.write(f"- 最大差异: {stats['voltage_statistics']['max_difference']:.3f} V\n\n")
                
                f.write(f"### 测试结果一致性: {stats['pass_rate_consistency']:.1f}%\n\n")
            
            # 偏差分析结果
            deviation = results['deviation_analysis']
            if deviation:
                f.write("## 偏差分析结果\n\n")
                f.write("### 实部阻抗偏差\n")
                f.write(f"- 平均偏差: {deviation['overall_real_deviation']['mean']:.6f} mΩ\n")
                f.write(f"- 标准差: {deviation['overall_real_deviation']['std']:.6f} mΩ\n")
                f.write(f"- 最大偏差: {deviation['overall_real_deviation']['max']:.6f} mΩ\n\n")
                
                f.write("### 虚部阻抗偏差\n")
                f.write(f"- 平均偏差: {deviation['overall_imag_deviation']['mean']:.6f} mΩ\n")
                f.write(f"- 标准差: {deviation['overall_imag_deviation']['std']:.6f} mΩ\n")
                f.write(f"- 最大偏差: {deviation['overall_imag_deviation']['max']:.6f} mΩ\n\n")
        
        logger.info(f"对比报告已生成: {report_path}")
    
    def _generate_visualization_charts(self, results: Dict):
        """生成可视化图表"""
        logger.info("生成可视化图表...")
        
        # Rs值对比散点图
        self._plot_rs_comparison(results['summary_comparison'])
        
        # Rct值对比散点图
        self._plot_rct_comparison(results['summary_comparison'])
        
        # 频率响应对比图
        self._plot_frequency_response_comparison(results['frequency_comparison'])
    
    def _plot_rs_comparison(self, summary_comparisons: List[Dict]):
        """绘制Rs值对比散点图"""
        if not summary_comparisons:
            return
        
        df = pd.DataFrame(summary_comparisons)
        
        plt.figure(figsize=(10, 8))
        plt.scatter(df['production_rs'], df['research_rs'], alpha=0.6)
        
        # 添加对角线
        min_val = min(df['production_rs'].min(), df['research_rs'].min())
        max_val = max(df['production_rs'].max(), df['research_rs'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', label='理想一致线')
        
        plt.xlabel('产线系统 Rs值 (mΩ)')
        plt.ylabel('研发系统 Rs值 (mΩ)')
        plt.title('Rs值对比散点图')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.report_dir, 'rs_comparison.png'), dpi=300)
        plt.close()
    
    def _plot_rct_comparison(self, summary_comparisons: List[Dict]):
        """绘制Rct值对比散点图"""
        if not summary_comparisons:
            return
        
        df = pd.DataFrame(summary_comparisons)
        
        plt.figure(figsize=(10, 8))
        plt.scatter(df['production_rct'], df['research_rct'], alpha=0.6)
        
        # 添加对角线
        min_val = min(df['production_rct'].min(), df['research_rct'].min())
        max_val = max(df['production_rct'].max(), df['research_rct'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', label='理想一致线')
        
        plt.xlabel('产线系统 Rct值 (mΩ)')
        plt.ylabel('研发系统 Rct值 (mΩ)')
        plt.title('Rct值对比散点图')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.report_dir, 'rct_comparison.png'), dpi=300)
        plt.close()
    
    def _plot_frequency_response_comparison(self, frequency_comparisons: List[Dict]):
        """绘制频率响应对比图"""
        if not frequency_comparisons:
            return
        
        # 选择第一个样本进行展示
        sample = frequency_comparisons[0]
        points = sample['frequency_points']
        
        if not points:
            return
        
        df = pd.DataFrame(points)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 实部对比
        ax1.semilogx(df['frequency'], df['production_real'], 'b-o', label='产线系统', markersize=4)
        ax1.semilogx(df['frequency'], df['research_real'], 'r-s', label='研发系统', markersize=4)
        ax1.set_xlabel('频率 (Hz)')
        ax1.set_ylabel('实部阻抗 (mΩ)')
        ax1.set_title(f'频率响应对比 - {sample["battery_code"]} 通道{sample["channel_number"]}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 虚部对比
        ax2.semilogx(df['frequency'], df['production_imag'], 'b-o', label='产线系统', markersize=4)
        ax2.semilogx(df['frequency'], df['research_imag'], 'r-s', label='研发系统', markersize=4)
        ax2.set_xlabel('频率 (Hz)')
        ax2.set_ylabel('虚部阻抗 (mΩ)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.report_dir, 'frequency_response_comparison.png'), dpi=300)
        plt.close()


def main():
    """主函数"""
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    validator = DataComparisonValidator(project_root)
    validator.run_validation()


if __name__ == "__main__":
    main()
