#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试上层区域布局调整效果
验证批次信息、统计信息、测试控制按钮区域的高度增加效果

Author: Assistant
Date: 2025-01-27
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LayoutTestWindow(QMainWindow):
    """布局测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 上层区域布局调整测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 添加说明标签
        self._add_description(main_layout)
        
        # 创建对比布局
        self._create_comparison_layout(main_layout)
    
    def _add_description(self, layout):
        """添加说明文字"""
        desc_label = QLabel("JCY5001AS 上层区域布局调整对比")
        desc_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(desc_label)
        
        # 添加调整说明
        changes_label = QLabel("""
        <b>主要调整内容：</b><br>
        • 上层区域高度：203px → 280px（增加77px，约38%）<br>
        • 批次信息组件：增加边距和间距，字体12pt→12pt<br>
        • 统计信息组件：增加边距和间距，字体14pt→16pt<br>
        • 测试控制组件：增加按钮高度35px→45px，字体12pt→14pt<br>
        • 整体布局比例：标题5% + 统计30% + 通道65%
        """)
        changes_label.setFont(QFont("Microsoft YaHei", 10))
        changes_label.setStyleSheet("""
            QLabel {
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                line-height: 1.6;
            }
        """)
        layout.addWidget(changes_label)
    
    def _create_comparison_layout(self, layout):
        """创建对比布局"""
        comparison_widget = QWidget()
        comparison_layout = QHBoxLayout(comparison_widget)
        comparison_layout.setSpacing(20)
        
        # 调整前布局
        before_widget = self._create_layout_demo("调整前布局", {
            "header_height": 54,
            "upper_height": 203,
            "channels_height": 543,
            "font_size": "11pt",
            "button_height": 35
        })
        comparison_layout.addWidget(before_widget)
        
        # 调整后布局
        after_widget = self._create_layout_demo("调整后布局", {
            "header_height": 54,
            "upper_height": 280,
            "channels_height": 466,
            "font_size": "14pt",
            "button_height": 45
        })
        comparison_layout.addWidget(after_widget)
        
        layout.addWidget(comparison_widget)
    
    def _create_layout_demo(self, title, config):
        """创建布局演示"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout(demo_widget)
        demo_layout.setContentsMargins(10, 10, 10, 10)
        demo_layout.setSpacing(5)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                background-color: #3498db;
                padding: 8px;
                border-radius: 4px;
                font-size: {config['font_size']};
            }}
        """)
        demo_layout.addWidget(title_label)
        
        # 标题区域
        header_area = QLabel(f"标题区域 ({config['header_height']}px)")
        header_area.setFixedHeight(config['header_height'])
        header_area.setAlignment(Qt.AlignCenter)
        header_area.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                border: 1px solid #c0392b;
                font-weight: bold;
            }
        """)
        demo_layout.addWidget(header_area)
        
        # 上层区域
        upper_area = QLabel(f"上层区域 ({config['upper_height']}px)\n批次信息 + 统计信息 + 测试控制")
        upper_area.setFixedHeight(config['upper_height'])
        upper_area.setAlignment(Qt.AlignCenter)
        upper_area.setStyleSheet(f"""
            QLabel {{
                background-color: #27ae60;
                color: white;
                border: 1px solid #229954;
                font-weight: bold;
                font-size: {config['font_size']};
            }}
        """)
        demo_layout.addWidget(upper_area)
        
        # 通道区域
        channels_area = QLabel(f"通道显示区域 ({config['channels_height']}px)\n8个通道卡片")
        channels_area.setFixedHeight(config['channels_height'])
        channels_area.setAlignment(Qt.AlignCenter)
        channels_area.setStyleSheet("""
            QLabel {
                background-color: #f39c12;
                color: white;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)
        demo_layout.addWidget(channels_area)
        
        # 添加尺寸信息
        info_label = QLabel(f"""
        总高度: {config['header_height'] + config['upper_height'] + config['channels_height']}px
        上层占比: {config['upper_height'] / (config['header_height'] + config['upper_height'] + config['channels_height']) * 100:.1f}%
        字体大小: {config['font_size']}
        按钮高度: {config['button_height']}px
        """)
        info_label.setFont(QFont("Microsoft YaHei", 9))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        demo_layout.addWidget(info_label)
        
        return demo_widget


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        
        # 设置应用程序样式
        app.setStyle('Fusion')
        
        # 创建测试窗口
        window = LayoutTestWindow()
        window.show()
        
        logger.info("布局调整测试窗口已启动")
        logger.info("主要改进：")
        logger.info("• 上层区域高度从203px增加到280px")
        logger.info("• 批次信息、统计信息、测试控制组件都有更充足的显示空间")
        logger.info("• 字体大小和按钮尺寸相应增加")
        logger.info("• 整体视觉效果更加协调")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
