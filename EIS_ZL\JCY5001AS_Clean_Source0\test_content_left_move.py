#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 内容向左移动和绿色边框补全测试
测试右边内容向左移动到空白处，绿色线框补全
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QWidget, QHBoxLayout, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.components.statistics_widget import StatisticsWidget

class TestContentLeftMoveWindow(QMainWindow):
    """内容向左移动和绿色边框补全测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001AS 内容向左移动和绿色边框补全测试")
        self.setGeometry(100, 100, 1200, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加标题
        title_label = QLabel("JCY5001AS 内容向左移动和绿色边框补全测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("""
测试内容：
1. ✅ 将右边内容往左边空白处移动
   - Rs档位范围和Rct档位范围向左移动
   - 档位分布表格向左移动
   - 减少左右间距，内容更紧凑

2. ✅ 将绿色线框补全
   - 增加边框宽度到3px
   - 确保边框样式完整
   - 边框颜色保持绿色(#27ae60)

3. ✅ 布局优化
   - 添加右侧弹性空间，将内容推向左侧
   - 减少组件间距，让布局更紧凑
   - 优化内边距，充分利用空间
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 创建统计显示组件
        self.statistics_widget = StatisticsWidget()
        main_layout.addWidget(self.statistics_widget)
        
        # 添加测试数据
        self.load_test_data()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 更新统计数据
            self.statistics_widget.update_statistics(
                total_count=1530,
                qualified_count=870,
                unqualified_count=660,
                yield_rate=56.9
            )
            
            # 更新档位分布数据
            grade_data = {
                (0, 0): 114, (0, 1): 112, (0, 2): 90,   # Rs1行
                (1, 0): 96,  (1, 1): 106, (1, 2): 101,  # Rs2行  
                (2, 0): 88,  (2, 1): 82,  (2, 2): 81    # Rs3行
            }
            
            self.statistics_widget.update_grade_distribution(grade_data)
            
            print("✅ 测试数据加载完成")
            print("📊 统计数据: 总数=1530, 合格=870, 不合格=660, 良率=56.9%")
            print("📋 档位分布: 3x3表格数据已填充")
            print("🎯 重点观察:")
            print("   1. Rs档位范围和Rct档位范围是否向左移动到空白处")
            print("   2. 绿色边框是否完整补全")
            print("   3. 整体布局是否更紧凑")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("JCY5001AS内容左移测试")
    app.setApplicationVersion("1.0")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = TestContentLeftMoveWindow()
    window.show()
    
    print("🚀 JCY5001AS 内容向左移动和绿色边框补全测试启动")
    print("📝 测试重点:")
    print("   1. 右边内容是否向左移动到空白处")
    print("   2. 绿色线框是否完整补全")
    print("   3. 布局是否更紧凑合理")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
