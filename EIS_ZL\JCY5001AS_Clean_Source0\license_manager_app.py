#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 授权管理软件
独立的软件授权管理应用程序

Author: Jack
Date: 2025-06-08
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('license_manager.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class LicenseManagerApp:
    """授权管理应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        self.app = None
        self.main_dialog = None
        self.config_manager = None
        
    def setup_application(self):
        """设置应用程序基本属性"""
        try:
            self.app = QApplication(sys.argv)
            
            # 设置应用程序基本信息
            self.app.setApplicationName("JCY5001AS授权管理器")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("鲸测云")
            self.app.setOrganizationDomain("jingceyun.com")
            
            # 设置应用程序图标
            icon_path = os.path.join(os.path.dirname(__file__), "resources", "icons", "license.png")
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
            else:
                # 使用默认图标
                icon_path = os.path.join(os.path.dirname(__file__), "resources", "icons", "app_icon.ico")
                if os.path.exists(icon_path):
                    self.app.setWindowIcon(QIcon(icon_path))
            
            # 设置默认字体
            font = QFont("Microsoft YaHei", 9)
            self.app.setFont(font)
            
            # 设置应用程序样式
            self.app.setStyleSheet("""
                QApplication {
                    background-color: #f5f5f5;
                }
                QMessageBox {
                    background-color: white;
                }
            """)
            
            logger.info("应用程序基本属性设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置应用程序失败: {e}")
            return False
    
    def show_splash_screen(self):
        """显示启动画面"""
        try:
            # 创建启动画面
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.white)
            
            splash = QSplashScreen(splash_pixmap)
            splash.setStyleSheet("""
                QSplashScreen {
                    background-color: #34495e;
                    color: white;
                    font-size: 14pt;
                    font-weight: bold;
                }
            """)
            
            splash.show()
            splash.showMessage(
                "JCY5001AS 授权管理器\n\n正在启动...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.white
            )
            
            # 处理事件，确保启动画面显示
            self.app.processEvents()
            
            # 延迟2秒
            QTimer.singleShot(2000, splash.close)
            
            return splash
            
        except Exception as e:
            logger.error(f"显示启动画面失败: {e}")
            return None
    
    def initialize_config_manager(self):
        """初始化配置管理器"""
        try:
            from utils.config_manager import ConfigManager
            self.config_manager = ConfigManager()
            logger.info("配置管理器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化配置管理器失败: {e}")
            QMessageBox.critical(
                None,
                "初始化错误",
                f"配置管理器初始化失败：\n\n{e}\n\n程序将退出。"
            )
            return False
    
    def create_main_dialog(self):
        """创建主对话框"""
        try:
            from ui.dialogs.license_manager_dialog import LicenseManagerDialog
            
            self.main_dialog = LicenseManagerDialog(self.config_manager)
            
            # 连接信号
            self.main_dialog.license_status_changed.connect(self._on_license_status_changed)
            
            logger.info("主对话框创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建主对话框失败: {e}")
            QMessageBox.critical(
                None,
                "创建错误",
                f"主对话框创建失败：\n\n{e}\n\n程序将退出。"
            )
            return False
    
    def run(self):
        """运行应用程序"""
        try:
            logger.info("=" * 50)
            logger.info("JCY5001AS 授权管理器启动")
            logger.info("=" * 50)
            
            # 1. 设置应用程序
            if not self.setup_application():
                return 1
            
            # 2. 显示启动画面
            splash = self.show_splash_screen()
            
            # 3. 初始化配置管理器
            if not self.initialize_config_manager():
                return 1
            
            # 4. 创建主对话框
            if not self.create_main_dialog():
                return 1
            
            # 5. 关闭启动画面并显示主对话框
            if splash:
                splash.close()
            
            # 显示主对话框
            self.main_dialog.show()
            self.main_dialog.raise_()
            self.main_dialog.activateWindow()
            
            logger.info("授权管理器启动完成")
            
            # 6. 运行应用程序
            result = self.app.exec_()
            
            logger.info("授权管理器已退出")
            return result
            
        except Exception as e:
            logger.error(f"运行应用程序失败: {e}")
            QMessageBox.critical(
                None,
                "运行错误",
                f"应用程序运行失败：\n\n{e}"
            )
            return 1
    
    def _on_license_status_changed(self):
        """授权状态变更处理"""
        try:
            logger.info("授权状态已变更")
        except Exception as e:
            logger.error(f"处理授权状态变更失败: {e}")


def check_dependencies():
    """检查依赖库"""
    try:
        logger.info("检查依赖库...")
        
        # 检查必要的库
        required_modules = [
            'PyQt5',
            'cryptography'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                logger.info(f"✅ {module} - 已安装")
            except ImportError:
                missing_modules.append(module)
                logger.error(f"❌ {module} - 未安装")
        
        if missing_modules:
            error_msg = f"缺少必要的依赖库：\n\n{', '.join(missing_modules)}\n\n请先安装这些库后再运行程序。"
            QMessageBox.critical(None, "依赖检查失败", error_msg)
            return False
        
        logger.info("✅ 所有依赖库检查通过")
        return True
        
    except Exception as e:
        logger.error(f"检查依赖库失败: {e}")
        return False


def main():
    """主函数"""
    try:
        # 检查依赖库
        if not check_dependencies():
            sys.exit(1)
        
        # 创建并运行应用程序
        app = LicenseManagerApp()
        result = app.run()
        
        sys.exit(result)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        try:
            QMessageBox.critical(
                None,
                "程序错误",
                f"程序发生未处理的异常：\n\n{e}\n\n程序将退出。"
            )
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    main()
