#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储管理设置页面
提供存储使用情况显示、清理策略配置和手动清理功能

功能：
1. 显示当前存储使用情况
2. 配置清理策略
3. 手动执行清理操作
4. 存储使用趋势图表
5. 清理历史记录

作者：Jack
日期：2025-01-31
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QSpinBox, QCheckBox, QPushButton, QProgressBar,
    QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QFrame, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette

logger = logging.getLogger(__name__)


class StorageManagementWidget(QWidget):
    """存储管理设置页面"""
    
    # 信号定义
    settings_changed = pyqtSignal()
    cleanup_requested = pyqtSignal(str)  # 清理请求信号
    
    def __init__(self, config_manager, parent=None):
        """
        初始化存储管理设置页面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.config_manager = config_manager
        self._loading = False
        
        # 存储监控和清理管理器
        self.storage_monitor = None
        self.cleanup_manager = None

        # 🔧 修复：初始化存储管理器
        self._initialize_managers()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_storage_status)
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 加载设置
        self.load_settings()
        
        # 启动状态更新
        self.status_timer.start(60000)  # 每分钟更新一次
        
        logger.debug("存储管理设置页面初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：存储状态和配置
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.addWidget(self._create_storage_status_group())
        left_layout.addWidget(self._create_cleanup_config_group())
        left_layout.addWidget(self._create_manual_cleanup_group())
        splitter.addWidget(left_widget)
        
        # 右侧：清理历史和日志
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.addWidget(self._create_cleanup_history_group())
        right_layout.addWidget(self._create_cleanup_log_group())
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
    
    def _create_storage_status_group(self) -> QGroupBox:
        """创建存储状态组"""
        group = QGroupBox("存储使用情况")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 数据库状态
        layout.addWidget(QLabel("数据库文件:"), 0, 0)
        self.db_size_label = QLabel("0 MB")
        self.db_progress = QProgressBar()
        self.db_progress.setMaximum(100)
        layout.addWidget(self.db_size_label, 0, 1)
        layout.addWidget(self.db_progress, 0, 2)
        
        # 日志文件状态
        layout.addWidget(QLabel("日志文件:"), 1, 0)
        self.log_size_label = QLabel("0 MB")
        self.log_progress = QProgressBar()
        self.log_progress.setMaximum(100)
        layout.addWidget(self.log_size_label, 1, 1)
        layout.addWidget(self.log_progress, 1, 2)
        
        # 临时文件状态
        layout.addWidget(QLabel("临时文件:"), 2, 0)
        self.temp_size_label = QLabel("0 MB")
        layout.addWidget(self.temp_size_label, 2, 1)
        
        # 缓存文件状态
        layout.addWidget(QLabel("缓存文件:"), 3, 0)
        self.cache_size_label = QLabel("0 MB")
        layout.addWidget(self.cache_size_label, 3, 1)
        
        # 总使用量
        layout.addWidget(QLabel("总使用量:"), 4, 0)
        self.total_size_label = QLabel("0 MB")
        self.total_size_label.setFont(QFont("", 10, QFont.Bold))
        layout.addWidget(self.total_size_label, 4, 1)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新状态")
        self.refresh_button.clicked.connect(self._update_storage_status)
        layout.addWidget(self.refresh_button, 5, 0, 1, 3)
        
        return group
    
    def _create_cleanup_config_group(self) -> QGroupBox:
        """创建清理配置组"""
        group = QGroupBox("清理策略配置")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 数据库大小限制
        layout.addWidget(QLabel("数据库大小限制 (MB):"), 0, 0)
        self.db_limit_spin = QSpinBox()
        self.db_limit_spin.setRange(100, 10000)
        self.db_limit_spin.setValue(500)
        self.db_limit_spin.setSuffix(" MB")
        layout.addWidget(self.db_limit_spin, 0, 1)
        
        # 日志大小限制
        layout.addWidget(QLabel("日志大小限制 (MB):"), 1, 0)
        self.log_limit_spin = QSpinBox()
        self.log_limit_spin.setRange(10, 1000)
        self.log_limit_spin.setValue(100)
        self.log_limit_spin.setSuffix(" MB")
        layout.addWidget(self.log_limit_spin, 1, 1)
        
        # 警告阈值
        layout.addWidget(QLabel("警告阈值 (%):"), 2, 0)
        self.warning_threshold_spin = QSpinBox()
        self.warning_threshold_spin.setRange(50, 95)
        self.warning_threshold_spin.setValue(80)
        self.warning_threshold_spin.setSuffix(" %")
        layout.addWidget(self.warning_threshold_spin, 2, 1)
        
        # 自动清理开关
        self.auto_cleanup_check = QCheckBox("启用自动清理")
        layout.addWidget(self.auto_cleanup_check, 3, 0, 1, 2)
        
        # 清理频率
        layout.addWidget(QLabel("清理频率 (天):"), 4, 0)
        self.cleanup_frequency_spin = QSpinBox()
        self.cleanup_frequency_spin.setRange(1, 30)
        self.cleanup_frequency_spin.setValue(7)
        self.cleanup_frequency_spin.setSuffix(" 天")
        layout.addWidget(self.cleanup_frequency_spin, 4, 1)
        
        # 日志保留天数
        layout.addWidget(QLabel("日志保留天数:"), 5, 0)
        self.log_retention_spin = QSpinBox()
        self.log_retention_spin.setRange(7, 365)
        self.log_retention_spin.setValue(30)
        self.log_retention_spin.setSuffix(" 天")
        layout.addWidget(self.log_retention_spin, 5, 1)
        
        # 数据归档天数
        layout.addWidget(QLabel("数据归档天数:"), 6, 0)
        self.data_archive_spin = QSpinBox()
        self.data_archive_spin.setRange(30, 365)
        self.data_archive_spin.setValue(90)
        self.data_archive_spin.setSuffix(" 天")
        layout.addWidget(self.data_archive_spin, 6, 1)
        
        return group
    
    def _create_manual_cleanup_group(self) -> QGroupBox:
        """创建手动清理组"""
        group = QGroupBox("手动清理操作")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 清理按钮布局
        button_layout = QGridLayout()
        
        # 🔧 修复：清理临时文件
        self.cleanup_temp_button = QPushButton("清理临时文件")
        self.cleanup_temp_button.clicked.connect(self._on_cleanup_temp_files)
        button_layout.addWidget(self.cleanup_temp_button, 0, 0)

        # 🔧 修复：清理旧日志
        self.cleanup_logs_button = QPushButton("清理旧日志")
        self.cleanup_logs_button.clicked.connect(self._on_cleanup_old_logs)
        button_layout.addWidget(self.cleanup_logs_button, 0, 1)

        # 🔧 修复：归档旧数据
        self.archive_data_button = QPushButton("归档旧数据")
        self.archive_data_button.clicked.connect(self._on_archive_old_data)
        button_layout.addWidget(self.archive_data_button, 1, 0)

        # 🔧 修复：完整清理
        self.full_cleanup_button = QPushButton("完整清理")
        self.full_cleanup_button.clicked.connect(self._on_full_cleanup)
        self.full_cleanup_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(self.full_cleanup_button, 1, 1)
        
        layout.addLayout(button_layout)
        
        # 清理进度
        self.cleanup_progress = QProgressBar()
        self.cleanup_progress.setVisible(False)
        layout.addWidget(self.cleanup_progress)
        
        # 清理状态
        self.cleanup_status_label = QLabel("就绪")
        layout.addWidget(self.cleanup_status_label)
        
        return group
    
    def _create_cleanup_history_group(self) -> QGroupBox:
        """创建清理历史组"""
        group = QGroupBox("清理历史")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 历史表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(4)
        self.history_table.setHorizontalHeaderLabels(["时间", "类型", "删除文件", "释放空间"])
        
        # 设置表格属性
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        self.history_table.setAlternatingRowColors(True)
        self.history_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.history_table)
        
        return group
    
    def _create_cleanup_log_group(self) -> QGroupBox:
        """创建清理日志组"""
        group = QGroupBox("清理日志")
        group.setFont(QFont("", 10, QFont.Bold))
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 日志文本框
        self.cleanup_log_text = QTextEdit()
        self.cleanup_log_text.setMaximumHeight(150)
        self.cleanup_log_text.setReadOnly(True)
        self.cleanup_log_text.setFont(QFont("Consolas", 9))
        
        layout.addWidget(self.cleanup_log_text)
        
        # 清空日志按钮
        clear_log_button = QPushButton("清空日志")
        clear_log_button.clicked.connect(self.cleanup_log_text.clear)
        layout.addWidget(clear_log_button)
        
        return group
    
    def _connect_signals(self):
        """连接信号"""
        # 配置变更信号
        self.db_limit_spin.valueChanged.connect(self._on_setting_changed)
        self.log_limit_spin.valueChanged.connect(self._on_setting_changed)
        self.warning_threshold_spin.valueChanged.connect(self._on_setting_changed)
        self.auto_cleanup_check.toggled.connect(self._on_setting_changed)
        self.cleanup_frequency_spin.valueChanged.connect(self._on_setting_changed)
        self.log_retention_spin.valueChanged.connect(self._on_setting_changed)
        self.data_archive_spin.valueChanged.connect(self._on_setting_changed)

    def _initialize_managers(self):
        """初始化存储监控和清理管理器"""
        try:
            # 初始化存储监控管理器
            from utils.storage_monitor import get_storage_monitor, initialize_storage_monitor
            self.storage_monitor = get_storage_monitor()
            if not self.storage_monitor:
                self.storage_monitor = initialize_storage_monitor(self.config_manager)

            # 🔧 修复：获取数据库管理器实例
            from data.database_manager import get_database_manager
            database_manager = get_database_manager()

            # 初始化数据清理管理器
            from utils.data_cleanup_manager import get_data_cleanup_manager, initialize_data_cleanup_manager
            self.cleanup_manager = get_data_cleanup_manager()
            if not self.cleanup_manager:
                # 🔧 修复：传递数据库管理器给数据清理管理器
                self.cleanup_manager = initialize_data_cleanup_manager(self.config_manager, database_manager)

            # 连接清理管理器信号
            if self.cleanup_manager:
                self.cleanup_manager.cleanup_started.connect(self._on_cleanup_started)
                self.cleanup_manager.cleanup_progress.connect(self._on_cleanup_progress)
                self.cleanup_manager.cleanup_completed.connect(self._on_cleanup_completed)
                self.cleanup_manager.cleanup_error.connect(self._on_cleanup_error)

            # 连接存储监控信号
            if self.storage_monitor:
                self.storage_monitor.storage_status_updated.connect(self._on_storage_status_updated)

            logger.debug("存储管理器初始化完成")

        except Exception as e:
            logger.error(f"初始化存储管理器失败: {e}")

    def _on_setting_changed(self):
        """设置变更处理"""
        if not self._loading:
            self.settings_changed.emit()

    def _on_cleanup_temp_files(self):
        """清理临时文件按钮处理"""
        try:
            if not self.cleanup_manager:
                self.add_cleanup_log("❌ 清理管理器未初始化")
                return

            self.add_cleanup_log("🧹 开始清理临时文件...")
            self._set_cleanup_buttons_enabled(False)

            # 执行清理
            result = self.cleanup_manager.cleanup_temp_files()

        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
            self.add_cleanup_log(f"❌ 清理临时文件失败: {e}")
            self._set_cleanup_buttons_enabled(True)

    def _on_cleanup_old_logs(self):
        """清理旧日志按钮处理"""
        try:
            if not self.cleanup_manager:
                self.add_cleanup_log("❌ 清理管理器未初始化")
                return

            self.add_cleanup_log("🧹 开始清理旧日志文件...")
            self._set_cleanup_buttons_enabled(False)

            # 执行清理
            result = self.cleanup_manager.cleanup_old_logs()

        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            self.add_cleanup_log(f"❌ 清理旧日志失败: {e}")
            self._set_cleanup_buttons_enabled(True)

    def _on_archive_old_data(self):
        """归档旧数据按钮处理"""
        try:
            if not self.cleanup_manager:
                self.add_cleanup_log("❌ 清理管理器未初始化")
                return

            self.add_cleanup_log("📦 开始归档旧测试数据...")
            self._set_cleanup_buttons_enabled(False)

            # 执行归档
            result = self.cleanup_manager.archive_old_data()

        except Exception as e:
            logger.error(f"归档旧数据失败: {e}")
            self.add_cleanup_log(f"❌ 归档旧数据失败: {e}")
            self._set_cleanup_buttons_enabled(True)

    def _on_full_cleanup(self):
        """完整清理按钮处理"""
        try:
            if not self.cleanup_manager:
                self.add_cleanup_log("❌ 清理管理器未初始化")
                return

            # 确认对话框
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "确认清理",
                "完整清理将删除临时文件、旧日志和归档数据。\n确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.add_cleanup_log("🧹 开始执行完整清理...")
                self._set_cleanup_buttons_enabled(False)

                # 执行完整清理
                result = self.cleanup_manager.perform_full_cleanup()

        except Exception as e:
            logger.error(f"完整清理失败: {e}")
            self.add_cleanup_log(f"❌ 完整清理失败: {e}")
            self._set_cleanup_buttons_enabled(True)

    def _on_cleanup_started(self, message: str):
        """清理开始信号处理"""
        try:
            self.add_cleanup_log(f"🚀 {message}")
            self.update_cleanup_progress(message, 0)

        except Exception as e:
            logger.error(f"处理清理开始信号失败: {e}")

    def _on_cleanup_progress(self, message: str, progress: int):
        """清理进度信号处理"""
        try:
            self.add_cleanup_log(f"⏳ {message} ({progress}%)")
            self.update_cleanup_progress(message, progress)

        except Exception as e:
            logger.error(f"处理清理进度信号失败: {e}")

    def _on_cleanup_completed(self, result: dict):
        """清理完成信号处理"""
        try:
            cleanup_type = result.get('type', 'unknown')
            files_deleted = result.get('files_deleted', 0)
            space_freed = result.get('space_freed_mb', 0)
            errors = result.get('errors', [])

            # 添加清理日志
            if errors:
                self.add_cleanup_log(f"⚠️ 清理完成，但有错误: 删除 {files_deleted} 个文件，释放 {space_freed:.2f}MB")
                for error in errors[:3]:  # 只显示前3个错误
                    self.add_cleanup_log(f"   ❌ {error}")
            else:
                self.add_cleanup_log(f"✅ 清理完成: 删除 {files_deleted} 个文件，释放 {space_freed:.2f}MB")

            # 添加到历史记录
            self.add_cleanup_history(cleanup_type, files_deleted, space_freed)

            # 更新存储状态
            self._update_storage_status()

            # 重置进度
            self.update_cleanup_progress("清理完成", 100)
            self._set_cleanup_buttons_enabled(True)

        except Exception as e:
            logger.error(f"处理清理完成信号失败: {e}")

    def _on_cleanup_error(self, cleanup_type: str, error_message: str):
        """清理错误信号处理"""
        try:
            self.add_cleanup_log(f"❌ 清理失败 ({cleanup_type}): {error_message}")
            self.update_cleanup_progress("清理失败", 0)
            self._set_cleanup_buttons_enabled(True)

        except Exception as e:
            logger.error(f"处理清理错误信号失败: {e}")

    def _on_storage_status_updated(self, status: dict):
        """存储状态更新信号处理"""
        try:
            # 更新UI显示
            self._update_storage_status_from_data(status)

        except Exception as e:
            logger.error(f"处理存储状态更新信号失败: {e}")

    def _set_cleanup_buttons_enabled(self, enabled: bool):
        """设置清理按钮启用状态"""
        try:
            self.cleanup_temp_button.setEnabled(enabled)
            self.cleanup_logs_button.setEnabled(enabled)
            self.archive_data_button.setEnabled(enabled)
            self.full_cleanup_button.setEnabled(enabled)

        except Exception as e:
            logger.error(f"设置清理按钮状态失败: {e}")
    
    def load_settings(self):
        """加载设置"""
        try:
            self._loading = True
            
            # 加载存储管理配置
            self.db_limit_spin.setValue(
                self.config_manager.get('storage_management.database_size_limit_mb', 500)
            )
            self.log_limit_spin.setValue(
                self.config_manager.get('storage_management.log_size_limit_mb', 100)
            )
            self.warning_threshold_spin.setValue(
                self.config_manager.get('storage_management.warning_threshold_percent', 80)
            )
            self.auto_cleanup_check.setChecked(
                self.config_manager.get('storage_management.auto_cleanup_enabled', True)
            )
            self.cleanup_frequency_spin.setValue(
                self.config_manager.get('storage_management.cleanup_frequency_days', 7)
            )
            self.log_retention_spin.setValue(
                self.config_manager.get('storage_management.log_retention_days', 30)
            )
            self.data_archive_spin.setValue(
                self.config_manager.get('storage_management.data_archive_days', 90)
            )
            
            # 更新存储状态
            self._update_storage_status()
            
            logger.debug("存储管理设置加载完成")
            
        except Exception as e:
            logger.error(f"加载存储管理设置失败: {e}")
        finally:
            self._loading = False
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 保存存储管理配置
            self.config_manager.set('storage_management.database_size_limit_mb', self.db_limit_spin.value())
            self.config_manager.set('storage_management.log_size_limit_mb', self.log_limit_spin.value())
            self.config_manager.set('storage_management.warning_threshold_percent', self.warning_threshold_spin.value())
            self.config_manager.set('storage_management.auto_cleanup_enabled', self.auto_cleanup_check.isChecked())
            self.config_manager.set('storage_management.cleanup_frequency_days', self.cleanup_frequency_spin.value())
            self.config_manager.set('storage_management.log_retention_days', self.log_retention_spin.value())
            self.config_manager.set('storage_management.data_archive_days', self.data_archive_spin.value())
            
            logger.info("存储管理设置应用成功")
            
        except Exception as e:
            logger.error(f"应用存储管理设置失败: {e}")
            raise
    
    def _update_storage_status(self):
        """更新存储状态显示"""
        try:
            # 🔧 修复：从存储监控管理器获取实际状态
            if self.storage_monitor:
                status = self.storage_monitor.get_storage_status()
                self._update_storage_status_from_data(status)
            else:
                # 如果存储监控器未初始化，使用模拟数据
                self._update_storage_status_fallback()

        except Exception as e:
            logger.error(f"更新存储状态失败: {e}")
            # 出错时使用模拟数据
            self._update_storage_status_fallback()

    def _update_storage_status_from_data(self, status: dict):
        """从状态数据更新存储状态显示"""
        try:
            # 更新数据库状态
            db_status = status.get('database', {})
            db_size = db_status.get('size_mb', 0)
            db_limit = db_status.get('limit_mb', self.db_limit_spin.value())
            db_usage = db_status.get('usage_percent', 0)

            self.db_size_label.setText(f"{db_size:.1f} MB / {db_limit} MB")
            self.db_progress.setValue(int(db_usage))

            # 设置数据库进度条颜色
            if db_usage >= 90:
                self.db_progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
            elif db_usage >= 80:
                self.db_progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
            else:
                self.db_progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")

            # 更新日志状态
            log_status = status.get('logs', {})
            log_size = log_status.get('total_size_mb', 0)
            log_limit = log_status.get('limit_mb', self.log_limit_spin.value())
            log_usage = log_status.get('usage_percent', 0)

            self.log_size_label.setText(f"{log_size:.1f} MB / {log_limit} MB")
            self.log_progress.setValue(int(log_usage))

            # 设置日志进度条颜色
            if log_usage >= 90:
                self.log_progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
            elif log_usage >= 80:
                self.log_progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
            else:
                self.log_progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")

            # 更新其他文件状态
            temp_status = status.get('temp_files', {})
            cache_status = status.get('cache_files', {})
            temp_size = temp_status.get('total_size_mb', 0)
            cache_size = cache_status.get('total_size_mb', 0)
            total_size = status.get('total_usage', 0)

            self.temp_size_label.setText(f"{temp_size:.1f} MB")
            self.cache_size_label.setText(f"{cache_size:.1f} MB")
            self.total_size_label.setText(f"{total_size:.1f} MB")

        except Exception as e:
            logger.error(f"从状态数据更新存储状态显示失败: {e}")

    def _update_storage_status_fallback(self):
        """模拟数据更新存储状态显示（备用方案）"""
        try:
            # 模拟数据库状态
            db_size = 125.5  # MB
            db_limit = self.db_limit_spin.value()
            db_usage = (db_size / db_limit) * 100

            self.db_size_label.setText(f"{db_size:.1f} MB / {db_limit} MB")
            self.db_progress.setValue(int(db_usage))

            # 设置进度条颜色
            if db_usage >= 90:
                self.db_progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
            elif db_usage >= 80:
                self.db_progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
            else:
                self.db_progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")

            # 模拟日志状态
            log_size = 45.2  # MB
            log_limit = self.log_limit_spin.value()
            log_usage = (log_size / log_limit) * 100

            self.log_size_label.setText(f"{log_size:.1f} MB / {log_limit} MB")
            self.log_progress.setValue(int(log_usage))

            if log_usage >= 90:
                self.log_progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
            elif log_usage >= 80:
                self.log_progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
            else:
                self.log_progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")

            # 模拟其他文件状态
            temp_size = 12.3
            cache_size = 8.7
            total_size = db_size + log_size + temp_size + cache_size

            self.temp_size_label.setText(f"{temp_size:.1f} MB")
            self.cache_size_label.setText(f"{cache_size:.1f} MB")
            self.total_size_label.setText(f"{total_size:.1f} MB")

        except Exception as e:
            logger.error(f"模拟数据更新存储状态显示失败: {e}")
    
    def add_cleanup_log(self, message: str):
        """添加清理日志"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}"
            self.cleanup_log_text.append(log_message)
            
            # 自动滚动到底部
            scrollbar = self.cleanup_log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            logger.error(f"添加清理日志失败: {e}")
    
    def update_cleanup_progress(self, message: str, progress: int):
        """更新清理进度"""
        try:
            self.cleanup_status_label.setText(message)
            self.cleanup_progress.setValue(progress)
            
            if progress > 0:
                self.cleanup_progress.setVisible(True)
            else:
                self.cleanup_progress.setVisible(False)
                
        except Exception as e:
            logger.error(f"更新清理进度失败: {e}")
    
    def add_cleanup_history(self, cleanup_type: str, files_deleted: int, space_freed: float):
        """添加清理历史记录"""
        try:
            row = self.history_table.rowCount()
            self.history_table.insertRow(row)
            
            # 时间
            time_item = QTableWidgetItem(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            self.history_table.setItem(row, 0, time_item)
            
            # 类型
            type_item = QTableWidgetItem(cleanup_type)
            self.history_table.setItem(row, 1, type_item)
            
            # 删除文件数
            files_item = QTableWidgetItem(str(files_deleted))
            self.history_table.setItem(row, 2, files_item)
            
            # 释放空间
            space_item = QTableWidgetItem(f"{space_freed:.2f} MB")
            self.history_table.setItem(row, 3, space_item)
            
            # 滚动到最新记录
            self.history_table.scrollToBottom()
            
        except Exception as e:
            logger.error(f"添加清理历史记录失败: {e}")
