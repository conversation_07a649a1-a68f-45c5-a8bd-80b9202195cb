REM 创建专利附图的LibreOffice Basic宏
Sub CreatePatentFigure()
    Dim oDoc As Object
    Dim oDrawPage As Object
    Dim oShape As Object
    Dim oText As Object
    
    REM 创建新的Draw文档
    oDoc = StarDesktop.loadComponentFromURL("private:factory/sdraw", "_blank", 0, Array())
    oDrawPage = oDoc.getDrawPages().getByIndex(0)
    
    REM 设置页面大小为A4横向
    Dim oPageProps As Object
    oPageProps = oDrawPage
    oPageProps.Width = 29700  REM A4宽度(mm*100)
    oPageProps.Height = 21000 REM A4高度(mm*100)
    
    REM 创建标题
    oShape = oDoc.createInstance("com.sun.star.drawing.TextShape")
    oShape.Position = CreatePoint(14850, 1000)  REM 居中位置
    oShape.Size = CreateSize(15000, 1500)
    oShape.String = "图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图"
    oDrawPage.add(oShape)
    
    REM 1-被测电池
    oShape = CreateRectangleWithText(oDoc, 2000, 3000, 4000, 2500, "1-被测电池" + Chr(10) + "1.9V-5.5V", RGB(173, 216, 230))
    oDrawPage.add(oShape)
    
    REM 7-测试夹具
    oShape = CreateRectangleWithText(oDoc, 2000, 7000, 4000, 2500, "7-测试夹具" + Chr(10) + "四线制连接" + Chr(10) + "精确测量", RGB(245, 222, 179))
    oDrawPage.add(oShape)
    
    REM 2-DNB1101BB芯片
    oShape = CreateRectangleWithText(oDoc, 9000, 6000, 6000, 4000, "2-DNB1101BB" + Chr(10) + "EIS测试芯片" + Chr(10) + "0.0075Hz-7800Hz", RGB(144, 238, 144))
    oDrawPage.add(oShape)
    
    REM 4-外部电流源
    oShape = CreateRectangleWithText(oDoc, 2000, 12000, 4000, 3000, "4-外部电流源" + Chr(10) + "PMV28UNEA" + Chr(10) + "20Ω/10Ω/6.67Ω/5Ω", RGB(240, 128, 128))
    oDrawPage.add(oShape)
    
    REM 3-STM32控制器
    oShape = CreateRectangleWithText(oDoc, 17000, 6000, 5000, 4000, "3-STM32F103RCT6" + Chr(10) + "主控制器" + Chr(10) + "72MHz ARM", RGB(255, 255, 224))
    oDrawPage.add(oShape)
    
    REM 5-串口显示屏
    oShape = CreateRectangleWithText(oDoc, 17000, 12000, 5000, 3000, "5-串口显示屏" + Chr(10) + "实时显示" + Chr(10) + "测试结果", RGB(211, 211, 211))
    oDrawPage.add(oShape)
    
    REM 6-PC上位机
    oShape = CreateRectangleWithText(oDoc, 17000, 2000, 5000, 2500, "6-PC上位机" + Chr(10) + "Modbus RTU" + Chr(10) + "数据分析", RGB(176, 196, 222))
    oDrawPage.add(oShape)
    
    REM 保存文档
    Dim sURL As String
    sURL = "file:///D:/PROG/EIS_ZL/EIS专利申请/editable_figures/figure1_native.odg"
    oDoc.storeAsURL(sURL, Array())
    
    MsgBox "专利附图已创建完成！文件保存为: figure1_native.odg"
End Sub

Function CreateRectangleWithText(oDoc As Object, x As Long, y As Long, width As Long, height As Long, text As String, color As Long) As Object
    Dim oShape As Object
    
    REM 创建矩形
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.Position = CreatePoint(x, y)
    oShape.Size = CreateSize(width, height)
    oShape.FillColor = color
    oShape.LineColor = RGB(0, 0, 0)
    oShape.LineWidth = 50  REM 线宽
    
    REM 添加文本
    oShape.String = text
    
    REM 设置文本格式
    Dim oText As Object
    oText = oShape.getText()
    oText.setString(text)
    
    CreateRectangleWithText = oShape
End Function

Function CreatePoint(x As Long, y As Long) As Object
    Dim oPoint As Object
    oPoint = CreateUnoStruct("com.sun.star.awt.Point")
    oPoint.X = x
    oPoint.Y = y
    CreatePoint = oPoint
End Function

Function CreateSize(width As Long, height As Long) As Object
    Dim oSize As Object
    oSize = CreateUnoStruct("com.sun.star.awt.Size")
    oSize.Width = width
    oSize.Height = height
    CreateSize = oSize
End Function