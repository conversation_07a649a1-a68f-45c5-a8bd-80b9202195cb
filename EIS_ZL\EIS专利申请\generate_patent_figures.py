#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专利附图生成器
生成DNB1101BB发明专利申请所需的10张附图

Author: AI Assistant
Date: 2025-08-09
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle, Rectangle, Arrow
import numpy as np
import os
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PatentFigureGenerator:
    """专利附图生成器"""
    
    def __init__(self, output_dir="patent_figures"):
        """初始化生成器"""
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 设置图片参数
        self.fig_size = (12, 8)  # A4比例
        self.dpi = 300  # 高分辨率
        
    def generate_all_figures(self):
        """生成所有10张附图"""
        print("开始生成专利附图...")
        
        self.generate_figure1_system_architecture()
        self.generate_figure2_chip_circuit()
        self.generate_figure3_current_source()
        self.generate_figure4_eis_flowchart()
        self.generate_figure5_nyquist_plot()
        self.generate_figure6_parameter_extraction()
        self.generate_figure7_grouping_algorithm()
        self.generate_figure8_modbus_timing()
        self.generate_figure9_frequency_scan()
        self.generate_figure10_gain_control()
        
        print("所有附图生成完成！")
    
    def generate_figure1_system_architecture(self):
        """图1：系统整体架构图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 8)
        ax.axis('off')
        
        # 标题
        ax.text(5, 7.5, '图1：基于DNB1101BB芯片的电池一致性筛选设备系统整体架构图', 
                ha='center', va='center', fontsize=14, fontweight='bold')
        
        # 被测电池
        battery = FancyBboxPatch((0.5, 5), 1.5, 1, boxstyle="round,pad=0.1", 
                                facecolor='lightblue', edgecolor='black', linewidth=2)
        ax.add_patch(battery)
        ax.text(1.25, 5.5, '1-被测电池\n1.9V-5.5V', ha='center', va='center', fontsize=10)
        
        # DNB1101BB芯片
        chip = FancyBboxPatch((3, 4.5), 2, 2, boxstyle="round,pad=0.1", 
                             facecolor='lightgreen', edgecolor='black', linewidth=2)
        ax.add_patch(chip)
        ax.text(4, 5.5, '2-DNB1101BB\nEIS测试芯片\n0.0075Hz-7800Hz', 
                ha='center', va='center', fontsize=10)
        
        # STM32控制器
        mcu = FancyBboxPatch((6, 4.5), 2, 2, boxstyle="round,pad=0.1", 
                            facecolor='lightyellow', edgecolor='black', linewidth=2)
        ax.add_patch(mcu)
        ax.text(7, 5.5, '3-STM32F103RCT6\n主控制器\n72MHz ARM', 
                ha='center', va='center', fontsize=10)
        
        # 外部电流源
        current_source = FancyBboxPatch((3, 2), 2, 1.5, boxstyle="round,pad=0.1", 
                                       facecolor='lightcoral', edgecolor='black', linewidth=2)
        ax.add_patch(current_source)
        ax.text(4, 2.75, '4-外部电流源\nPMV28UNEA\n20Ω/10Ω/6.67Ω/5Ω', 
                ha='center', va='center', fontsize=10)
        
        # 串口显示屏
        display = FancyBboxPatch((6, 2), 2, 1.5, boxstyle="round,pad=0.1", 
                                facecolor='lightgray', edgecolor='black', linewidth=2)
        ax.add_patch(display)
        ax.text(7, 2.75, '5-串口显示屏\n实时显示\n测试结果', 
                ha='center', va='center', fontsize=10)
        
        # PC上位机
        pc = FancyBboxPatch((8.5, 4.5), 1.5, 2, boxstyle="round,pad=0.1", 
                           facecolor='lightsteelblue', edgecolor='black', linewidth=2)
        ax.add_patch(pc)
        ax.text(9.25, 5.5, '6-PC上位机\nModbus RTU\n数据分析', 
                ha='center', va='center', fontsize=10)
        
        # 测试夹具
        fixture = FancyBboxPatch((0.5, 2), 1.5, 1.5, boxstyle="round,pad=0.1", 
                                facecolor='wheat', edgecolor='black', linewidth=2)
        ax.add_patch(fixture)
        ax.text(1.25, 2.75, '7-测试夹具\n四线制连接\n精确测量', 
                ha='center', va='center', fontsize=10)
        
        # 连接线
        # 电池到夹具
        ax.arrow(1.25, 5, 0, -1.3, head_width=0.1, head_length=0.1, fc='black', ec='black')
        # 夹具到芯片
        ax.arrow(2, 2.75, 0.8, 1.5, head_width=0.1, head_length=0.1, fc='black', ec='black')
        # 芯片到MCU (SPI)
        ax.arrow(5, 5.5, 0.8, 0, head_width=0.1, head_length=0.1, fc='blue', ec='blue')
        ax.text(5.5, 5.8, 'SPI 1Mbps', ha='center', va='center', fontsize=8, color='blue')
        # MCU到PC (USB/UART)
        ax.arrow(8, 5.5, 0.4, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
        ax.text(8.25, 5.8, 'USB/UART', ha='center', va='center', fontsize=8, color='red')
        # MCU到显示屏
        ax.arrow(7, 4.5, 0, -0.8, head_width=0.1, head_length=0.1, fc='green', ec='green')
        ax.text(7.3, 3.8, 'UART\n115200bps', ha='center', va='center', fontsize=8, color='green')
        # 芯片到电流源
        ax.arrow(4, 4.5, 0, -0.8, head_width=0.1, head_length=0.1, fc='purple', ec='purple')
        ax.text(4.3, 3.8, 'VSW/VDR', ha='center', va='center', fontsize=8, color='purple')
        
        # 技术参数标注
        ax.text(5, 0.5, '系统技术参数：工作电压5V±5%，最大功耗15W，工作温度-10°C~50°C', 
                ha='center', va='center', fontsize=10, style='italic')
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图1_系统整体架构图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图1：系统整体架构图 - 生成完成")
    
    def generate_figure2_chip_circuit(self):
        """图2：DNB1101BB芯片引脚连接电路图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 10)
        ax.axis('off')
        
        # 标题
        ax.text(6, 9.5, '图2：DNB1101BB芯片引脚连接电路图及外围器件配置图', 
                ha='center', va='center', fontsize=14, fontweight='bold')
        
        # DNB1101BB芯片主体 (HTSSOP20封装)
        chip_body = Rectangle((4, 3), 4, 4, facecolor='lightblue', edgecolor='black', linewidth=2)
        ax.add_patch(chip_body)
        ax.text(6, 5, 'DNB1101BB\nHTSSOP20\n6.5mm×4.4mm', ha='center', va='center', fontsize=12, fontweight='bold')
        
        # 左侧引脚 (1-10)
        pin_names_left = ['NC', 'VDR', 'VSW', 'VCLg', 'VCLm', 'VSS', 'DIOBOTp', 'DIOBOTn', 'SPI_En', 'MISO']
        pin_numbers_left = list(range(1, 11))
        
        for i, (num, name) in enumerate(zip(pin_numbers_left, pin_names_left)):
            y_pos = 6.5 - i * 0.4
            # 引脚
            ax.plot([3.5, 4], [y_pos, y_pos], 'k-', linewidth=2)
            # 引脚号
            ax.text(3.3, y_pos, f'{num}', ha='right', va='center', fontsize=8)
            # 引脚名
            ax.text(3.2, y_pos, name, ha='right', va='center', fontsize=8)
        
        # 右侧引脚 (11-20)
        pin_names_right = ['NC', 'NC', 'NC', 'NC', 'VBAT', 'VCHm', 'VCHg', 'VHP', 'VBAT_FIL', 'NC']
        pin_numbers_right = list(range(11, 21))
        
        for i, (num, name) in enumerate(zip(pin_numbers_right, pin_names_right)):
            y_pos = 6.5 - i * 0.4
            # 引脚
            ax.plot([8, 8.5], [y_pos, y_pos], 'k-', linewidth=2)
            # 引脚号
            ax.text(8.7, y_pos, f'{num}', ha='left', va='center', fontsize=8)
            # 引脚名
            ax.text(8.8, y_pos, name, ha='left', va='center', fontsize=8)
        
        # 外围器件
        # C1: 10μF去耦电容
        c1 = Rectangle((1, 7), 0.8, 0.5, facecolor='yellow', edgecolor='black')
        ax.add_patch(c1)
        ax.text(1.4, 7.25, 'C1\n10μF', ha='center', va='center', fontsize=8)
        
        # C2: 100nF滤波电容
        c2 = Rectangle((1, 6), 0.8, 0.5, facecolor='yellow', edgecolor='black')
        ax.add_patch(c2)
        ax.text(1.4, 6.25, 'C2\n100nF', ha='center', va='center', fontsize=8)
        
        # R1: 4.7kΩ上拉电阻
        r1 = Rectangle((1, 5), 0.8, 0.5, facecolor='lightgreen', edgecolor='black')
        ax.add_patch(r1)
        ax.text(1.4, 5.25, 'R1\n4.7kΩ', ha='center', va='center', fontsize=8)
        
        # 被测电池连接
        battery_conn = Rectangle((10, 6), 1.5, 1, facecolor='lightcoral', edgecolor='black')
        ax.add_patch(battery_conn)
        ax.text(10.75, 6.5, '被测电池\n1.9V-5.5V', ha='center', va='center', fontsize=9)
        
        # STM32连接
        mcu_conn = Rectangle((1, 3), 1.5, 1, facecolor='lightsteelblue', edgecolor='black')
        ax.add_patch(mcu_conn)
        ax.text(1.75, 3.5, 'STM32\nSPI接口', ha='center', va='center', fontsize=9)
        
        # 连接线
        # VBAT到电池正极
        ax.plot([8.5, 10], [6.5, 6.5], 'r-', linewidth=2)
        ax.text(9.25, 6.7, 'VBAT(+)', ha='center', va='center', fontsize=8, color='red')
        
        # VSS到电池负极
        ax.plot([3.5, 10], [4.1, 6], 'k-', linewidth=2)
        ax.text(6.75, 5, 'VSS(-)', ha='center', va='center', fontsize=8)
        
        # SPI连接到STM32
        ax.plot([3.5, 2.5], [4.9, 4], 'b-', linewidth=1)  # DIOBOTp/MOSI
        ax.plot([3.5, 2.5], [4.5, 3.5], 'b-', linewidth=1)  # DIOBOTn/SCK
        ax.plot([3.5, 2.5], [3.7, 3], 'b-', linewidth=1)  # MISO
        ax.text(2.8, 3.8, 'SPI', ha='center', va='center', fontsize=8, color='blue')
        
        # 电源连接
        ax.plot([1.8, 3.5], [7.25, 6.5], 'r-', linewidth=1)  # C1到VBAT
        ax.plot([1.8, 3.5], [6.25, 4.1], 'k-', linewidth=1)  # C2到VSS
        
        # 技术规格标注
        specs_text = """关键技术规格：
• 电压测量精度：±2mV
• 温度测量精度：±2.5K  
• SPI通信速率：1Mbps
• 增益档位：1x/4x/16x
• 复阻抗格式：16位实部+16位虚部"""
        
        ax.text(6, 1.5, specs_text, ha='center', va='center', fontsize=9, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图2_DNB1101BB芯片连接电路图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图2：DNB1101BB芯片连接电路图 - 生成完成")
    
    def generate_figure3_current_source(self):
        """图3：外部电流源电路详细图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 8)
        ax.axis('off')
        
        # 标题
        ax.text(6, 7.5, '图3：外部电流源电路详细图（含MOSFET驱动电路）', 
                ha='center', va='center', fontsize=14, fontweight='bold')
        
        # DNB1101BB芯片（部分）
        chip_part = Rectangle((1, 4), 2, 2, facecolor='lightblue', edgecolor='black', linewidth=2)
        ax.add_patch(chip_part)
        ax.text(2, 5, 'DNB1101BB\n(部分)', ha='center', va='center', fontsize=10, fontweight='bold')
        
        # VSW引脚
        ax.plot([3, 4], [5.5, 5.5], 'k-', linewidth=2)
        ax.text(3.5, 5.7, 'VSW', ha='center', va='center', fontsize=9)
        
        # VDR引脚
        ax.plot([3, 4], [4.5, 4.5], 'k-', linewidth=2)
        ax.text(3.5, 4.3, 'VDR', ha='center', va='center', fontsize=9)
        
        # MOSFET (PMV28UNEA)
        # 栅极
        ax.plot([4, 5], [5.5, 5.5], 'k-', linewidth=2)
        # 漏极
        ax.plot([5.5, 5.5], [6.5, 6], 'k-', linewidth=2)
        ax.plot([5.5, 6.5], [6, 6], 'k-', linewidth=2)
        # 源极
        ax.plot([5.5, 5.5], [5, 4.5], 'k-', linewidth=2)
        ax.plot([5.5, 6.5], [4.5, 4.5], 'k-', linewidth=2)
        # MOSFET符号
        mosfet_body = Rectangle((5.2, 5.2), 0.6, 0.6, facecolor='white', edgecolor='black', linewidth=2)
        ax.add_patch(mosfet_body)
        ax.text(5.5, 5.5, 'Q1', ha='center', va='center', fontsize=8, fontweight='bold')
        ax.text(6.8, 5.5, 'PMV28UNEA\nVDS=30V, ID=8.8A\nRDS(on)=28mΩ', 
                ha='left', va='center', fontsize=8)
        
        # 功率电阻选择
        resistor_box = Rectangle((6.5, 2.5), 3, 1.5, facecolor='lightgreen', edgecolor='black', linewidth=2)
        ax.add_patch(resistor_box)
        ax.text(8, 3.25, '功率电阻选择\n20Ω/1W (100mA)\n10Ω/2W (200mA)\n6.67Ω/1.5W (300mA)\n5Ω/2W (400mA)', 
                ha='center', va='center', fontsize=9)
        
        # 连接到电池
        ax.plot([6.5, 6.5], [4.5, 2.5], 'k-', linewidth=2)
        ax.plot([8, 8], [4, 2.5], 'k-', linewidth=2)
        
        # 被测电池
        battery = Rectangle((10, 3), 1.5, 2, facecolor='lightcoral', edgecolor='black', linewidth=2)
        ax.add_patch(battery)
        ax.text(10.75, 4, '被测电池\nVBAT', ha='center', va='center', fontsize=10)
        
        # 电流路径
        ax.plot([9.5, 10], [3.25, 3.5], 'r-', linewidth=3)
        ax.text(9.75, 3.1, 'I激励', ha='center', va='center', fontsize=9, color='red')
        
        # 保护电路
        # 保护二极管D1
        d1 = Circle((4.5, 4.5), 0.2, facecolor='orange', edgecolor='black')
        ax.add_patch(d1)
        ax.text(4.5, 4.5, 'D1', ha='center', va='center', fontsize=8)
        ax.text(4.5, 4.1, 'PESD5V0V1BL', ha='center', va='center', fontsize=7)
        
        # 滤波电容C4
        c4 = Rectangle((4, 3), 0.5, 0.3, facecolor='yellow', edgecolor='black')
        ax.add_patch(c4)
        ax.text(4.25, 3.15, 'C4\n10nF', ha='center', va='center', fontsize=7)
        
        # 栅极限流电阻R4
        r4 = Rectangle((4, 5.3), 0.5, 0.3, facecolor='lightgreen', edgecolor='black')
        ax.add_patch(r4)
        ax.text(4.25, 5.45, 'R4\n10Ω', ha='center', va='center', fontsize=7)
        
        # VDR监控连接
        ax.plot([4, 5.5], [4.5, 6], 'b-', linewidth=1, linestyle='--')
        ax.text(4.75, 5.2, 'VDR监控', ha='center', va='center', fontsize=8, color='blue')
        
        # 工作原理说明
        principle_text = """工作原理：
1. VSW输出PWM信号驱动MOSFET栅极
2. PWM占空比可调(12.5%-100%)
3. 激励电流 I = (VBAT - VDS) / R_ext
4. VDR监控MOSFET开关状态
5. 频率范围：0.0075Hz-7800Hz"""
        
        ax.text(2, 1.5, principle_text, ha='left', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图3_外部电流源电路详细图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图3：外部电流源电路详细图 - 生成完成")

    def generate_figure4_eis_flowchart(self):
        """图4：EIS测试完整流程图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 12)
        ax.axis('off')

        # 标题
        ax.text(5, 11.5, '图4：电化学阻抗谱(EIS)测试完整流程图',
                ha='center', va='center', fontsize=14, fontweight='bold')

        # 流程框
        steps = [
            ("开始", 10.5),
            ("系统初始化\n芯片复位、ID分配", 9.5),
            ("参数配置\n频率序列、增益、电阻", 8.5),
            ("频率扫描开始\nf = 0.0075Hz-7800Hz", 7.5),
            ("激励信号生成\nPWM驱动电流源", 6.5),
            ("数据采集\n电压电流同步测量", 5.5),
            ("复阻抗计算\nZ = Re + j×Im", 4.5),
            ("质量控制检查\n削波检测、噪声监控", 3.5),
            ("频率扫描完成？", 2.5),
            ("参数提取\nRs、Rp、SEI、Warburg", 1.5),
            ("结束", 0.5)
        ]

        # 绘制流程框
        for i, (text, y) in enumerate(steps):
            if i == 8:  # 判断框
                # 菱形判断框
                diamond = patches.FancyBboxPatch((3.5, y-0.3), 3, 0.6,
                                               boxstyle="round,pad=0.1",
                                               facecolor='lightyellow',
                                               edgecolor='black', linewidth=2)
                ax.add_patch(diamond)
            else:
                # 矩形处理框
                rect = FancyBboxPatch((3.5, y-0.3), 3, 0.6,
                                     boxstyle="round,pad=0.1",
                                     facecolor='lightblue',
                                     edgecolor='black', linewidth=2)
                ax.add_patch(rect)

            ax.text(5, y, text, ha='center', va='center', fontsize=10)

        # 连接箭头
        for i in range(len(steps)-1):
            if i == 8:  # 从判断框出来
                continue
            y_start = steps[i][1] - 0.3
            y_end = steps[i+1][1] + 0.3
            ax.arrow(5, y_start, 0, y_end - y_start - 0.1,
                    head_width=0.1, head_length=0.1, fc='black', ec='black')

        # 判断框的特殊连接
        # "否" - 回到频率扫描
        ax.arrow(3.5, 2.5, -1.5, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
        ax.arrow(2, 2.5, 0, 4.5, head_width=0.1, head_length=0.1, fc='red', ec='red')
        ax.arrow(2, 7, 1.4, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
        ax.text(2.5, 2.7, '否', ha='center', va='center', fontsize=9, color='red')

        # "是" - 继续到参数提取
        ax.arrow(5, 2.2, 0, -0.4, head_width=0.1, head_length=0.1, fc='green', ec='green')
        ax.text(5.3, 2, '是', ha='center', va='center', fontsize=9, color='green')

        # 技术参数标注
        params_text = """关键技术参数：
• 频率点数：约100个
• 测量时间：3-5分钟
• 数据格式：64位复阻抗
• 精度：±0.1mΩ@1kHz"""

        ax.text(8.5, 6, params_text, ha='left', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen'))

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图4_EIS测试完整流程图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图4：EIS测试完整流程图 - 生成完成")

    def generate_figure5_nyquist_plot(self):
        """图5：典型电池奈奎斯特曲线示例图"""
        fig, ax = plt.subplots(figsize=self.fig_size)

        # 标题
        ax.set_title('图5：典型电池奈奎斯特曲线示例图（含参数标注）', fontsize=14, fontweight='bold', pad=20)

        # 生成模拟的奈奎斯特曲线数据
        frequencies = np.logspace(-2, 4, 100)  # 0.01Hz to 10kHz
        Rs = 12.5e-3  # 欧姆阻抗 12.5mΩ
        Rp = 18.3e-3  # 极化阻抗 18.3mΩ
        Cp = 0.1      # 双电层电容 0.1F

        # 计算复阻抗
        omega = 2 * np.pi * frequencies
        Z_real = Rs + Rp / (1 + (omega * Rp * Cp)**2)
        Z_imag = -Rp * omega * Rp * Cp / (1 + (omega * Rp * Cp)**2)

        # 转换为mΩ单位
        Z_real_mOhm = Z_real * 1000
        Z_imag_mOhm = Z_imag * 1000

        # 绘制奈奎斯特曲线
        ax.plot(Z_real_mOhm, -Z_imag_mOhm, 'b-', linewidth=2, label='EIS测试曲线')
        ax.scatter(Z_real_mOhm[::10], -Z_imag_mOhm[::10], c='red', s=30, zorder=5)

        # 标注关键频率点
        freq_indices = [0, 20, 40, 60, 80, 99]
        freq_labels = ['0.01Hz', '0.1Hz', '1Hz', '10Hz', '100Hz', '10kHz']

        for i, (idx, label) in enumerate(zip(freq_indices, freq_labels)):
            ax.annotate(label, (Z_real_mOhm[idx], -Z_imag_mOhm[idx]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)

        # 标注Rs和Rp参数
        ax.axvline(x=Rs*1000, color='green', linestyle='--', alpha=0.7)
        ax.text(Rs*1000, max(-Z_imag_mOhm)*0.8, f'Rs = {Rs*1000:.1f}mΩ',
                rotation=90, ha='right', va='center', fontsize=10, color='green')

        ax.axvline(x=(Rs+Rp)*1000, color='orange', linestyle='--', alpha=0.7)
        ax.text((Rs+Rp)*1000, max(-Z_imag_mOhm)*0.8, f'Rs+Rp = {(Rs+Rp)*1000:.1f}mΩ',
                rotation=90, ha='right', va='center', fontsize=10, color='orange')

        # 标注Rp
        ax.annotate(f'Rp = {Rp*1000:.1f}mΩ',
                   xy=((Rs+Rp/2)*1000, max(-Z_imag_mOhm)/2),
                   xytext=(20, 20), textcoords='offset points',
                   arrowprops=dict(arrowstyle='->', color='red'),
                   fontsize=10, color='red')

        # 设置坐标轴
        ax.set_xlabel('阻抗实部 Re(Z) [mΩ]', fontsize=12)
        ax.set_ylabel('阻抗虚部 -Im(Z) [mΩ]', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')

        # 添加图例和说明
        legend_text = """频率区域特征：
• 高频区(1kHz-10kHz): 欧姆阻抗Rs
• 中频区(0.1Hz-1kHz): 极化阻抗Rp
• 低频区(<0.1Hz): 瓦尔堡阻抗"""

        ax.text(0.02, 0.98, legend_text, transform=ax.transAxes, fontsize=9,
                verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图5_典型电池奈奎斯特曲线示例图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图5：典型电池奈奎斯特曲线示例图 - 生成完成")

    def generate_figure6_parameter_extraction(self):
        """图6：多维参数提取算法流程图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # 标题
        ax.text(6, 9.5, '图6：多维参数提取算法流程图',
                ha='center', va='center', fontsize=14, fontweight='bold')

        # 算法步骤
        steps = [
            ("输入复阻抗数据\nZ(ω) = Re(ω) + j×Im(ω)", 8.5, 'lightblue'),
            ("数据预处理\n异常值检测、平滑滤波", 7.5, 'lightgreen'),
            ("等效电路模型选择\nRs + (Rp||Cp) + Zw", 6.5, 'lightyellow'),
            ("参数初值估算\nRs、Rp、Cp初值", 5.5, 'lightcoral'),
            ("非线性最小二乘优化\nLevenberg-Marquardt", 4.5, 'lightsteelblue'),
            ("参数验证\n物理合理性检验", 3.5, 'lightgray'),
            ("输出多维参数\nRs、Rp、SEI、Warburg", 2.5, 'lightpink')
        ]

        # 绘制主流程
        for i, (text, y, color) in enumerate(steps):
            rect = FancyBboxPatch((4, y-0.4), 4, 0.8,
                                 boxstyle="round,pad=0.1",
                                 facecolor=color,
                                 edgecolor='black', linewidth=2)
            ax.add_patch(rect)
            ax.text(6, y, text, ha='center', va='center', fontsize=10)

            # 连接箭头
            if i < len(steps) - 1:
                ax.arrow(6, y-0.4, 0, -0.3, head_width=0.1, head_length=0.1, fc='black', ec='black')

        # 左侧详细说明
        left_details = [
            ("Kramers-Kronig\n因果性检验", 7.5),
            ("AIC信息准则\n模型选择", 6.5),
            ("高频实轴截距\nRs估算", 5.5),
            ("目标函数\nχ² = Σ|Z_exp - Z_model|²", 4.5),
            ("拟合优度\nR² > 0.99", 3.5)
        ]

        for text, y in left_details:
            rect = FancyBboxPatch((0.5, y-0.3), 2.5, 0.6,
                                 boxstyle="round,pad=0.1",
                                 facecolor='white',
                                 edgecolor='gray', linewidth=1)
            ax.add_patch(rect)
            ax.text(1.75, y, text, ha='center', va='center', fontsize=8)

            # 连接线
            ax.plot([3, 4], [y, y], 'gray', linestyle='--', alpha=0.7)

        # 右侧参数说明
        right_details = [
            ("3σ准则\n离群点剔除", 7.5),
            ("Savitzky-Golay\n滤波器", 7.1),
            ("Rs: 欧姆阻抗\nRp: 极化阻抗\nCp: 双电层电容\nσ: 瓦尔堡系数", 5.5),
            ("收敛准则\n相对误差<0.1%", 4.5),
            ("参数不确定度\n置信区间", 3.5),
            ("JSON/XML/CSV\n数据格式", 2.5)
        ]

        for text, y in right_details:
            rect = FancyBboxPatch((9, y-0.3), 2.5, 0.6,
                                 boxstyle="round,pad=0.1",
                                 facecolor='white',
                                 edgecolor='gray', linewidth=1)
            ax.add_patch(rect)
            ax.text(10.25, y, text, ha='center', va='center', fontsize=8)

            # 连接线
            ax.plot([8, 9], [y, y], 'gray', linestyle='--', alpha=0.7)

        # 算法性能指标
        performance_text = """算法性能指标：
• 拟合精度：R² > 0.99
• 计算时间：<1秒
• 参数精度：±2%
• 重现性：CV < 3%"""

        ax.text(6, 1, performance_text, ha='center', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcyan'))

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图6_多维参数提取算法流程图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图6：多维参数提取算法流程图 - 生成完成")

    def generate_figure7_grouping_algorithm(self):
        """图7：九档智能分组决策流程图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 14)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # 标题
        ax.text(7, 9.5, '图7：基于Rs和Rp的九档智能分组决策流程图',
                ha='center', va='center', fontsize=14, fontweight='bold')

        # 主流程
        main_steps = [
            ("批量电池数据采集\n样本数≥100只", 8.5),
            ("Rs参数统计分析\nMed(Rs), σ(Rs)", 7.5),
            ("Rp参数统计分析\nMed(Rp), σ(Rp)", 6.5),
            ("阈值计算\nMed ± 0.5×σ", 5.5),
            ("单电池档位判定\nRs档位 × Rp档位", 4.5),
            ("九档组合分组\n1-1 至 3-3", 3.5),
            ("分组结果输出\n统计分析", 2.5)
        ]

        # 绘制主流程
        for i, (text, y) in enumerate(main_steps):
            rect = FancyBboxPatch((5, y-0.4), 4, 0.8,
                                 boxstyle="round,pad=0.1",
                                 facecolor='lightblue',
                                 edgecolor='black', linewidth=2)
            ax.add_patch(rect)
            ax.text(7, y, text, ha='center', va='center', fontsize=10)

            if i < len(main_steps) - 1:
                ax.arrow(7, y-0.4, 0, -0.3, head_width=0.1, head_length=0.1, fc='black', ec='black')

        # 左侧Rs分析详情
        rs_box = FancyBboxPatch((0.5, 6), 3.5, 2.5,
                               boxstyle="round,pad=0.1",
                               facecolor='lightgreen',
                               edgecolor='black', linewidth=2)
        ax.add_patch(rs_box)
        rs_text = """Rs参数分析：
档位1: Rs ≤ Med(Rs) - 0.5×σ(Rs)
档位2: Med(Rs) - 0.5×σ(Rs) < Rs ≤ Med(Rs) + 0.5×σ(Rs)
档位3: Rs > Med(Rs) + 0.5×σ(Rs)

正态性检验: Shapiro-Wilk
异常值处理: 3σ准则"""
        ax.text(2.25, 7.25, rs_text, ha='center', va='center', fontsize=8)

        # 右侧Rp分析详情
        rp_box = FancyBboxPatch((10, 6), 3.5, 2.5,
                               boxstyle="round,pad=0.1",
                               facecolor='lightcoral',
                               edgecolor='black', linewidth=2)
        ax.add_patch(rp_box)
        rp_text = """Rp参数分析：
档位1: Rp ≤ Med(Rp) - 0.5×σ(Rp)
档位2: Med(Rp) - 0.5×σ(Rp) < Rp ≤ Med(Rp) + 0.5×σ(Rp)
档位3: Rp > Med(Rp) + 0.5×σ(Rp)

相关性分析: Rs与Rp相关系数
独立性验证: 互补信息确认"""
        ax.text(11.75, 7.25, rp_text, ha='center', va='center', fontsize=8)

        # 连接线
        ax.plot([4, 5], [7.5, 7.5], 'green', linestyle='--', linewidth=2)
        ax.plot([9, 10], [6.5, 6.5], 'red', linestyle='--', linewidth=2)

        # 九档分组矩阵
        matrix_box = FancyBboxPatch((1, 1), 12, 1.5,
                                   boxstyle="round,pad=0.1",
                                   facecolor='lightyellow',
                                   edgecolor='black', linewidth=2)
        ax.add_patch(matrix_box)

        # 绘制3x3矩阵
        matrix_text = """九档分组矩阵：
        Rp档位1    Rp档位2    Rp档位3
Rs档位1   1-1(最优)   1-2(良好)   1-3(关注)
Rs档位2   2-1(良好)   2-2(标准)   2-3(一般)
Rs档位3   3-1(关注)   3-2(一般)   3-3(需要关注)"""

        ax.text(7, 1.75, matrix_text, ha='center', va='center', fontsize=9, family='monospace')

        # 质量指标
        quality_text = """分组质量指标：
• 组内一致性: CV < 5%
• 分组均匀性: 方差最小化
• 置信度评估: 距离阈值相对位置"""

        ax.text(7, 0.3, quality_text, ha='center', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightsteelblue'))

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图7_九档智能分组决策流程图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图7：九档智能分组决策流程图 - 生成完成")

    def generate_figure8_modbus_timing(self):
        """图8：Modbus RTU通信协议时序图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 8)
        ax.axis('off')

        # 标题
        ax.text(6, 7.5, '图8：Modbus RTU通信协议时序图',
                ha='center', va='center', fontsize=14, fontweight='bold')

        # 时间轴
        ax.plot([1, 11], [6, 6], 'k-', linewidth=2)
        ax.text(6, 6.3, '时间轴', ha='center', va='center', fontsize=10)

        # 主机信号
        ax.text(0.5, 5.5, '主机', ha='center', va='center', fontsize=10, fontweight='bold')
        ax.plot([1, 11], [5.5, 5.5], 'k-', linewidth=1)

        # 从机信号
        ax.text(0.5, 4.5, '从机', ha='center', va='center', fontsize=10, fontweight='bold')
        ax.plot([1, 11], [4.5, 4.5], 'k-', linewidth=1)

        # 通信帧
        # 主机发送请求帧
        request_frame = Rectangle((2, 5.3), 2.5, 0.4, facecolor='lightblue', edgecolor='black')
        ax.add_patch(request_frame)
        ax.text(3.25, 5.5, '请求帧', ha='center', va='center', fontsize=9)

        # 从机接收处理
        process_frame = Rectangle((2.2, 4.3), 2.1, 0.4, facecolor='lightgreen', edgecolor='black')
        ax.add_patch(process_frame)
        ax.text(3.25, 4.5, '接收处理', ha='center', va='center', fontsize=9)

        # 从机发送响应帧
        response_frame = Rectangle((5.5, 4.3), 2.5, 0.4, facecolor='lightcoral', edgecolor='black')
        ax.add_patch(response_frame)
        ax.text(6.75, 4.5, '响应帧', ha='center', va='center', fontsize=9)

        # 主机接收响应
        receive_frame = Rectangle((5.7, 5.3), 2.1, 0.4, facecolor='lightyellow', edgecolor='black')
        ax.add_patch(receive_frame)
        ax.text(6.75, 5.5, '接收响应', ha='center', va='center', fontsize=9)

        # 时间标注
        time_points = [1, 2, 4.5, 5.5, 8, 9, 11]
        time_labels = ['0ms', '静默\n3.5字符', '发送\n完成', '处理\n开始', '响应\n开始', '传输\n完成', '100ms\n超时']

        for time, label in zip(time_points, time_labels):
            ax.plot([time, time], [5.9, 6.1], 'k-', linewidth=1)
            ax.text(time, 6.2, label, ha='center', va='bottom', fontsize=8)

        # 帧格式详细说明
        frame_format = FancyBboxPatch((1, 2.5), 10, 1.5,
                                     boxstyle="round,pad=0.1",
                                     facecolor='lightsteelblue',
                                     edgecolor='black', linewidth=2)
        ax.add_patch(frame_format)

        frame_text = """Modbus RTU帧格式：
请求帧: [设备地址][功能码][起始地址高][起始地址低][数据长度高][数据长度低][CRC高][CRC低]
响应帧: [设备地址][功能码][字节数][数据内容...][CRC高][CRC低]
异常帧: [设备地址][功能码+80H][异常码][CRC高][CRC低]

通信参数: 波特率115200bps, 8数据位, 1停止位, 无校验"""

        ax.text(6, 3.25, frame_text, ha='center', va='center', fontsize=9, family='monospace')

        # 关键寄存器地址
        register_text = """关键寄存器地址：
• 频率设置: 4200H-427FH (支持3位小数)
• 增益设置: 4280H-42BFH (1x/4x/16x)
• 阻抗数据: 3000H-30FFH (64位复阻抗)
• 设备状态: 3380H-33BFH"""

        ax.text(6, 1.5, register_text, ha='center', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcyan'))

        # 错误处理
        error_text = """异常处理：
01H-非法功能码  02H-非法地址  03H-非法数据值  04H-设备故障"""

        ax.text(6, 0.5, error_text, ha='center', va='center', fontsize=8,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='mistyrose'))

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图8_Modbus RTU通信协议时序图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图8：Modbus RTU通信协议时序图 - 生成完成")

    def generate_figure9_frequency_scan(self):
        """图9：频率扫描序列设置图"""
        fig, ax = plt.subplots(figsize=self.fig_size)

        # 标题
        ax.set_title('图9：频率扫描序列设置图', fontsize=14, fontweight='bold', pad=20)

        # 生成频率序列
        frequencies = np.logspace(-2, 4, 100)  # 0.01Hz to 10kHz

        # 绘制频率分布
        ax.semilogx(frequencies, np.ones_like(frequencies), 'bo', markersize=4, alpha=0.7)

        # 标注关键频率区域
        regions = [
            (0.0075, 0.1, '超低频区\n扩散过程', 'lightcoral'),
            (0.1, 1, '低频区\n瓦尔堡阻抗', 'lightyellow'),
            (1, 100, '中频区\n极化阻抗', 'lightgreen'),
            (100, 1000, '高频区\n欧姆阻抗', 'lightblue'),
            (1000, 10000, '超高频区\n电感效应', 'lightgray')
        ]

        for i, (f_min, f_max, label, color) in enumerate(regions):
            ax.axvspan(f_min, f_max, alpha=0.3, color=color)
            ax.text(np.sqrt(f_min * f_max), 1.5 + i*0.1, label,
                   ha='center', va='center', fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.2", facecolor=color))

        # 频率计算公式说明
        formula_text = """频率计算公式：f = k × M × 2^E
k = 7.4506mHz (基准常数)
M = 1-255 (尾数，8位)
E = 0-15 (指数，4位)
精度：支持3位小数"""

        ax.text(0.02, 0.98, formula_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightsteelblue'))

        # 扫描策略说明
        strategy_text = """扫描策略：
• 对数均匀分布
• 每十倍频程20个点
• 从高频到低频扫描
• 总计约100个频点
• 测试时间3-5分钟"""

        ax.text(0.98, 0.98, strategy_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcyan'))

        # 设置坐标轴
        ax.set_xlabel('频率 [Hz]', fontsize=12)
        ax.set_ylabel('频率点分布', fontsize=12)
        ax.set_xlim(0.005, 20000)
        ax.set_ylim(0.5, 2.5)
        ax.grid(True, alpha=0.3)

        # 标注关键频率点
        key_frequencies = [0.0075, 0.1, 1, 10, 100, 1000, 7800]
        for freq in key_frequencies:
            ax.axvline(x=freq, color='red', linestyle='--', alpha=0.7)
            ax.text(freq, 0.7, f'{freq}Hz', rotation=90, ha='right', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图9_频率扫描序列设置图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图9：频率扫描序列设置图 - 生成完成")

    def generate_figure10_gain_control(self):
        """图10：增益自适应调节流程图"""
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # 标题
        ax.text(6, 9.5, '图10：增益自适应调节流程图',
                ha='center', va='center', fontsize=14, fontweight='bold')

        # 主流程
        steps = [
            ("信号幅度检测", 8.5, 'lightblue'),
            ("信号幅度 < 20%？", 7.5, 'lightyellow'),
            ("信号幅度 > 80%？", 6.5, 'lightyellow'),
            ("增益调节", 5.5, 'lightgreen'),
            ("削波检测", 4.5, 'lightcoral'),
            ("噪声评估", 3.5, 'lightgray'),
            ("增益确认", 2.5, 'lightsteelblue')
        ]

        # 绘制主流程
        for i, (text, y, color) in enumerate(steps):
            if "？" in text:  # 判断框
                diamond = patches.FancyBboxPatch((4.5, y-0.3), 3, 0.6,
                                               boxstyle="round,pad=0.1",
                                               facecolor=color,
                                               edgecolor='black', linewidth=2)
                ax.add_patch(diamond)
            else:
                rect = FancyBboxPatch((4.5, y-0.3), 3, 0.6,
                                     boxstyle="round,pad=0.1",
                                     facecolor=color,
                                     edgecolor='black', linewidth=2)
                ax.add_patch(rect)

            ax.text(6, y, text, ha='center', va='center', fontsize=10)

        # 增益档位说明
        gain_levels = [
            ("增益1x", 8.5, "适用范围: <10mΩ\n输入范围: ±2.5V\n分辨率: 最高"),
            ("增益4x", 7, "适用范围: 10-40mΩ\n输入范围: ±625mV\n分辨率: 中等"),
            ("增益16x", 5.5, "适用范围: >40mΩ\n输入范围: ±156mV\n分辨率: 最低")
        ]

        for i, (title, y, desc) in enumerate(gain_levels):
            rect = FancyBboxPatch((0.5, y-0.4), 3, 0.8,
                                 boxstyle="round,pad=0.1",
                                 facecolor='lightcyan',
                                 edgecolor='black', linewidth=1)
            ax.add_patch(rect)
            ax.text(2, y, f"{title}\n{desc}", ha='center', va='center', fontsize=8)

        # 决策逻辑
        decision_logic = [
            ("上调增益", 7.5, "信号太小\n提高灵敏度"),
            ("下调增益", 6.5, "信号过大\n防止削波"),
            ("保持增益", 4, "信号适中\n20%-80%区间")
        ]

        for title, y, desc in decision_logic:
            rect = FancyBboxPatch((8.5, y-0.4), 3, 0.8,
                                 boxstyle="round,pad=0.1",
                                 facecolor='lightpink',
                                 edgecolor='black', linewidth=1)
            ax.add_patch(rect)
            ax.text(10, y, f"{title}\n{desc}", ha='center', va='center', fontsize=8)

        # 连接箭头和决策分支
        # 主流程箭头
        for i in range(len(steps)-1):
            if i not in [1, 2]:  # 跳过判断框
                y_start = steps[i][1] - 0.3
                y_end = steps[i+1][1] + 0.3
                ax.arrow(6, y_start, 0, y_end - y_start - 0.1,
                        head_width=0.1, head_length=0.1, fc='black', ec='black')

        # 判断框分支
        # 第一个判断框
        ax.arrow(4.5, 7.5, -0.8, 0, head_width=0.1, head_length=0.1, fc='green', ec='green')
        ax.text(4, 7.7, '是', ha='center', va='center', fontsize=8, color='green')
        ax.arrow(7.5, 7.5, 0.8, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
        ax.text(8, 7.7, '否', ha='center', va='center', fontsize=8, color='red')

        # 第二个判断框
        ax.arrow(4.5, 6.5, -0.8, 0, head_width=0.1, head_length=0.1, fc='green', ec='green')
        ax.text(4, 6.7, '是', ha='center', va='center', fontsize=8, color='green')
        ax.arrow(7.5, 6.5, 0.8, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
        ax.text(8, 6.7, '否', ha='center', va='center', fontsize=8, color='red')

        # 性能指标
        performance_text = """性能指标：
• 切换时间: <1ms
• 精度误差: <0.1%
• 温度补偿: -40°C~105°C
• 频响一致性: ±0.5°相位"""

        ax.text(6, 1.5, performance_text, ha='center', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图10_增益自适应调节流程图.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
        print("图10：增益自适应调节流程图 - 生成完成")

if __name__ == "__main__":
    generator = PatentFigureGenerator()
    generator.generate_all_figures()
