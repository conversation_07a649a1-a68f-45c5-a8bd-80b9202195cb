# -*- coding: utf-8 -*-
"""
电化学阻抗谱(EIS)分析器
按照标准EIS分析方法计算Rs和Rct值

Author: Jack
Date: 2025-01-27
"""

import numpy as np
import logging
from typing import Optional, Dict, List, Tuple
from datetime import datetime

# numpy安全配置：防止内存访问违例
np.seterr(all='raise')  # 将numpy错误转换为异常
np.seterrcall(None)     # 清除错误回调
import logging
from typing import Dict, List, Tuple, Optional
from scipy import interpolate
from scipy.optimize import curve_fit
import math

logger = logging.getLogger(__name__)


class EISAnalyzer:
    """电化学阻抗谱分析器"""

    def __init__(self):
        """初始化EIS分析器"""
        self.logger = logging.getLogger(__name__)
        self.logger.info("EIS分析器初始化完成")

    def calculate_rs_rct_standard(self, frequencies: List[float],
                                 real_parts: List[float],
                                 imag_parts: List[float]) -> <PERSON><PERSON>[float, float]:
        """
        按照标准EIS分析方法计算Rs和Rct值

        Args:
            frequencies: 频率列表 (Hz)
            real_parts: 实部阻抗列表 (mΩ)
            imag_parts: 虚部阻抗列表 (mΩ)

        Returns:
            (Rs值, Rct值) 单位：mΩ
        """
        try:
            if len(frequencies) == 0 or len(real_parts) == 0 or len(imag_parts) == 0:
                self.logger.warning("输入数据为空，使用默认值")
                return 5.0, 10.0

            # 确保数据长度一致
            min_length = min(len(frequencies), len(real_parts), len(imag_parts))
            frequencies = np.array(frequencies[:min_length])
            real_parts = np.array(real_parts[:min_length])
            imag_parts = np.array(imag_parts[:min_length])

            self.logger.info(f"开始标准EIS分析，数据点数: {min_length}")
            self.logger.debug(f"频率范围: {frequencies.min():.3f} - {frequencies.max():.3f} Hz")
            self.logger.debug(f"实部范围: {real_parts.min():.3f} - {real_parts.max():.3f} mΩ")
            self.logger.debug(f"虚部范围: {imag_parts.min():.3f} - {imag_parts.max():.3f} mΩ")

            # 计算Rs值
            rs_value = self._calculate_rs_standard(frequencies, real_parts, imag_parts)

            # 计算Rct值
            rct_value = self._calculate_rct_standard(frequencies, real_parts, imag_parts, rs_value)

            self.logger.info(f"标准EIS分析完成: Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ")
            return rs_value, rct_value

        except Exception as e:
            self.logger.error(f"标准EIS分析失败: {e}")
            return 5.0, 10.0

    def _calculate_rs_standard(self, frequencies: np.ndarray,
                              real_parts: np.ndarray,
                              imag_parts: np.ndarray) -> float:
        """
        按照优化方法计算Rs值（虚部过零点优先）

        优化说明：
        1. 优先使用虚部过零点方法（适用于低阻抗电池）
        2. 备用方法：最高频点方法
        3. 最后方法：高频段线性拟合延长线

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            imag_parts: 虚部数组

        Returns:
            Rs值 (mΩ)
        """
        try:
            self.logger.debug("开始计算Rs值（虚部过零点优先）...")

            # 方法1: 虚部过零点方法（优先使用，适用于低阻抗电池）
            rs_from_zero_crossing = self._find_imaginary_zero_crossing(real_parts, imag_parts)

            if rs_from_zero_crossing is not None and 0.01 <= rs_from_zero_crossing <= 50.0:
                self.logger.info(f"通过虚部过零点计算Rs: {rs_from_zero_crossing:.3f}mΩ")
                return rs_from_zero_crossing

            # 方法2: 最高频点方法（备用方法）
            rs_from_high_freq = self._approximate_rs_from_high_frequency(frequencies, real_parts, imag_parts)
            self.logger.info(f"通过最高频点计算Rs: {rs_from_high_freq:.3f}mΩ")

            # 验证最高频点方法的结果是否合理
            if 0.01 <= rs_from_high_freq <= 50.0:  # 放宽Rs值范围，支持低阻抗电池
                return rs_from_high_freq

            # 方法3: 高频段线性拟合延长线（最后方法）
            rs_from_extrapolation = self._extrapolate_high_frequency_rs(frequencies, real_parts, imag_parts)

            if rs_from_extrapolation is not None and 0.01 <= rs_from_extrapolation <= 50.0:
                self.logger.info(f"通过高频段拟合延长线计算Rs: {rs_from_extrapolation:.3f}mΩ")
                return rs_from_extrapolation

            # 如果虚部过零点方法有结果，即使超出范围也使用
            if rs_from_zero_crossing is not None:
                self.logger.info(f"使用虚部过零点结果（超出范围）: {rs_from_zero_crossing:.3f}mΩ")
                return max(0.01, rs_from_zero_crossing)

            # 最后使用最高频点结果
            self.logger.warning(f"使用最高频点结果: {rs_from_high_freq:.3f}mΩ")
            return max(0.01, rs_from_high_freq)

        except Exception as e:
            self.logger.error(f"计算Rs值失败: {e}")
            return 0.2  # 默认值改为0.2mΩ，适用于低阻抗电池

    def _find_imaginary_zero_crossing(self, real_parts: np.ndarray,
                                     imag_parts: np.ndarray) -> Optional[float]:
        """
        寻找虚部过零点对应的实部值（修复版本）

        修复说明：
        1. 优先寻找虚部符号变化的过零点（线性插值）
        2. 如果有多个过零点，选择最小的实部值（对应高频）
        3. 避免使用低频区域的"最小虚部值"作为过零点

        Args:
            real_parts: 实部数组
            imag_parts: 虚部数组

        Returns:
            Rs值或None
        """
        try:
            # 安全检查：确保数组不为空且长度一致
            if len(real_parts) == 0 or len(imag_parts) == 0:
                self.logger.warning("输入数组为空")
                return None

            if len(real_parts) != len(imag_parts):
                self.logger.warning("实部和虚部数组长度不一致")
                min_len = min(len(real_parts), len(imag_parts))
                real_parts = real_parts[:min_len]
                imag_parts = imag_parts[:min_len]

            if len(imag_parts) < 2:
                self.logger.warning("数据点太少，无法寻找过零点")
                return None

            # 检查数据有效性
            if not np.all(np.isfinite(real_parts)) or not np.all(np.isfinite(imag_parts)):
                self.logger.warning("数据包含无效值(NaN或Inf)")
                return None

            self.logger.debug(f"虚部数据范围: {imag_parts.min():.3f} 到 {imag_parts.max():.3f} mΩ")

            # 方法1: 寻找虚部符号变化的点（优先方法）
            imag_signs = np.sign(imag_parts)
            sign_diffs = np.diff(imag_signs)
            sign_changes = np.where(sign_diffs != 0)[0]

            # 找到所有过零点
            zero_crossings = []
            for i in sign_changes:
                if i + 1 < len(imag_parts):
                    # 线性插值找到精确的过零点
                    y1, y2 = imag_parts[i], imag_parts[i + 1]
                    x1, x2 = real_parts[i], real_parts[i + 1]

                    # 更严格的除零检查
                    if abs(y2 - y1) > 1e-10:  # 避免除零
                        # 线性插值计算过零点的实部值
                        x_zero = x1 - y1 * (x2 - x1) / (y2 - y1)

                        # 检查计算结果的有效性
                        if np.isfinite(x_zero) and 0.01 <= x_zero <= 1000.0:
                            zero_crossings.append(x_zero)
                            self.logger.debug(f"找到过零点: 实部={x_zero:.3f}mΩ")

            if zero_crossings:
                # 选择最小的实部值作为Rs（通常对应高频）
                rs_value = min(zero_crossings)
                self.logger.info(f"通过符号变化找到Rs: {rs_value:.3f}mΩ")
                return max(0.1, min(100.0, rs_value))

            # 方法2: 如果没有符号变化，寻找最接近零的虚部值（但要谨慎）
            abs_imag = np.abs(imag_parts)
            min_abs_idx = np.argmin(abs_imag)
            min_abs_value = abs_imag[min_abs_idx]

            self.logger.debug(f"最接近零的虚部值: {imag_parts[min_abs_idx]:.3f}mΩ (索引{min_abs_idx})")

            # 只有当虚部值非常接近零时才使用（更严格的条件）
            if min_abs_value < 0.1:  # 虚部绝对值小于0.1mΩ认为接近零
                rs_value = real_parts[min_abs_idx]
                self.logger.info(f"通过最小虚部值找到Rs: {rs_value:.3f}mΩ (虚部={imag_parts[min_abs_idx]:.3f}mΩ)")
                return max(0.1, min(100.0, rs_value))

            # 如果都没找到合适的过零点，返回None让其他方法处理
            self.logger.debug("未找到合适的虚部过零点")
            return None

        except Exception as e:
            self.logger.error(f"寻找虚部过零点失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def _extrapolate_high_frequency_rs(self, frequencies: np.ndarray,
                                      real_parts: np.ndarray,
                                      imag_parts: np.ndarray) -> Optional[float]:
        """
        通过高频段线性拟合延长线计算Rs

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            imag_parts: 虚部数组

        Returns:
            Rs值或None
        """
        try:
            # 选择高频段数据（频率最高的30%数据点）
            n_points = len(frequencies)
            high_freq_count = max(3, n_points // 3)  # 至少3个点

            # 按频率排序，选择最高频率的数据点
            freq_indices = np.argsort(frequencies)[-high_freq_count:]

            high_freq_real = real_parts[freq_indices]
            high_freq_imag = imag_parts[freq_indices]

            self.logger.debug(f"选择{high_freq_count}个高频点进行线性拟合")

            # 对高频段的奈奎斯特图进行线性拟合
            # 拟合 imag = k * real + b
            if len(high_freq_real) >= 2:
                # 使用最小二乘法拟合
                coeffs = np.polyfit(high_freq_real, high_freq_imag, 1)
                k, b = coeffs[0], coeffs[1]

                # 计算拟合质量
                fitted_imag = k * high_freq_real + b
                r_squared = 1 - np.sum((high_freq_imag - fitted_imag)**2) / np.sum((high_freq_imag - np.mean(high_freq_imag))**2)

                self.logger.debug(f"高频段线性拟合: k={k:.6f}, b={b:.3f}, R²={r_squared:.3f}")

                # 如果拟合质量足够好，计算与虚部=0轴的交点
                if r_squared > 0.5:  # 拟合质量阈值
                    # 求解 0 = k * rs + b，得到 rs = -b/k
                    if abs(k) > 1e-6:  # 避免除零
                        rs_value = -b / k
                        self.logger.debug(f"线性拟合延长线Rs值: {rs_value:.3f}mΩ")

                        # 验证Rs值的合理性
                        if 0.1 <= rs_value <= 100.0:
                            return rs_value
                        else:
                            self.logger.warning(f"拟合得到的Rs值不合理: {rs_value:.3f}mΩ")
                else:
                    self.logger.debug(f"高频段拟合质量不佳: R²={r_squared:.3f}")

            return None

        except Exception as e:
            self.logger.error(f"高频段线性拟合失败: {e}")
            return None

    def _approximate_rs_from_high_frequency(self, frequencies: np.ndarray,
                                           real_parts: np.ndarray,
                                           imag_parts: np.ndarray) -> float:
        """
        通过最高频点计算Rs（修复版本）

        修复说明：
        直接取最高频点的实部阻抗值作为Rs，这是标准的EIS分析方法

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            imag_parts: 虚部数组

        Returns:
            Rs值
        """
        try:
            # 找到最高频率点的索引
            max_freq_idx = np.argmax(frequencies)

            # 取最高频点的实部阻抗值作为Rs
            rs_value = float(real_parts[max_freq_idx])

            max_freq = frequencies[max_freq_idx]
            imag_at_max_freq = imag_parts[max_freq_idx]

            self.logger.debug(f"最高频点Rs计算: 频率={max_freq:.3f}Hz, Rs={rs_value:.3f}mΩ, 虚部={imag_at_max_freq:.3f}mΩ")

            # 确保Rs值在合理范围内
            if rs_value < 0.1:
                self.logger.warning(f"Rs值过小: {rs_value:.3f}mΩ，调整为0.1mΩ")
                return 0.1
            elif rs_value > 100.0:
                self.logger.warning(f"Rs值过大: {rs_value:.3f}mΩ，可能存在硬件问题")
                return min(100.0, rs_value)
            else:
                return rs_value

        except Exception as e:
            self.logger.error(f"最高频点Rs计算失败: {e}")
            return 5.0

    def _calculate_rct_standard(self, frequencies: np.ndarray,
                               real_parts: np.ndarray,
                               imag_parts: np.ndarray,
                               rs_value: float) -> float:
        """
        按照优化方法计算Rct值（低阻抗电池优化）

        方法：
        1. 使用最低频点的实部值作为总阻抗(Rs + Rct)
        2. Rct = 总阻抗 - Rs
        3. 确保Rct值为正数

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            imag_parts: 虚部数组
            rs_value: 已计算的Rs值

        Returns:
            Rct值 (mΩ)
        """
        try:
            self.logger.debug("开始计算Rct值（低阻抗电池优化）...")

            # 方法1: 低频点方法（优化版，适用于低阻抗电池）
            rct_from_low_freq = self._approximate_rct_from_low_frequency(frequencies, real_parts, rs_value)
            self.logger.info(f"通过低频点近似计算Rct: {rct_from_low_freq:.3f}mΩ")

            # 对于低阻抗电池，接受更小的Rct值
            if rct_from_low_freq >= 0.01:  # 放宽Rct值范围，支持低阻抗电池
                return rct_from_low_freq

            # 方法2: 虚部峰值分析（备用方法）
            rct_from_peak = self._find_rct_from_peak_analysis(frequencies, real_parts, imag_parts, rs_value)

            if rct_from_peak is not None and rct_from_peak >= 0.01:
                self.logger.info(f"通过峰值分析计算Rct: {rct_from_peak:.3f}mΩ")
                return rct_from_peak

            # 如果计算出负值，返回0
            if rct_from_low_freq < 0:
                self.logger.warning(f"计算得到负Rct值: {rct_from_low_freq:.3f}mΩ，设置为0")
                return 0.0

            # 返回低频点结果（即使很小）
            return max(0.0, rct_from_low_freq)

        except Exception as e:
            self.logger.error(f"计算Rct值失败: {e}")
            return 0.1  # 默认值改为0.1mΩ，适用于低阻抗电池

    def _find_rct_from_peak_analysis(self, frequencies: np.ndarray,
                                    real_parts: np.ndarray,
                                    imag_parts: np.ndarray,
                                    rs_value: float) -> Optional[float]:
        """
        通过虚部峰值分析计算Rct

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            imag_parts: 虚部数组
            rs_value: Rs值

        Returns:
            Rct值或None
        """
        try:
            # 找到虚部的最高点（绝对值最大）
            abs_imag = np.abs(imag_parts)
            peak_idx = np.argmax(abs_imag)

            self.logger.debug(f"虚部峰值位置: 索引{peak_idx}, 频率{frequencies[peak_idx]:.3f}Hz, "
                            f"虚部{imag_parts[peak_idx]:.3f}mΩ")

            # 从峰值向低频方向寻找拐点
            inflection_point = self._find_inflection_point_towards_low_freq(
                frequencies, real_parts, imag_parts, peak_idx
            )

            if inflection_point is not None:
                real_at_inflection = inflection_point
                rct_value = real_at_inflection - rs_value

                self.logger.debug(f"拐点实部值: {real_at_inflection:.3f}mΩ, "
                                f"计算得Rct: {rct_value:.3f}mΩ")

                # 确保Rct值合理
                if rct_value > 0.1:
                    return min(200.0, rct_value)
                else:
                    self.logger.warning(f"计算得到的Rct值过小: {rct_value:.3f}mΩ")

            return None

        except Exception as e:
            self.logger.error(f"峰值分析计算Rct失败: {e}")
            return None

    def _find_inflection_point_towards_low_freq(self, frequencies: np.ndarray,
                                               real_parts: np.ndarray,
                                               imag_parts: np.ndarray,
                                               peak_idx: int) -> Optional[float]:
        """
        从峰值向低频方向寻找拐点

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            imag_parts: 虚部数组
            peak_idx: 峰值索引

        Returns:
            拐点对应的实部值或None
        """
        try:
            # 按频率排序，确定低频方向
            freq_order = np.argsort(frequencies)
            peak_freq = frequencies[peak_idx]

            # 找到比峰值频率更低的频率点
            low_freq_indices = [i for i in freq_order if frequencies[i] < peak_freq]

            if len(low_freq_indices) < 2:
                self.logger.debug("低频数据点不足，无法寻找拐点")
                return None

            # 计算虚部的二阶导数来寻找拐点
            low_freq_real = real_parts[low_freq_indices]
            low_freq_imag = np.abs(imag_parts[low_freq_indices])  # 使用绝对值

            if len(low_freq_imag) >= 3:
                # 计算二阶导数
                second_derivative = np.gradient(np.gradient(low_freq_imag))

                # 寻找二阶导数的零点或极值点
                sign_changes = np.where(np.diff(np.sign(second_derivative)))[0]

                if len(sign_changes) > 0:
                    # 选择第一个拐点
                    inflection_idx = sign_changes[0]
                    if inflection_idx < len(low_freq_real):
                        inflection_real = float(low_freq_real[inflection_idx])
                        self.logger.debug(f"找到拐点: 实部={inflection_real:.3f}mΩ")
                        return inflection_real

            # 如果没有找到明显拐点，使用低频段的最大实部值
            if len(low_freq_real) > 0:
                max_real = float(np.max(low_freq_real))
                self.logger.debug(f"使用低频段最大实部值作为拐点: {max_real:.3f}mΩ")
                return max_real

            return None

        except Exception as e:
            self.logger.error(f"寻找拐点失败: {e}")
            return None

    def _approximate_rct_from_low_frequency(self, frequencies: np.ndarray,
                                          real_parts: np.ndarray,
                                          rs_value: float) -> float:
        """
        通过低频点近似计算Rct

        Args:
            frequencies: 频率数组
            real_parts: 实部数组
            rs_value: Rs值

        Returns:
            Rct值
        """
        try:
            # 找到最低频率点
            min_freq_idx = np.argmin(frequencies)

            # 选择低频段的几个点取平均
            n_points = len(frequencies)
            low_freq_count = min(3, n_points)  # 最多3个点

            # 按频率排序，选择最低的几个频率点
            freq_indices = np.argsort(frequencies)[:low_freq_count]
            low_freq_real = real_parts[freq_indices]

            # 对于低阻抗电池，直接使用最低频点的实部值作为总阻抗
            total_resistance = real_parts[min_freq_idx]

            # 计算Rct
            rct_value = total_resistance - rs_value

            self.logger.debug(f"低频点近似: 总阻抗={total_resistance:.3f}mΩ, "
                            f"Rs={rs_value:.3f}mΩ, Rct={rct_value:.3f}mΩ")

            # 确保Rct值在合理范围内，但允许小值
            if rct_value < 0:
                self.logger.warning(f"计算得到负Rct值: {rct_value:.3f}mΩ，设置为0")
                return 0.0
            return float(min(200.0, rct_value))

        except Exception as e:
            self.logger.error(f"低频点近似计算Rct失败: {e}")
            return 10.0

    def validate_eis_data(self, frequencies: List[float],
                         real_parts: List[float],
                         imag_parts: List[float]) -> Dict:
        """
        验证EIS数据质量

        Args:
            frequencies: 频率列表
            real_parts: 实部列表
            imag_parts: 虚部列表

        Returns:
            验证结果字典
        """
        try:
            validation_result = {
                'is_valid': True,
                'warnings': [],
                'data_quality': 'good',
                'frequency_range': 'adequate',
                'data_points': len(frequencies)
            }

            # 检查数据点数量
            if len(frequencies) < 3:
                validation_result['warnings'].append("数据点数量过少，可能影响分析精度")
                validation_result['data_quality'] = 'poor'

            # 检查频率范围
            if len(frequencies) > 0:
                freq_range = max(frequencies) / min(frequencies) if min(frequencies) > 0 else 1
                if freq_range < 10:
                    validation_result['warnings'].append("频率范围过窄，建议扩大频率范围")
                    validation_result['frequency_range'] = 'narrow'

            # 检查数据连续性
            unique_real_ratio = len(set(real_parts)) / len(real_parts) if len(real_parts) > 0 else 0
            unique_imag_ratio = len(set(imag_parts)) / len(imag_parts) if len(imag_parts) > 0 else 0

            if unique_real_ratio < 0.8:
                validation_result['warnings'].append("实部数据重复过多")
                validation_result['data_quality'] = 'poor'

            if unique_imag_ratio < 0.8:
                validation_result['warnings'].append("虚部数据重复过多")
                validation_result['data_quality'] = 'poor'

            # 如果数据重复严重，标记为无效
            if unique_real_ratio < 0.3 and unique_imag_ratio < 0.3:
                validation_result['is_valid'] = False
                validation_result['warnings'].append("数据重复过于严重，无法进行有效分析")

            # 检查数据合理性
            if any(r < 0 for r in real_parts):
                validation_result['warnings'].append("存在负实部值，数据可能异常")
                validation_result['is_valid'] = False

            self.logger.debug(f"EIS数据验证完成: {validation_result}")
            return validation_result

        except Exception as e:
            self.logger.error(f"EIS数据验证失败: {e}")
            return {
                'is_valid': False,
                'warnings': [f"验证过程异常: {e}"],
                'data_quality': 'unknown',
                'frequency_range': 'unknown',
                'data_points': 0
            }
