#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A GUI启动测试脚本
测试主窗口是否能正常启动和显示
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_startup():
    """测试GUI启动"""
    print('=' * 60)
    print('JCY5001A GUI启动测试')
    print('=' * 60)
    
    try:
        # 设置Qt属性
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("JCY5001A鲸测云8路EIS阻抗筛选仪")
        app.setApplicationVersion("V0.80.10")
        app.setOrganizationName("鲸测云")
        app.setOrganizationDomain("jingceyun.com")
        
        print('✅ QApplication创建成功')
        
        # 导入配置管理器
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print('✅ 配置管理器初始化成功')
        
        # 导入主窗口
        from ui.main_window import MainWindow
        print('✅ 主窗口类导入成功')
        
        # 创建主窗口
        main_window = MainWindow(config)
        print('✅ 主窗口创建成功')
        
        # 显示窗口
        main_window.show()
        print('✅ 主窗口显示成功')
        
        # 获取窗口信息
        window_title = main_window.windowTitle()
        window_size = main_window.size()
        print(f'窗口标题: {window_title}')
        print(f'窗口大小: {window_size.width()}x{window_size.height()}')
        
        # 设置自动关闭定时器（3秒后关闭）
        def close_app():
            print('✅ GUI测试完成，自动关闭窗口')
            main_window.close()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(close_app)
        timer.start(3000)  # 3秒后关闭
        
        print('⏰ 窗口将在3秒后自动关闭...')
        
        # 运行应用程序
        result = app.exec_()
        
        print('✅ GUI启动测试完成')
        return True
        
    except Exception as e:
        print(f'❌ GUI启动测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """测试GUI组件导入"""
    print('\n--- 测试GUI组件导入 ---')
    
    components = [
        ('主窗口', 'ui.main_window', 'MainWindow'),
        ('设备连接管理器', 'ui.device_connection_manager', 'DeviceConnectionManager'),
        ('菜单管理器', 'ui.menu_manager', 'MenuManager'),
        ('窗口管理器', 'ui.window_manager', 'WindowManager'),
    ]
    
    success_count = 0
    for name, module_name, class_name in components:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f'✅ {name}: 导入成功')
            success_count += 1
        except Exception as e:
            print(f'❌ {name}: 导入失败 - {e}')
    
    total_count = len(components)
    print(f'\nGUI组件测试: {success_count}/{total_count} 个组件导入成功')
    
    return success_count == total_count

def main():
    """主测试函数"""
    print('开始JCY5001A GUI测试...\n')
    
    # 测试GUI组件导入
    components_ok = test_gui_components()
    
    if not components_ok:
        print('⚠️ GUI组件导入测试失败，跳过启动测试')
        return False
    
    # 测试GUI启动
    startup_ok = test_gui_startup()
    
    print('\n' + '=' * 60)
    print('GUI测试结果汇总:')
    print('=' * 60)
    
    if components_ok and startup_ok:
        print('🎉 所有GUI测试通过！应用程序GUI正常，可以进行打包。')
        return True
    else:
        print('⚠️ GUI测试失败，建议检查相关问题后再进行打包。')
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
