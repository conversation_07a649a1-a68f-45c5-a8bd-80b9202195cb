# -*- coding: utf-8 -*-
"""
通道UI布局管理器
负责单通道显示组件的UI布局创建和样式管理

Author: Jack
Date: 2025-01-30
"""

import logging
from PyQt5.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QGroupBox,
    QProgressBar, QFrame, QLineEdit, QSizePolicy, QWidget
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

logger = logging.getLogger(__name__)


class ChannelUILayoutManager:
    """通道UI布局管理器"""
    
    def __init__(self, channel_number: int, parent_widget):
        """
        初始化UI布局管理器
        
        Args:
            channel_number: 通道号
            parent_widget: 父组件
        """
        self.channel_number = channel_number
        self.parent_widget = parent_widget
        self.ui_elements = {}  # 存储UI元素引用
        
    def create_main_layout(self):
        """创建主布局"""
        try:
            # 创建主布局
            main_layout = QVBoxLayout(self.parent_widget)
            main_layout.setContentsMargins(2, 2, 2, 2)
            main_layout.setSpacing(2)

            # 创建分组框
            group_box = QGroupBox()
            group_box.setObjectName("channelGroup")
            main_layout.addWidget(group_box)

            # 创建自定义标题布局
            self._create_custom_title(group_box)

            # 获取或创建内容布局
            content_layout = group_box.layout()
            if content_layout is None:
                content_layout = QVBoxLayout(group_box)
                content_layout.setContentsMargins(6, 8, 6, 6)
                content_layout.setSpacing(3)

            # 创建显示区域
            self._create_display_areas(content_layout)
            
            return main_layout
            
        except Exception as e:
            logger.error(f"通道{self.channel_number}创建主布局失败: {e}")
            return None
    
    def _create_display_areas(self, layout):
        """创建显示区域 - 三行等分布局"""
        # 创建各个区域，使用等分布局权重
        self._create_main_content_area(layout)
        self._create_progress_area(layout)
        self._create_result_area(layout)

    def _create_main_content_area(self, layout):
        """创建主内容区域 - 2列布局，等分高度"""
        # 创建主内容容器
        main_container = QFrame()
        main_container.setObjectName("mainContentContainer")
        main_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 改为可扩展

        main_layout = QHBoxLayout(main_container)
        main_layout.setSpacing(8)  # 适当减小间距
        main_layout.setContentsMargins(4, 4, 4, 4)  # 减小边距

        # 左列：基本信息（进一步压缩宽度）
        left_column = self._create_left_column()
        main_layout.addLayout(left_column, 15)  # 左列占1.5份权重（进一步减少）

        # 右列：阻抗值显示（扩展更多空间）
        right_column = self._create_right_column()
        main_layout.addLayout(right_column, 35)  # 右列占3.5份权重（进一步增加）

        layout.addWidget(main_container, 1)  # 添加权重1，实现等分

    def _create_left_column(self):
        """创建左列 - 基本信息显示，适应等分布局"""
        left_column = QVBoxLayout()
        left_column.setSpacing(2)  # 减小间距以适应等分布局

        # 测试计数和测试时间区域（第一行）
        self._create_count_time_area(left_column)

        # 电池码输入区域（第二行）
        self._create_battery_code_area(left_column)

        # 电压显示区域（第三行）
        self._create_voltage_area(left_column)

        # 频点显示区域（第四行）
        self._create_frequency_display(left_column)

        # 添加弹性空间，确保内容合理分布
        left_column.addStretch()

        return left_column

    def _create_count_time_area(self, layout):
        """创建测试计数和测试时间区域（压缩宽度版本）"""
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(4)  # 减少间距从8到4
        count_time_layout.setContentsMargins(0, 0, 0, 2)

        # 测试计数显示（压缩标签文字）
        count_label = QLabel("计数:")  # 简化标签文字
        count_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")  # 减小字体
        count_label.setMinimumWidth(30)  # 设置最小宽度
        count_label.setMaximumWidth(35)  # 设置最大宽度
        count_time_layout.addWidget(count_label)

        test_count_label = QLabel("0")
        test_count_label.setObjectName("countLabel")
        test_count_label.setStyleSheet("font-size: 10pt; color: #27ae60; font-weight: bold;")  # 减小字体
        test_count_label.setMinimumWidth(20)  # 设置最小宽度
        test_count_label.setMaximumWidth(30)  # 设置最大宽度
        count_time_layout.addWidget(test_count_label)
        self.ui_elements['test_count_label'] = test_count_label

        # 分隔符（压缩）
        separator = QLabel("|")
        separator.setMaximumWidth(8)  # 限制分隔符宽度
        count_time_layout.addWidget(separator)

        # 测试时间显示（压缩标签文字）
        time_label = QLabel("用时:")  # 简化标签文字
        time_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")  # 减小字体
        time_label.setMinimumWidth(30)  # 设置最小宽度
        time_label.setMaximumWidth(35)  # 设置最大宽度
        count_time_layout.addWidget(time_label)

        test_time_label = QLabel("00:00:00")
        test_time_label.setObjectName("timeLabel")
        test_time_label.setStyleSheet("font-size: 9pt; color: #2c3e50; font-weight: bold;")  # 减小字体
        test_time_label.setMinimumWidth(50)  # 设置最小宽度
        test_time_label.setMaximumWidth(60)  # 设置最大宽度
        count_time_layout.addWidget(test_time_label)
        self.ui_elements['test_time_label'] = test_time_label

        # 减少弹性空间，让组件更紧凑
        count_time_layout.addStretch(1)  # 减少弹性空间权重
        layout.addLayout(count_time_layout)

    def _create_battery_code_area(self, layout):
        """创建电池码输入区域（压缩宽度版本）"""
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(4)  # 减少间距从8到4

        # 电池码标签 - 压缩宽度
        battery_label = QLabel("电池:")  # 简化标签文字
        battery_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")  # 减小字体
        battery_label.setMinimumWidth(35)  # 减少最小宽度
        battery_label.setMaximumWidth(40)  # 设置最大宽度
        battery_layout.addWidget(battery_label)

        # 电池码输入框 - 🔧 适当加长，避免重叠Rs(mΩ)
        battery_code_edit = QLineEdit()
        battery_code_edit.setObjectName("batteryCodeEdit")
        battery_code_edit.setPlaceholderText("扫码或输入电池条码")  # 🔧 简化占位符，避免过长
        battery_code_edit.setMinimumWidth(180)  # 🔧 适当增加：120px → 180px
        battery_code_edit.setMaximumWidth(220)  # 🔧 适当增加：180px → 220px，避免重叠
        # 🔧 强制CSS设置，去掉测试背景色
        battery_code_edit.setStyleSheet("min-width: 180px !important; max-width: 220px !important;")  # 🔧 去掉黄色背景
        battery_layout.addWidget(battery_code_edit, 2)  # 🔧 适当权重：3 → 2

        # 减少弹性空间
        self.ui_elements['battery_code_edit'] = battery_code_edit

        layout.addLayout(battery_layout)

    def _create_voltage_area(self, layout):
        """创建电压显示区域（压缩宽度版本）"""
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(4)  # 减少间距从6到4

        # 电压标签 - 压缩宽度
        voltage_label = QLabel("电压:")  # 简化标签文字
        voltage_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")  # 减小字体
        voltage_label.setMinimumWidth(35)  # 减少最小宽度
        voltage_label.setMaximumWidth(40)  # 设置最大宽度
        voltage_layout.addWidget(voltage_label)

        # 电压数值标签 - 压缩但保持可读性
        voltage_value_label = QLabel("0.000")
        voltage_value_label.setObjectName("dataLabel")
        voltage_value_label.setStyleSheet("font-size: 9pt; color: #2c3e50; font-weight: bold;")  # 减小字体
        voltage_value_label.setMinimumWidth(45)  # 减少最小宽度
        voltage_value_label.setMaximumWidth(55)  # 设置最大宽度
        voltage_layout.addWidget(voltage_value_label)

        # 减少弹性空间，让组件更紧凑
        voltage_layout.addStretch(1)  # 减少弹性空间权重
        self.ui_elements['voltage_label'] = voltage_value_label

        layout.addLayout(voltage_layout)

    def _create_right_column(self):
        """创建右列 - 阻抗值显示（优化布局，移除不需要的显示项）"""
        right_column = QVBoxLayout()
        right_column.setSpacing(4)  # 增加间距，因为移除了部分显示项
        right_column.setContentsMargins(0, 0, 0, 0)

        # 创建Rs显示区域（紧凑单行格式）
        self._create_compact_impedance_area(right_column, "Rs(mΩ)", "rs")

        # 创建Rct显示区域（紧凑单行格式）
        self._create_compact_impedance_area(right_column, "Rct(mΩ)", "rct")

        # 移除离群率和容量预测显示区域，简化布局
        # 这样可以为其他区域提供更多空间

        # 添加更多弹性空间，确保Rs和Rct显示区域有足够空间
        right_column.addStretch()

        return right_column

    def _create_compact_impedance_area(self, layout, title: str, object_name: str):
        """
        创建阻抗值显示区域（扩展版本，利用从左列释放的空间）

        Args:
            layout: 父布局
            title: 显示标题
            object_name: 对象名称前缀
        """
        impedance_layout = QHBoxLayout()
        impedance_layout.setSpacing(12)  # 增加间距，利用更多空间

        # 🔧 添加左侧弹性空间，让标签向右移动
        impedance_layout.addStretch(5)  # 🔧 添加弹性空间，让标签向右移动

        # 标题标签 - 🔧 按示意图向右移动更多
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12pt; color: #7f8c8d; font-weight: bold;")  # 增加字体大小
        title_label.setMinimumWidth(80)  # 🔧 按示意图大幅增加宽度，让标签向右移动
        title_label.setMaximumWidth(85)  # 🔧 增加最大宽度
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 🔧 右对齐，向中心靠拢
        impedance_layout.addWidget(title_label)

        # 数值标签 - 🔧 调整宽度，确保"0.000"完整显示
        value_label = QLabel("0.000")
        value_label.setObjectName(f"{object_name}Value")
        value_label.setMinimumWidth(45)  # 🔧 增加到45px，确保"0.000"三位小数完整显示
        value_label.setMaximumWidth(45)  # 🔧 固定45px宽度，仍比原来的120px缩短很多
        value_label.setStyleSheet("font-size: 11pt; font-weight: bold; min-width: 45px !important; max-width: 45px !important; width: 45px !important;")  # 🔧 强制设置45px宽度
        impedance_layout.addWidget(value_label)

        # 利用释放的空间，给阻抗值显示更多空间
        impedance_layout.addStretch(3)  # 增加弹性空间权重，利用更多空间

        # 保存数值标签引用
        if object_name == "rs":
            self.ui_elements['rs_label'] = value_label
        elif object_name == "rct":
            self.ui_elements['rct_label'] = value_label

        layout.addLayout(impedance_layout)

    # 移除离群率显示区域方法 - 不再需要此功能
    # def _create_outlier_rate_area(self, layout):
    #     """创建离群率显示区域（已移除）"""
    #     pass

    # 移除容量预测显示区域方法 - 不再需要此功能
    # def _create_capacity_prediction_area(self, layout):
    #     """创建容量预测显示区域（已移除）"""
    #     pass

    def _create_progress_area(self, layout):
        """创建进度条区域 - 等分高度"""
        # 创建进度条容器，确保等分高度
        progress_container = QFrame()
        progress_container.setObjectName("progressContainer")
        progress_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(4, 4, 4, 4)
        progress_layout.setSpacing(2)

        # 添加弹性空间，使进度条居中
        progress_layout.addStretch()

        # 创建进度条
        progress_bar = QProgressBar()
        progress_bar.setObjectName("testProgressBar")
        progress_bar.setRange(0, 100)
        progress_bar.setValue(0)
        progress_bar.setTextVisible(True)
        progress_bar.setFormat("进度: %p%")
        progress_bar.setFixedHeight(24)  # 设置固定高度
        progress_layout.addWidget(progress_bar)
        self.ui_elements['progress_bar'] = progress_bar

        # 添加弹性空间，使进度条居中
        progress_layout.addStretch()

        layout.addWidget(progress_container, 1)  # 添加权重1，实现等分

    def _create_result_area(self, layout):
        """创建测试结果显示区域（档位+判定双区域格式）- 等分高度布局"""
        # 创建结果容器，确保等分高度
        result_container = QFrame()
        result_container.setObjectName("resultContainer")
        result_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 创建外层布局，用于垂直居中
        outer_layout = QVBoxLayout(result_container)
        outer_layout.setContentsMargins(4, 4, 4, 4)
        outer_layout.setSpacing(2)

        # 添加弹性空间，使结果区域居中
        outer_layout.addStretch()

        # 创建内层结果布局容器
        inner_container = QFrame()
        inner_container.setObjectName("innerResultContainer")
        result_layout = QHBoxLayout(inner_container)
        result_layout.setContentsMargins(0, 0, 0, 0)
        result_layout.setSpacing(6)  # 适当减小间距

        # 档位显示区域 (1/3宽度) - 调整尺寸适应等分布局
        grade_label = QLabel("--")
        grade_label.setObjectName("gradeDisplay")
        grade_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        grade_label.setMinimumHeight(40)  # 设置最小高度，确保文字显示
        grade_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        result_layout.addWidget(grade_label, 1)  # 占1份权重
        self.ui_elements['grade_label'] = grade_label

        # 判定结果显示区域 (2/3宽度) - 调整尺寸适应等分布局
        result_label = QLabel("待测试")
        result_label.setObjectName("resultWaiting")
        result_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        result_label.setMinimumHeight(40)  # 设置最小高度，确保文字显示
        result_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        result_layout.addWidget(result_label, 2)  # 占2份权重
        self.ui_elements['result_label'] = result_label

        outer_layout.addWidget(inner_container)

        # 添加弹性空间，使结果区域居中
        outer_layout.addStretch()

        layout.addWidget(result_container, 1)  # 添加权重1，实现等分

    def _create_frequency_display(self, layout):
        """创建频点显示区域"""
        # ===== 频点显示功能暂时屏蔽 =====
        # 说明：为简化当前界面，专注于核心测试功能，暂时隐藏频点显示
        # 如需启用，可调用 enable_frequency_display(True)
        pass

    def get_ui_element(self, name: str):
        """获取UI元素引用"""
        return self.ui_elements.get(name)
    
    def get_all_ui_elements(self):
        """获取所有UI元素引用"""
        return self.ui_elements.copy()

    def _create_custom_title(self, group_box):
        """创建自定义标题，包含通道号和电池状态指示器"""
        try:
            # 创建标题容器
            title_widget = QWidget()
            title_layout = QHBoxLayout(title_widget)
            title_layout.setContentsMargins(8, 4, 8, 4)
            title_layout.setSpacing(8)

            # 通道号标签
            channel_title = QLabel(f"通道 {self.channel_number}")
            channel_title.setObjectName("channelTitle")
            channel_title.setStyleSheet("font-weight: bold; font-size: 12pt;")
            title_layout.addWidget(channel_title)

            # 添加弹性空间
            title_layout.addStretch()

            # 电池状态指示器 - 增大尺寸优化
            battery_status_indicator = QLabel("○")
            battery_status_indicator.setObjectName("batteryStatusIndicator")
            battery_status_indicator.setStyleSheet("""
                QLabel {
                    font-size: 18pt;
                    font-weight: bold;
                    color: #95a5a6;
                    min-width: 28px;
                    min-height: 28px;
                    text-align: center;
                    border-radius: 14px;
                    padding: 2px;
                }
            """)
            battery_status_indicator.setToolTip("电池状态：未知")
            # 🔧 修复：确保电池状态指示器可见
            battery_status_indicator.setVisible(True)
            battery_status_indicator.show()
            title_layout.addWidget(battery_status_indicator)

            # 将标题容器设置为分组框的标题
            group_box.setTitle("")  # 清空默认标题

            # 获取分组框的布局并在顶部插入标题
            if group_box.layout() is None:
                # 如果还没有布局，先创建一个
                temp_layout = QVBoxLayout(group_box)
                temp_layout.setContentsMargins(6, 8, 6, 6)
                temp_layout.setSpacing(3)

            # 在分组框布局的顶部插入标题
            group_box.layout().insertWidget(0, title_widget)

            # 保存电池状态指示器的引用
            self.ui_elements['battery_status_indicator'] = battery_status_indicator
            self.ui_elements['channel_title'] = channel_title

            logger.debug(f"通道{self.channel_number}自定义标题创建完成")

        except Exception as e:
            logger.error(f"通道{self.channel_number}创建自定义标题失败: {e}")
            # 回退到默认标题
            group_box.setTitle(f"通道 {self.channel_number}")
