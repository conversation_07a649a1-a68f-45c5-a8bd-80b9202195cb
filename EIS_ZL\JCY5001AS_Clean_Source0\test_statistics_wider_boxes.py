#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001AS 统计框宽度增加测试脚本

测试目标：
- 将四个统计框的宽度增加到能完全显示数值的长度
- 总测试数框和合格数框：60px → 100px
- 不合格数框和良率框：50px → 90px
- 确保数值完全显示，不被截断

作者：weiwei
日期：2025-01-06
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QFrame)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 尝试导入真实组件
REAL_COMPONENT_AVAILABLE = False
try:
    from core.config_manager import ConfigManager
    from ui.components.statistics_widget import StatisticsWidget
    REAL_COMPONENT_AVAILABLE = True
    print("✅ 成功导入真实统计组件")
except ImportError as e:
    print(f"⚠️  无法导入真实组件: {e}")
    print("将使用模拟组件进行演示")


class StatisticsWiderBoxesTestWindow(QMainWindow):
    """统计框宽度增加测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 统计框宽度增加测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 初始化界面
        self._init_ui()
        
        # 如果有真实组件，设置测试数据
        if hasattr(self, 'statistics_widget'):
            self._set_test_statistics_data()
    
    def _init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加标题
        title_label = QLabel("📊 JCY5001AS 统计框宽度增加测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #ecf0f1; 
            padding: 15px; 
            border-radius: 10px; 
            border: 3px solid #3498db;
        """)
        main_layout.addWidget(title_label)
        
        # 添加说明信息
        info_label = QLabel("""
        🎯 测试目标：将四个统计框的宽度增加到能完全显示数值的长度
        
        📊 四个统计框的宽度变化：
        • 总测试数框 (valueLabel): 60px → 100px (+67%)
        • 合格数框 (passLabel): 60px → 100px (+67%)  
        • 不合格数框 (failLabel): 50px → 90px (+80%)
        • 良率框 (yieldLabel): 50px → 90px (+80%)
        
        🔧 技术实现：
        • 使用 !important 强制CSS覆盖
        • 同时设置 min-width 和 max-width 确保固定宽度
        • 增加内边距确保数字显示舒适
        • 保持字体大小和颜色不变
        
        ✅ 预期效果：
        • 四个统计框明显变宽，能完全显示数值
        • 数字内容显示更加舒适，不会被截断
        • 类似红色框标示的长度效果
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            color: #2c3e50; 
            background-color: #e8f4fd; 
            padding: 15px; 
            border-radius: 8px; 
            border: 2px solid #3498db;
            line-height: 1.4;
        """)
        main_layout.addWidget(info_label)
        
        # 创建测试区域
        self._create_test_area(main_layout)
        
        # 添加控制按钮
        self._create_control_buttons(main_layout)
        
        # 初始化定时器用于模拟数据更新
        self._init_timer()
    
    def _create_test_area(self, layout):
        """创建测试区域"""
        test_frame = QFrame()
        test_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        test_layout = QVBoxLayout(test_frame)
        
        if REAL_COMPONENT_AVAILABLE:
            # 使用真实统计组件
            try:
                config_manager = ConfigManager()
                
                # 创建统计组件
                self.statistics_widget = StatisticsWidget(config_manager)
                
                # 设置测试数据
                self._set_test_statistics_data()
                
                test_layout.addWidget(self.statistics_widget)
                
            except Exception as e:
                error_label = QLabel(f"❌ 创建真实组件失败: {str(e)}")
                error_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
                test_layout.addWidget(error_label)
                
                # 创建模拟组件
                self._create_mock_component(test_layout)
        else:
            # 创建模拟组件
            self._create_mock_component(test_layout)
        
        layout.addWidget(test_frame)
    
    def _create_mock_component(self, layout):
        """创建模拟统计组件"""
        mock_label = QLabel("📝 模拟统计组件（真实组件不可用时的演示）")
        mock_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        mock_label.setStyleSheet("color: #e67e22; padding: 10px;")
        layout.addWidget(mock_label)
        
        # 创建模拟的统计框
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            background-color: white; 
            border: 2px solid #27ae60; 
            border-radius: 5px; 
            padding: 10px;
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        # 模拟统计数据
        mock_data = [
            ("总测试数:", "1530", "#ecf0f1", "#2c3e50", "100px"),
            ("合格数:", "870", "#d5f4e6", "#27ae60", "100px"),
            ("不合格数:", "660", "#fadbd8", "#e74c3c", "90px"),
            ("良率:", "36.9%", "#ebf3fd", "#3498db", "90px")
        ]
        
        for label_text, value_text, bg_color, text_color, width in mock_data:
            label = QLabel(label_text)
            label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
            stats_layout.addWidget(label)
            
            value_label = QLabel(value_text)
            value_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setStyleSheet(f"""
                background-color: {bg_color};
                color: {text_color};
                border: 1px solid {text_color};
                border-radius: 4px;
                padding: 4px 12px;
                min-width: {width};
                max-width: {width};
            """)
            stats_layout.addWidget(value_label)
        
        layout.addWidget(stats_frame)
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 更新数据按钮
        update_btn = QPushButton("🔄 更新测试数据")
        update_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        update_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        update_btn.clicked.connect(self._update_test_data)
        button_layout.addWidget(update_btn)
        
        # 重置数据按钮
        reset_btn = QPushButton("🔄 重置数据")
        reset_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        reset_btn.clicked.connect(self._reset_test_data)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _init_timer(self):
        """初始化定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self._auto_update_data)
        self.timer.start(3000)  # 每3秒自动更新一次数据
    
    def _set_test_statistics_data(self):
        """设置测试统计数据"""
        if hasattr(self, 'statistics_widget'):
            try:
                # 设置测试数据
                self.statistics_widget.update_statistics({
                    'total_count': 1530,
                    'pass_count': 870,
                    'fail_count': 660,
                    'yield_rate': 56.9
                })
                print("✅ 测试数据设置成功")
            except Exception as e:
                print(f"❌ 设置测试数据失败: {e}")
    
    def _update_test_data(self):
        """更新测试数据"""
        if hasattr(self, 'statistics_widget'):
            import random
            total = random.randint(1000, 2000)
            passed = random.randint(500, total)
            failed = total - passed
            yield_rate = (passed / total) * 100 if total > 0 else 0
            
            self.statistics_widget.update_statistics({
                'total_count': total,
                'pass_count': passed,
                'fail_count': failed,
                'yield_rate': yield_rate
            })
            print(f"🔄 数据已更新: 总数={total}, 合格={passed}, 不合格={failed}, 良率={yield_rate:.1f}%")
    
    def _reset_test_data(self):
        """重置测试数据"""
        if hasattr(self, 'statistics_widget'):
            self.statistics_widget.update_statistics({
                'total_count': 0,
                'pass_count': 0,
                'fail_count': 0,
                'yield_rate': 0.0
            })
            print("🔄 数据已重置")
    
    def _auto_update_data(self):
        """自动更新数据"""
        self._update_test_data()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建测试窗口
    window = StatisticsWiderBoxesTestWindow()
    window.show()
    
    print("🚀 JCY5001AS 统计框宽度增加测试启动")
    print("📊 查看统计框宽度是否增加到能完全显示数值")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
