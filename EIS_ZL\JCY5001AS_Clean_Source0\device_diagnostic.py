#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备通信诊断工具
用于诊断JCY5001AS设备的通信问题并找到正确的设备地址

Author: Assistant
Date: 2025-07-04
"""

import serial
import time
import struct
from typing import List, Tuple, Optional

class DeviceDiagnostic:
    """设备通信诊断器"""
    
    def __init__(self, port: str = "COM13", baudrate: int = 115200):
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        
    def connect(self) -> bool:
        """连接串口"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            print(f"✅ 串口连接成功: {self.port}")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("✅ 串口已断开")
    
    def calculate_crc16(self, data: bytes) -> int:
        """计算Modbus CRC16校验码"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc
    
    def build_modbus_command(self, device_addr: int, function_code: int, 
                           start_addr: int, count: int) -> bytes:
        """构建Modbus RTU命令"""
        # 构建数据部分
        data = struct.pack('>BBHH', device_addr, function_code, start_addr, count)
        # 计算CRC
        crc = self.calculate_crc16(data)
        # 添加CRC（小端序）
        command = data + struct.pack('<H', crc)
        return command
    
    def send_command(self, command: bytes, timeout: float = 1.0) -> Optional[bytes]:
        """发送命令并接收响应"""
        if not self.ser or not self.ser.is_open:
            return None
        
        try:
            # 清空缓冲区
            self.ser.reset_input_buffer()
            self.ser.reset_output_buffer()
            
            # 发送命令
            self.ser.write(command)
            print(f"📤 发送: {' '.join(f'{b:02X}' for b in command)}")
            
            # 等待响应
            time.sleep(0.1)
            response = self.ser.read(100)  # 读取最多100字节
            
            if response:
                print(f"📥 接收: {' '.join(f'{b:02X}' for b in response)}")
                return response
            else:
                print("⚠️ 无响应")
                return None
                
        except Exception as e:
            print(f"❌ 通信错误: {e}")
            return None
    
    def test_device_address(self, device_addr: int) -> bool:
        """测试指定设备地址是否响应"""
        print(f"\n🔍 测试设备地址: {device_addr}")
        
        # 测试读取保持寄存器（功能码03）
        command = self.build_modbus_command(device_addr, 0x03, 0x0000, 0x0001)
        response = self.send_command(command)
        
        if response and len(response) >= 3:
            if response[0] == device_addr and response[1] == 0x03:
                print(f"✅ 设备地址 {device_addr} 响应正常")
                return True
            elif response[1] & 0x80:  # 错误响应
                print(f"⚠️ 设备地址 {device_addr} 返回错误码: {response[2]:02X}")
                return False
        
        print(f"❌ 设备地址 {device_addr} 无响应")
        return False
    
    def scan_device_addresses(self, start: int = 1, end: int = 10) -> List[int]:
        """扫描设备地址范围"""
        print(f"\n🔍 扫描设备地址范围: {start}-{end}")
        valid_addresses = []
        
        for addr in range(start, end + 1):
            if self.test_device_address(addr):
                valid_addresses.append(addr)
            time.sleep(0.2)  # 避免过快发送
        
        return valid_addresses
    
    def test_basic_commands(self, device_addr: int):
        """测试基本命令"""
        print(f"\n🧪 测试设备地址 {device_addr} 的基本命令:")

        # 测试不同的功能码和寄存器地址
        test_cases = [
            ("读取保持寄存器 0x0000", 0x03, 0x0000, 1),
            ("读取保持寄存器 0x0001", 0x03, 0x0001, 1),
            ("读取保持寄存器 0x0010", 0x03, 0x0010, 1),
            ("读取保持寄存器 0x0100", 0x03, 0x0100, 1),
            ("读取保持寄存器 0x1000", 0x03, 0x1000, 1),
            ("读取输入寄存器 0x0000", 0x04, 0x0000, 1),
            ("读取输入寄存器 0x0001", 0x04, 0x0001, 1),
            ("读取输入寄存器 0x0010", 0x04, 0x0010, 1),
            ("读取线圈状态 0x0000", 0x01, 0x0000, 8),
            ("读取离散输入 0x0000", 0x02, 0x0000, 8),
        ]
        
        for desc, func_code, start_addr, count in test_cases:
            print(f"\n  📋 {desc}")
            command = self.build_modbus_command(device_addr, func_code, start_addr, count)
            response = self.send_command(command)
            
            if response:
                if len(response) >= 3 and response[0] == device_addr:
                    if response[1] == func_code:
                        print(f"    ✅ 成功 - 数据长度: {len(response)} 字节")
                    elif response[1] & 0x80:
                        error_code = response[2] if len(response) > 2 else 0
                        print(f"    ⚠️ 错误响应 - 错误码: {error_code:02X}")
                else:
                    print(f"    ❌ 响应格式错误")
            else:
                print(f"    ❌ 无响应")
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        print("🔧 JCY5001AS 设备通信诊断工具")
        print("=" * 50)
        
        if not self.connect():
            return
        
        try:
            # 1. 扫描设备地址
            valid_addresses = self.scan_device_addresses(1, 10)
            
            if not valid_addresses:
                print("\n❌ 未找到响应的设备地址")
                print("💡 建议检查:")
                print("   - 设备是否正确连接")
                print("   - 串口号是否正确")
                print("   - 波特率是否匹配")
                print("   - 设备是否已开机")
            else:
                print(f"\n✅ 找到响应的设备地址: {valid_addresses}")
                
                # 2. 测试每个有效地址的基本命令
                for addr in valid_addresses:
                    self.test_basic_commands(addr)
                
                # 3. 给出建议
                print(f"\n💡 建议配置:")
                print(f"   - 设备地址: {valid_addresses[0]}")
                print(f"   - 串口: {self.port}")
                print(f"   - 波特率: {self.baudrate}")
                
        finally:
            self.disconnect()

def main():
    """主函数"""
    import sys
    
    port = "COM13"
    if len(sys.argv) > 1:
        port = sys.argv[1]
    
    diagnostic = DeviceDiagnostic(port)
    diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    main()
