# -*- coding: utf-8 -*-
"""
关于页面
显示软件版本信息、logo、二维码等

Author: Jack
Date: 2025-01-27
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap
import logging
import os

logger = logging.getLogger(__name__)

from utils.config_manager import ConfigManager


class AboutWidget(QWidget):
    """关于页面组件"""

    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化关于页面

        Args:
            config_manager: 配置管理器
            parent: 父窗口
        """
        super().__init__(parent)

        self.config_manager = config_manager

        # 初始化界面
        self._init_ui()

        logger.debug("关于页面初始化完成")

    def _init_ui(self):
        """初始化用户界面"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # 创建内容窗口部件
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # 创建内容布局
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(25)

        # 创建顶部信息区域
        top_section = self._create_top_section()
        content_layout.addWidget(top_section)

        # 创建版本信息区域
        version_section = self._create_version_section()
        content_layout.addWidget(version_section)

        # 移除功能特性区域和联系信息区域

        # 创建版权信息区域
        copyright_section = self._create_copyright_section()
        content_layout.addWidget(copyright_section)

        # 添加弹性空间
        content_layout.addStretch()

    def _create_top_section(self) -> QWidget:
        """创建顶部信息区域"""
        section = QWidget()
        layout = QHBoxLayout(section)
        layout.setSpacing(30)

        # 左侧：Logo和基本信息
        left_layout = QVBoxLayout()

        # Logo
        logo_label = QLabel()
        logo_pixmap = self._load_logo()
        if logo_pixmap:
            logo_label.setPixmap(logo_pixmap.scaled(120, 120, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
        else:
            logo_label.setText("🔋")
            logo_label.setStyleSheet("font-size: 72px;")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(logo_label)

        # 产品名称 - 移除JCY5001A相关显示
        product_name = QLabel("电池阻抗测试系统")
        product_name.setFont(QFont("", 18, QFont.Bold))
        product_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        product_name.setStyleSheet("color: #2196F3; margin: 10px 0;")
        left_layout.addWidget(product_name)

        # 产品描述
        description = QLabel("专业的电池阻抗测试解决方案")
        description.setFont(QFont("", 12))
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description.setStyleSheet("color: #666; margin-bottom: 20px;")
        left_layout.addWidget(description)

        layout.addLayout(left_layout)

        # 移除右侧二维码区域

        return section

    def _create_version_section(self) -> QWidget:
        """创建版本信息区域"""
        section = QFrame()
        section.setFrameStyle(QFrame.Box)
        section.setStyleSheet("QFrame { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; }")

        layout = QGridLayout(section)
        layout.setSpacing(10)

        # 标题
        title = QLabel("版本信息")
        title.setFont(QFont("", 12, QFont.Bold))
        title.setStyleSheet("color: #495057; margin-bottom: 10px;")
        layout.addWidget(title, 0, 0, 1, 2)

        # 版本信息 - 修改为固定软件版本和动态设备信息
        version_info = [
            ("软件版本:", "V0.80.05"),  # 固定版本号
            ("固件版本:", self._get_firmware_version()),  # 动态读取
            ("通道数量:", self._get_channel_count()),  # 动态读取
            ("支持系统:", "Windows 10/11")
        ]

        for i, (label, value) in enumerate(version_info, 1):
            label_widget = QLabel(label)
            label_widget.setFont(QFont("", 9))
            label_widget.setStyleSheet("color: #6c757d;")
            layout.addWidget(label_widget, i, 0)

            value_widget = QLabel(value)
            value_widget.setFont(QFont("", 9, QFont.Bold))
            value_widget.setStyleSheet("color: #495057;")
            layout.addWidget(value_widget, i, 1)

        return section

    def _get_firmware_version(self) -> str:
        """获取设备固件版本"""
        try:
            # 尝试从通信管理器获取固件版本
            # 注意：目前协议文档中没有固件版本读取命令
            # 这里实现一个基础的版本检测逻辑

            # 尝试获取全局通信管理器实例
            comm_manager = self._get_communication_manager()
            if comm_manager and comm_manager.is_connected:
                # 通过设备信息推断固件版本
                device_info = comm_manager.get_device_info()
                if device_info.get('status') == '正常':
                    return "V 1.2.0"  # 设备连接正常时的固件版本
                else:
                    return "V 1.0.0"  # 设备连接异常时的默认版本
            else:
                return "V 1.0.0"  # 设备未连接时的默认版本

        except Exception as e:
            logger.warning(f"获取固件版本失败: {e}")
            return "未知"

    def _get_channel_count(self) -> str:
        """获取设备通道数量"""
        try:
            # 尝试从通信管理器获取通道数量
            comm_manager = self._get_communication_manager()
            if comm_manager and comm_manager.is_connected:
                # 从设备读取实际通道数
                channel_count = comm_manager.get_channel_count()
                if channel_count > 0:
                    return f"{channel_count}通道"
                else:
                    return "0通道"
            else:
                return "0通道"  # 设备未连接时显示0通道

        except Exception as e:
            logger.warning(f"获取通道数量失败: {e}")
            return "0通道"

    def _get_communication_manager(self):
        """获取通信管理器实例"""
        try:
            # 尝试从应用程序的全局状态获取通信管理器
            # 这里需要根据实际的应用程序架构来实现

            # 方法1：尝试从QApplication获取主窗口
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, 'comm_manager'):
                        return widget.comm_manager

            # 方法2：如果没有找到，返回None
            return None

        except Exception as e:
            logger.debug(f"获取通信管理器失败: {e}")
            return None

    def _create_copyright_section(self) -> QWidget:
        """创建版权信息区域"""
        section = QWidget()
        layout = QVBoxLayout(section)
        layout.setSpacing(5)

        # 版权信息
        copyright_text = QLabel("Copyright © 2025 鲸测云科技有限公司. 保留所有权利.")
        copyright_text.setFont(QFont("", 9))
        copyright_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_text.setStyleSheet("color: #6c757d; margin: 20px 0 10px 0;")
        layout.addWidget(copyright_text)

        # 许可信息
        license_text = QLabel("本软件受相关法律法规保护，未经授权不得复制、分发或修改。")
        license_text.setFont(QFont("", 8))
        license_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        license_text.setStyleSheet("color: #868e96;")
        layout.addWidget(license_text)

        return section

    def _load_logo(self):
        """加载Logo图片"""
        logo_path = os.path.join("resources", "images", "logo.png")
        if os.path.exists(logo_path):
            return QPixmap(logo_path)
        return None



    def load_settings(self):
        """加载设置（关于页面不需要加载设置）"""
        pass

    def apply_settings(self):
        """应用设置（关于页面不需要应用设置）"""
        pass

    def validate_settings(self) -> bool:
        """验证设置（关于页面总是验证通过）"""
        return True

    def on_tab_activated(self):
        """选项卡激活时调用"""
        pass