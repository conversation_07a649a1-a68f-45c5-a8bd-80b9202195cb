#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查可用串口
"""

import serial.tools.list_ports

def check_available_ports():
    """检查可用的串口"""
    print("🔌 检查可用串口...")
    print("=" * 40)
    
    ports = serial.tools.list_ports.comports()
    
    if ports:
        print(f"发现 {len(ports)} 个串口:")
        for i, port in enumerate(ports, 1):
            print(f"   {i}. {port.device}")
            print(f"      描述: {port.description}")
            print(f"      硬件ID: {port.hwid}")
            print()
    else:
        print("❌ 未发现可用串口")
    
    return [port.device for port in ports]

if __name__ == "__main__":
    available_ports = check_available_ports()
    
    if available_ports:
        print(f"✅ 可用串口列表: {available_ports}")
    else:
        print("❌ 没有可用串口")
