#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通道显示组件字体显示修复效果
验证Rs和Rct阻抗值显示区域以及电池码扫码区域的字体显示问题修复

Author: Assistant
Date: 2025-01-27
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ChannelFontTestWindow(QMainWindow):
    """通道字体显示测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JCY5001AS 通道字体显示修复测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        self._add_description(main_layout)
        
        # 创建对比测试
        self._create_comparison_test(main_layout)
    
    def _add_description(self, layout):
        """添加说明文字"""
        desc_label = QLabel("JCY5001AS 通道显示组件字体显示修复测试")
        desc_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 12px;
                background-color: #ecf0f1;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(desc_label)
        
        # 添加修复说明
        changes_label = QLabel("""
        <b>主要修复内容：</b><br>
        • <b>左右列权重调整</b>：左列2份权重 → 右列3份权重（给Rs/Rct更多空间）<br>
        • <b>Rs和Rct显示优化</b>：标题最小宽度60px，数值最小宽度80px，字体12pt<br>
        • <b>电池码输入优化</b>：标签最小宽度50px，输入框最小宽度120px<br>
        • <b>电压显示优化</b>：标签最小宽度50px，数值最小宽度60px<br>
        • <b>样式改进</b>：增加内边距、圆角，确保文字完整显示
        """)
        changes_label.setFont(QFont("Microsoft YaHei", 10))
        changes_label.setStyleSheet("""
            QLabel {
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                line-height: 1.6;
            }
        """)
        layout.addWidget(changes_label)
    
    def _create_comparison_test(self, layout):
        """创建对比测试"""
        comparison_widget = QWidget()
        comparison_layout = QHBoxLayout(comparison_widget)
        comparison_layout.setSpacing(20)
        
        # 修复前模拟
        before_widget = self._create_channel_demo("修复前效果", {
            "left_weight": 3,
            "right_weight": 2,
            "rs_title_width": "auto",
            "rs_value_width": "auto",
            "battery_input_width": "auto",
            "font_size": "11pt",
            "padding": "2px 4px"
        })
        comparison_layout.addWidget(before_widget)
        
        # 修复后效果
        after_widget = self._create_channel_demo("修复后效果", {
            "left_weight": 2,
            "right_weight": 3,
            "rs_title_width": "60px",
            "rs_value_width": "80px",
            "battery_input_width": "120px",
            "font_size": "12pt",
            "padding": "3px 6px"
        })
        comparison_layout.addWidget(after_widget)
        
        layout.addWidget(comparison_widget)
    
    def _create_channel_demo(self, title, config):
        """创建通道演示"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout(demo_widget)
        demo_layout.setContentsMargins(10, 10, 10, 10)
        demo_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                background-color: #3498db;
                padding: 8px;
                border-radius: 4px;
                font-size: 12pt;
            }}
        """)
        demo_layout.addWidget(title_label)
        
        # 通道模拟框
        channel_frame = QFrame()
        channel_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
        """)
        channel_layout = QVBoxLayout(channel_frame)
        
        # 通道标题
        channel_title = QLabel("通道 1")
        channel_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        channel_layout.addWidget(channel_title)
        
        # 主内容区域
        main_content = QFrame()
        main_content_layout = QHBoxLayout(main_content)
        
        # 左列
        left_column = self._create_left_column_demo(config)
        main_content_layout.addLayout(left_column, config["left_weight"])
        
        # 右列
        right_column = self._create_right_column_demo(config)
        main_content_layout.addLayout(right_column, config["right_weight"])
        
        channel_layout.addWidget(main_content)
        
        # 添加配置信息
        info_label = QLabel(f"""
        左列权重: {config['left_weight']} | 右列权重: {config['right_weight']}
        Rs标题宽度: {config['rs_title_width']} | Rs数值宽度: {config['rs_value_width']}
        电池码输入宽度: {config['battery_input_width']}
        字体大小: {config['font_size']} | 内边距: {config['padding']}
        """)
        info_label.setFont(QFont("Microsoft YaHei", 8))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        demo_layout.addWidget(info_label)
        
        demo_layout.addWidget(channel_frame)
        return demo_widget
    
    def _create_left_column_demo(self, config):
        """创建左列演示"""
        left_layout = QVBoxLayout()
        
        # 测试计数
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("测试计数:"))
        count_layout.addWidget(QLabel("45"))
        count_layout.addStretch()
        left_layout.addLayout(count_layout)
        
        # 电池码输入
        battery_layout = QHBoxLayout()
        battery_label = QLabel("电池码:")
        battery_layout.addWidget(battery_label)
        
        battery_input = QLabel("JCY-20250705-6274")
        battery_input.setStyleSheet(f"""
            QLabel {{
                border: 1px solid #bdc3c7;
                padding: 4px;
                background-color: white;
                min-width: {config['battery_input_width']};
            }}
        """)
        battery_layout.addWidget(battery_input)
        battery_layout.addStretch()
        left_layout.addLayout(battery_layout)
        
        # 电压显示
        voltage_layout = QHBoxLayout()
        voltage_layout.addWidget(QLabel("电压(V):"))
        voltage_layout.addWidget(QLabel("3.984"))
        voltage_layout.addStretch()
        left_layout.addLayout(voltage_layout)
        
        left_layout.addStretch()
        return left_layout
    
    def _create_right_column_demo(self, config):
        """创建右列演示"""
        right_layout = QVBoxLayout()
        
        # Rs显示
        rs_layout = QHBoxLayout()
        rs_title = QLabel("Rs(mΩ)")
        rs_title.setStyleSheet(f"min-width: {config['rs_title_width']};")
        rs_layout.addWidget(rs_title)
        
        rs_value = QLabel("17.807mΩ")
        rs_value.setStyleSheet(f"""
            QLabel {{
                font-size: {config['font_size']};
                font-weight: bold;
                padding: {config['padding']};
                border: 1px solid #ecf0f1;
                border-radius: 4px;
                background-color: #f8f9fa;
                min-width: {config['rs_value_width']};
            }}
        """)
        rs_layout.addWidget(rs_value)
        rs_layout.addStretch()
        right_layout.addLayout(rs_layout)
        
        # Rct显示
        rct_layout = QHBoxLayout()
        rct_title = QLabel("Rct(mΩ)")
        rct_title.setStyleSheet(f"min-width: {config['rs_title_width']};")
        rct_layout.addWidget(rct_title)
        
        rct_value = QLabel("2.224mΩ")
        rct_value.setStyleSheet(f"""
            QLabel {{
                font-size: {config['font_size']};
                font-weight: bold;
                padding: {config['padding']};
                border: 1px solid #ecf0f1;
                border-radius: 4px;
                background-color: #f8f9fa;
                min-width: {config['rs_value_width']};
            }}
        """)
        rct_layout.addWidget(rct_value)
        rct_layout.addStretch()
        right_layout.addLayout(rct_layout)
        
        # 离群率
        outlier_layout = QHBoxLayout()
        outlier_layout.addWidget(QLabel("离群率:"))
        outlier_layout.addWidget(QLabel("检测失效"))
        outlier_layout.addStretch()
        right_layout.addLayout(outlier_layout)
        
        # 容量
        capacity_layout = QHBoxLayout()
        capacity_layout.addWidget(QLabel("容量:"))
        capacity_layout.addWidget(QLabel("--"))
        capacity_layout.addStretch()
        right_layout.addLayout(capacity_layout)
        
        right_layout.addStretch()
        return right_layout


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        
        # 设置应用程序样式
        app.setStyle('Fusion')
        
        # 创建测试窗口
        window = ChannelFontTestWindow()
        window.show()
        
        logger.info("通道字体显示修复测试窗口已启动")
        logger.info("主要修复：")
        logger.info("• 左右列权重调整：左列2份 → 右列3份")
        logger.info("• Rs/Rct显示优化：标题60px，数值80px，字体12pt")
        logger.info("• 电池码输入优化：输入框最小宽度120px")
        logger.info("• 样式改进：增加内边距和圆角")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
