2025-07-02 19:18:03,696 [INFO] __main__: 🚀 启动UI显示修复验证测试
2025-07-02 19:18:03,697 [INFO] __main__: 🔍 开始测试UI显示修复...
2025-07-02 19:18:03,697 [INFO] __main__: 1. 测试配置管理器...
2025-07-02 19:18:03,716 [INFO] utils.config_manager: 配置文件加载成功: config\app_config.json
2025-07-02 19:18:03,717 [INFO] __main__: ✅ 配置管理器初始化成功
2025-07-02 19:18:03,717 [INFO] __main__: 2. 测试通信管理器...
2025-07-02 19:18:04,384 [INFO] backend.communication_manager: 🚀 开始初始化通信管理器
2025-07-02 19:18:04,385 [INFO] backend.modbus_protocol_handler: Modbus协议处理器初始化完成，设备地址: 1
2025-07-02 19:18:04,385 [INFO] backend.serial_connection_manager: 串口连接管理器初始化完成: COM16, 115200
2025-07-02 19:18:04,386 [INFO] backend.device_command_manager: 设备命令管理器初始化完成
2025-07-02 19:18:04,386 [INFO] backend.data_read_manager: 数据读取管理器初始化完成
2025-07-02 19:18:04,386 [INFO] backend.device_status_manager: 设备状态码管理器初始化完成
2025-07-02 19:18:04,386 [INFO] backend.communication_manager: ✅ 初始化通信管理器成功 (耗时=0.002s)
2025-07-02 19:18:04,387 [INFO] backend.communication_manager: ✅ 通信管理器初始化成功 (端口=COM16, 波特率=115200, 设备地址=1)
2025-07-02 19:18:04,387 [ERROR] __main__: ❌ 测试失败: 'bool' object is not callable
2025-07-02 19:18:04,388 [ERROR] __main__: 详细错误: Traceback (most recent call last):
  File "E:\gaga\JCY5001AS_Clean_Source\test_ui_display_fix.py", line 48, in test_ui_display_fix
    logger.info(f"通信管理器连接状态: {comm_manager.is_connected()}")
                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'bool' object is not callable

2025-07-02 19:18:04,389 [ERROR] __main__: ❌ 测试失败，需要进一步检查
2025-07-02 19:18:27,422 [INFO] __main__: 🚀 启动UI显示修复验证测试
2025-07-02 19:18:27,422 [INFO] __main__: 🔍 开始测试UI显示修复...
2025-07-02 19:18:27,423 [INFO] __main__: 1. 测试配置管理器...
2025-07-02 19:18:27,443 [INFO] utils.config_manager: 配置文件加载成功: config\app_config.json
2025-07-02 19:18:27,444 [INFO] __main__: ✅ 配置管理器初始化成功
2025-07-02 19:18:27,444 [INFO] __main__: 2. 测试通信管理器...
2025-07-02 19:18:28,018 [INFO] backend.communication_manager: 🚀 开始初始化通信管理器
2025-07-02 19:18:28,019 [INFO] backend.modbus_protocol_handler: Modbus协议处理器初始化完成，设备地址: 1
2025-07-02 19:18:28,019 [INFO] backend.serial_connection_manager: 串口连接管理器初始化完成: COM16, 115200
2025-07-02 19:18:28,019 [INFO] backend.device_command_manager: 设备命令管理器初始化完成
2025-07-02 19:18:28,020 [INFO] backend.data_read_manager: 数据读取管理器初始化完成
2025-07-02 19:18:28,020 [INFO] backend.device_status_manager: 设备状态码管理器初始化完成
2025-07-02 19:18:28,020 [INFO] backend.communication_manager: ✅ 初始化通信管理器成功 (耗时=0.002s)
2025-07-02 19:18:28,021 [INFO] backend.communication_manager: ✅ 通信管理器初始化成功 (端口=COM16, 波特率=115200, 设备地址=1)
2025-07-02 19:18:28,021 [INFO] __main__: 通信管理器连接状态: False
2025-07-02 19:18:28,021 [INFO] __main__: 3. 测试UI组件...
2025-07-02 19:18:28,045 [INFO] __main__: 4. 测试通道显示组件...
2025-07-02 19:18:28,285 [INFO] __main__: ✅ 通道显示组件创建成功
2025-07-02 19:18:28,285 [INFO] __main__: 5. 测试数据更新...
2025-07-02 19:18:28,286 [INFO] ui.components.channel_display_widget: 🎯 [进度管理] 通道1检测到新测试开始，重置进度状态
2025-07-02 19:18:28,286 [INFO] ui.components.channel_display_widget: 🎯 [进度管理] 通道1进度状态重置: 0%/0% -> 0%/0%
2025-07-02 19:18:28,287 [INFO] __main__: ✅ 数据更新成功: V=3.75V, Rs=1.234mΩ, Rct=2.567mΩ
2025-07-02 19:18:28,292 [INFO] __main__: 6. 测试结果设置...
2025-07-02 19:18:28,292 [INFO] ui.components.channel_display_widget: 🔧 通道1设置测试完成状态: 不合格, Rs档位=2, Rct档位=3
2025-07-02 19:18:28,293 [INFO] utils.config_manager: 🔧 配置已更新: test_count.channel_1 = 42
2025-07-02 19:18:28,294 [INFO] ui.components.channel_display_widget: 通道1测试计数增加: 42
2025-07-02 19:18:28,294 [INFO] ui.components.channel_display_widget: 🔧 [打印数据] 通道1构建test_result: Rs=1.234mΩ, Rct=2.567mΩ, V=3.750V
2025-07-02 19:18:28,295 [INFO] ui.components.channel_display_widget: ✅ 通道1测试完成状态已设置: 不合格, 档位2-3
2025-07-02 19:18:28,296 [INFO] __main__: ✅ 测试结果设置成功
2025-07-02 19:18:28,296 [INFO] __main__: 7. 验证UI更新器...
2025-07-02 19:18:28,296 [INFO] __main__: ✅ UI更新器存在
2025-07-02 19:18:28,297 [INFO] __main__: ✅ UI更新器功能正常
2025-07-02 19:18:28,297 [INFO] __main__: 8. 测试模拟模式...
2025-07-02 19:18:28,313 [ERROR] __main__: ❌ 测试失败: TestFlowManagerAdapter.__init__() missing 2 required positional arguments: 'comm_manager' and 'device_connection_manager'
2025-07-02 19:18:28,314 [ERROR] __main__: 详细错误: Traceback (most recent call last):
  File "E:\gaga\JCY5001AS_Clean_Source\test_ui_display_fix.py", line 106, in test_ui_display_fix
    test_adapter = TestFlowManagerAdapter(None, config_manager)  # main_window为None用于测试
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: TestFlowManagerAdapter.__init__() missing 2 required positional arguments: 'comm_manager' and 'device_connection_manager'

2025-07-02 19:18:28,317 [ERROR] __main__: ❌ 测试失败，需要进一步检查
2025-07-02 19:18:55,475 [INFO] __main__: 🚀 启动UI显示修复验证测试
2025-07-02 19:18:55,475 [INFO] __main__: 🔍 开始测试UI显示修复...
2025-07-02 19:18:55,475 [INFO] __main__: 1. 测试配置管理器...
2025-07-02 19:18:55,495 [INFO] utils.config_manager: 配置文件加载成功: config\app_config.json
2025-07-02 19:18:55,495 [INFO] __main__: ✅ 配置管理器初始化成功
2025-07-02 19:18:55,495 [INFO] __main__: 2. 测试通信管理器...
2025-07-02 19:18:56,064 [INFO] backend.communication_manager: 🚀 开始初始化通信管理器
2025-07-02 19:18:56,064 [INFO] backend.modbus_protocol_handler: Modbus协议处理器初始化完成，设备地址: 1
2025-07-02 19:18:56,064 [INFO] backend.serial_connection_manager: 串口连接管理器初始化完成: COM16, 115200
2025-07-02 19:18:56,065 [INFO] backend.device_command_manager: 设备命令管理器初始化完成
2025-07-02 19:18:56,065 [INFO] backend.data_read_manager: 数据读取管理器初始化完成
2025-07-02 19:18:56,065 [INFO] backend.device_status_manager: 设备状态码管理器初始化完成
2025-07-02 19:18:56,065 [INFO] backend.communication_manager: ✅ 初始化通信管理器成功 (耗时=0.001s)
2025-07-02 19:18:56,066 [INFO] backend.communication_manager: ✅ 通信管理器初始化成功 (端口=COM16, 波特率=115200, 设备地址=1)
2025-07-02 19:18:56,066 [INFO] __main__: 通信管理器连接状态: False
2025-07-02 19:18:56,066 [INFO] __main__: 3. 测试UI组件...
2025-07-02 19:18:56,089 [INFO] __main__: 4. 测试通道显示组件...
2025-07-02 19:18:56,310 [INFO] __main__: ✅ 通道显示组件创建成功
2025-07-02 19:18:56,311 [INFO] __main__: 5. 测试数据更新...
2025-07-02 19:18:56,311 [INFO] ui.components.channel_display_widget: 🎯 [进度管理] 通道1检测到新测试开始，重置进度状态
2025-07-02 19:18:56,311 [INFO] ui.components.channel_display_widget: 🎯 [进度管理] 通道1进度状态重置: 0%/0% -> 0%/0%
2025-07-02 19:18:56,312 [INFO] __main__: ✅ 数据更新成功: V=3.75V, Rs=1.234mΩ, Rct=2.567mΩ
2025-07-02 19:18:56,312 [INFO] __main__: 6. 测试结果设置...
2025-07-02 19:18:56,313 [INFO] ui.components.channel_display_widget: 🔧 通道1设置测试完成状态: 不合格, Rs档位=2, Rct档位=3
2025-07-02 19:18:56,315 [INFO] utils.config_manager: 🔧 配置已更新: test_count.channel_1 = 42
2025-07-02 19:18:56,315 [INFO] ui.components.channel_display_widget: 通道1测试计数增加: 42
2025-07-02 19:18:56,316 [INFO] ui.components.channel_display_widget: 🔧 [打印数据] 通道1构建test_result: Rs=1.234mΩ, Rct=2.567mΩ, V=3.750V
2025-07-02 19:18:56,316 [INFO] ui.components.channel_display_widget: ✅ 通道1测试完成状态已设置: 不合格, 档位2-3
2025-07-02 19:18:56,316 [INFO] __main__: ✅ 测试结果设置成功
2025-07-02 19:18:56,317 [INFO] __main__: 7. 验证UI更新器...
2025-07-02 19:18:56,318 [INFO] __main__: ✅ UI更新器存在
2025-07-02 19:18:56,318 [INFO] __main__: ✅ UI更新器功能正常
2025-07-02 19:18:56,318 [INFO] __main__: 8. 测试模拟模式...
2025-07-02 19:18:56,325 [INFO] ui.device_connection_manager: 设备连接管理器初始化完成
2025-07-02 19:18:56,326 [INFO] ui.test_flow_managers.test_flow_manager_adapter: 🔧 初始化重构后的管理器...
2025-07-02 19:18:56,327 [INFO] ui.test_flow_managers.test_flow_controller: 测试流程控制器初始化完成
2025-07-02 19:18:56,327 [INFO] ui.test_flow_managers.test_precheck_manager: 测试前预检查管理器初始化完成
2025-07-02 19:18:56,327 [INFO] ui.test_flow_managers.test_configuration_manager: 测试配置管理器初始化完成
2025-07-02 19:18:56,328 [INFO] ui.test_flow_managers.test_statistics_manager: 测试统计管理器初始化完成
2025-07-02 19:18:56,328 [INFO] ui.test_flow_managers.test_ui_update_manager: 测试UI更新管理器初始化完成
2025-07-02 19:18:56,328 [INFO] ui.test_flow_managers.test_error_handler: 测试错误处理器初始化完成
2025-07-02 19:18:56,328 [INFO] ui.test_flow_managers.test_flow_controller: 测试流程控制器依赖管理器设置完成
2025-07-02 19:18:56,328 [INFO] ui.test_flow_managers.test_flow_manager_adapter: ✅ 重构后的管理器初始化完成
2025-07-02 19:18:56,332 [WARNING] ui.test_flow_managers.test_flow_manager_adapter: ❌ 主界面UI组件管理器未找到，将稍后重试
2025-07-02 19:18:56,332 [INFO] ui.test_flow_managers.test_flow_manager_adapter: 测试流程管理器适配器初始化完成
2025-07-02 19:18:56,332 [INFO] ui.test_flow_managers.test_flow_manager_adapter: 🎭 启动模拟测试模式...
2025-07-02 19:18:56,333 [INFO] ui.test_flow_managers.test_flow_manager_adapter: ✅ 模拟测试模式启动成功
2025-07-02 19:18:56,334 [INFO] __main__: ✅ 模拟模式启动成功
2025-07-02 19:18:56,334 [INFO] __main__: 🎉 UI显示修复测试完成
2025-07-02 19:18:56,337 [INFO] __main__: ✅ 所有测试通过，UI显示修复验证成功
