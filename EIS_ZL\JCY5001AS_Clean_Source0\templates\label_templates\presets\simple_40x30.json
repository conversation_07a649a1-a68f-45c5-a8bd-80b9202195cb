{"template_id": "simple_40x30", "name": "简化模板", "description": "40x30mm简化标签模板，突出重点信息", "size": "40x30mm", "elements": [{"element_id": "title", "element_type": "text", "x": 10, "y": 8, "width": 180, "height": 20, "content": "电池测试", "font_family": "微软雅黑", "font_size": 18, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "battery_code", "element_type": "text", "x": 10, "y": 30, "width": 180, "height": 18, "content": "{battery_code}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "impedance_values", "element_type": "text", "x": 10, "y": 50, "width": 180, "height": 18, "content": "Rs:{rs_value:.2f} Rct:{rct_value:.2f}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "grades", "element_type": "text", "x": 10, "y": 70, "width": 180, "height": 18, "content": "档位: G{rs_grade}-G{rct_grade}", "font_family": "微软雅黑", "font_size": 12, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "status", "element_type": "text", "x": 10, "y": 90, "width": 100, "height": 20, "content": "{is_pass}", "font_family": "微软雅黑", "font_size": 16, "font_style": "bold", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}, {"element_id": "qr_code", "element_type": "qr_code", "x": 220, "y": 50, "width": 70, "height": 70, "content": "{battery_code}", "font_family": "微软雅黑", "font_size": 14, "font_style": "normal", "text_color": "black", "text_alignment": "left", "qr_error_correction": "M", "barcode_type": "CODE128", "background_color": "transparent", "border_width": 0, "border_color": "black", "visible": true}], "version": "1.0", "created_time": "2025-06-05T17:20:57.712871", "modified_time": "2025-06-05T17:20:57.712871", "author": "系统预设"}